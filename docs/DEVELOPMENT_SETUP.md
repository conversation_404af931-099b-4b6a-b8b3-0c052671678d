# SIMILE Development Setup Guide

## Overview

This guide covers the hot reload development setup for SIMILE using Docker with volume mounts. This solution addresses the previous issue where code changes weren't automatically picked up by the containers.

## Quick Start

```bash
# Start development environment with hot reload (recommended)
docker-compose -f docker-compose.dev.yml up -d

# View logs to see services starting up
docker-compose -f docker-compose.dev.yml logs -f

# Make changes to code in backend/src/ or frontend/src/
# Changes will be automatically reflected without rebuilding containers
```

## Architecture

### Development vs Production

- **Development** (`docker-compose.dev.yml`): Uses volume mounts for hot reload
- **Production** (`docker-compose.yml`): Copies code into containers for optimized deployment

### Volume Mounting Strategy

The development setup mounts source directories as read-only volumes:

```yaml
volumes:
  # Backend
  - ./backend/src:/app/src:ro
  - ./backend/tests:/app/tests:ro
  
  # Frontend  
  - ./frontend/src:/app/src:ro
  - ./frontend/public:/app/public:ro
```

## Setup Requirements

### Prerequisites

1. **Docker & Docker Compose**
   ```bash
   docker --version  # Should be >= 20.0
   docker-compose --version  # Should be >= 2.0
   ```

2. **Available Ports**
   - 3000: Frontend (React)
   - 8000: Backend (FastAPI)
   - 5432: Database (PostgreSQL)

### First Time Setup

```bash
# Clone and navigate to project
cd simile-web-app

# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Verify services are running
docker-compose -f docker-compose.dev.yml ps

# Check logs for any errors
docker-compose -f docker-compose.dev.yml logs
```

## Hot Reload Features

### Backend (FastAPI)
- **What reloads**: Python files in `backend/src/`
- **Reload trigger**: File save
- **Reload time**: ~1-2 seconds
- **Logs**: Watch `docker-compose -f docker-compose.dev.yml logs -f backend`

### Frontend (React)
- **What reloads**: Files in `frontend/src/`
- **Reload trigger**: File save
- **Reload time**: ~2-5 seconds  
- **Browser**: Auto-refreshes connected browsers
- **Logs**: Watch `docker-compose -f docker-compose.dev.yml logs -f frontend`

### Database
- **Persistence**: Data survives container restarts
- **Migrations**: Apply manually when schema changes
- **Reset**: Use `docker-compose -f docker-compose.dev.yml down -v` to reset data

## Development Workflow

### Daily Development
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Work on code (changes auto-reload)
# - Edit files in backend/src/ or frontend/src/
# - Save changes
# - See changes reflected in browser/API

# View logs when debugging
docker-compose -f docker-compose.dev.yml logs -f [service-name]

# Stop when done
docker-compose -f docker-compose.dev.yml down
```

### Testing Changes
```bash
# Backend tests (while containers running)
cd backend
source venv/bin/activate
./run_tests.sh

# Frontend tests  
cd frontend
npm test

# E2E tests (requires containers running)
cd frontend  
npm run test:e2e
```

### Production Testing
```bash
# Test production build
docker-compose build
docker-compose up -d

# Verify production deployment works
curl http://localhost:8000/health
curl http://localhost:3000

# Clean up
docker-compose down
```

## Troubleshooting

### Common Issues

#### 1. "Port already in use" Error
```bash
# Check what's using the ports
lsof -i :3000  # Frontend
lsof -i :8000  # Backend  
lsof -i :5432  # Database

# Stop conflicting services or use different ports
# Edit docker-compose.dev.yml to change port mappings
```

#### 2. Volume Mount Permission Issues
```bash
# If you see permission denied errors:
# 1. Check file ownership
ls -la backend/src/

# 2. Try without read-only flag (remove :ro from volumes)
# Edit docker-compose.dev.yml volumes section

# 3. On macOS, ensure Docker has file access permissions
# System Preferences > Security & Privacy > Privacy > Files and Folders
```

#### 3. Hot Reload Not Working

**Backend not reloading:**
```bash
# Check uvicorn is running with --reload
docker-compose -f docker-compose.dev.yml logs backend | grep reload

# Verify volume mount
docker-compose -f docker-compose.dev.yml exec backend ls -la /app/src/

# Test manual restart
docker-compose -f docker-compose.dev.yml restart backend
```

**Frontend not reloading:**
```bash
# Check React dev server is running
docker-compose -f docker-compose.dev.yml logs frontend | grep webpack

# Verify polling is enabled (for some filesystems)
# Check CHOKIDAR_USEPOLLING=true in docker-compose.dev.yml

# Test manual restart
docker-compose -f docker-compose.dev.yml restart frontend
```

#### 4. Database Connection Issues
```bash
# Check database is healthy
docker-compose -f docker-compose.dev.yml ps database

# Test connection
docker-compose -f docker-compose.dev.yml exec database psql -U postgres -d simile -c "SELECT 1;"

# Reset database
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up -d
```

#### 5. Container Build Issues
```bash
# Force rebuild containers
docker-compose -f docker-compose.dev.yml build --no-cache

# Clean Docker system (removes unused images/containers)
docker system prune -f

# Remove specific images and rebuild
docker rmi simile-web-app_backend:latest
docker rmi simile-web-app_frontend:latest
docker-compose -f docker-compose.dev.yml build
```

### Performance Issues

#### Slow Hot Reload
```bash
# Enable polling for file watchers (frontend)
# Add to docker-compose.dev.yml frontend environment:
CHOKIDAR_USEPOLLING: true
WATCHPACK_POLLING: true

# Exclude unnecessary directories from volume mounts
# Add to frontend volumes:
- /app/node_modules  # Prevents host node_modules conflicts
- /app/build         # Prevents build artifacts sync
```

#### High Resource Usage
```bash
# Monitor container resource usage
docker stats

# Limit container resources in docker-compose.dev.yml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
```

### Switching Between Podman and Docker

If you need to use Podman instead of Docker:

```bash
# Stop Docker containers first
docker-compose -f docker-compose.dev.yml down

# Use Podman (may have volume mount issues)
podman-compose up -d

# If volume mounts fail with Podman, use rebuild workflow:
# 1. Make code changes
# 2. Rebuild specific service: podman-compose build backend  
# 3. Restart service: podman-compose restart backend
```

## Integration with IDE

### VS Code
- Install Docker extension for container management
- Use "Remote-Containers" extension to develop inside containers
- Configure file watchers to trigger on save

### File Watching
The development setup uses these file watchers:
- **FastAPI**: uvicorn built-in reload (Python files)
- **React**: webpack-dev-server (JS/TS/CSS files)
- **Polling fallback**: Enabled for networked filesystems

## Production Deployment

When ready to deploy:

```bash
# Build production images
docker-compose build

# Test production locally
docker-compose up -d

# Deploy to production environment
# (use docker-compose.yml, not docker-compose.dev.yml)
```

## Comparison: Before vs After

### Before (Copy-based)
- Code changes required container rebuilds
- 30-60 second feedback loop
- Full container restart needed
- Development productivity bottleneck

### After (Volume-based)  
- Code changes instantly reflected
- 1-5 second feedback loop
- No rebuilds required during development
- Optimized for development speed

## Support

If you encounter issues not covered here:

1. Check container logs: `docker-compose -f docker-compose.dev.yml logs [service]`
2. Verify Docker setup: `docker version && docker-compose version`  
3. Test with production setup: `docker-compose up -d`
4. Create GitHub issue with error logs and system details