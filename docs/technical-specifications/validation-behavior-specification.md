# Form Validation Behavior Specification
**Project**: SIMILE Web Application  
**Phase**: 1.1 - Form Validation Consistency  
**Date**: June 22, 2025  
**Author**: Development Team  
**For QA Handoff**: Phase 1.1 → 1.2 Transition  

## Overview
This document specifies the new consistent real-time validation behavior implemented across all forms in the SIMILE application. All forms now follow the same validation patterns for a consistent user experience.

## Validation Timing
**Pattern**: Real-time validation with enhanced user feedback
- **Trigger Events**: 
  - `onBlur` (when user leaves a field)
  - `onChange` (immediate feedback after first interaction)
  - `onSubmit` (final validation before form submission)
- **Initial State**: Neutral (no validation feedback until user interaction)
- **Touched State**: Validation activates after first user interaction with field

## Visual Validation States
All form inputs follow these visual states:

### 1. Neutral State (Default)
- **Appearance**: Standard input styling
- **Border**: Default gray (#ddd)
- **Background**: White
- **When**: Initial load, before user interaction

### 2. Valid State
- **Appearance**: Green success styling
- **Border**: Green (#28a745) with success shadow
- **Checkmark**: Green ✓ appears in help text
- **When**: <PERSON> passes all validation rules after user interaction

### 3. Invalid State
- **Appearance**: Red error styling
- **Border**: Red (#dc3545) with error shadow
- **Error Message**: Specific error text displayed below field
- **When**: Field fails validation rules after user interaction

## Form-Specific Validation Rules

### EntityForm (`/frontend/src/components/EntityForm.tsx`)
**Fields**: Entity Name (text input)

#### Entity Name Validation
- **Required**: Field cannot be empty
  - Error: "Entity name is required"
- **Length**: Maximum 20 characters
  - Error: "Entity name must be 20 characters or less"
- **Pattern**: Only letters and spaces allowed
  - Error: "Entity name can only contain letters and spaces"
- **Real-time**: Validates on blur and change after first interaction
- **Submit Button**: Disabled when field is invalid or empty

#### Implementation Details
```typescript
// Validation triggers
- onChange: Updates value and validates if touched
- onBlur: Marks as touched and validates
- onSubmit: Final validation before API call

// Visual feedback
- CSS classes: form-input, valid, invalid
- ARIA attributes: aria-invalid, aria-describedby
- Success indicator: ✓ Valid in help text
```

### ConnectionForm (`/frontend/src/components/ConnectionForm.tsx`)
**Fields**: From Entity (autocomplete), To Entity (autocomplete), Unit (select), Multiplier (number)

#### Entity Selection Validation
- **Required**: Both entities must be selected
  - Error: "Both entities must be selected"
- **Different**: From and To entities must be different
  - Error: "From and To entities must be different"
- **Real-time**: Validates when either entity selection changes
- **Shared Validation**: Both autocomplete fields share validation state

#### Unit Selection Validation
- **Required**: Unit must be selected
  - Error: "Unit is required"
- **Real-time**: Validates on selection change and blur

#### Multiplier Validation
- **Required**: Field cannot be empty
  - Error: "Multiplier is required"
- **Numeric**: Must be valid number
  - Error: "Multiplier must be a valid number"
- **Positive**: Must be greater than 0
  - Error: "Multiplier must be a positive number"
- **Decimal Places**: Maximum 1 decimal place
  - Error: "Multiplier must have at most 1 decimal place"
- **Real-time**: Validates on change and blur after first interaction

#### Implementation Details
```typescript
// Validation state management
- validationErrors: {[field]: string} - Error messages per field
- touched: {[field]: boolean} - Track user interaction
- validationStates: {[field]: 'neutral'|'valid'|'invalid'} - Visual states

// Submit button logic
- Disabled when any field is invalid or required fields empty
- All validations must pass before form submission
```

### ComparisonForm (`/frontend/src/components/ComparisonForm.tsx`)
**Fields**: From Entity (datalist), To Entity (datalist), Unit (select), Count (number)

#### Real-time Calculation
- **Auto-Calculate**: Automatically calculates comparison when all fields filled
- **Error Display**: Shows "No path found" when entities not connected
- **Loading State**: Shows "..." during calculation
- **Result Display**: Updates calculated value in real-time

#### Validation Behavior
- **Real-time**: Instant feedback and calculation
- **Error Handling**: Network and calculation errors displayed immediately
- **Accessibility**: Error messages with aria-live="polite"

## Accessibility Features
All forms implement comprehensive accessibility:

### ARIA Attributes
- `aria-invalid`: Indicates field validation state
- `aria-describedby`: Links fields to error messages and help text
- `role="alert"`: For error messages
- `aria-live="polite"`: For dynamic validation feedback

### Screen Reader Support
- Error messages announced when validation fails
- Success feedback announced when validation passes
- Field requirements clearly stated in labels and help text

### Keyboard Navigation
- Tab order follows logical field sequence
- All interactive elements keyboard accessible
- Form submission possible via Enter key

## Error Message Standards

### Message Characteristics
- **Specific**: Clear description of what's wrong
- **Actionable**: Tell user how to fix the issue
- **Friendly**: Helpful tone, not accusatory
- **Consistent**: Same language patterns across forms

### Error Message Templates
```
Required fields: "[Field name] is required"
Length validation: "[Field name] must be [X] characters or less"
Pattern validation: "[Field name] can only contain [allowed characters]"
Number validation: "[Field name] must be a [positive] number"
Selection validation: "[Field] must be [selected/different]"
```

## Form State Management

### Loading States
- **Initial Loading**: Skeleton forms during data fetch
- **Submission Loading**: Disabled form with loading indicators
- **Button States**: Loading text ("Saving...", "Creating...")

### Error States
- **Validation Errors**: Field-specific error messages
- **Network Errors**: Form-level error for API failures
- **Recovery**: Clear errors when user corrects issues

### Success States
- **Visual Feedback**: Green checkmarks and success borders
- **Form Completion**: Success callback triggers navigation/updates

## Testing Scenarios for QA

### Critical Test Cases

#### EntityForm Testing
1. **Empty Field Submission**
   - Enter empty name → Submit → Should show "Entity name is required"
2. **Length Validation**
   - Enter 21+ characters → Should show length error immediately on blur
3. **Character Validation**
   - Enter numbers/symbols → Should show pattern error on blur
4. **Valid Input**
   - Enter valid name → Should show green border and checkmark
5. **Real-time Updates**
   - Type valid then invalid → Validation should update immediately

#### ConnectionForm Testing
1. **Required Fields**
   - Leave any field empty → Submit button should be disabled
2. **Entity Validation**
   - Select same entity for both → Should show "entities must be different"
3. **Multiplier Validation**
   - Enter negative number → Should show "must be positive"
   - Enter 2+ decimal places → Should show decimal error
4. **Form Flow**
   - Complete valid form → All fields should show green validation
5. **AutoComplete Integration**
   - Test entity selection → Should trigger validation immediately

#### ComparisonForm Testing
1. **Auto-calculation**
   - Select all fields → Should calculate immediately
2. **No Path Scenarios**
   - Select unconnected entities → Should show "No path found"
3. **Loading States**
   - Verify loading indicators during calculation
4. **Error Recovery**
   - Fix invalid selections → Errors should clear automatically

### Cross-browser Testing
- Chrome, Firefox, Safari, Edge
- Mobile Safari, Chrome Mobile
- Keyboard navigation on all browsers
- Screen reader testing (recommended: NVDA, JAWS, VoiceOver)

### Performance Testing
- Validation should not cause noticeable lag
- Form interactions should be responsive
- Large datasets (100+ entities) should not slow validation

## Technical Implementation Notes

### CSS Classes
```css
.form-input - Base input styling with transition
.form-input.valid - Green border and success shadow
.form-input.invalid - Red border and error shadow
.validation-success - Green checkmark styling
.error-message - Error message styling with proper spacing
```

### React Patterns
```typescript
// Validation state structure
const [validationState, setValidationState] = useState<'neutral'|'valid'|'invalid'>('neutral');
const [touched, setTouched] = useState(false);
const [error, setError] = useState<string | null>(null);

// Validation function pattern
const validateField = (value: string): { isValid: boolean; message?: string } => {
  // Validation logic
  return { isValid: true/false, message: "Error message" };
};

// Effect for real-time validation
useEffect(() => {
  if (!touched) return;
  const validation = validateField(value);
  setValidationState(validation.isValid ? 'valid' : 'invalid');
  setError(validation.message || null);
}, [value, touched]);
```

## Deployment Checklist
- [ ] All three forms implement consistent validation patterns
- [ ] CSS validation classes added and tested
- [ ] Accessibility attributes properly implemented
- [ ] Error messages follow specification templates
- [ ] Real-time validation working on all fields
- [ ] Submit button logic properly implemented
- [ ] Cross-browser testing completed
- [ ] Screen reader testing completed
- [ ] Performance testing passed

## QA Handoff Requirements

### Deliverables Completed
1. ✅ **Updated Forms**: EntityForm, ConnectionForm, ComparisonForm with consistent validation
2. ✅ **CSS Styling**: Validation state classes and visual feedback
3. ✅ **Accessibility**: ARIA attributes and screen reader support
4. ✅ **Documentation**: This comprehensive specification document

### Demo Script for QA Team
1. **EntityForm Demo**: Show real-time validation for all error cases and success state
2. **ConnectionForm Demo**: Demonstrate complex multi-field validation
3. **ComparisonForm Demo**: Show real-time calculation and error handling
4. **Accessibility Demo**: Demonstrate screen reader support and keyboard navigation
5. **Cross-form Consistency**: Show how all forms now behave similarly

### Next Steps for QA (Phase 1.2)
1. Update E2E tests to match new validation behavior patterns
2. Test AutoComplete interaction improvements
3. Validate form tests with updated validation timing
4. Create regression tests for validation consistency
5. Cross-browser validation testing

---
**Document Version**: 1.0  
**Status**: Ready for QA Handoff  
**Phase**: 1.1 Complete → 1.2 Ready to Begin