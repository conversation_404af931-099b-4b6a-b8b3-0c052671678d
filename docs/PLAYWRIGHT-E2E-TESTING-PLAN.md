# Playwright E2E Testing Implementation Plan

## Overview
This document provides a complete implementation guide for setting up <PERSON><PERSON> end-to-end testing for the SIMILE frontend application. This testing framework will run separately from existing Jest unit tests and will test the full user journey through the application.

## Prerequisites
- SIMILE backend services must be running (podman-compose up)
- Frontend application must be accessible at http://localhost:3000
- Node.js and npm installed

## Phase 1: Environment Setup

### 1.1 Install Playwright Dependencies
```bash
cd frontend
npm install --save-dev @playwright/test
npx playwright install
```

### 1.2 Verify Installation
```bash
npx playwright --version
```

## Phase 2: Configuration

### 2.1 Create Playwright Configuration
Create `frontend/playwright.config.ts`:

```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e/tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'npm start',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },
});
```

## Phase 3: Directory Structure

### 3.1 Create E2E Directory Structure
```
frontend/e2e/
├── tests/
│   ├── navigation.spec.ts
│   ├── entities.spec.ts
│   ├── connections.spec.ts
│   ├── comparisons.spec.ts
│   └── error-handling.spec.ts
├── fixtures/
│   ├── test-data.ts
│   └── page-objects.ts
└── utils/
    └── helpers.ts
```

## Phase 4: Page Object Models

### 4.1 Base Page Object
Create `frontend/e2e/fixtures/page-objects.ts`:

```typescript
import { Page, Locator } from '@playwright/test';

export class BasePage {
  readonly page: Page;
  readonly navigation: Locator;

  constructor(page: Page) {
    this.page = page;
    this.navigation = page.locator('nav');
  }

  async goto(path: string = '/') {
    await this.page.goto(path);
  }

  async navigateTo(section: 'entities' | 'connections' | 'home') {
    const links = {
      home: '/',
      entities: '/entities',
      connections: '/connections'
    };
    await this.page.click(`nav a[href="${links[section]}"]`);
  }
}

export class EntityManagerPage extends BasePage {
  readonly entityForm: Locator;
  readonly entityList: Locator;
  readonly nameInput: Locator;
  readonly unitSelect: Locator;
  readonly submitButton: Locator;

  constructor(page: Page) {
    super(page);
    this.entityForm = page.locator('[data-testid="entity-form"]');
    this.entityList = page.locator('[data-testid="entity-list"]');
    this.nameInput = page.locator('input[name="name"]');
    this.unitSelect = page.locator('select[name="unit"]');
    this.submitButton = page.locator('button[type="submit"]');
  }

  async createEntity(name: string, unit: string) {
    await this.nameInput.fill(name);
    await this.unitSelect.selectOption(unit);
    await this.submitButton.click();
  }

  async deleteEntity(name: string) {
    await this.page.click(`[data-testid="delete-${name}"]`);
  }
}

export class ConnectionManagerPage extends BasePage {
  readonly connectionForm: Locator;
  readonly connectionList: Locator;
  readonly entityASelect: Locator;
  readonly entityBSelect: Locator;
  readonly relationshipInput: Locator;

  constructor(page: Page) {
    super(page);
    this.connectionForm = page.locator('[data-testid="connection-form"]');
    this.connectionList = page.locator('[data-testid="connection-list"]');
    this.entityASelect = page.locator('select[name="entityA"]');
    this.entityBSelect = page.locator('select[name="entityB"]');
    this.relationshipInput = page.locator('input[name="relationship"]');
  }

  async createConnection(entityA: string, entityB: string, relationship: string) {
    await this.entityASelect.selectOption(entityA);
    await this.entityBSelect.selectOption(entityB);
    await this.relationshipInput.fill(relationship);
    await this.page.click('button[type="submit"]');
  }
}

export class ComparisonManagerPage extends BasePage {
  readonly comparisonForm: Locator;
  readonly resultArea: Locator;
  readonly entityAInput: Locator;
  readonly entityBInput: Locator;

  constructor(page: Page) {
    super(page);
    this.comparisonForm = page.locator('[data-testid="comparison-form"]');
    this.resultArea = page.locator('[data-testid="comparison-result"]');
    this.entityAInput = page.locator('input[name="entityA"]');
    this.entityBInput = page.locator('input[name="entityB"]');
  }

  async compareEntities(entityA: string, entityB: string) {
    await this.entityAInput.fill(entityA);
    await this.entityBInput.fill(entityB);
    await this.page.click('button[type="submit"]');
  }
}
```

## Phase 5: Test Data Management

### 5.1 Test Data Fixtures
Create `frontend/e2e/fixtures/test-data.ts`:

```typescript
export const testEntities = [
  { name: 'Human', unit: 'length' },
  { name: 'Basketball', unit: 'length' },
  { name: 'Building', unit: 'length' },
  { name: 'Car', unit: 'mass' },
  { name: 'Elephant', unit: 'mass' },
];

export const testConnections = [
  { entityA: 'Human', entityB: 'Basketball', relationship: '10.0' },
  { entityA: 'Basketball', entityB: 'Building', relationship: '50.0' },
  { entityA: 'Car', entityB: 'Elephant', relationship: '0.3' },
];

export const invalidEntityNames = [
  '', // Empty string
  'A'.repeat(101), // Too long
  'Test123!', // Invalid characters
];

export const invalidConnections = [
  { entityA: 'Human', entityB: 'Car', relationship: '2.0' }, // Different units
  { entityA: 'Human', entityB: 'Basketball', relationship: '-5.0' }, // Negative
  { entityA: 'Human', entityB: 'Basketball', relationship: '2.55' }, // Too many decimals
];
```

## Phase 6: Test Implementation

### 6.1 Navigation Tests
Create `frontend/e2e/tests/navigation.spec.ts`:

```typescript
import { test, expect } from '@playwright/test';
import { BasePage } from '../fixtures/page-objects';

test.describe('Navigation', () => {
  test('should navigate between all pages', async ({ page }) => {
    const basePage = new BasePage(page);
    
    await basePage.goto();
    await expect(page).toHaveTitle(/SIMILE/);
    
    // Test navigation to entities page
    await basePage.navigateTo('entities');
    await expect(page).toHaveURL(/\/entities/);
    
    // Test navigation to connections page
    await basePage.navigateTo('connections');
    await expect(page).toHaveURL(/\/connections/);
    
    // Test navigation back to home
    await basePage.navigateTo('home');
    await expect(page).toHaveURL(/\/$/);
  });

  test('should highlight active navigation item', async ({ page }) => {
    const basePage = new BasePage(page);
    
    await basePage.goto('/entities');
    const activeLink = page.locator('nav a.active');
    await expect(activeLink).toHaveAttribute('href', '/entities');
  });
});
```

### 6.2 Entity Management Tests
Create `frontend/e2e/tests/entities.spec.ts`:

```typescript
import { test, expect } from '@playwright/test';
import { EntityManagerPage } from '../fixtures/page-objects';
import { testEntities, invalidEntityNames } from '../fixtures/test-data';

test.describe('Entity Management', () => {
  test.beforeEach(async ({ page }) => {
    const entityPage = new EntityManagerPage(page);
    await entityPage.goto('/entities');
  });

  test('should create a new entity', async ({ page }) => {
    const entityPage = new EntityManagerPage(page);
    
    await entityPage.createEntity('Test Entity', 'length');
    
    // Verify entity appears in list
    await expect(entityPage.entityList).toContainText('Test Entity');
  });

  test('should validate entity name requirements', async ({ page }) => {
    const entityPage = new EntityManagerPage(page);
    
    for (const invalidName of invalidEntityNames) {
      await entityPage.nameInput.fill(invalidName);
      await entityPage.unitSelect.selectOption('length');
      await entityPage.submitButton.click();
      
      // Should show validation error
      await expect(page.locator('.error-message')).toBeVisible();
    }
  });

  test('should prevent duplicate entity names', async ({ page }) => {
    const entityPage = new EntityManagerPage(page);
    
    // Create first entity
    await entityPage.createEntity('Duplicate Test', 'length');
    
    // Try to create duplicate
    await entityPage.createEntity('Duplicate Test', 'length');
    
    // Should show error message
    await expect(page.locator('.error-message')).toContainText('already exists');
  });

  test('should delete an entity', async ({ page }) => {
    const entityPage = new EntityManagerPage(page);
    
    // Create entity first
    await entityPage.createEntity('Delete Me', 'length');
    await expect(entityPage.entityList).toContainText('Delete Me');
    
    // Delete the entity
    await entityPage.deleteEntity('Delete Me');
    
    // Verify entity is removed
    await expect(entityPage.entityList).not.toContainText('Delete Me');
  });
});
```

### 6.3 Connection Management Tests
Create `frontend/e2e/tests/connections.spec.ts`:

```typescript
import { test, expect } from '@playwright/test';
import { EntityManagerPage, ConnectionManagerPage } from '../fixtures/page-objects';
import { testEntities, testConnections, invalidConnections } from '../fixtures/test-data';

test.describe('Connection Management', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test entities first
    const entityPage = new EntityManagerPage(page);
    await entityPage.goto('/entities');
    
    for (const entity of testEntities) {
      await entityPage.createEntity(entity.name, entity.unit);
    }
    
    // Navigate to connections page
    const connectionPage = new ConnectionManagerPage(page);
    await connectionPage.goto('/connections');
  });

  test('should create bidirectional connections', async ({ page }) => {
    const connectionPage = new ConnectionManagerPage(page);
    
    await connectionPage.createConnection('Human', 'Basketball', '10.0');
    
    // Verify both directions appear in list
    await expect(connectionPage.connectionList).toContainText('Human → Basketball: 10.0');
    await expect(connectionPage.connectionList).toContainText('Basketball → Human: 0.1');
  });

  test('should validate same-unit connections only', async ({ page }) => {
    const connectionPage = new ConnectionManagerPage(page);
    
    // Try to connect different units
    await connectionPage.createConnection('Human', 'Car', '2.0');
    
    // Should show error message
    await expect(page.locator('.error-message')).toContainText('same unit');
  });

  test('should validate positive relationship values', async ({ page }) => {
    const connectionPage = new ConnectionManagerPage(page);
    
    await connectionPage.createConnection('Human', 'Basketball', '-5.0');
    
    // Should show error message
    await expect(page.locator('.error-message')).toContainText('positive');
  });

  test('should validate decimal precision', async ({ page }) => {
    const connectionPage = new ConnectionManagerPage(page);
    
    await connectionPage.createConnection('Human', 'Basketball', '2.55');
    
    // Should show error message about decimal places
    await expect(page.locator('.error-message')).toContainText('decimal');
  });
});
```

### 6.4 Comparison/Pathfinding Tests
Create `frontend/e2e/tests/comparisons.spec.ts`:

```typescript
import { test, expect } from '@playwright/test';
import { EntityManagerPage, ConnectionManagerPage, ComparisonManagerPage } from '../fixtures/page-objects';
import { testEntities, testConnections } from '../fixtures/test-data';

test.describe('Entity Comparisons', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test data
    const entityPage = new EntityManagerPage(page);
    await entityPage.goto('/entities');
    
    for (const entity of testEntities) {
      await entityPage.createEntity(entity.name, entity.unit);
    }
    
    const connectionPage = new ConnectionManagerPage(page);
    await connectionPage.goto('/connections');
    
    for (const connection of testConnections) {
      await connectionPage.createConnection(
        connection.entityA,
        connection.entityB,
        connection.relationship
      );
    }
    
    // Navigate to comparison page
    const comparisonPage = new ComparisonManagerPage(page);
    await comparisonPage.goto('/');
  });

  test('should calculate direct relationships', async ({ page }) => {
    const comparisonPage = new ComparisonManagerPage(page);
    
    await comparisonPage.compareEntities('Human', 'Basketball');
    
    // Should show direct relationship
    await expect(comparisonPage.resultArea).toContainText('Human is 10.0x Basketball');
  });

  test('should calculate transitive relationships', async ({ page }) => {
    const comparisonPage = new ComparisonManagerPage(page);
    
    await comparisonPage.compareEntities('Human', 'Building');
    
    // Should show calculated transitive relationship (10.0 * 50.0 = 500.0)
    await expect(comparisonPage.resultArea).toContainText('Human is 500.0x Building');
  });

  test('should handle no path found', async ({ page }) => {
    const comparisonPage = new ComparisonManagerPage(page);
    
    // Try to compare entities with different units (no connection possible)
    await comparisonPage.compareEntities('Human', 'Car');
    
    // Should show no path message
    await expect(comparisonPage.resultArea).toContainText('No connection found');
  });

  test('should respect max path length', async ({ page }) => {
    const comparisonPage = new ComparisonManagerPage(page);
    
    // This would require testing with a chain longer than 6 hops
    // Implementation depends on having sufficient test data
  });
});
```

## Phase 7: NPM Scripts

### 7.1 Update package.json
Add these scripts to `frontend/package.json`:

```json
{
  "scripts": {
    "test:e2e": "playwright test",
    "test:e2e:headed": "playwright test --headed",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:report": "playwright show-report",
    "test:e2e:install": "playwright install"
  }
}
```

## Phase 8: Running Tests

### 8.1 Prerequisites
Before running tests, ensure:
1. Backend services are running: `podman-compose up -d`
2. Frontend dev server is running: `npm start` (or let Playwright start it)

### 8.2 Test Execution Commands
```bash
# Run all tests
npm run test:e2e

# Run tests with browser UI
npm run test:e2e:headed

# Run tests in interactive mode
npm run test:e2e:ui

# View test report
npm run test:e2e:report

# Run specific test file
npx playwright test entities.spec.ts

# Run tests in specific browser
npx playwright test --project=chromium
```

## Phase 9: CI/CD Integration

### 9.1 GitHub Actions Workflow
Create `.github/workflows/e2e-tests.yml`:

```yaml
name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Install Playwright
      run: |
        cd frontend
        npx playwright install --with-deps
    
    - name: Start services
      run: |
        podman-compose up -d
        # Wait for services to be ready
        sleep 30
    
    - name: Run E2E tests
      run: |
        cd frontend
        npm run test:e2e
    
    - name: Upload test results
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: playwright-report
        path: frontend/playwright-report/
        retention-days: 7
    
    - name: Stop services
      if: always()
      run: podman-compose down
```

## Phase 10: Maintenance & Best Practices

### 10.1 Test Data Management
- Use test database or reset state between tests
- Clean up created entities/connections after each test
- Use unique names with timestamps for test entities

### 10.2 Debugging Failed Tests
- Screenshots are automatically captured on failure
- Use `page.pause()` for interactive debugging
- Check browser console logs: `page.on('console', console.log)`

### 10.3 Performance Considerations
- Tests should complete within 5-10 minutes total
- Use `test.slow()` for tests that legitimately take longer
- Parallelize tests where possible (default behavior)

## Troubleshooting Guide

### Common Issues

1. **Port conflicts**: Ensure frontend runs on 3000, backend on 8000
2. **Service startup time**: Add wait conditions or increase timeouts
3. **Flaky tests**: Add proper wait conditions using `waitFor` methods
4. **Data isolation**: Ensure tests don't interfere with each other

### Debug Commands
```bash
# Run single test with debug info
npx playwright test entities.spec.ts --debug

# Run with trace
npx playwright test --trace on

# Generate and view trace
npx playwright show-trace trace.zip
```

## Completion Checklist

- [ ] Playwright installed and configured
- [ ] Directory structure created
- [ ] Page object models implemented
- [ ] All test suites created and passing
- [ ] NPM scripts added
- [ ] CI/CD integration configured
- [ ] Documentation complete
- [ ] Tests run independently of existing Jest tests
- [ ] Error handling and edge cases covered

## Handoff Notes

This implementation provides a complete E2E testing framework that:
- Tests all major user journeys through the SIMILE application
- Validates business rules and constraints
- Provides clear error reporting and debugging capabilities
- Integrates with existing CI/CD workflows
- Maintains separation from unit tests

The framework is designed to be maintainable and extensible for future feature additions.