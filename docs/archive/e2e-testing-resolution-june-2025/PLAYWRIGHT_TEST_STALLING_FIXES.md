# Playwright Test Stalling Issues - Root Cause and Fixes

**Date**: June 20, 2025  
**Priority**: HIGH - Test Infrastructure Fix  
**Issue**: E2E tests stalling and requiring manual intervention  

## 🚨 ROOT CAUSES IDENTIFIED

### 1. Missing Global Timeouts
**Problem**: No global timeouts in `playwright.config.ts`
- Tests can run indefinitely waiting for elements
- No automatic failure after reasonable time

### 2. Parallel Execution Conflicts  
**Problem**: `fullyParallel: true` with shared entity data
- Multiple tests creating entities simultaneously
- Tests interfering with each other's data
- Database state conflicts

### 3. Inefficient Wait Strategies
**Problem**: Hard-coded delays in page objects
- `await this.page.waitForTimeout(2000)` - Wastes time
- Long assertion timeouts (10 seconds) without proper fallbacks
- Tests waiting for elements that may never appear

### 4. Dialog Handler Issues
**Problem**: Dialog handlers set inside methods
- Memory leaks from repeated handler registration
- Handlers not properly cleaned up
- Can cause browser processes to hang

### 5. Resource Cleanup Issues
**Problem**: No proper test isolation
- Browser contexts not properly isolated
- Test data not cleaned up between tests
- Accumulated state causing conflicts

## 🔧 COMPREHENSIVE FIXES

### Fix 1: Update Playwright Configuration
**File**: `playwright.config.ts`

```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e/tests',
  
  // FIX: Add global timeouts to prevent stalling
  timeout: 30 * 1000,           // 30 seconds max per test
  globalTimeout: 10 * 60 * 1000, // 10 minutes max for entire run
  
  // FIX: Disable parallel execution to prevent data conflicts
  fullyParallel: false,
  workers: 1, // Run tests sequentially
  
  forbidOnly: !!(process.env as any).CI,
  retries: (process.env as any).CI ? 2 : 0,
  reporter: 'html',
  
  use: {
    baseURL: 'http://localhost:3000',
    
    // FIX: Add action and navigation timeouts
    actionTimeout: 5 * 1000,      // 5 seconds for actions
    navigationTimeout: 10 * 1000, // 10 seconds for navigation
    
    // FIX: Set expect timeout
    expect: {
      timeout: 5 * 1000          // 5 seconds for assertions
    },
    
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    
    // FIX: Add proper cleanup
    video: 'retain-on-failure',
  },
  
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox', 
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  
  webServer: {
    command: 'npm start',
    url: 'http://localhost:3000',
    reuseExistingServer: !(process.env as any).CI,
    timeout: 120 * 1000,
  },
});
```

### Fix 2: Improve Page Object Methods
**File**: `e2e/fixtures/page-objects.ts`

```typescript
// FIX: Replace inefficient entity creation method
async createEntity(name: string) {
  await this.clickCreateNew();
  await this.nameInput.fill(name);
  await this.submitButton.click();
  
  // FIX: Remove hard-coded wait, use proper waiting strategy
  try {
    // Wait for either success or error with shorter timeout
    await Promise.race([
      this.entityForm.waitFor({ state: 'hidden', timeout: 5000 }),
      this.errorMessage.waitFor({ state: 'visible', timeout: 5000 })
    ]);
    
    // If form is hidden, entity creation succeeded
    if (await this.entityForm.isHidden()) {
      // FIX: Wait for entity to appear with proper timeout
      await expect(this.page.locator(`:text("${name}")`))
        .toBeVisible({ timeout: 5000 });
    } else {
      // Form still visible means error occurred
      const errorText = await this.errorMessage.textContent();
      throw new Error(`Entity creation failed: ${errorText}`);
    }
  } catch (error) {
    // FIX: Provide clear error message instead of hanging
    throw new Error(`Entity creation timed out or failed: ${error.message}`);
  }
}

// FIX: Improve delete method with proper cleanup
async deleteEntity(name: string) {
  const deleteButton = this.page.locator(`button[data-testid="delete-${name}"]`);
  
  // FIX: Handle dialogs at test level, not method level
  await deleteButton.click();
  
  // FIX: Wait for entity to disappear with timeout
  await expect(this.page.locator(`:text("${name}")`))
    .not.toBeVisible({ timeout: 5000 });
}
```

### Fix 3: Add Test Setup and Cleanup
**File**: `e2e/fixtures/test-helpers.ts`

```typescript
export class TestHelpers {
  constructor(private page: Page) {}

  // FIX: Centralized dialog handler setup
  async setupDialogHandler() {
    this.page.on('dialog', async dialog => {
      console.log(`Dialog appeared: ${dialog.message()}`);
      await dialog.accept();
    });
  }

  // FIX: Wait for app to be ready with timeout
  async waitForAppReady() {
    try {
      await this.page.waitForLoadState('networkidle', { timeout: 5000 });
      await this.page.waitForSelector('nav', { timeout: 5000 });
    } catch (error) {
      console.warn('App ready check timed out, continuing anyway');
    }
  }

  // FIX: Add test cleanup method
  async cleanup() {
    try {
      // Clear any remaining dialogs
      await this.page.evaluate(() => {
        // Close any open modals or dialogs
        const modals = document.querySelectorAll('.modal, .dialog');
        modals.forEach(modal => (modal as HTMLElement).style.display = 'none');
      });
    } catch (error) {
      console.warn('Cleanup warning:', error);
    }
  }
}
```

### Fix 4: Update Test Structure
**Example for test files**:

```typescript
test.describe('Entity Management', () => {
  let entityPage: EntityManagerPage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.setupDialogHandler();
    
    entityPage = new EntityManagerPage(page);
    
    // FIX: Add timeout to beforeEach setup
    await test.step('Navigate to entities page', async () => {
      await entityPage.goto('/entities');
      await helpers.waitForAppReady();
    });
  });

  test.afterEach(async ({ page }) => {
    // FIX: Add cleanup after each test
    await helpers.cleanup();
  });

  test('should create entity successfully', async ({ page }) => {
    // FIX: Use unique but valid entity names
    const entityName = `Test Entity ${Math.random().toString(36).substring(7).toUpperCase()}`;
    
    await test.step('Create entity', async () => {
      await entityPage.createEntity(entityName);
    });
    
    await test.step('Verify entity appears', async () => {
      await expect(page.locator(`:text("${entityName}")`)).toBeVisible();
    });
  });
});
```

## 🎯 IMPLEMENTATION PLAN

### Phase 1: Configuration Fixes (15 minutes)
1. Update `playwright.config.ts` with proper timeouts
2. Disable parallel execution to prevent conflicts
3. Add action and navigation timeouts

### Phase 2: Page Object Improvements (30 minutes)  
1. Replace hard-coded waits with proper waiting strategies
2. Add timeout handling to prevent hanging
3. Improve error messages for debugging

### Phase 3: Test Structure Updates (30 minutes)
1. Add proper setup/cleanup in test files
2. Centralize dialog handling
3. Add test isolation measures

### Phase 4: Validation (15 minutes)
1. Run test suite to verify no stalling
2. Confirm automatic completion
3. Check timeout behavior

## 📊 EXPECTED IMPROVEMENTS

### Before Fixes
- ❌ Tests stall indefinitely
- ❌ Manual intervention required
- ❌ Parallel execution conflicts
- ❌ Hard-coded delays waste time

### After Fixes
- ✅ Tests complete automatically within 30 seconds each
- ✅ No manual intervention needed
- ✅ Sequential execution prevents conflicts
- ✅ Efficient waiting strategies
- ✅ Proper error handling and timeouts

## 🚀 IMMEDIATE NEXT STEPS

1. **Apply configuration fixes** to `playwright.config.ts`
2. **Update page object methods** to remove stalling waits
3. **Test with a single spec file** to verify fixes
4. **Apply to all test files** once validated
5. **Run full suite** to confirm automatic completion

This will resolve the Playwright stalling issues and enable reliable, automatic test execution!