# Test Data Management Guide

## Overview

This guide establishes standards for test data management in the SIMILE backend project to ensure reliable, isolated, and maintainable tests while achieving 80%+ test coverage.

## Test Data Management Principles

### 1. Test Isolation
- Each test should be independent and not depend on data from other tests
- Tests should be able to run in any order without affecting each other
- Use unique, identifiable test data to prevent conflicts

### 2. Data Cleanup
- Clean up test data after each test to prevent pollution
- Use transaction rollback where possible for better performance
- Verify cleanup success to catch cleanup failures early

### 3. Naming Conventions
- Use descriptive, unique names for test data
- Include UUID suffixes to prevent name conflicts
- Follow consistent naming patterns across all tests

### 4. Data Categorization
- Separate test data by purpose (basic, edge cases, performance)
- Use appropriate fixtures for different data types
- Maintain clear boundaries between test data categories

## Test Data Naming Standards

### Entity Names
```python
# ✅ Good: Unique, descriptive, follows validation rules
entity_name = create_test_entity_name("EntityForConnectionTest")
# Result: "EntityForConnectionTest abcd1234"

# ❌ Bad: Generic, could conflict with other tests
entity_name = "Test Entity"

# ❌ Bad: Contains invalid characters
entity_name = "Test@Entity#123"
```

### Test Method Names
```python
# ✅ Good: Descriptive, specific about what is being tested
def test_create_connection_with_decimal_multiplier_success():
    pass

def test_entity_name_validation_rejects_special_characters():
    pass

# ❌ Bad: Generic, unclear what is being tested
def test_create_connection():
    pass

def test_validation():
    pass
```

### Test Data Variables
```python
# ✅ Good: Clear purpose and unique identification
test_entity_source = await create_entity("SourceEntity")
test_entity_target = await create_entity("TargetEntity")
test_connection_basic = await create_connection(...)

# ❌ Bad: Generic names that could conflict
entity1 = await create_entity("Entity")
entity2 = await create_entity("Entity")
```

## Using Enhanced Fixtures

### Basic Test Setup
```python
import pytest
from tests.enhanced_fixtures import TestDataFactory, create_test_entity_name

@pytest.mark.asyncio
async def test_entity_creation(test_client, test_factory):
    """Test entity creation with enhanced fixtures."""
    # Create entity with unique name
    entity_name = create_test_entity_name("CreationTest")
    entity = await test_factory.create_entity(entity_name)
    
    # Verify entity was created
    assert entity["name"] == entity_name
    assert "id" in entity
    
    # Factory automatically tracks entity for cleanup
```

### Transaction-Based Tests
```python
@pytest.mark.asyncio
async def test_with_transaction_rollback(db_transaction):
    """Test using transaction-based isolation."""
    # Create data within transaction
    entity = Entity(name="TransactionTestEntity")
    db_transaction.add(entity)
    await db_transaction.commit()
    
    # Test logic here
    # Transaction will be rolled back automatically
```

### Test Data Verification
```python
@pytest.mark.asyncio
async def test_with_data_verification(test_client, test_data_tracker):
    """Test with automatic data cleanup verification."""
    # Create entity
    response = await test_client.post(
        "/api/v1/entities/",
        json={"name": create_test_entity_name()}
    )
    entity_id = response.json()["id"]
    
    # Track entity for cleanup verification
    test_data_tracker.track_entity(entity_id)
    
    # Test logic here
    # Cleanup will be automatically verified
```

## Test Data Categories

### 1. Basic Test Data
Use for standard happy path tests:
```python
@pytest.mark.asyncio
async def test_basic_entity_crud(test_factory):
    """Test basic CRUD operations."""
    setup = await test_factory.create_test_setup()
    
    entity = setup['entity1']
    units = setup['units']
    
    # Standard test logic
```

### 2. Edge Case Test Data
Use for boundary conditions and validation:
```python
@pytest.mark.asyncio
async def test_entity_name_edge_cases(test_client, edge_case_entities):
    """Test entity name validation edge cases."""
    # Test maximum length
    response = await test_client.post(
        "/api/v1/entities/",
        json={"name": edge_case_entities['max_length']}
    )
    assert response.status_code == 201
    
    # Test single character
    response = await test_client.post(
        "/api/v1/entities/",
        json={"name": edge_case_entities['single_char']}
    )
    assert response.status_code == 201
```

### 3. Performance Test Data
Use for load and performance tests:
```python
@pytest.mark.asyncio
async def test_bulk_entity_creation(test_client, test_factory):
    """Test creating many entities."""
    entities = []
    for i in range(100):
        entity_name = create_test_entity_name(f"BulkTest{i}")
        entity = await test_factory.create_entity(entity_name)
        entities.append(entity)
    
    # Performance assertions
    assert len(entities) == 100
```

## Cleanup Strategies

### 1. Automatic Cleanup (Recommended)
```python
# Use existing fixtures for automatic cleanup
@pytest.mark.asyncio
async def test_with_automatic_cleanup(test_client):
    """Test with automatic cleanup via existing fixtures."""
    # Create test data
    response = await test_client.post(
        "/api/v1/entities/",
        json={"name": create_test_entity_name()}
    )
    
    # Test logic here
    # Cleanup happens automatically via clean_database_between_tests
```

### 2. Manual Cleanup (When Needed)
```python
@pytest.mark.asyncio
async def test_with_manual_cleanup(test_client, comprehensive_cleanup):
    """Test with manual cleanup control."""
    # Create test data
    entity_ids = []
    for i in range(5):
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": create_test_entity_name(f"Manual{i}")}
        )
        entity_ids.append(response.json()["id"])
    
    # Test logic here
    
    # Manual cleanup if needed
    await comprehensive_cleanup()
```

### 3. Transaction Rollback (Best Performance)
```python
@pytest.mark.asyncio
async def test_with_transaction_rollback(db_transaction):
    """Test using transaction rollback for cleanup."""
    # Create data directly in database
    entity = Entity(name=create_test_entity_name())
    db_transaction.add(entity)
    await db_transaction.flush()  # Get ID without committing
    
    # Test logic here
    # Transaction rollback happens automatically
```

## Data Verification

### 1. Existence Verification
```python
from tests.enhanced_fixtures import assert_entity_exists, assert_entity_not_exists

@pytest.mark.asyncio
async def test_entity_deletion_verification(test_client, db_transaction):
    """Test entity deletion with verification."""
    # Create entity
    entity = Entity(name=create_test_entity_name())
    db_transaction.add(entity)
    await db_transaction.flush()
    
    # Verify entity exists
    await assert_entity_exists(db_transaction, entity.id)
    
    # Delete entity
    await test_client.delete(f"/api/v1/entities/{entity.id}")
    
    # Verify entity is deleted
    await assert_entity_not_exists(db_transaction, entity.id)
```

### 2. State Verification
```python
@pytest.mark.asyncio
async def test_database_state_verification(test_client):
    """Test database state before and after operations."""
    # Get initial state
    initial_response = await test_client.get("/api/v1/entities/")
    initial_count = len(initial_response.json())
    
    # Create entities
    for i in range(3):
        await test_client.post(
            "/api/v1/entities/",
            json={"name": create_test_entity_name(f"StateTest{i}")}
        )
    
    # Verify state change
    final_response = await test_client.get("/api/v1/entities/")
    final_count = len(final_response.json())
    
    assert final_count == initial_count + 3
```

## Best Practices

### 1. Test Independence
```python
# ✅ Good: Each test creates its own data
@pytest.mark.asyncio
async def test_connection_creation_independent(test_factory):
    """Test connection creation with independent data."""
    setup = await test_factory.create_test_setup()
    
    connection = await test_factory.create_connection(
        setup['entity1']['id'],
        setup['entity2']['id'],
        setup['length_unit']['id'],
        Decimal('2.5')
    )
    
    assert connection['multiplier'] == 2.5

# ❌ Bad: Depends on data from other tests
@pytest.mark.asyncio
async def test_connection_creation_dependent(test_client):
    """Test that depends on existing data."""
    # Assumes entities already exist - fragile!
    response = await test_client.get("/api/v1/entities/")
    entities = response.json()
    # This will fail if no entities exist
```

### 2. Descriptive Test Data
```python
# ✅ Good: Clear purpose and unique identification
@pytest.mark.asyncio
async def test_connection_inverse_creation(test_factory):
    """Test automatic inverse connection creation."""
    setup = await test_factory.create_test_setup()
    
    source_entity = setup['entity1']
    target_entity = setup['entity2']
    
    # Create connection from source to target
    connection = await test_factory.create_connection(
        source_entity['id'],
        target_entity['id'],
        setup['length_unit']['id'],
        Decimal('2.0')
    )
    
    # Verify inverse connection was created
    # Test logic here
```

### 3. Error Handling
```python
@pytest.mark.asyncio
async def test_with_error_handling(test_client):
    """Test with proper error handling."""
    try:
        # Test operations that might fail
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": ""}  # Invalid name
        )
        assert response.status_code == 422
    except Exception as e:
        # Log error for debugging
        logger.error(f"Test failed with error: {e}")
        raise
```

## Common Pitfalls to Avoid

### 1. Test Data Pollution
```python
# ❌ Bad: Creates data that persists across tests
@pytest.mark.asyncio
async def test_creates_persistent_data(test_client):
    """Test that creates persistent data."""
    await test_client.post(
        "/api/v1/entities/",
        json={"name": "PersistentEntity"}  # Same name used in multiple tests
    )
    # This will cause conflicts in subsequent tests

# ✅ Good: Creates unique data that doesn't conflict
@pytest.mark.asyncio
async def test_creates_unique_data(test_client):
    """Test that creates unique data."""
    await test_client.post(
        "/api/v1/entities/",
        json={"name": create_test_entity_name("UniqueTest")}
    )
    # Each test run gets a unique entity name
```

### 2. Test Ordering Dependencies
```python
# ❌ Bad: Test depends on execution order
@pytest.mark.asyncio
async def test_depends_on_order_1(test_client):
    """First test that creates data."""
    await test_client.post(
        "/api/v1/entities/",
        json={"name": "OrderedEntity"}
    )

@pytest.mark.asyncio
async def test_depends_on_order_2(test_client):
    """Second test that depends on first test."""
    # This will fail if tests run in different order
    response = await test_client.get("/api/v1/entities/")
    entities = response.json()
    ordered_entity = next(e for e in entities if e['name'] == 'OrderedEntity')
    # Fragile dependency on test execution order

# ✅ Good: Each test is independent
@pytest.mark.asyncio
async def test_independent_1(test_factory):
    """Independent test that creates its own data."""
    entity = await test_factory.create_entity("IndependentTest1")
    # Test logic using entity

@pytest.mark.asyncio
async def test_independent_2(test_factory):
    """Independent test that creates its own data."""
    entity = await test_factory.create_entity("IndependentTest2")
    # Test logic using entity
```

### 3. Incomplete Cleanup
```python
# ❌ Bad: Doesn't clean up all created data
@pytest.mark.asyncio
async def test_incomplete_cleanup(test_client):
    """Test that doesn't clean up properly."""
    # Create entities
    entity1 = await test_client.post("/api/v1/entities/", json={"name": "Entity1"})
    entity2 = await test_client.post("/api/v1/entities/", json={"name": "Entity2"})
    
    # Create connection
    connection = await test_client.post("/api/v1/connections/", json={...})
    
    # Only clean up entities, forget connection
    await test_client.delete(f"/api/v1/entities/{entity1.json()['id']}")
    # Connection might still exist, causing issues

# ✅ Good: Uses automatic cleanup or cleans up comprehensively
@pytest.mark.asyncio
async def test_complete_cleanup(test_factory):
    """Test that uses automatic cleanup."""
    setup = await test_factory.create_test_setup()
    connection = await test_factory.create_connection(...)
    
    # Factory automatically tracks all created data for cleanup
    # No manual cleanup needed
```

## Testing Checklist

Before writing tests, ensure:
- [ ] Test data uses unique names (UUID-based)
- [ ] Test is independent of other tests
- [ ] Test data is properly categorized
- [ ] Cleanup strategy is defined
- [ ] Test data is tracked for verification
- [ ] Error handling is implemented
- [ ] Test name is descriptive and specific

## Troubleshooting

### Common Issues and Solutions

1. **Test Data Conflicts**
   - Use `create_test_entity_name()` for unique names
   - Check existing test data before creating new data

2. **Cleanup Failures**
   - Use `comprehensive_cleanup()` fixture
   - Verify cleanup with `test_data_tracker`

3. **Test Ordering Issues**
   - Make tests independent
   - Use fixtures for test data creation

4. **Database State Issues**
   - Use `database_state_validation` fixture
   - Check logs for state changes

5. **Performance Issues**
   - Use transaction rollback for faster cleanup
   - Limit test data size for performance tests

---

*This guide ensures reliable, maintainable tests while achieving 80%+ test coverage.*