# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## SIMILE Project Context

SIMILE is a microservices application that allows users to compare entities based on measurable relationships. It calculates transitive relationships (e.g., if A is 2x B and B is 3x C, then A is 6x C).

## Key Architecture Decisions
- **Database**: PostgreSQL with recursive CTEs for graph traversal
- **Backend**: Python FastAPI microservices
- **Frontend**: React SPA served by FastAPI
- **Container**: Podman preferred (Docker-compatible, better licensing)
- **Orchestration**: podman-compose for local development
- **Authentication**: None for MVP
- **Service Discovery**: Environment variables

## Services
1. **data-service**: Pure persistence layer for PostgreSQL
2. **entity-service**: Business logic for entity management
3. **connection-service**: Business logic for connections and path queries
4. **web-ui**: React frontend + FastAPI gateway

## Data Model
- **entities**: id, name (letters and spaces only, max 20 chars)
- **units**: Predefined (length, mass, time, count, volume, area)
- **connections**: Bidirectional, auto-creates inverse (A→B creates B→A with 1/x)

## Important Constraints
- All numeric values use 1 decimal place precision
- Connection values must be positive
- Only same-unit connections can be traversed
- Default max path length: 6 hops
- Rate limit: 100 requests/minute/IP

## Development Workflow
- **Git Workflow**: Using git flow for team collaboration
  - Main branches: `main` (production) and `develop`
  - Feature branches: `feature/task-id-description`
  - Release branches: `release/version`
  - Hotfix branches: `hotfix/description`
  - Use `git flow` tool for branch management
- Test-First Development (TDD)
- Tests in root-level test directory
- Minimum 80% test coverage
- Python: PEP 8 + Black formatting
- React: Standard ESLint configuration

## Git Flow Commands
```bash
# Initialize git flow (one time)
git flow init

# Start a new feature
git flow feature start task-001-create-database-schema

# Finish a feature (merges to develop)
git flow feature finish task-001-create-database-schema

# Start a release
git flow release start 1.0.0

# Finish a release (merges to main and develop)
git flow release finish 1.0.0

# Start a hotfix
git flow hotfix start critical-bug-fix

# Finish a hotfix
git flow hotfix finish critical-bug-fix
```

## Container Commands

### Docker (Recommended for Development)
```bash
# Development mode with hot reload (recommended for development)
docker-compose -f docker-compose.dev.yml up -d
docker-compose -f docker-compose.dev.yml down

# Production mode (for testing production builds)
docker-compose up -d
docker-compose down

# Build specific services
docker-compose -f docker-compose.dev.yml build backend
docker-compose -f docker-compose.dev.yml build frontend
docker-compose -f docker-compose.dev.yml build database

# View logs
docker-compose -f docker-compose.dev.yml logs -f
docker-compose -f docker-compose.dev.yml logs -f backend
```

### Podman (Alternative)
```bash
# Using Podman (fallback option)
podman-compose up -d
podman-compose down
podman build -t service-name .

# Note: Podman may have volume mounting issues on some systems
# Use Docker for development if you encounter permission problems
```

## Testing Commands

### All Tests (Recommended)
```bash
# Run ALL tests (backend + frontend unit + frontend E2E)
./run_all_tests.sh

# Prerequisites for run_all_tests.sh:
# 1. Backend services running: podman-compose up -d
# 2. Backend venv created: cd backend && python3.11 -m venv venv && source venv/bin/activate && pip install -r requirements.txt
# 3. Frontend deps installed: cd frontend && npm install
```

### Backend Testing
```bash
cd backend
python3.11 -m venv venv  # Use Python 3.11 specifically
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# IMPORTANT: Install greenlet for SQLAlchemy async
pip install greenlet

# Run all backend tests
./run_tests.sh

# Individual commands:
# Set up test database (automatic with make)
make test-setup  # Or: python scripts/setup_test_db.py

# Run linting
make lint  # Or: flake8 src tests

# Run type checking  
make typecheck  # Or: mypy src

# Run tests (auto-creates test database)
make test  # Or: PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest

# Run with coverage
make coverage  # Or: PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --cov=src --cov-report=term-missing
```

### Frontend Testing
```bash
cd frontend
npm install

# Unit Tests
npm test
npm test -- --coverage

# E2E Tests (requires backend services running)
npm run test:e2e                    # Run all E2E tests
npm run test:e2e:headed            # Run with browser UI
npm run test:e2e:ui                # Interactive mode
npm run test:e2e:debug             # Debug mode
npm run test:e2e:report            # View test report

# Linting and Type Checking
npm run lint
npm run typecheck
```

## Build Commands
```bash
# Build all containers
podman-compose build

# Build specific service
podman-compose build backend
podman-compose build frontend
podman-compose build database

# Frontend production build
cd frontend && npm run build

# Backend doesn't need build (Python interpreted)
```

## Database Commands
```bash
# Connect to database
podman exec -it simile-db psql -U postgres -d simile

# Run migrations manually
podman exec -it simile-db psql -U postgres -d simile -f /docker-entrypoint-initdb.d/001_create_tables.sql

# Load demo data
podman exec -it simile-db psql -U postgres -d simile -f /docker-entrypoint-initdb.d/demo_data.sql

# Generate test data
python scripts/generate_test_data.py --entities 100 --connections 500
```

## Running the Application

### Development Mode (Hot Reload)
```bash
# Start development environment with hot reload
docker-compose -f docker-compose.dev.yml up -d

# View logs to see hot reload in action
docker-compose -f docker-compose.dev.yml logs -f

# Stop development environment
docker-compose -f docker-compose.dev.yml down

# Reset everything (including volumes)
docker-compose -f docker-compose.dev.yml down -v
```

### Production Mode
```bash
# Start production build
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Reset everything (including volumes)
docker-compose down -v
```

### Hot Reload Features
- **Backend**: FastAPI auto-reloads on Python file changes in `backend/src/`
- **Frontend**: React hot-reloads on file changes in `frontend/src/`
- **Database**: Persistent data across container restarts
- **No rebuilds needed**: Code changes are immediately reflected

## PRD Location
The complete Product Requirements Document is at: `PLAN.md`

## Team Coordination
- Multiple developers (human and AI agents) working on this project
- Always check existing work before implementing new features
- Use descriptive commit messages
- Create feature branches for all new work
- Test your changes before committing

## Recent Infrastructure Fixes (June 13, 2025)
- Backend test infrastructure has been fixed
- All async event loop errors resolved (79 tests fixed)
- Use `TEST_DATABASE_HOST=localhost` when running tests locally
- pytest configuration updated to use `asyncio_mode = "strict"`
- See `docs/BACKEND-TEST-FIX-DOCUMENTATION.md` for details