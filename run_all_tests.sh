#!/bin/bash
set -e  # Exit on any error

echo "🧪 SIMILE - Running All Tests"
echo "============================="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Track overall status
OVERALL_SUCCESS=true

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2 - PASSED${NC}"
    else
        echo -e "${RED}❌ $2 - FAILED${NC}"
        OVERALL_SUCCESS=false
    fi
    echo
}

# Function to check if backend services are running
check_backend_services() {
    echo -e "${BLUE}🔍 Checking backend services...${NC}"
    
    # Check if PostgreSQL is accessible
    if ! command -v psql &> /dev/null; then
        echo -e "${YELLOW}⚠️  PostgreSQL client not found, trying to connect via backend script${NC}"
    fi
    
    # Check if database is accessible (via backend check script)
    cd backend
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        echo -e "${RED}❌ Backend virtual environment not found!${NC}"
        echo -e "${YELLOW}   Please run: python3.11 -m venv venv && source venv/bin/activate && pip install -r requirements.txt${NC}"
        cd ..
        return 1
    fi
    
    # Activate virtual environment for service check
    source venv/bin/activate
    
    if python scripts/check_test_services.py 2>/dev/null; then
        echo -e "${GREEN}✅ Backend services are running${NC}"
        deactivate
        cd ..
        return 0
    else
        echo -e "${RED}❌ Backend services not accessible${NC}"
        echo -e "${YELLOW}   Please run: podman-compose up -d${NC}"
        deactivate
        cd ..
        return 1
    fi
}

# Function to check if frontend dependencies are installed
check_frontend_deps() {
    echo -e "${BLUE}🔍 Checking frontend dependencies...${NC}"
    cd frontend
    
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}⚠️  Frontend dependencies not installed, installing...${NC}"
        npm install
    fi
    
    # Check if Playwright browsers are installed
    if ! npx playwright --version &> /dev/null; then
        echo -e "${YELLOW}⚠️  Playwright not found, installing...${NC}"
        npm run test:e2e:install
    fi
    
    echo -e "${GREEN}✅ Frontend dependencies ready${NC}"
    cd ..
}

echo -e "${BLUE}📋 Pre-flight checks${NC}"
echo "==================="

# Check backend services
if ! check_backend_services; then
    echo -e "${RED}❌ Cannot run tests without backend services${NC}"
    exit 1
fi

# Check frontend dependencies
check_frontend_deps

echo

# ============================================================================
# BACKEND TESTS
# ============================================================================

echo -e "${BLUE}🐍 Running Backend Tests${NC}"
echo "========================"

cd backend

# Activate virtual environment
echo -e "${YELLOW}🔧 Activating virtual environment...${NC}"
source venv/bin/activate

# Verify virtual environment is activated
if [[ -z "$VIRTUAL_ENV" ]]; then
    echo -e "${RED}❌ Failed to activate virtual environment!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Using virtual environment: $VIRTUAL_ENV${NC}"

# Run backend tests using existing script
echo -e "${YELLOW}🏃 Running backend tests...${NC}"
./run_tests.sh
BACKEND_EXIT_CODE=$?

# Deactivate virtual environment
deactivate

cd ..

print_status $BACKEND_EXIT_CODE "Backend Tests"

# ============================================================================
# FRONTEND UNIT TESTS
# ============================================================================

echo -e "${BLUE}⚛️  Running Frontend Unit Tests${NC}"
echo "==============================="

cd frontend

echo -e "${YELLOW}🏃 Running Jest unit tests...${NC}"
npm test -- --coverage --watchAll=false --passWithNoTests
FRONTEND_UNIT_EXIT_CODE=$?

cd ..

print_status $FRONTEND_UNIT_EXIT_CODE "Frontend Unit Tests"

# ============================================================================
# FRONTEND E2E TESTS
# ============================================================================

echo -e "${BLUE}🎭 Running Frontend E2E Tests${NC}"
echo "============================="

cd frontend

# Check if frontend dev server is already running
if curl -s http://localhost:3000 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Frontend dev server already running${NC}"
    SERVER_RUNNING=true
else
    echo -e "${YELLOW}🔧 Frontend dev server not running - Playwright will start it${NC}"
    SERVER_RUNNING=false
fi

echo -e "${YELLOW}🏃 Running Playwright E2E tests...${NC}"
npm run test:e2e
FRONTEND_E2E_EXIT_CODE=$?

cd ..

print_status $FRONTEND_E2E_EXIT_CODE "Frontend E2E Tests"

# ============================================================================
# SUMMARY
# ============================================================================

echo -e "${BLUE}📊 Test Summary${NC}"
echo "==============="

if [ $BACKEND_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ Backend Tests: PASSED${NC}"
else
    echo -e "${RED}❌ Backend Tests: FAILED (exit code: $BACKEND_EXIT_CODE)${NC}"
fi

if [ $FRONTEND_UNIT_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ Frontend Unit Tests: PASSED${NC}"
else
    echo -e "${RED}❌ Frontend Unit Tests: FAILED (exit code: $FRONTEND_UNIT_EXIT_CODE)${NC}"
fi

if [ $FRONTEND_E2E_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ Frontend E2E Tests: PASSED${NC}"
else
    echo -e "${RED}❌ Frontend E2E Tests: FAILED (exit code: $FRONTEND_E2E_EXIT_CODE)${NC}"
fi

echo

if $OVERALL_SUCCESS; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED! 🎉${NC}"
    echo -e "${GREEN}The SIMILE application is ready for deployment.${NC}"
    exit 0
else
    echo -e "${RED}💥 SOME TESTS FAILED! 💥${NC}"
    echo -e "${YELLOW}Please review the output above and fix failing tests.${NC}"
    
    # Provide helpful commands for debugging
    echo
    echo -e "${BLUE}Debug Commands:${NC}"
    echo "==============="
    
    if [ $BACKEND_EXIT_CODE -ne 0 ]; then
        echo -e "${YELLOW}Backend:${NC} cd backend && source venv/bin/activate && ./run_tests.sh"
    fi
    
    if [ $FRONTEND_UNIT_EXIT_CODE -ne 0 ]; then
        echo -e "${YELLOW}Frontend Unit:${NC} cd frontend && npm test"
    fi
    
    if [ $FRONTEND_E2E_EXIT_CODE -ne 0 ]; then
        echo -e "${YELLOW}Frontend E2E:${NC} cd frontend && npm run test:e2e:headed"
        echo -e "${YELLOW}E2E Debug:${NC} cd frontend && npm run test:e2e:debug"
    fi
    
    exit 1
fi