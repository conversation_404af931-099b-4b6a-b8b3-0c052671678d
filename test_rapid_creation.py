#!/usr/bin/env python3
"""
Test script to investigate rapid entity creation functionality.
This will help identify race conditions, validation issues, or other problems.
"""

import asyncio
import aiohttp
import json
import time
from typing import List, Dict, Any
import uuid

BASE_URL = "http://localhost:8000"

async def create_entity(session: aiohttp.ClientSession, name: str) -> Dict[str, Any]:
    """Create a single entity and return the response details."""
    url = f"{BASE_URL}/api/v1/entities"
    data = {"name": name}
    
    start_time = time.time()
    try:
        async with session.post(url, json=data) as response:
            end_time = time.time()
            response_text = await response.text()
            
            result = {
                "name": name,
                "status_code": response.status,
                "response_time_ms": round((end_time - start_time) * 1000, 2),
                "success": response.status == 201,
                "timestamp": time.time()
            }
            
            if response.status == 201:
                response_data = json.loads(response_text)
                result["entity_id"] = response_data.get("id")
            else:
                result["error"] = response_text
                
            return result
    except Exception as e:
        return {
            "name": name,
            "status_code": None,
            "response_time_ms": None,
            "success": False,
            "error": str(e),
            "timestamp": time.time()
        }

async def test_simultaneous_creation(entity_names: List[str]) -> List[Dict[str, Any]]:
    """Test creating multiple entities simultaneously (no delays)."""
    print(f"Testing simultaneous creation of {len(entity_names)} entities...")
    
    async with aiohttp.ClientSession() as session:
        tasks = [create_entity(session, name) for name in entity_names]
        results = await asyncio.gather(*tasks)
    
    return results

async def test_sequential_creation(entity_names: List[str], delay_ms: int = 0) -> List[Dict[str, Any]]:
    """Test creating multiple entities sequentially with optional delay."""
    print(f"Testing sequential creation with {delay_ms}ms delay...")
    
    results = []
    async with aiohttp.ClientSession() as session:
        for name in entity_names:
            result = await create_entity(session, name)
            results.append(result)
            
            if delay_ms > 0:
                await asyncio.sleep(delay_ms / 1000)
    
    return results

async def cleanup_entities(entity_ids: List[int]):
    """Clean up created entities."""
    print(f"Cleaning up {len(entity_ids)} entities...")
    
    async with aiohttp.ClientSession() as session:
        for entity_id in entity_ids:
            try:
                url = f"{BASE_URL}/api/v1/entities/{entity_id}"
                async with session.delete(url) as response:
                    if response.status != 204:
                        print(f"Warning: Failed to delete entity {entity_id}")
            except Exception as e:
                print(f"Error deleting entity {entity_id}: {e}")

def generate_unique_names(count: int, prefix: str = "RapidTest") -> List[str]:
    """Generate unique entity names for testing."""
    unique_id = str(uuid.uuid4())[:8]
    return [f"{prefix} {unique_id} {i+1}" for i in range(count)]

def analyze_results(results: List[Dict[str, Any]], test_name: str):
    """Analyze and print test results."""
    print(f"\n=== {test_name} Results ===")
    
    successful = [r for r in results if r["success"]]
    failed = [r for r in results if not r["success"]]
    
    print(f"Total entities: {len(results)}")
    print(f"Successful: {len(successful)}")
    print(f"Failed: {len(failed)}")
    
    if successful:
        response_times = [r["response_time_ms"] for r in successful if r["response_time_ms"]]
        if response_times:
            print(f"Avg response time: {sum(response_times) / len(response_times):.2f}ms")
            print(f"Min response time: {min(response_times):.2f}ms")
            print(f"Max response time: {max(response_times):.2f}ms")
    
    if failed:
        print("\nFailures:")
        for failure in failed:
            print(f"  {failure['name']}: {failure.get('error', 'Unknown error')}")
    
    # Return entity IDs for cleanup
    return [r.get("entity_id") for r in successful if r.get("entity_id")]

async def main():
    """Run all rapid creation tests."""
    print("Starting rapid entity creation investigation...\n")
    
    all_entity_ids = []
    
    try:
        # Test 1: Simultaneous creation (3 entities, no delay)
        names_1 = generate_unique_names(3, "Simultaneous")
        results_1 = await test_simultaneous_creation(names_1)
        entity_ids_1 = analyze_results(results_1, "Simultaneous Creation")
        all_entity_ids.extend(entity_ids_1)
        
        await asyncio.sleep(1)  # Brief pause between tests
        
        # Test 2: Sequential creation (3 entities, no delay)
        names_2 = generate_unique_names(3, "Sequential-NoDelay")
        results_2 = await test_sequential_creation(names_2, 0)
        entity_ids_2 = analyze_results(results_2, "Sequential Creation (No Delay)")
        all_entity_ids.extend(entity_ids_2)
        
        await asyncio.sleep(1)
        
        # Test 3: Sequential creation (3 entities, 100ms delay)
        names_3 = generate_unique_names(3, "Sequential-100ms")
        results_3 = await test_sequential_creation(names_3, 100)
        entity_ids_3 = analyze_results(results_3, "Sequential Creation (100ms delay)")
        all_entity_ids.extend(entity_ids_3)
        
        await asyncio.sleep(1)
        
        # Test 4: Sequential creation (3 entities, 500ms delay - like E2E test)
        names_4 = generate_unique_names(3, "Sequential-500ms")
        results_4 = await test_sequential_creation(names_4, 500)
        entity_ids_4 = analyze_results(results_4, "Sequential Creation (500ms delay - E2E Test Style)")
        all_entity_ids.extend(entity_ids_4)
        
        await asyncio.sleep(1)
        
        # Test 5: Higher volume simultaneous creation (10 entities)
        names_5 = generate_unique_names(10, "HighVolume")
        results_5 = await test_simultaneous_creation(names_5)
        entity_ids_5 = analyze_results(results_5, "High Volume Simultaneous Creation")
        all_entity_ids.extend(entity_ids_5)
        
        print(f"\n=== Summary ===")
        print(f"Total tests run: 5")
        print(f"Total entities created: {len(all_entity_ids)}")
        
    finally:
        # Clean up all created entities
        if all_entity_ids:
            await cleanup_entities(all_entity_ids)
            print("Cleanup completed.")

if __name__ == "__main__":
    asyncio.run(main())