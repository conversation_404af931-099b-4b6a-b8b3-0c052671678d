version: '3.8'

services:
  database:
    build: ./database
    container_name: simile-db-dev
    environment:
      POSTGRES_DB: simile
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      target: development
    container_name: simile-api-dev
    environment:
      PYTHONPATH: /app
      DATABASE_URL: postgresql+asyncpg://postgres:postgres@database:5432/simile
      CORS_ORIGINS: '["http://localhost:3000"]'
    ports:
      - "8000:8000"
    volumes:
      # Mount source code for hot reload
      - ./backend/src:/app/src:ro
      - ./backend/tests:/app/tests:ro
      # Mount requirements for dependency changes
      - ./backend/requirements.txt:/app/requirements.txt:ro
    depends_on:
      database:
        condition: service_healthy
    command: ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  frontend:
    build:
      context: ./frontend
      target: development
    container_name: simile-ui-dev
    environment:
      REACT_APP_API_URL: http://localhost:8000
      # Enable React hot reload
      CHOKIDAR_USEPOLLING: true
      WATCHPACK_POLLING: true
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot reload
      - ./frontend/src:/app/src:ro
      - ./frontend/public:/app/public:ro
      - ./frontend/package.json:/app/package.json:ro
      - ./frontend/package-lock.json:/app/package-lock.json:ro
      - ./frontend/tsconfig.json:/app/tsconfig.json:ro
      # Exclude node_modules to prevent conflicts
      - /app/node_modules
      - /app/build
    depends_on:
      - backend
    command: ["npm", "start"]

volumes:
  postgres_data_dev: