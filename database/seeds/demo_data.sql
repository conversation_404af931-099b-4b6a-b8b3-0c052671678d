-- Insert demo entities
INSERT INTO entities (name) VALUES 
    ('Human'),
    ('Giraffe'),
    ('Elephant'),
    ('Skyscraper'),
    ('Swimming Pool');

-- Get IDs for reference (PostgreSQL specific)
WITH entity_ids AS (
    SELECT 
        id,
        name
    FROM entities
    WHERE name IN ('Human', 'Giraffe', 'Elephant', 'Skyscraper', 'Swimming Pool')
),
unit_ids AS (
    SELECT 
        id,
        name
    FROM units
)
-- Insert connections with their inverses
INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier)
SELECT 
    e1.id, 
    e2.id, 
    u.id, 
    multiplier
FROM (
    -- Giraffe is 3x Human in length
    SELECT 'Giraffe' as from_name, 'Human' as to_name, 'Length' as unit_name, 3.0 as multiplier
    UNION ALL
    SELECT 'Human', 'Giraffe', 'Length', 0.3
    
    -- Giraffe is 10x Human in mass
    UNION ALL
    SELECT 'Giraffe', 'Human', 'Mass', 10.0
    UNION ALL
    SELECT 'Human', 'Giraffe', 'Mass', 0.1
    
    -- Elephant is 2x Giraffe in length
    UNION ALL
    SELECT 'Elephant', 'Giraffe', 'Length', 2.0
    UNION ALL
    SELECT 'Giraffe', 'Elephant', 'Length', 0.5
    
    -- Elephant is 50x Giraffe in mass
    UNION ALL
    SELECT 'Elephant', 'Giraffe', 'Mass', 50.0
    UNION ALL
    SELECT 'Giraffe', 'Elephant', 'Mass', 0.1
    
    -- Skyscraper is 50x Giraffe in length
    UNION ALL
    SELECT 'Skyscraper', 'Giraffe', 'Length', 50.0
    UNION ALL
    SELECT 'Giraffe', 'Skyscraper', 'Length', 0.1
    
    -- Swimming Pool is 1000x Human in volume
    UNION ALL
    SELECT 'Swimming Pool', 'Human', 'Volume', 1000.0
    UNION ALL
    SELECT 'Human', 'Swimming Pool', 'Volume', 0.1
) AS connection_data
JOIN entity_ids e1 ON e1.name = connection_data.from_name
JOIN entity_ids e2 ON e2.name = connection_data.to_name
JOIN unit_ids u ON u.name = connection_data.unit_name;