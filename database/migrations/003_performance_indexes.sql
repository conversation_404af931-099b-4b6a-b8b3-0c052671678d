-- Performance optimization indexes for SIMILE database
-- This migration adds indexes to optimize common query patterns

-- Index for recursive CTE path-finding queries
-- This helps with the JOIN in the recursive part of the CTE
CREATE INDEX idx_connections_join_optimized 
ON connections(unit_id, from_entity_id, to_entity_id);

-- Index for reverse path-finding (when we need to find paths going the other direction)
CREATE INDEX idx_connections_reverse_lookup 
ON connections(unit_id, to_entity_id, from_entity_id);

-- Composite index for connection existence checks
-- Used when checking if a direct connection exists before running path-finding
CREATE INDEX idx_connections_direct_lookup 
ON connections(from_entity_id, to_entity_id, unit_id, multiplier);

-- Index to optimize the ANY() queries for entity name lookups
-- This helps with "WHERE id = ANY(:entity_ids)" queries
CREATE INDEX idx_entities_id_lookup 
ON entities(id) 
WHERE id IS NOT NULL;

-- Index for filtering connections by unit (used in list views)
CREATE INDEX idx_connections_by_unit 
ON connections(unit_id, created_at DESC);

-- Index for entity lookups with ordering (used in autocomplete and list views)
CREATE INDEX idx_entities_name_ordered 
ON entities(LOWER(name), id);

-- Partial index for recent connections (improves connection list performance)
-- Note: Using a static date to avoid IMMUTABLE function requirement
CREATE INDEX idx_connections_recent 
ON connections(created_at DESC, id);

-- Index for counting connections per entity (useful for analytics)
CREATE INDEX idx_connections_count_from 
ON connections(from_entity_id) 
WHERE from_entity_id IS NOT NULL;

CREATE INDEX idx_connections_count_to 
ON connections(to_entity_id) 
WHERE to_entity_id IS NOT NULL;

-- Covering index for entity details (includes all commonly needed columns)
-- This allows index-only scans for entity queries
CREATE INDEX idx_entities_covering 
ON entities(id, name, created_at, updated_at);

-- Covering index for unit details
CREATE INDEX idx_units_covering 
ON units(id, name, symbol, created_at, updated_at);

-- Index for connection multiplier range queries (if we add filtering by value range)
CREATE INDEX idx_connections_multiplier_range 
ON connections(multiplier, unit_id) 
WHERE multiplier > 0;

-- Partial index for high-value multipliers (optimize queries for large differences)
CREATE INDEX idx_connections_high_multiplier 
ON connections(from_entity_id, to_entity_id, unit_id, multiplier) 
WHERE multiplier >= 10.0;

-- Index for path validation (ensures referential integrity is fast to check)
CREATE INDEX idx_connections_validity 
ON connections(from_entity_id, to_entity_id) 
WHERE from_entity_id != to_entity_id;

-- Comment documenting the indexing strategy
COMMENT ON INDEX idx_connections_join_optimized IS 
'Optimizes recursive CTE joins in path-finding algorithm';

COMMENT ON INDEX idx_connections_reverse_lookup IS 
'Optimizes reverse path-finding queries';

COMMENT ON INDEX idx_connections_direct_lookup IS 
'Optimizes direct connection existence checks';

COMMENT ON INDEX idx_entities_covering IS 
'Covering index for entity queries - allows index-only scans';

COMMENT ON INDEX idx_connections_recent IS 
'Index for ordered connection lists - improves list performance';

-- Analyze tables to update statistics after creating indexes
ANALYZE entities;
ANALYZE connections;
ANALYZE units;