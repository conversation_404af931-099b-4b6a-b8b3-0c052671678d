-- Create units table
CREATE TABLE units (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create entities table
CREATE TABLE entities (
    id SERIAL PRIMARY KEY,
    name VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create case-insensitive unique index
CREATE UNIQUE INDEX entities_name_unique ON entities (LOWER(name));

-- Create connections table
CREATE TABLE connections (
    id SERIAL PRIMARY KEY,
    from_entity_id INTEGER NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    to_entity_id INTEGER NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    unit_id INTEGER NOT NULL REFERENCES units(id) ON DELETE RESTRICT,
    multiplier DECIMAL(10, 1) NOT NULL CHECK (multiplier > 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT connections_unique UNIQUE (from_entity_id, to_entity_id, unit_id),
    CONSTRAINT connections_no_self_reference CHECK (from_entity_id != to_entity_id)
);

-- Create indexes for performance
CREATE INDEX idx_entities_name ON entities(LOWER(name));
CREATE INDEX idx_connections_lookup ON connections(from_entity_id, to_entity_id, unit_id);
CREATE INDEX idx_connections_from ON connections(from_entity_id);
CREATE INDEX idx_connections_to ON connections(to_entity_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_units_updated_at BEFORE UPDATE ON units
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_entities_updated_at BEFORE UPDATE ON entities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_connections_updated_at BEFORE UPDATE ON connections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();