-- Performance optimization indexes for SIMILE database
-- This migration adds indexes to optimize common query patterns

-- Drop any existing indexes that might conflict (ignore errors if they don't exist)
DROP INDEX IF EXISTS idx_connections_join_optimized;
DROP INDEX IF EXISTS idx_connections_reverse_lookup;
DROP INDEX IF EXISTS idx_connections_direct_lookup;
DROP INDEX IF EXISTS idx_entities_id_lookup;
DROP INDEX IF EXISTS idx_connections_by_unit;
DROP INDEX IF EXISTS idx_entities_name_ordered;
DROP INDEX IF EXISTS idx_connections_recent;
DROP INDEX IF EXISTS idx_connections_count_from;
DROP INDEX IF EXISTS idx_connections_count_to;
DROP INDEX IF EXISTS idx_entities_covering;
DROP INDEX IF EXISTS idx_units_covering;
DROP INDEX IF EXISTS idx_connections_multiplier_range;
DROP INDEX IF EXISTS idx_connections_high_multiplier;
DROP INDEX IF EXISTS idx_connections_validity;

-- Index for recursive CTE path-finding queries
-- This helps with the JOIN in the recursive part of the CTE
CREATE INDEX idx_connections_join_optimized 
ON connections(unit_id, from_entity_id, to_entity_id);

-- Index for reverse path-finding (when we need to find paths going the other direction)
CREATE INDEX idx_connections_reverse_lookup 
ON connections(unit_id, to_entity_id, from_entity_id);

-- Composite index for connection existence checks
-- Used when checking if a direct connection exists before running path-finding
CREATE INDEX idx_connections_direct_lookup 
ON connections(from_entity_id, to_entity_id, unit_id, multiplier);

-- Index to optimize entity ID lookups
CREATE INDEX idx_entities_id_lookup 
ON entities(id);

-- Index for filtering connections by unit (used in list views)
CREATE INDEX idx_connections_by_unit 
ON connections(unit_id, created_at DESC);

-- Index for entity lookups with ordering (used in autocomplete and list views)
CREATE INDEX idx_entities_name_ordered 
ON entities(LOWER(name), id);

-- Index for connection list performance
CREATE INDEX idx_connections_recent 
ON connections(created_at DESC, id);

-- Index for counting connections per entity (useful for analytics)
CREATE INDEX idx_connections_count_from 
ON connections(from_entity_id);

CREATE INDEX idx_connections_count_to 
ON connections(to_entity_id);

-- Covering index for entity details (includes all commonly needed columns)
-- This allows index-only scans for entity queries
CREATE INDEX idx_entities_covering 
ON entities(id, name, created_at, updated_at);

-- Covering index for unit details
CREATE INDEX idx_units_covering 
ON units(id, name, symbol, created_at, updated_at);

-- Index for connection multiplier range queries
CREATE INDEX idx_connections_multiplier_range 
ON connections(multiplier, unit_id);

-- Partial index for high-value multipliers (optimize queries for large differences)
CREATE INDEX idx_connections_high_multiplier 
ON connections(from_entity_id, to_entity_id, unit_id, multiplier) 
WHERE multiplier >= 10.0;

-- Index for path validation (ensures referential integrity is fast to check)
CREATE INDEX idx_connections_validity 
ON connections(from_entity_id, to_entity_id) 
WHERE from_entity_id != to_entity_id;

-- Analyze tables to update statistics after creating indexes
ANALYZE entities;
ANALYZE connections;
ANALYZE units;