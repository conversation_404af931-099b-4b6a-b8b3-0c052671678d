#!/bin/bash
set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_FILE="test-frontend.log"

# Function to log and display messages
log_message() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Initialize log file
echo "SIMILE Frontend Unit Tests - $(date)" > "$LOG_FILE"
echo "====================================" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

log_message "${BLUE}⚛️  SIMILE Frontend Unit Tests${NC}"
log_message "============================="
log_message ""

# Change to frontend directory
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    log_message "${YELLOW}📦 Installing frontend dependencies...${NC}"
    if ! npm install 2>&1 | tee -a "../$LOG_FILE"; then
        log_message "${RED}❌ Failed to install dependencies${NC}"
        exit 1
    fi
    log_message "${GREEN}✅ Dependencies installed${NC}"
else
    log_message "${GREEN}✅ Dependencies already installed${NC}"
fi

log_message ""

# Check if backend services are running (needed for integration tests)
log_message "${BLUE}🔍 Checking backend services...${NC}"
if curl -s -f http://localhost:8000/health >/dev/null 2>&1; then
    log_message "${GREEN}✅ Backend services are running${NC}"
else
    log_message "${YELLOW}⚠️  Backend services not running - integration tests may fail${NC}"
    log_message "${YELLOW}   Start services with: docker-compose -f docker-compose.dev.yml up -d${NC}"
fi

log_message ""

# Run linting
log_message "${YELLOW}🧹 Running linting...${NC}"
if npm run lint 2>&1 | tee -a "../$LOG_FILE"; then
    log_message "${GREEN}✅ Linting passed${NC}"
else
    log_message "${YELLOW}⚠️  Linting issues found${NC}"
fi

log_message ""

# Run type checking
log_message "${YELLOW}🔍 Running type checking...${NC}"
if npm run typecheck 2>&1 | tee -a "../$LOG_FILE"; then
    log_message "${GREEN}✅ Type checking passed${NC}"
else
    log_message "${YELLOW}⚠️  Type checking issues found${NC}"
fi

log_message ""

# Run unit tests with coverage
log_message "${YELLOW}🧪 Running unit tests with coverage...${NC}"
if npm test -- --coverage --watchAll=false --passWithNoTests --verbose 2>&1 | tee -a "../$LOG_FILE"; then
    TEST_EXIT_CODE=0
    log_message "${GREEN}✅ All unit tests passed!${NC}"
else
    TEST_EXIT_CODE=1
    log_message "${RED}❌ Some unit tests failed!${NC}"
fi

log_message ""

# Display coverage summary if available
if [ -f "coverage/coverage-summary.json" ]; then
    log_message "${BLUE}📊 Coverage Summary:${NC}"
    if command -v jq &> /dev/null; then
        cat coverage/coverage-summary.json | jq -r '.total | "Lines: \(.lines.pct)% | Statements: \(.statements.pct)% | Functions: \(.functions.pct)% | Branches: \(.branches.pct)%"' | tee -a "../$LOG_FILE"
    else
        log_message "${YELLOW}   Coverage details saved to coverage/lcov-report/index.html${NC}"
    fi
fi

# Return to project root
cd ..

log_message ""
log_message "Frontend unit test run completed at $(date)"
log_message "Test results saved to: $LOG_FILE"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    log_message "${GREEN}🎉 Frontend unit tests completed successfully!${NC}"
else
    log_message "${RED}💥 Frontend unit tests failed!${NC}"
    log_message "${YELLOW}Check $LOG_FILE for details${NC}"
fi

exit $TEST_EXIT_CODE