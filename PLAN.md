# SIMILE Implementation Plan

## Project Overview
SIMILE is a web application for comparing physical entities based on measurable relationships, calculating transitive relationships automatically.

## Team Structure & Coordination
- **Product Owner**: Primary stakeholder and decision maker
- **Development Team**: Multiple developers (human and AI agents)
- **Coordination**: Critical - clear communication through git commits, documentation, and code comments
- **Code Reviews**: Required for all PRs before merging
- **Testing**: Developers test their own work + dedicated test engineer for comprehensive testing

## Core Decisions

### Data Model
- **Entities**: Physical objects with measurable properties
- **Units**: Length, Mass, Volume, Time, Count (extensible)
- **Relationships**: "A is Nx B" format where N can be fractional
- **Storage**: Both directions stored (A→B and B→A)
- **Precision**: 1 decimal place (e.g., 2.5 valid, 2.54 invalid)
- **Constraints**: Positive values only, same-unit traversal, max 6 hops

### Technology Stack
- **Frontend**: React with TypeScript
- **Backend**: Python with FastAPI
- **Database**: PostgreSQL (with recursive CTEs for path queries)
- **Container**: <PERSON><PERSON> (NOT Docker)
- **Orchestration**: podman-compose

### Architecture Pattern
- **API**: RESTful with structured parameters
- **API Versioning**: /api/v1/ prefix for all endpoints
- **UI**: Semi-structured natural language input
- **Development**: Parallel API and UI development
- **Testing**: TDD with 80% minimum coverage
- **Logging**: Console logging with structured format
- **Error Tracking**: Detailed error messages with stack traces for debugging

## Business Rules

### Entity Naming
- **Case Insensitive**: "human", "Human", "HUMAN" are the same entity
- **Allowed Characters**: Letters and spaces only
- **Maximum Length**: 20 characters
- **Duplicates**: Prevented (case-insensitive check)

### Path Calculation
- **Circular Paths**: If A→B→C→A exists, queries return direct path (A→C)
- **No Path Found**: Return user-friendly message prompting to create connection
- **Path Selection**: When multiple paths exist, return shortest path

### Input/Output Format
- **API Format**: Strict - "Entity1 is N times Entity2 in Unit" (N can be decimal or fraction)
- **UI Translation**: Semi-structured input that translates to API format
- **Query Results**: Simple format "A is Nx B in unit" with optional calculation details
- **Auto-suggestion**: Entity names suggested as user types
- **Error Messages**: Helpful suggestions for corrections and prompts to create missing connections

## Implementation Phases

### Phase 1: Foundation (Week 1-2) ✅ COMPLETED
**Goal**: Basic project structure and development environment

1. **Project Setup**
   - Initialize git flow
   - Create project structure (full skeleton)
   - Setup Python virtual environment
   - Configure TypeScript/React project
   - Create podman-compose configuration

2. **Database Design**
   ```sql
   -- Core tables
   entities (id, name, created_at, updated_at)
   units (id, name, symbol, created_at, updated_at)
   connections (
     id, 
     from_entity_id, 
     to_entity_id, 
     unit_id, 
     multiplier, 
     created_at,
     updated_at
   )
   
   -- Indexes for performance
   CREATE INDEX idx_entities_name ON entities(LOWER(name));
   CREATE INDEX idx_connections_lookup ON connections(from_entity_id, to_entity_id, unit_id);
   ```

3. **Development Environment**
   - Dockerfile for each service
   - podman-compose.yml for orchestration
   - Environment variable configuration
   - Development vs production configs
   - Seed data scripts
   - Data generator for testing

### Phase 2: Core Backend (Week 3-4) ✅ COMPLETED
**Goal**: Complete API with all CRUD operations

1. **Data Service Layer**
   - Database connection management
   - Entity CRUD operations
   - Unit CRUD operations
   - Connection CRUD with auto-inverse creation

2. **Business Logic Layer**
   - Path finding algorithm (recursive CTE)
   - Validation rules
   - Relationship calculation service

3. **API Endpoints (v1)**
   ```
   POST   /api/v1/entities
   GET    /api/v1/entities
   GET    /api/v1/entities/{id}
   PUT    /api/v1/entities/{id}
   DELETE /api/v1/entities/{id}
   
   POST   /api/v1/connections
   GET    /api/v1/connections
   DELETE /api/v1/connections/{id}
   
   GET    /api/v1/units
   POST   /api/v1/units
   
   GET    /api/v1/compare?from={id}&to={id}&unit={id}
   ```

4. **Testing**
   - Unit tests for business logic
   - Integration tests for API endpoints
   - Database transaction tests
   - Load testing with data generator

### Phase 3: Frontend Foundation (Week 3-4, parallel) ✅ COMPLETED
**Goal**: Basic UI with core functionality

1. **UI Components**
   - Entity selector/creator with auto-complete
   - Connection input form
   - Comparison query interface
   - Results display with calculation details toggle

2. **State Management**
   - React Context or Redux setup
   - API client service
   - Error handling

3. **Routing**
   - Home/Query page
   - Entities management page
   - Connections view page

### Phase 4: Integration & Polish (Week 5) ✅ COMPLETED
**Goal**: Full system integration and UX refinement

1. **Integration**
   - Connect frontend to backend
   - End-to-end testing
   - Performance optimization

2. **UX Enhancements**
   - Natural language input parsing
   - Autocomplete for entities
   - Visual feedback for operations
   - Responsive design

3. **Demo Preparation**
   - Load demo data (5 entities)
   - Example comparisons
   - User guide

### Phase 5: Deployment & Documentation (Week 6) 🚧 IN PROGRESS
**Goal**: Production-ready application

1. **Deployment**
   - Production podman configurations
   - Environment setup documentation
   - Deployment scripts

2. **Documentation**
   - API documentation (OpenAPI/Swagger)
   - User guide
   - Developer setup guide
   - Team coordination guide

## Testing Strategy

### Test Types
1. **Unit Tests**: All business logic functions
2. **Integration Tests**: API endpoints, database operations
3. **End-to-End Tests**: Full user workflows
4. **Performance Tests**: Load testing with data generator
5. **Stress Tests**: Large dataset handling (10,000+ entities)

### Test Data
1. **Seed Data**: Demo scenario with 5 entities
2. **Test Generator**: Creates random entities and connections
3. **Edge Cases**: Circular paths, max hop limits, invalid inputs

## Demo Scenario (5 Entities)

### Entities:
1. **Human** (base reference)
2. **Giraffe** (length: 3x human, mass: 10x human)
3. **Elephant** (length: 2x giraffe, mass: 50x giraffe)
4. **Skyscraper** (length: 50x giraffe)
5. **Swimming Pool** (volume: 1000x human)

### Demo Queries:
- "How many humans tall is a skyscraper?" → 150
- "How many giraffes heavy is an elephant?" → 50
- "How many humans heavy is an elephant?" → 500

## Development Milestones

1. **Week 1**: Development environment complete
2. **Week 2**: Database design implemented
3. **Week 3**: Backend API functional
4. **Week 4**: Frontend connected to backend
5. **Week 5**: Full feature set working
6. **Week 6**: Production-ready with documentation

## Risk Mitigation

1. **Technical Risks**
   - PostgreSQL recursive CTE performance → Index optimization
   - Complex UI state → Start simple, iterate

2. **Schedule Risks**
   - Parallel development coordination → Daily sync points
   - Integration issues → Early integration tests

3. **Team Risks**
   - Multiple developers → Clear git flow process
   - AI agent coordination → Detailed commit messages

## Success Criteria

1. All MVP features implemented ✅
2. 80% test coverage achieved ✅ (Frontend: 84%, Backend: Tests written)
3. Demo scenario fully functional ✅
4. Sub-2 second query response time ✅
5. Clean, maintainable codebase ✅
6. All tests passing ⚠️ (Frontend: ✅, Backend: Database connectivity issues)
7. Documentation complete 🚧 (In progress)

## Current Status Summary (June 2025)

### Completed ✅
- **Phase 1-4**: All foundation, backend, frontend, and integration work complete
- **Core Features**: Entity management, connections with auto-inverse, path-finding, comparison UI
- **Template Interface**: Seamless comparison interface with native text inputs
- **Test Infrastructure**: Comprehensive test suites for both frontend and backend
- **Containerization**: Full Podman support with compose files
- **Demo Data**: 5-entity demo scenario implemented

### In Progress 🚧
- **Phase 5**: Deployment and documentation
- **Backend Test Fixes**: Resolving database connectivity in test environment
- **Documentation Updates**: API docs, deployment guide, user manual

### Remaining Work 📋
- **Authentication**: User accounts and access control (post-MVP)
- **Import/Export**: CSV/JSON data import/export functionality
- **Advanced Features**: Bulk operations, favorites, comparison history
- **Production Deploy**: CI/CD pipeline, monitoring, logging
- **Performance**: Caching layer for complex queries
- **UI Enhancements**: Dark mode, mobile optimization