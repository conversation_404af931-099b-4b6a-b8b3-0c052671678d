#!/bin/bash
set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_FILE="lint-backend.log"

# Function to log and display messages
log_message() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Initialize log file
echo "SIMILE Backend Linting - $(date)" > "$LOG_FILE"
echo "=================================" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

log_message "${BLUE}🧹 SIMILE Backend Linting${NC}"
log_message "========================="
log_message ""

# Change to backend directory
cd backend

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    log_message "${RED}❌ Backend virtual environment not found!${NC}"
    log_message "${YELLOW}   Please run: python3.11 -m venv venv && source venv/bin/activate && pip install -r requirements.txt${NC}"
    exit 1
fi

# Activate virtual environment
log_message "${YELLOW}🔧 Activating virtual environment...${NC}"
source venv/bin/activate

# Verify virtual environment is activated
if [[ -z "$VIRTUAL_ENV" ]]; then
    log_message "${RED}❌ Failed to activate virtual environment!${NC}"
    exit 1
fi

log_message "${GREEN}✅ Using virtual environment: $VIRTUAL_ENV${NC}"
log_message ""

# Set environment variables
export PYTHONPATH="."

# Initialize exit code
LINT_EXIT_CODE=0

# Run linting
log_message "${YELLOW}🧹 Running linting...${NC}"
if ! make lint 2>&1 | tee -a "../$LOG_FILE"; then
    log_message "${YELLOW}⚠️  Linting issues found${NC}"
    LINT_EXIT_CODE=1
fi

log_message ""

# Run type checking
log_message "${YELLOW}🔍 Running type checking...${NC}"
if ! make typecheck 2>&1 | tee -a "../$LOG_FILE"; then
    log_message "${YELLOW}⚠️  Type checking issues found${NC}"
    LINT_EXIT_CODE=1
fi

log_message ""

# Deactivate virtual environment
deactivate

# Return to project root
cd ..

log_message ""
log_message "Backend linting completed at $(date)"
log_message "Linting results saved to: $LOG_FILE"

if [ $LINT_EXIT_CODE -eq 0 ]; then
    log_message "${GREEN}🎉 Backend linting completed successfully!${NC}"
else
    log_message "${RED}💥 Backend linting found issues!${NC}"
    log_message "${YELLOW}Check $LOG_FILE for details${NC}"
fi

exit $LINT_EXIT_CODE
