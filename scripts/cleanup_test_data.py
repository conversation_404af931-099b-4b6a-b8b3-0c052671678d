#!/usr/bin/env python3
"""
Clean up all test data from the database.
This script removes entities and connections that match test patterns.
"""

import requests
import json
import time
from typing import List, Dict, Any

API_BASE_URL = "http://localhost:8000/api/v1"

# Test patterns to match for cleanup
TEST_PATTERNS = [
    # Pattern prefixes that indicate test data
    "Test", "test", "TEST",
    "Human Test", "Ball Test", "Build Test", "Car Test", "Eleph Test", "Mouse Test",
    "Sample", "SAMPLE",
    "Delete", "DELETE", 
    "Temp", "TEMP",
    "Basketball Test", "Connection Test",
    "Rapid", "RAPID",
    "Quick", "QUICK",
    "Demo", "DEMO",
    # Specific test entity patterns with random suffixes
    "Human [A-Z]+", "Ball [A-Z]+", "Build [A-Z]+", 
    "Car [A-Z]+", "Eleph [A-Z]+", "Mouse [A-Z]+",
    # Other test patterns
    "Entity [A-Z]+", "Object [A-Z]+",
    "AAAA", "From ", "To ", "Duplicate",
    # Additional patterns for remaining test data
    "Console Test", "Frontend Test", "Server Error Test",
    "Large Building", "Small Object",
    "Refresh From", "Refresh To",
    "Large Num", "Edit Updated",
    # Entities with test-like short suffixes
    ".*Test.*", ".*test.*"
]

def get_all_entities() -> List[Dict[str, Any]]:
    """Fetch all entities from the API."""
    try:
        response = requests.get(f"{API_BASE_URL}/entities")
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error fetching entities: {e}")
        return []

def get_all_connections() -> List[Dict[str, Any]]:
    """Fetch all connections from the API."""
    try:
        response = requests.get(f"{API_BASE_URL}/connections")
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error fetching connections: {e}")
        return []

def is_test_entity(entity_name: str) -> bool:
    """Check if an entity name matches test patterns."""
    import re
    
    # Check exact prefix matches
    for pattern in TEST_PATTERNS:
        if not pattern.endswith("]"):  # Not a regex pattern
            if entity_name.startswith(pattern):
                return True
        else:  # Regex pattern
            if re.match(f"^{pattern}$", entity_name):
                return True
    
    # Check if it contains only uppercase letters (like AAAAAAA)
    if re.match(r'^[A-Z]+$', entity_name) and len(entity_name) > 3:
        return True
    
    # Check for entities with test-like suffixes
    if re.match(r'.+\s+[A-Z]{4,}[A-Z0-9]*$', entity_name):
        return True
        
    return False

def delete_entity(entity_id: int) -> bool:
    """Delete an entity by ID."""
    try:
        response = requests.delete(f"{API_BASE_URL}/entities/{entity_id}")
        return response.status_code in [200, 204]
    except Exception as e:
        print(f"Error deleting entity {entity_id}: {e}")
        return False

def delete_connection(from_id: int, to_id: int) -> bool:
    """Delete a connection."""
    try:
        response = requests.delete(f"{API_BASE_URL}/connections/{from_id}/{to_id}")
        return response.status_code in [200, 204]
    except Exception as e:
        print(f"Error deleting connection {from_id}->{to_id}: {e}")
        return False

def main():
    import sys
    
    # Check for auto-confirm flag
    auto_confirm = '--yes' in sys.argv or '-y' in sys.argv
    keep_only_real = '--keep-real' in sys.argv
    
    # Real entities to keep (if using --keep-real flag)
    REAL_ENTITIES = ["Elephant", "Giraffe"]
    
    print("🧹 Starting database cleanup...")
    print("=" * 60)
    
    # Get all entities
    entities = get_all_entities()
    print(f"📊 Found {len(entities)} total entities in database")
    
    # Identify test entities
    test_entities = []
    test_entity_ids = set()
    
    if keep_only_real:
        # Keep only specific real entities, delete everything else
        print(f"🎯 Keep-real mode: Will keep only {REAL_ENTITIES}")
        for entity in entities:
            if entity['name'] not in REAL_ENTITIES:
                test_entities.append(entity)
                test_entity_ids.add(entity['id'])
    else:
        # Normal mode: identify test entities by pattern
        for entity in entities:
            if is_test_entity(entity['name']):
                test_entities.append(entity)
                test_entity_ids.add(entity['id'])
    
    print(f"🔍 Identified {len(test_entities)} test entities to clean up")
    
    if test_entities:
        print("\n📋 Test entities to be deleted:")
        for entity in test_entities[:20]:  # Show first 20
            print(f"   - {entity['name']} (ID: {entity['id']})")
        if len(test_entities) > 20:
            print(f"   ... and {len(test_entities) - 20} more")
    
    # Get all connections
    connections = get_all_connections()
    print(f"\n📊 Found {len(connections)} total connections in database")
    
    # Identify connections involving test entities
    test_connections = []
    for conn in connections:
        if conn['from_entity_id'] in test_entity_ids or conn['to_entity_id'] in test_entity_ids:
            test_connections.append(conn)
    
    print(f"🔍 Identified {len(test_connections)} connections to clean up")
    
    if not test_entities and not test_connections:
        print("\n✅ No test data found to clean up!")
        return
    
    # Confirm before deletion
    print("\n" + "=" * 60)
    
    if auto_confirm:
        print(f"⚠️  Auto-confirming deletion of {len(test_entities)} entities and {len(test_connections)} connections")
        response = 'yes'
    else:
        response = input(f"⚠️  Delete {len(test_entities)} entities and {len(test_connections)} connections? (yes/no): ")
    
    if response.lower() != 'yes':
        print("❌ Cleanup cancelled")
        return
    
    # Delete connections first (due to foreign key constraints)
    print("\n🗑️  Deleting connections...")
    deleted_connections = 0
    for conn in test_connections:
        if delete_connection(conn['from_entity_id'], conn['to_entity_id']):
            deleted_connections += 1
            if deleted_connections % 10 == 0:
                print(f"   Deleted {deleted_connections}/{len(test_connections)} connections...")
    
    print(f"✅ Deleted {deleted_connections} connections")
    
    # Delete entities
    print("\n🗑️  Deleting entities...")
    deleted_entities = 0
    for entity in test_entities:
        if delete_entity(entity['id']):
            deleted_entities += 1
            if deleted_entities % 10 == 0:
                print(f"   Deleted {deleted_entities}/{len(test_entities)} entities...")
    
    print(f"✅ Deleted {deleted_entities} entities")
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎉 Cleanup completed!")
    print(f"   - Deleted {deleted_entities} test entities")
    print(f"   - Deleted {deleted_connections} test connections")
    
    # Show remaining entities
    remaining = get_all_entities()
    print(f"\n📊 Remaining entities in database: {len(remaining)}")
    if remaining:
        print("📋 Remaining entities:")
        for entity in remaining:
            print(f"   - {entity['name']} (ID: {entity['id']})")

if __name__ == "__main__":
    main()