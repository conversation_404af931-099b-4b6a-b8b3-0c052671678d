#!/usr/bin/env python3
"""
Test data generator for SIMILE application.
Generates random entities and connections for stress testing.
"""

import asyncio
import random
import string
from decimal import Decimal
from typing import List, Tuple

import asyncpg
from dotenv import load_dotenv
import os

load_dotenv()


class TestDataGenerator:
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.conn = None
        self.units = []
        self.entities = []

    async def connect(self):
        """Connect to the database."""
        # Convert SQLAlchemy URL to asyncpg format
        db_url = self.database_url.replace("postgresql+asyncpg://", "postgresql://")
        self.conn = await asyncpg.connect(db_url)

    async def disconnect(self):
        """Disconnect from the database."""
        if self.conn:
            await self.conn.close()

    async def load_existing_data(self):
        """Load existing units and entities."""
        self.units = await self.conn.fetch("SELECT id, name FROM units")
        self.entities = await self.conn.fetch("SELECT id, name FROM entities")

    def generate_entity_name(self) -> str:
        """Generate a random entity name."""
        prefixes = ["Big", "Small", "Fast", "Slow", "Heavy", "Light", "Long", "Short"]
        objects = ["Car", "Truck", "Plane", "Train", "Building", "Tree", "Mountain", "River"]
        suffix = ''.join(random.choices(string.digits, k=3))
        return f"{random.choice(prefixes)} {random.choice(objects)} {suffix}"[:20]

    def generate_multiplier(self) -> Decimal:
        """Generate a random multiplier with 1 decimal place."""
        # Generate values that create interesting relationships
        ranges = [
            (0.1, 0.9),    # Less than 1
            (1.0, 9.9),    # 1-10
            (10.0, 99.9),  # 10-100
            (100.0, 999.9) # 100-1000
        ]
        min_val, max_val = random.choice(ranges)
        return Decimal(str(round(random.uniform(min_val, max_val), 1)))

    async def create_entities(self, count: int):
        """Create random entities."""
        print(f"Creating {count} entities...")
        created = 0
        
        for _ in range(count):
            name = self.generate_entity_name()
            try:
                result = await self.conn.fetchrow(
                    "INSERT INTO entities (name) VALUES ($1) RETURNING id, name",
                    name
                )
                self.entities.append(result)
                created += 1
            except asyncpg.UniqueViolationError:
                # Name already exists, skip
                pass
        
        print(f"Created {created} new entities")

    async def create_connections(self, count: int):
        """Create random connections between entities."""
        print(f"Creating {count} connections...")
        created = 0
        
        if len(self.entities) < 2 or not self.units:
            print("Not enough entities or units to create connections")
            return
        
        for _ in range(count):
            # Select two different random entities
            entity1, entity2 = random.sample(self.entities, 2)
            unit = random.choice(self.units)
            multiplier = self.generate_multiplier()
            inverse_multiplier = Decimal(str(round(1 / float(multiplier), 1)))
            
            try:
                # Insert both directions in a transaction
                async with self.conn.transaction():
                    await self.conn.execute(
                        """INSERT INTO connections 
                           (from_entity_id, to_entity_id, unit_id, multiplier) 
                           VALUES ($1, $2, $3, $4)""",
                        entity1['id'], entity2['id'], unit['id'], multiplier
                    )
                    await self.conn.execute(
                        """INSERT INTO connections 
                           (from_entity_id, to_entity_id, unit_id, multiplier) 
                           VALUES ($1, $2, $3, $4)""",
                        entity2['id'], entity1['id'], unit['id'], inverse_multiplier
                    )
                created += 2
            except asyncpg.UniqueViolationError:
                # Connection already exists, skip
                pass
        
        print(f"Created {created} new connections")

    async def generate_test_data(self, entity_count: int, connection_count: int):
        """Generate test data."""
        try:
            await self.connect()
            await self.load_existing_data()
            
            print(f"Found {len(self.units)} units and {len(self.entities)} existing entities")
            
            await self.create_entities(entity_count)
            await self.load_existing_data()  # Reload to include new entities
            await self.create_connections(connection_count)
            
            # Print statistics
            total_entities = await self.conn.fetchval("SELECT COUNT(*) FROM entities")
            total_connections = await self.conn.fetchval("SELECT COUNT(*) FROM connections")
            
            print(f"\nDatabase statistics:")
            print(f"Total entities: {total_entities}")
            print(f"Total connections: {total_connections}")
            
        finally:
            await self.disconnect()


async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Generate test data for SIMILE")
    parser.add_argument("--entities", type=int, default=100, 
                       help="Number of entities to create (default: 100)")
    parser.add_argument("--connections", type=int, default=500,
                       help="Number of connections to create (default: 500)")
    parser.add_argument("--database-url", type=str, 
                       default=os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@localhost:5432/simile"),
                       help="Database URL")
    
    args = parser.parse_args()
    
    generator = TestDataGenerator(args.database_url)
    await generator.generate_test_data(args.entities, args.connections)


if __name__ == "__main__":
    asyncio.run(main())