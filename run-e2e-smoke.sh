#!/bin/bash
set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_FILE="smoke-e2e.log"

# Function to log and display messages
log_message() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Initialize log file
echo "SIMILE E2E Smoke Tests - $(date)" > "$LOG_FILE"
echo "=================================" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

log_message "${BLUE}🚀 SIMILE E2E Smoke Tests${NC}"
log_message "=========================="
log_message "${YELLOW}Running critical path tests only (~2 minutes)${NC}"
log_message ""

# Change to frontend directory
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    log_message "${RED}❌ Frontend dependencies not installed!${NC}"
    log_message "${YELLOW}   Please run: npm install${NC}"
    exit 1
fi

# Check if Playwright is installed
if ! npx playwright --version >/dev/null 2>&1; then
    log_message "${RED}❌ Playwright not installed!${NC}"
    log_message "${YELLOW}   Please run: npm run test:e2e:install${NC}"
    exit 1
fi

# Check if backend services are running
log_message "${BLUE}🔍 Checking backend services...${NC}"
if curl -s -f http://localhost:8000/api/v1/health >/dev/null 2>&1; then
    log_message "${GREEN}✅ Backend services are running${NC}"
else
    log_message "${RED}❌ Backend services not accessible${NC}"
    log_message "${YELLOW}   Please run: podman-compose up -d${NC}"
    log_message "${YELLOW}   Or: docker-compose -f docker-compose.dev.yml up -d${NC}"
    exit 1
fi

# Check if frontend dev server is running
log_message "${BLUE}🔍 Checking frontend dev server...${NC}"
if curl -s -f http://localhost:3000 >/dev/null 2>&1; then
    log_message "${GREEN}✅ Frontend dev server is running${NC}"
    SERVER_RUNNING=true
else
    log_message "${YELLOW}⚠️  Frontend dev server not running - Playwright will start it${NC}"
    SERVER_RUNNING=false
fi

log_message ""

# Define smoke test files (critical path functionality)
SMOKE_TESTS=(
    "e2e/tests/functional-areas/navigation/setup-verification.spec.ts"
    "e2e/tests/functional-areas/navigation/navigation.spec.ts"
    "e2e/tests/functional-areas/integration/backend-connectivity-validation.spec.ts"
)

# Run smoke tests
log_message "${YELLOW}🧪 Running smoke tests...${NC}"
log_message "Tests to run:"
for test in "${SMOKE_TESTS[@]}"; do
    log_message "  - $test"
done
log_message ""

# Execute smoke tests with optimized settings
TEST_EXIT_CODE=0

# Run tests and capture output
log_message "${BLUE}🔄 Executing Playwright tests...${NC}"
TEST_OUTPUT=$(npx playwright test \
    "${SMOKE_TESTS[@]}" \
    --reporter=line \
    --workers=1 \
    --timeout=30000 \
    --retries=1 \
    2>&1)

# Save output to log file
echo "$TEST_OUTPUT" | tee -a "../$LOG_FILE"

# Parse test results from output
if echo "$TEST_OUTPUT" | grep -q "failed$"; then
    # Extract failure count
    FAILED_COUNT=$(echo "$TEST_OUTPUT" | grep -o "[0-9]\+ failed" | grep -o "[0-9]\+" | head -1)
    TEST_EXIT_CODE=1
    log_message "${RED}❌ $FAILED_COUNT smoke tests failed!${NC}"
elif echo "$TEST_OUTPUT" | grep -q "passed"; then
    # Extract pass count
    PASSED_COUNT=$(echo "$TEST_OUTPUT" | grep -o "[0-9]\+ passed" | grep -o "[0-9]\+" | head -1)
    log_message "${GREEN}✅ All $PASSED_COUNT smoke tests passed!${NC}"
else
    # Fallback - check Playwright exit code
    if [ $? -ne 0 ]; then
        TEST_EXIT_CODE=1
        log_message "${RED}❌ Smoke tests encountered errors!${NC}"
    else
        log_message "${GREEN}✅ Smoke tests completed!${NC}"
    fi
fi

# Return to project root
cd ..

log_message ""
log_message "Smoke test run completed at $(date)"
log_message "Test results saved to: $LOG_FILE"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    log_message "${GREEN}🎉 Smoke tests completed successfully!${NC}"
    log_message "${BLUE}💡 Critical path functionality is working${NC}"
else
    log_message "${RED}💥 Smoke tests failed!${NC}"
    log_message "${YELLOW}Check $LOG_FILE for details${NC}"
    log_message "${YELLOW}Fix critical issues before running full test suite${NC}"
fi

exit $TEST_EXIT_CODE
