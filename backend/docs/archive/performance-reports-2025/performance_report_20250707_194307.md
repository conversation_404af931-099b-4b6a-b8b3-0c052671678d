# SIMILE Backend Performance Report

**Generated:** 2025-07-07 19:43:10
**Test Run:** 20250707_194307

## Executive Summary

- **Test Status:** PASSED
- **Execution Time:** 1.87 seconds
- **Return Code:** 0

## Test Results Summary

- **Total Tests:** 1
- **Passed:** 1
- **Failed:** 0
- **Skipped:** 0
- **Errors:** 0

## Performance Metrics

## Test Details

### Unknown

- **Outcome:** passed
- **Duration:** 0.000 seconds

## Console Output

### Standard Output

```
============================= test session starts ==============================
platform darwin -- Python 3.11.13, pytest-7.4.4, pluggy-1.6.0
benchmark: 4.0.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)
rootdir: /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend
configfile: pyproject.toml
plugins: anyio-4.9.0, asyncio-0.23.3, json-report-1.5.0, Faker-20.1.0, metadata-3.1.1, cov-4.1.0, xdist-3.5.0, benchmark-4.0.0
asyncio: mode=Mode.STRICT
collected 1 item

tests/test_performance_benchmarks.py .                                   [100%]

--------------------------------- JSON report ----------------------------------
report saved to: performance_reports/performance_test_results_20250707_194307.json

--------- coverage: platform darwin, python 3.11.13-final-0 ----------
Name                        Stmts   Miss  Cover   Missing
---------------------------------------------------------
src/__init__.py                 0      0   100%
src/app_factory.py             14      1    93%   45
src/config.py                  17      1    94%   30
src/database.py                21      2    90%   34, 65
src/main.py                    13     13     0%   1-37
src/models.py                  27      0   100%
src/routes/__init__.py          0      0   100%
src/routes/compare.py          30     19    37%   26-74
src/routes/connections.py     147    125    15%   30-188, 202-218, 227-233, 244-300, 309-334
src/routes/entities.py         68     43    37%   28-44, 58-67, 76-80, 90-105, 117-124
src/routes/units.py            34     19    44%   20-22, 31-39, 52-59
src/schemas.py                107     33    69%   15-17, 22-24, 46, 60-64, 91-101, 106-108, 128-140
src/services.py                39     33    15%   22-115, 123-149
---------------------------------------------------------
TOTAL                         517    289    44%

============================= slowest 10 durations =============================
0.88s call     tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance
0.13s setup    tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance

(1 durations < 0.005s hidden.  Use -vv to show these durations.)
============================== 1 passed in 1.05s ===============================

```

## Performance Recommendations

✅ **All performance tests passed successfully!**

### Recommendations:
- Continue monitoring performance metrics in CI/CD
- Consider adding more edge case performance tests
- Monitor memory usage patterns in production
- Set up performance alerts for production metrics

## Environment Information

- **Python Version:** 3.11.13 (main, Jun  3 2025, 18:38:25) [Clang 17.0.0 (clang-1700.0.13.3)]
- **Working Directory:** /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend
- **Test Database Host:** localhost
- **Virtual Environment:** Not detected
