# DevOps Test Infrastructure Design for SIMILE Backend

## Overview

This document outlines the comprehensive test infrastructure improvements needed for the SIMILE backend, focusing on performance testing, test environment enhancements, and utility automation.

## 1. Performance Testing Setup

### 1.1 Load Testing Tools Selection

#### Primary Tool: Locust (Python-based)
```python
# requirements-perf.txt
locust==2.17.0
psutil==5.9.6
prometheus-client==0.19.0
grafana-api==1.0.3
```

**Why Locust:**
- Native Python integration with FastAPI
- Distributed load testing capabilities
- Real-time web UI for monitoring
- Programmatic test scenarios
- Easy integration with existing test infrastructure

#### Secondary Tool: K6 (for advanced scenarios)
```yaml
# k6-config.yaml
scenarios:
  api_stress_test:
    executor: 'ramping-vus'
    startVUs: 0
    stages:
      - duration: '2m', target: 100
      - duration: '5m', target: 100
      - duration: '2m', target: 200
      - duration: '5m', target: 200
      - duration: '2m', target: 0
```

### 1.2 Performance Test Structure

```python
# tests/performance/locustfile.py
from locust import HttpUser, task, between
import random
import json

class SimileAPIUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """Initialize test data on user creation."""
        self.entity_ids = []
        self.connection_ids = []
        
    @task(3)
    def create_entity(self):
        """Test entity creation endpoint."""
        response = self.client.post("/api/entities", json={
            "name": f"Entity_{random.randint(1000, 9999)}",
            "unit_id": random.choice(["length", "mass", "time"])
        })
        if response.status_code == 201:
            self.entity_ids.append(response.json()["id"])
    
    @task(5)
    def list_entities(self):
        """Test entity listing with pagination."""
        self.client.get("/api/entities?limit=20&offset=0")
    
    @task(2)
    def create_connection(self):
        """Test connection creation."""
        if len(self.entity_ids) >= 2:
            from_id, to_id = random.sample(self.entity_ids, 2)
            self.client.post("/api/connections", json={
                "from_entity_id": from_id,
                "to_entity_id": to_id,
                "connection_value": round(random.uniform(0.1, 10.0), 1)
            })
    
    @task(4)
    def compare_entities(self):
        """Test comparison pathfinding."""
        if len(self.entity_ids) >= 2:
            from_id, to_id = random.sample(self.entity_ids, 2)
            self.client.get(f"/api/compare/{from_id}/{to_id}")
```

### 1.3 Database Performance Monitoring

```python
# scripts/db_performance_monitor.py
import asyncio
import asyncpg
import psutil
import time
from datetime import datetime
import json

class DatabasePerformanceMonitor:
    def __init__(self, connection_string):
        self.connection_string = connection_string
        self.metrics = []
        
    async def collect_metrics(self):
        """Collect database performance metrics."""
        conn = await asyncpg.connect(self.connection_string)
        
        try:
            # Active connections
            active_connections = await conn.fetchval(
                "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
            )
            
            # Database size
            db_size = await conn.fetchval(
                "SELECT pg_database_size('simile_test')"
            )
            
            # Query statistics
            query_stats = await conn.fetch("""
                SELECT 
                    query,
                    calls,
                    total_exec_time,
                    mean_exec_time,
                    stddev_exec_time,
                    rows
                FROM pg_stat_statements
                WHERE query NOT LIKE '%pg_stat%'
                ORDER BY total_exec_time DESC
                LIMIT 10
            """)
            
            # Lock statistics
            locks = await conn.fetchval(
                "SELECT count(*) FROM pg_locks WHERE granted = false"
            )
            
            # Cache hit ratio
            cache_hit_ratio = await conn.fetchrow("""
                SELECT 
                    sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) as ratio
                FROM pg_statio_user_tables
            """)
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "active_connections": active_connections,
                "database_size_bytes": db_size,
                "blocked_queries": locks,
                "cache_hit_ratio": float(cache_hit_ratio['ratio'] or 0),
                "top_queries": [dict(q) for q in query_stats],
                "system_metrics": {
                    "cpu_percent": psutil.cpu_percent(interval=1),
                    "memory_percent": psutil.virtual_memory().percent,
                    "disk_io": psutil.disk_io_counters()._asdict()
                }
            }
        finally:
            await conn.close()
```

### 1.4 Resource Usage Tracking

```python
# scripts/resource_tracker.py
import docker
import prometheus_client
import threading
import time

class ContainerResourceTracker:
    def __init__(self):
        self.client = docker.from_env()
        self.cpu_gauge = prometheus_client.Gauge('container_cpu_percent', 'CPU usage percentage', ['container'])
        self.memory_gauge = prometheus_client.Gauge('container_memory_mb', 'Memory usage in MB', ['container'])
        self.network_rx_gauge = prometheus_client.Gauge('container_network_rx_mb', 'Network RX in MB', ['container'])
        self.network_tx_gauge = prometheus_client.Gauge('container_network_tx_mb', 'Network TX in MB', ['container'])
        
    def collect_metrics(self):
        """Collect Docker container metrics."""
        containers = self.client.containers.list()
        
        for container in containers:
            stats = container.stats(stream=False)
            
            # CPU calculation
            cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                       stats['precpu_stats']['cpu_usage']['total_usage']
            system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                          stats['precpu_stats']['system_cpu_usage']
            cpu_percent = (cpu_delta / system_delta) * 100.0
            
            # Memory calculation
            memory_mb = stats['memory_stats']['usage'] / 1024 / 1024
            
            # Network stats
            networks = stats.get('networks', {})
            total_rx = sum(net['rx_bytes'] for net in networks.values()) / 1024 / 1024
            total_tx = sum(net['tx_bytes'] for net in networks.values()) / 1024 / 1024
            
            # Update Prometheus metrics
            self.cpu_gauge.labels(container=container.name).set(cpu_percent)
            self.memory_gauge.labels(container=container.name).set(memory_mb)
            self.network_rx_gauge.labels(container=container.name).set(total_rx)
            self.network_tx_gauge.labels(container=container.name).set(total_tx)
```

## 2. Test Environment Improvements

### 2.1 Test Database Isolation Validation

```python
# scripts/test_isolation_validator.py
import asyncio
import asyncpg
import uuid
from datetime import datetime

class TestIsolationValidator:
    def __init__(self, connection_string):
        self.connection_string = connection_string
        
    async def validate_isolation(self):
        """Validate test database isolation."""
        results = {
            "timestamp": datetime.utcnow().isoformat(),
            "checks": {}
        }
        
        # Check 1: Separate database
        conn = await asyncpg.connect(self.connection_string)
        try:
            db_name = await conn.fetchval("SELECT current_database()")
            results["checks"]["separate_database"] = {
                "passed": db_name == "simile_test",
                "message": f"Database name: {db_name}"
            }
            
            # Check 2: No production data
            table_counts = {}
            for table in ['entities', 'connections', 'units']:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                table_counts[table] = count
            
            results["checks"]["empty_tables"] = {
                "passed": all(count == 0 for count in table_counts.values()),
                "message": f"Table counts: {table_counts}"
            }
            
            # Check 3: Transaction isolation
            isolation_level = await conn.fetchval("SHOW transaction_isolation")
            results["checks"]["transaction_isolation"] = {
                "passed": isolation_level == "read committed",
                "message": f"Isolation level: {isolation_level}"
            }
            
            # Check 4: Connection limit
            max_connections = await conn.fetchval("SHOW max_connections")
            results["checks"]["connection_limit"] = {
                "passed": int(max_connections) >= 100,
                "message": f"Max connections: {max_connections}"
            }
            
        finally:
            await conn.close()
            
        return results

    async def test_concurrent_isolation(self, num_workers=10):
        """Test isolation under concurrent load."""
        async def worker(worker_id):
            conn = await asyncpg.connect(self.connection_string)
            try:
                # Create unique test data
                test_id = str(uuid.uuid4())
                await conn.execute(
                    "INSERT INTO entities (id, name, unit_id) VALUES ($1, $2, $3)",
                    test_id, f"Worker_{worker_id}_Test", "length"
                )
                
                # Verify only own data is visible
                count = await conn.fetchval(
                    "SELECT COUNT(*) FROM entities WHERE id = $1",
                    test_id
                )
                
                # Clean up
                await conn.execute("DELETE FROM entities WHERE id = $1", test_id)
                
                return count == 1
                
            finally:
                await conn.close()
        
        # Run concurrent workers
        tasks = [worker(i) for i in range(num_workers)]
        results = await asyncio.gather(*tasks)
        
        return all(results)
```

### 2.2 Concurrent Test Execution Support

```python
# tests/conftest_concurrent.py
import pytest
import asyncio
from contextlib import asynccontextmanager
import uuid

class ConcurrentTestManager:
    def __init__(self):
        self.test_namespaces = {}
        self.lock = asyncio.Lock()
    
    @asynccontextmanager
    async def test_namespace(self, test_name):
        """Create isolated namespace for concurrent tests."""
        namespace_id = f"{test_name}_{uuid.uuid4().hex[:8]}"
        
        async with self.lock:
            self.test_namespaces[namespace_id] = {
                "created_at": datetime.utcnow(),
                "entities": [],
                "connections": []
            }
        
        try:
            yield namespace_id
        finally:
            # Cleanup namespace data
            await self.cleanup_namespace(namespace_id)
    
    async def cleanup_namespace(self, namespace_id):
        """Clean up all data created in a namespace."""
        namespace = self.test_namespaces.get(namespace_id)
        if not namespace:
            return
            
        # Delete in reverse order of dependencies
        for conn_id in namespace["connections"]:
            await self.delete_connection(conn_id)
            
        for entity_id in namespace["entities"]:
            await self.delete_entity(entity_id)
            
        async with self.lock:
            del self.test_namespaces[namespace_id]

# Pytest fixture for concurrent testing
@pytest.fixture
async def concurrent_test_context(request):
    """Provide isolated context for concurrent tests."""
    manager = ConcurrentTestManager()
    async with manager.test_namespace(request.node.name) as namespace_id:
        yield {
            "namespace_id": namespace_id,
            "manager": manager
        }
```

### 2.3 Test Data Generation Utilities

```python
# scripts/generate_test_data.py
import asyncio
import asyncpg
import random
import faker
from datetime import datetime
import argparse
import json

class TestDataGenerator:
    def __init__(self, connection_string):
        self.connection_string = connection_string
        self.fake = faker.Faker()
        self.units = ["length", "mass", "time", "volume", "area", "count"]
        
    async def generate_entities(self, count=100):
        """Generate test entities with realistic names."""
        entities = []
        
        # Predefined categories for realistic names
        categories = {
            "length": ["Bridge", "Tower", "Building", "Road", "River"],
            "mass": ["Vehicle", "Aircraft", "Ship", "Package", "Container"],
            "time": ["Process", "Journey", "Task", "Event", "Duration"],
            "volume": ["Tank", "Pool", "Reservoir", "Container", "Lake"],
            "area": ["Field", "Park", "City", "Country", "Forest"],
            "count": ["Population", "Items", "Units", "Batch", "Group"]
        }
        
        conn = await asyncpg.connect(self.connection_string)
        try:
            for i in range(count):
                unit = random.choice(self.units)
                category = random.choice(categories[unit])
                name = f"{category}_{self.fake.word().capitalize()}"[:20]
                
                entity_id = await conn.fetchval(
                    "INSERT INTO entities (name, unit_id) VALUES ($1, $2) RETURNING id",
                    name, unit
                )
                entities.append({
                    "id": entity_id,
                    "name": name,
                    "unit_id": unit
                })
                
        finally:
            await conn.close()
            
        return entities
    
    async def generate_connections(self, entities, connection_count=500):
        """Generate realistic connections between entities."""
        connections = []
        conn = await asyncpg.connect(self.connection_string)
        
        try:
            # Group entities by unit
            entities_by_unit = {}
            for entity in entities:
                unit = entity["unit_id"]
                if unit not in entities_by_unit:
                    entities_by_unit[unit] = []
                entities_by_unit[unit].append(entity)
            
            # Generate connections within same unit
            for _ in range(connection_count):
                unit = random.choice(list(entities_by_unit.keys()))
                if len(entities_by_unit[unit]) < 2:
                    continue
                    
                from_entity, to_entity = random.sample(entities_by_unit[unit], 2)
                
                # Generate realistic connection values
                value_ranges = {
                    "length": (0.1, 1000.0),
                    "mass": (0.1, 10000.0),
                    "time": (0.1, 3600.0),
                    "volume": (0.1, 1000000.0),
                    "area": (0.1, 1000000.0),
                    "count": (1.0, 10000.0)
                }
                
                min_val, max_val = value_ranges.get(unit, (0.1, 100.0))
                connection_value = round(random.uniform(min_val, max_val), 1)
                
                try:
                    conn_id = await conn.fetchval("""
                        INSERT INTO connections (from_entity_id, to_entity_id, connection_value)
                        VALUES ($1, $2, $3)
                        ON CONFLICT (from_entity_id, to_entity_id) DO NOTHING
                        RETURNING id
                    """, from_entity["id"], to_entity["id"], connection_value)
                    
                    if conn_id:
                        connections.append({
                            "id": conn_id,
                            "from_entity_id": from_entity["id"],
                            "to_entity_id": to_entity["id"],
                            "connection_value": connection_value
                        })
                except Exception as e:
                    print(f"Failed to create connection: {e}")
                    
        finally:
            await conn.close()
            
        return connections
    
    async def generate_complex_graph(self, num_clusters=5, entities_per_cluster=20):
        """Generate a complex graph structure for pathfinding tests."""
        all_entities = []
        
        # Generate clusters
        for cluster_id in range(num_clusters):
            cluster_entities = await self.generate_entities(entities_per_cluster)
            
            # Create dense connections within cluster
            await self.generate_connections(
                cluster_entities, 
                connection_count=entities_per_cluster * 3
            )
            
            # Add bridge connections to previous cluster
            if cluster_id > 0 and all_entities:
                bridge_count = random.randint(2, 5)
                for _ in range(bridge_count):
                    from_entity = random.choice(cluster_entities)
                    to_entity = random.choice(all_entities[-entities_per_cluster:])
                    
                    if from_entity["unit_id"] == to_entity["unit_id"]:
                        await self.create_connection(
                            from_entity["id"],
                            to_entity["id"],
                            round(random.uniform(1.0, 10.0), 1)
                        )
            
            all_entities.extend(cluster_entities)
            
        return all_entities
```

## 3. Testing Utilities and Scripts

### 3.1 Automated Test Data Generation Script

```bash
#!/bin/bash
# scripts/generate_test_data.sh

set -e

# Parse command line arguments
ENTITY_COUNT=${1:-100}
CONNECTION_COUNT=${2:-500}
COMPLEXITY=${3:-simple}  # simple, moderate, complex

echo "Generating test data..."
echo "Entities: $ENTITY_COUNT"
echo "Connections: $CONNECTION_COUNT"
echo "Complexity: $COMPLEXITY"

# Run the Python script
python scripts/test_data_generator.py \
    --entities $ENTITY_COUNT \
    --connections $CONNECTION_COUNT \
    --complexity $COMPLEXITY \
    --output test_data_$(date +%Y%m%d_%H%M%S).json

echo "Test data generation complete!"
```

### 3.2 Database State Validation

```python
# scripts/validate_db_state.py
import asyncio
import asyncpg
from datetime import datetime
import json

class DatabaseStateValidator:
    def __init__(self, connection_string):
        self.connection_string = connection_string
        
    async def capture_state(self):
        """Capture current database state."""
        conn = await asyncpg.connect(self.connection_string)
        try:
            state = {
                "timestamp": datetime.utcnow().isoformat(),
                "tables": {}
            }
            
            # Capture row counts
            for table in ['entities', 'connections', 'units']:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                state["tables"][table] = {"count": count}
                
                # Sample data
                rows = await conn.fetch(f"SELECT * FROM {table} LIMIT 5")
                state["tables"][table]["sample"] = [dict(row) for row in rows]
            
            # Capture constraints
            constraints = await conn.fetch("""
                SELECT 
                    tc.table_name,
                    tc.constraint_name,
                    tc.constraint_type
                FROM information_schema.table_constraints tc
                WHERE tc.table_schema = 'public'
            """)
            state["constraints"] = [dict(c) for c in constraints]
            
            # Capture indexes
            indexes = await conn.fetch("""
                SELECT 
                    indexname,
                    tablename,
                    indexdef
                FROM pg_indexes
                WHERE schemaname = 'public'
            """)
            state["indexes"] = [dict(i) for i in indexes]
            
            return state
            
        finally:
            await conn.close()
    
    async def compare_states(self, state1, state2):
        """Compare two database states."""
        differences = {
            "table_counts": {},
            "missing_constraints": [],
            "missing_indexes": []
        }
        
        # Compare table counts
        for table in state1["tables"]:
            count1 = state1["tables"][table]["count"]
            count2 = state2["tables"].get(table, {}).get("count", 0)
            if count1 != count2:
                differences["table_counts"][table] = {
                    "before": count1,
                    "after": count2,
                    "difference": count2 - count1
                }
        
        # Compare constraints
        constraints1 = set(c["constraint_name"] for c in state1["constraints"])
        constraints2 = set(c["constraint_name"] for c in state2["constraints"])
        differences["missing_constraints"] = list(constraints1 - constraints2)
        
        # Compare indexes
        indexes1 = set(i["indexname"] for i in state1["indexes"])
        indexes2 = set(i["indexname"] for i in state2["indexes"])
        differences["missing_indexes"] = list(indexes1 - indexes2)
        
        return differences
```

### 3.3 Performance Baseline Tracking

```python
# scripts/performance_baseline.py
import json
import statistics
from datetime import datetime
from pathlib import Path

class PerformanceBaselineTracker:
    def __init__(self, baseline_file="performance_baseline.json"):
        self.baseline_file = Path(baseline_file)
        self.baselines = self.load_baselines()
        
    def load_baselines(self):
        """Load existing baselines."""
        if self.baseline_file.exists():
            with open(self.baseline_file, 'r') as f:
                return json.load(f)
        return {}
    
    def save_baselines(self):
        """Save baselines to file."""
        with open(self.baseline_file, 'w') as f:
            json.dump(self.baselines, f, indent=2)
    
    def record_metric(self, test_name, metric_name, value):
        """Record a performance metric."""
        if test_name not in self.baselines:
            self.baselines[test_name] = {}
            
        if metric_name not in self.baselines[test_name]:
            self.baselines[test_name][metric_name] = {
                "history": [],
                "baseline": None
            }
        
        # Add to history
        self.baselines[test_name][metric_name]["history"].append({
            "timestamp": datetime.utcnow().isoformat(),
            "value": value
        })
        
        # Keep only last 100 measurements
        history = self.baselines[test_name][metric_name]["history"]
        if len(history) > 100:
            self.baselines[test_name][metric_name]["history"] = history[-100:]
        
        # Update baseline (using median of last 20 runs)
        recent_values = [h["value"] for h in history[-20:]]
        if len(recent_values) >= 5:
            self.baselines[test_name][metric_name]["baseline"] = {
                "value": statistics.median(recent_values),
                "stddev": statistics.stdev(recent_values) if len(recent_values) > 1 else 0,
                "samples": len(recent_values)
            }
        
        self.save_baselines()
    
    def check_regression(self, test_name, metric_name, current_value, threshold=1.2):
        """Check if current value indicates a performance regression."""
        if test_name not in self.baselines:
            return False, "No baseline established"
            
        if metric_name not in self.baselines[test_name]:
            return False, "No baseline for this metric"
            
        baseline = self.baselines[test_name][metric_name].get("baseline")
        if not baseline:
            return False, "Insufficient baseline data"
        
        baseline_value = baseline["value"]
        stddev = baseline["stddev"]
        
        # Check if current value exceeds threshold
        if current_value > baseline_value * threshold:
            return True, f"Performance regression detected: {current_value:.2f} > {baseline_value * threshold:.2f} (baseline: {baseline_value:.2f})"
        
        # Check if value is more than 3 standard deviations away
        if stddev > 0 and current_value > baseline_value + (3 * stddev):
            return True, f"Performance anomaly detected: {current_value:.2f} is {(current_value - baseline_value) / stddev:.1f} standard deviations from baseline"
        
        return False, f"Performance within acceptable range: {current_value:.2f} (baseline: {baseline_value:.2f})"
```

### 3.4 Comprehensive Test Runner Script

```bash
#!/bin/bash
# scripts/run_performance_tests.sh

set -e

# Configuration
RESULTS_DIR="performance_results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RESULT_FILE="$RESULTS_DIR/perf_test_$TIMESTAMP.json"

# Create results directory
mkdir -p $RESULTS_DIR

echo "Starting performance test suite..."

# 1. Validate test environment
echo "Validating test environment..."
python scripts/test_isolation_validator.py

# 2. Generate test data
echo "Generating test data..."
python scripts/test_data_generator.py \
    --entities 1000 \
    --connections 5000 \
    --complexity moderate

# 3. Capture initial state
echo "Capturing initial database state..."
python scripts/validate_db_state.py --capture before.json

# 4. Start resource monitoring
echo "Starting resource monitoring..."
python scripts/resource_tracker.py &
MONITOR_PID=$!

# 5. Run Locust tests
echo "Running load tests..."
locust -f tests/performance/locustfile.py \
    --headless \
    --users 100 \
    --spawn-rate 10 \
    --run-time 5m \
    --host http://localhost:8000 \
    --html $RESULTS_DIR/locust_report_$TIMESTAMP.html

# 6. Run database performance tests
echo "Running database performance tests..."
python tests/performance/test_db_performance.py

# 7. Capture final state
echo "Capturing final database state..."
python scripts/validate_db_state.py --capture after.json

# 8. Stop monitoring
kill $MONITOR_PID

# 9. Analyze results
echo "Analyzing results..."
python scripts/analyze_performance_results.py \
    --before before.json \
    --after after.json \
    --output $RESULT_FILE

# 10. Check for regressions
echo "Checking for performance regressions..."
python scripts/performance_baseline.py --check $RESULT_FILE

echo "Performance tests complete! Results saved to $RESULT_FILE"
```

## 4. Integration with CI/CD

### 4.1 GitHub Actions Workflow

```yaml
# .github/workflows/performance-tests.yml
name: Performance Tests

on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM
  workflow_dispatch:
  pull_request:
    types: [opened, synchronize]

jobs:
  performance-test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: simile_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -r requirements-perf.txt
          
      - name: Run performance tests
        run: |
          ./scripts/run_performance_tests.sh
          
      - name: Upload results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance_results/
          
      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const results = JSON.parse(fs.readFileSync('performance_results/latest.json'));
            
            const comment = `## Performance Test Results
            
            | Metric | Value | Baseline | Status |
            |--------|-------|----------|--------|
            | Avg Response Time | ${results.avg_response_time}ms | ${results.baseline_response_time}ms | ${results.response_time_status} |
            | Requests/sec | ${results.requests_per_sec} | ${results.baseline_rps} | ${results.rps_status} |
            | Error Rate | ${results.error_rate}% | ${results.baseline_error_rate}% | ${results.error_status} |
            
            [Full Report](${results.report_url})`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
```

### 4.2 Docker Compose for Performance Testing

```yaml
# docker-compose.perf.yml
version: '3.8'

services:
  database:
    image: postgres:15
    environment:
      POSTGRES_DB: simile_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "-c shared_buffers=256MB -c max_connections=200"
    ports:
      - "5432:5432"
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
          
  backend:
    build:
      context: ./backend
      target: production
    environment:
      DATABASE_URL: postgresql+asyncpg://postgres:postgres@database:5432/simile_test
      WORKERS: 4
    ports:
      - "8000:8000"
    depends_on:
      - database
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 1G
          
  prometheus:
    image: prom/prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
      
  grafana:
    image: grafana/grafana
    volumes:
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      
  locust:
    build:
      context: .
      dockerfile: Dockerfile.locust
    volumes:
      - ./tests/performance:/tests
    ports:
      - "8089:8089"
    command: -f /tests/locustfile.py --host http://backend:8000
```

## 5. Monitoring and Alerting

### 5.1 Prometheus Configuration

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
      
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
```

### 5.2 Grafana Dashboard

```json
{
  "dashboard": {
    "title": "SIMILE Performance Dashboard",
    "panels": [
      {
        "title": "API Response Time",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, http_request_duration_seconds_bucket)"
          }
        ]
      },
      {
        "title": "Database Query Performance",
        "targets": [
          {
            "expr": "rate(pg_stat_statements_total_time_seconds[5m])"
          }
        ]
      },
      {
        "title": "Connection Pool Usage",
        "targets": [
          {
            "expr": "sqlalchemy_connection_pool_size - sqlalchemy_connection_pool_checked_in"
          }
        ]
      }
    ]
  }
}
```

## 6. Best Practices and Guidelines

### 6.1 Performance Testing Guidelines

1. **Baseline Establishment**
   - Run performance tests on clean environment
   - Collect at least 20 runs for stable baseline
   - Document hardware specifications

2. **Test Data Management**
   - Use consistent seed data for reproducibility
   - Generate realistic data distributions
   - Clean up after each test run

3. **Monitoring During Tests**
   - Track all system resources
   - Monitor database metrics
   - Capture application logs

4. **Regression Detection**
   - Set clear performance thresholds
   - Automate regression checks
   - Alert on significant deviations

### 6.2 Troubleshooting Common Issues

1. **Database Connection Pool Exhaustion**
   ```python
   # Add connection pool monitoring
   from sqlalchemy.pool import NullPool, QueuePool
   
   engine = create_async_engine(
       DATABASE_URL,
       pool_size=20,
       max_overflow=10,
       pool_pre_ping=True,
       pool_recycle=3600
   )
   ```

2. **Memory Leaks During Tests**
   ```python
   # Use memory profiling
   import tracemalloc
   
   tracemalloc.start()
   # Run tests
   snapshot = tracemalloc.take_snapshot()
   top_stats = snapshot.statistics('lineno')
   ```

3. **Test Flakiness**
   ```python
   # Add retry logic for transient failures
   @pytest.mark.flaky(reruns=3, reruns_delay=2)
   async def test_concurrent_operations():
       pass
   ```

## Summary

This comprehensive test infrastructure design provides:

1. **Performance Testing**: Locust-based load testing with real-time monitoring
2. **Database Monitoring**: Detailed metrics collection and analysis
3. **Resource Tracking**: Container and system resource monitoring
4. **Test Isolation**: Validated concurrent test execution
5. **Data Generation**: Realistic test data with configurable complexity
6. **State Validation**: Before/after database state comparison
7. **Baseline Tracking**: Automated regression detection
8. **CI/CD Integration**: Automated performance testing in pipelines
9. **Monitoring Stack**: Prometheus + Grafana for visualization
10. **Best Practices**: Guidelines for maintainable test infrastructure

The infrastructure is designed to scale with the SIMILE project and provide comprehensive insights into system performance under various load conditions.