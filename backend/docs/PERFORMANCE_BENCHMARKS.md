# SIMILE Backend Performance Benchmarks

This document describes the comprehensive performance benchmarking test suite for the SIMILE backend API.

## Overview

The performance benchmark suite provides automated testing of API response times, database query performance, pathfinding efficiency, and resource usage patterns. It integrates with the existing test infrastructure and can be run in CI/CD pipelines.

## Test Categories

### 1. Entity Performance Tests (`TestEntityPerformance`)
- **Entity Creation Performance**: Benchmarks entity creation under load
- **Entity Listing Performance**: Tests pagination and listing with various page sizes
- **Entity Retrieval Performance**: Measures individual entity lookup times

### 2. Connection Performance Tests (`TestConnectionPerformance`)
- **Connection Creation Performance**: Tests connection establishment timing
- **Connection Listing Performance**: Evaluates connection queries with large datasets

### 3. Pathfinding Performance Tests (`TestPathfindingPerformance`)
- **Graph Size Performance**: Tests pathfinding with different graph sizes (10-100 entities)
- **Path Depth Performance**: Evaluates performance with different maximum path lengths
- **Complex Graph Traversal**: Tests performance on dense connection graphs

### 4. Database Performance Tests (`TestDatabasePerformance`)
- **Query Performance**: Direct database query benchmarks
- **Recursive CTE Performance**: Tests the core pathfinding algorithm performance
- **Join Query Performance**: Evaluates complex relationship queries

### 5. Concurrent Performance Tests (`TestConcurrentPerformance`)
- **Concurrent Entity Operations**: Tests performance under concurrent entity creation
- **Concurrent Pathfinding**: Evaluates pathfinding under concurrent load
- **Resource Contention**: Tests database and application resource handling

### 6. Resource Efficiency Tests (`TestResourceEfficiency`)
- **Memory Usage Patterns**: Monitors memory consumption during operations
- **Response Time Consistency**: Tests performance consistency over time
- **Resource Leak Detection**: Identifies potential memory or connection leaks

## Running Performance Tests

### Prerequisites

1. **Install Dependencies**:
   ```bash
   make perf-deps
   ```

2. **Ensure Services Are Running**:
   ```bash
   make check-services
   ```

3. **Set Up Test Environment**:
   ```bash
   export TEST_DATABASE_HOST=localhost
   export DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test"
   ```

### Running Tests

#### Quick Performance Check
```bash
make perf-benchmark
```

#### CI/CD Integration (JSON Output Only)
```bash
make perf-benchmark-ci
```

#### Manual Execution
```bash
# Run all performance tests
python scripts/run_performance_benchmark.py

# Run specific test patterns
python scripts/run_performance_benchmark.py --test-pattern "test_performance_benchmarks.py::TestEntityPerformance"

# Generate only JSON summary
python scripts/run_performance_benchmark.py --json-only
```

#### Using pytest directly
```bash
# Run all performance tests
pytest tests/test_performance_benchmarks.py -v -m performance

# Run specific test categories
pytest tests/test_performance_benchmarks.py::TestPathfindingPerformance -v

# Run with custom markers
pytest tests/test_performance_benchmarks.py -v -m "performance and not slow"
```

## Performance Thresholds

The benchmark suite includes the following performance expectations:

### API Response Times
- **Entity Creation**: < 50ms average, < 100ms 95th percentile
- **Entity Listing**: < 30ms + 0.5ms per entity
- **Entity Retrieval**: < 20ms average
- **Connection Creation**: < 100ms average, < 200ms 95th percentile
- **Connection Listing**: < 50ms + 1ms per connection
- **Pathfinding**: < 100ms + 2ms per entity in graph

### Database Performance
- **Simple Queries**: < 10ms per 100 operations
- **Complex Joins**: < 20ms per 50 operations
- **Recursive CTEs**: < 100ms average, < 200ms 95th percentile

### Resource Usage
- **Memory Growth**: < 100MB maximum, < 50MB average
- **Response Consistency**: Coefficient of variation < 1.0
- **Concurrent Success Rate**: > 95%

## Test Data Generation

The benchmark suite includes utilities for generating test data:

```python
# Create test data for performance testing
test_data = await self.create_performance_test_data(
    test_client, 
    entity_count=50,          # Number of entities to create
    connection_density=0.3    # Percentage of possible connections to create
)
```

### Test Data Patterns

- **Linear Chains**: For testing path depth performance
- **Dense Graphs**: For testing complex pathfinding scenarios
- **Sparse Graphs**: For testing scalability with minimal connections
- **Random Graphs**: For testing realistic usage patterns

## Performance Monitoring

The benchmark suite includes comprehensive performance monitoring:

### Metrics Collected
- **Response Times**: Average, median, 95th percentile
- **Memory Usage**: RSS memory consumption during operations
- **CPU Usage**: Process CPU utilization
- **Success Rates**: Percentage of successful operations
- **Database Query Counts**: Number of queries executed (when available)

### Performance Metrics Class
```python
@dataclass
class PerformanceMetrics:
    response_times: List[float]
    memory_usage: List[float]
    cpu_usage: List[float]
    database_queries: int
    success_rate: float
```

## Report Generation

The benchmark runner generates comprehensive reports:

### Markdown Report
- Executive summary with pass/fail status
- Detailed performance metrics
- Test execution details
- Performance recommendations
- Environment information

### JSON Summary
- Machine-readable performance data
- Integration-friendly format for CI/CD
- Key metrics extraction
- Threshold compliance status

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run Performance Benchmarks
  run: |
    make perf-benchmark-ci
    
- name: Upload Performance Report
  uses: actions/upload-artifact@v3
  with:
    name: performance-report
    path: performance_reports/
```

### Performance Regression Detection
The benchmark suite can be used to detect performance regressions by comparing results across builds:

```bash
# Generate baseline
make perf-benchmark-ci > baseline_performance.json

# Compare with current performance
python scripts/compare_performance.py baseline_performance.json current_performance.json
```

## Troubleshooting

### Common Issues

1. **Test Database Connection Failures**:
   - Ensure PostgreSQL is running on localhost:5432
   - Check TEST_DATABASE_HOST environment variable
   - Verify database credentials

2. **Memory Usage Warnings**:
   - May indicate memory leaks or inefficient queries
   - Check for unclosed database connections
   - Review query optimization

3. **High Response Times**:
   - May indicate database performance issues
   - Check for missing database indexes
   - Review query execution plans

4. **Concurrent Test Failures**:
   - May indicate race conditions or resource contention
   - Check database connection pool configuration
   - Review transaction isolation levels

### Performance Optimization Tips

1. **Database Optimization**:
   - Add indexes on frequently queried columns
   - Optimize recursive CTE queries
   - Use connection pooling

2. **Application Optimization**:
   - Implement caching for frequently accessed data
   - Use async/await properly
   - Optimize JSON serialization

3. **Resource Management**:
   - Properly close database connections
   - Use context managers for resource cleanup
   - Monitor memory usage patterns

## Extending the Benchmark Suite

### Adding New Performance Tests

1. **Create Test Class**:
   ```python
   class TestNewFeaturePerformance(PerformanceTestMixin):
       @pytest.mark.asyncio
       async def test_new_feature_performance(self, test_client):
           # Test implementation
   ```

2. **Use Performance Utilities**:
   ```python
   metrics = await self.measure_endpoint_performance(
       test_client, "GET", "/api/v1/new-endpoint", iterations=100
   )
   ```

3. **Add Performance Assertions**:
   ```python
   assert metrics.avg_response_time < 50.0
   assert metrics.success_rate > 0.95
   ```

### Custom Performance Metrics

```python
@contextmanager
def custom_performance_monitor():
    # Custom monitoring logic
    start_time = time.time()
    yield
    execution_time = time.time() - start_time
    logger.info(f"Custom operation took {execution_time:.2f}s")
```

## Best Practices

1. **Test Data Isolation**: Always use unique test data to avoid conflicts
2. **Resource Cleanup**: Ensure proper cleanup of test resources
3. **Realistic Scenarios**: Test with realistic data sizes and patterns
4. **Threshold Setting**: Set appropriate performance thresholds based on requirements
5. **Continuous Monitoring**: Run performance tests regularly in CI/CD
6. **Performance Regression**: Track performance metrics over time
7. **Resource Monitoring**: Monitor memory and CPU usage during tests

## Related Documentation

- [Backend Test Infrastructure](BACKEND-TEST-FIX-DOCUMENTATION.md)
- [DevOps Test Infrastructure](DEVOPS-TEST-INFRASTRUCTURE-DESIGN.md)
- [Performance Test Results](../performance_reports/)
- [Locust Performance Tests](../tests/performance/locustfile.py)