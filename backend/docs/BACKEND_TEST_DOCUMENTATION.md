# Backend Test Documentation

## Overview

This document consolidates all backend testing documentation for the SIMILE project. It provides a comprehensive guide to the test infrastructure, test data management, and specific test implementations.

## Test Infrastructure

### Test Environment Setup

The backend uses a comprehensive test infrastructure with:

- **Test Database**: `simile_test` database with complete isolation
- **Test Configuration**: `TEST_DATABASE_HOST=localhost` environment variable
- **Database Pool**: NullPool for tests to ensure connection isolation
- **Async Testing**: pytest-asyncio with strict mode
- **Coverage**: pytest-cov with detailed reporting

### Running Tests

```bash
# All tests
cd backend
source venv/bin/activate
TEST_DATABASE_HOST=localhost python -m pytest

# Specific test categories
TEST_DATABASE_HOST=localhost python -m pytest tests/test_entities*.py
TEST_DATABASE_HOST=localhost python -m pytest tests/test_connections*.py
TEST_DATABASE_HOST=localhost python -m pytest tests/test_performance*.py

# With coverage
TEST_DATABASE_HOST=localhost python -m pytest --cov=src --cov-report=term-missing
```

## Test Categories

### 1. Entity Tests
- **Basic CRUD Operations**: Create, read, update, delete entities
- **Validation Tests**: Name pattern validation (letters and spaces only)
- **Constraint Tests**: Unique name constraints, length limits
- **Error Handling**: Database errors, validation errors

### 2. Connection Tests
- **Connection Creation**: Forward and inverse connection creation
- **Validation**: Self-reference prevention, positive multipliers
- **Multiplier Precision**: Decimal rounding to 1 decimal place
- **Inverse Relationships**: Automatic inverse creation (1/x)
- **Constraint Handling**: Foreign key constraints, check constraints

### 3. Pathfinding Tests
- **Shortest Path**: Graph traversal algorithms
- **Maximum Hops**: Path length limiting
- **Unit Consistency**: Same-unit path traversal
- **Edge Cases**: Disconnected graphs, cycles, deep paths

### 4. Performance Tests
- **Concurrent Operations**: Multiple simultaneous requests
- **Response Time**: API endpoint performance
- **Database Performance**: Query optimization
- **Memory Usage**: Resource consumption monitoring

### 5. Integration Tests
- **API Contract**: Request/response validation
- **Error Scenarios**: Error handling and recovery
- **Database Transactions**: ACID compliance
- **Cleanup**: Proper resource cleanup

## Test Data Management

### Entity Names
- **Pattern**: `^[a-zA-Z\s]+$` (letters and spaces only)
- **Generation**: `create_test_entity_name()` function
- **Uniqueness**: Automatic suffix generation
- **Cleanup**: Automatic test data cleanup

### Test Fixtures
- **Complex Graphs**: Star, chain, grid, complete graph topologies
- **Performance Data**: Large-scale test data generation
- **Validation Data**: Valid/invalid data sets
- **Edge Cases**: Boundary conditions and error scenarios

### Database Isolation
- **Test Database**: Separate `simile_test` database
- **Transaction Isolation**: Each test runs in isolated transaction
- **Cleanup**: Automatic cleanup between tests
- **Fixtures**: Shared test data with proper cleanup

## Common Test Patterns

### Validation Error Testing
```python
# Test expects 422 status code for validation errors
response = await test_client.post("/api/v1/entities/", json={"name": "Test123"})
assert response.status_code == 422
```

### Connection Testing
```python
# Test connection with automatic inverse creation
response = await test_client.post("/api/v1/connections/", json={
    "from_entity_id": entity1_id,
    "to_entity_id": entity2_id,
    "unit_id": unit_id,
    "multiplier": 2.0
})
assert response.status_code == 201
# Verify inverse connection exists
```

### Performance Testing
```python
# Concurrent operation testing
tasks = [create_entity_task(i) for i in range(25)]
results = await asyncio.gather(*tasks)
# Verify performance metrics
```

## Test Configuration

### pytest.ini Settings
```ini
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "strict"
```

### Environment Variables
- `TEST_DATABASE_HOST=localhost`: Database host for tests
- `PYTHONPATH=.`: Python path for imports
- `pytest` in `os.environ.get("_", "")`: Test detection

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure PostgreSQL is running and accessible
2. **Entity Names**: Use letters-only names in tests
3. **Status Codes**: Expect 422 for validation errors, not 400
4. **Async Issues**: Use pytest-asyncio properly
5. **Decimal Precision**: Account for rounding in multipliers

### Test Isolation
- Each test runs in clean database state
- Proper cleanup between tests
- No shared state between tests
- Isolated transaction handling

## Performance Considerations

### Test Environment
- **NullPool**: No connection pooling in tests
- **Realistic Thresholds**: Adjusted for test environment
- **Concurrent Limits**: Reduced concurrent operations
- **Memory Monitoring**: Resource usage tracking

### Optimization
- **Minimal Logging**: Reduced log levels in tests
- **Efficient Queries**: Optimized database queries
- **Batch Operations**: Grouped database operations
- **Connection Management**: Proper session handling

## Documentation References

- **Test Data Management**: `tests/TEST_DATA_MANAGEMENT_GUIDE.md`
- **Connection Tests**: `tests/CONNECTIONS_TEST_IMPLEMENTATION_SUMMARY.md`
- **Performance Benchmarks**: `docs/PERFORMANCE_BENCHMARKS.md`
- **Infrastructure Design**: `docs/DEVOPS-TEST-INFRASTRUCTURE-DESIGN.md`

## Maintenance

### Regular Tasks
- **Test Data Cleanup**: Remove temporary test files
- **Coverage Reports**: Monitor test coverage
- **Performance Baselines**: Update performance expectations
- **Documentation Updates**: Keep documentation current

### Best Practices
- **Test First**: Write tests before implementation
- **Meaningful Names**: Use descriptive test names
- **Isolated Tests**: Each test is independent
- **Proper Cleanup**: Clean up resources after tests
- **Error Testing**: Test error conditions thoroughly

For specific implementation details, refer to the individual test files and the original documentation files in the archive.