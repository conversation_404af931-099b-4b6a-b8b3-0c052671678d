import asyncio
import httpx
from src.app_factory import create_app

async def reproduce_test_failure():
    app = create_app()
    async with httpx.AsyncClient(app=app, base_url='http://test') as client:
        # Create entities with valid names (letters and spaces only)
        entity1 = await client.post('/api/v1/entities/', json={'name': 'Fresh Test Entity A'})
        entity2 = await client.post('/api/v1/entities/', json={'name': 'Fresh Test Entity B'})
        
        if entity1.status_code != 201 or entity2.status_code != 201:
            print('Entity creation failed')
            return
            
        entity1_id = entity1.json()['id']
        entity2_id = entity2.json()['id']
        
        units = await client.get('/api/v1/units/')
        unit_id = units.json()[0]['id']
        
        # Create connection 
        await client.post('/api/v1/connections/', json={
            'from_entity_id': entity1_id,
            'to_entity_id': entity2_id,
            'unit_id': unit_id,
            'multiplier': 3.0
        })
        
        # Try to find inverse with default limit (reproduces test failure)
        connections = await client.get('/api/v1/connections/')
        print(f'Default connections returned: {len(connections.json())}')
        
        try:
            inverse = next(
                c for c in connections.json()
                if c['from_entity_id'] == entity2_id and c['to_entity_id'] == entity1_id
            )
            print(f'Found inverse: {inverse["multiplier"]}')
        except StopIteration:
            print('StopIteration: No inverse connection found in default results')
            
        # Try with higher limit
        connections_all = await client.get('/api/v1/connections/', params={'limit': 1000})
        print(f'All connections returned: {len(connections_all.json())}')
        
        try:
            inverse = next(
                c for c in connections_all.json()
                if c['from_entity_id'] == entity2_id and c['to_entity_id'] == entity1_id
            )
            print(f'Found inverse with higher limit: {inverse["multiplier"]}')
        except StopIteration:
            print('Still no inverse found even with higher limit')

if __name__ == "__main__":
    asyncio.run(reproduce_test_failure())