#!/usr/bin/env python3
"""
Fix the classclass issue created by the automated fix.
"""

import re

def fix_classclass_issue(file_path):
    """Fix the classclass issue."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix the classclass issue
    content = re.sub(r'classclass ', 'class ', content)
    
    # Fix remaining E122 issues by ensuring proper indentation
    content = re.sub(
        r'(\s+)create_response = await test_client\.post\(\n(\s+)("/api/v1/entities/", json=entity_data\))',
        r'\1create_response = await test_client.post(\n\1    \3',
        content
    )
    
    content = re.sub(
        r'(\s+)response = await test_client\.get\(\n(\s+)(f"/api/v1/)',
        r'\1response = await test_client.get(\n\1    \3',
        content
    )
    
    content = re.sub(
        r'(\s+)conn_response = await test_client\.post\(\n(\s+)("/api/v1/connections/", json=conn_data\))',
        r'\1conn_response = await test_client.post(\n\1    \3',
        content
    )
    
    content = re.sub(
        r'(\s+)conn_response = await test_client\.post\(\n(\s+)("/api/v1/connections/", json=connection_data\))',
        r'\1conn_response = await test_client.post(\n\1    \3',
        content
    )
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed classclass issue in {file_path}")

if __name__ == "__main__":
    files_to_fix = [
        "tests/test_compare_comprehensive.py",
        "tests/test_compare_error_scenarios.py", 
        "tests/test_comprehensive_connections.py",
        "tests/test_comprehensive_entities.py",
        "tests/test_comprehensive_pathfinding.py"
    ]
    
    for file_path in files_to_fix:
        try:
            fix_classclass_issue(file_path)
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")