# Database Transaction Test Specifications for SIMILE Backend

## Overview
This document specifies comprehensive test cases for database transaction integrity, rollback scenarios, and concurrent operations in the SIMILE FastAPI/PostgreSQL application.

## Test Categories

### 1. Transaction Integrity Tests
Test scenarios that verify transactions are properly committed or rolled back as expected.

#### 1.1 Entity Creation Transaction Tests
- **Test ID**: TXN-001
- **Scenario**: Single entity creation with successful commit
- **Expected**: Entity is created and immediately queryable
- **Validation**: Entity exists in database after transaction

- **Test ID**: TXN-002
- **Scenario**: Entity creation with duplicate name constraint violation
- **Expected**: IntegrityError raised, transaction rolled back
- **Validation**: No partial data remains in database

- **Test ID**: TXN-003
- **Scenario**: Entity creation with invalid data causing rollback
- **Expected**: Transaction rolled back completely
- **Validation**: Database state unchanged

#### 1.2 Connection Creation Transaction Tests
- **Test ID**: TXN-004
- **<PERSON><PERSON><PERSON>**: Connection creation with automatic inverse creation
- **Expected**: Both forward and inverse connections created atomically
- **Validation**: Both connections exist or neither exists

- **Test ID**: TXN-005
- **Scenario**: Connection creation with constraint violation (self-reference)
- **Expected**: Transaction rolled back, no connections created
- **Validation**: No partial connections in database

- **Test ID**: TXN-006
- **Scenario**: Connection creation with invalid entity reference
- **Expected**: Foreign key constraint violation, rollback
- **Validation**: No orphaned connections created

#### 1.3 Complex Multi-Operation Transaction Tests
- **Test ID**: TXN-007
- **Scenario**: Multiple entity creation in single transaction
- **Expected**: All entities created atomically
- **Validation**: All entities exist or none exist

- **Test ID**: TXN-008
- **Scenario**: Entity creation followed by connection creation
- **Expected**: Both operations succeed atomically
- **Validation**: Entity and connections exist together

- **Test ID**: TXN-009
- **Scenario**: Batch connection creation with one failure
- **Expected**: All connections rolled back on any failure
- **Validation**: Database state remains consistent

### 2. Rollback Scenarios
Test scenarios that verify proper rollback behavior during failures.

#### 2.1 Database Constraint Violations
- **Test ID**: RBK-001
- **Scenario**: Entity name uniqueness constraint violation
- **Expected**: Transaction rolled back, original data preserved
- **Validation**: Database state unchanged from before transaction

- **Test ID**: RBK-002
- **Scenario**: Connection multiplier check constraint violation (negative value)
- **Expected**: Transaction rolled back, no connections created
- **Validation**: No partial connection data remains

- **Test ID**: RBK-003
- **Scenario**: Connection unique constraint violation
- **Expected**: Existing connection updated, not duplicated
- **Validation**: Only one connection exists between entities

#### 2.2 Application-Level Failures
- **Test ID**: RBK-004
- **Scenario**: Exception raised during connection inverse creation
- **Expected**: Both forward and inverse creation rolled back
- **Validation**: No partial connection pairs exist

- **Test ID**: RBK-005
- **Scenario**: Database connection lost during transaction
- **Expected**: Transaction automatically rolled back
- **Validation**: Database state consistent after reconnection

- **Test ID**: RBK-006
- **Scenario**: Manual rollback after successful operations
- **Expected**: All operations in transaction reversed
- **Validation**: Database returns to pre-transaction state

### 3. Isolation Levels and Concurrent Transaction Handling
Test scenarios that verify proper isolation between concurrent transactions.

#### 3.1 Read Phenomena Prevention
- **Test ID**: ISO-001
- **Scenario**: Dirty read prevention during concurrent entity creation
- **Expected**: Uncommitted data not visible to other transactions
- **Validation**: Other transactions don't see uncommitted entities

- **Test ID**: ISO-002
- **Scenario**: Non-repeatable read prevention during entity updates
- **Expected**: Consistent read within transaction
- **Validation**: Same query returns same results within transaction

- **Test ID**: ISO-003
- **Scenario**: Phantom read prevention during connection queries
- **Expected**: Consistent result set within transaction
- **Validation**: Connection count remains stable within transaction

#### 3.2 Concurrent Modification Scenarios
- **Test ID**: ISO-004
- **Scenario**: Concurrent entity creation with same name
- **Expected**: One succeeds, one fails with constraint violation
- **Validation**: Only one entity exists with the name

- **Test ID**: ISO-005
- **Scenario**: Concurrent connection creation between same entities
- **Expected**: One creates, one updates existing connection
- **Validation**: Single connection exists with latest multiplier

- **Test ID**: ISO-006
- **Scenario**: Concurrent entity deletion and connection creation
- **Expected**: Either entity exists with connection or neither exists
- **Validation**: No orphaned connections created

### 4. Database Constraint Violation Recovery
Test scenarios that verify proper handling of constraint violations.

#### 4.1 Primary Key Constraints
- **Test ID**: CNS-001
- **Scenario**: Manual primary key collision (simulated)
- **Expected**: Constraint violation detected and handled
- **Validation**: Database integrity maintained

#### 4.2 Foreign Key Constraints
- **Test ID**: CNS-002
- **Scenario**: Connection creation with non-existent entity
- **Expected**: Foreign key constraint violation
- **Validation**: No orphaned connections created

- **Test ID**: CNS-003
- **Scenario**: Entity deletion with existing connections
- **Expected**: CASCADE delete removes connections
- **Validation**: All related connections removed

#### 4.3 Check Constraints
- **Test ID**: CNS-004
- **Scenario**: Connection with zero multiplier
- **Expected**: Check constraint violation
- **Validation**: No invalid connections created

- **Test ID**: CNS-005
- **Scenario**: Connection from entity to itself
- **Expected**: Self-reference constraint violation
- **Validation**: No self-referencing connections created

### 5. Connection Cleanup After Failures
Test scenarios that verify proper cleanup after transaction failures.

#### 5.1 Session Cleanup
- **Test ID**: CLN-001
- **Scenario**: Database session cleanup after transaction failure
- **Expected**: Session properly closed and resources released
- **Validation**: No connection leaks or hanging sessions

- **Test ID**: CLN-002
- **Scenario**: Multiple transaction failures in succession
- **Expected**: Each failure properly cleaned up
- **Validation**: Database connection pool remains healthy

#### 5.2 Connection Pool Management
- **Test ID**: CLN-003
- **Scenario**: Transaction failure with connection pool exhaustion
- **Expected**: Failed transactions release connections back to pool
- **Validation**: Connection pool recovers and remains available

- **Test ID**: CLN-004
- **Scenario**: Long-running transaction failure
- **Expected**: Connection returned to pool after timeout
- **Validation**: Pool maintains expected size

### 6. Error Recovery Scenarios
Test scenarios that verify system recovery from various error conditions.

#### 6.1 Database Recovery
- **Test ID**: ERR-001
- **Scenario**: Database server restart during transaction
- **Expected**: Application detects failure and handles gracefully
- **Validation**: Application continues functioning after restart

- **Test ID**: ERR-002
- **Scenario**: Network interruption during transaction
- **Expected**: Transaction fails with appropriate error handling
- **Validation**: System state remains consistent

#### 6.2 Application Recovery
- **Test ID**: ERR-003
- **Scenario**: Application restart with incomplete transactions
- **Expected**: Database maintains consistent state
- **Validation**: No partial transactions remain after restart

## Test Implementation Strategy

### Test Database Setup
- Use isolated test database for each test
- Implement proper cleanup between tests
- Use transaction rollback where appropriate

### Concurrent Testing Approach
- Use asyncio for concurrent operation simulation
- Implement proper synchronization primitives
- Use database-level locking where necessary

### Error Simulation
- Use mock failures for network/database issues
- Implement controlled constraint violations
- Use transaction contexts for rollback testing

### Validation Methods
- Direct database queries for state verification
- Transaction log analysis where applicable
- Connection pool monitoring
- Resource leak detection

## Test Data Requirements

### Entities
- Valid entity names following pattern ^[a-zA-Z\\s]+$
- Unique entity names for each test
- Edge cases: maximum length, minimum length, special characters

### Connections
- Valid multipliers (positive decimal values)
- Various unit types (Length, Mass, Time, Count, Volume)
- Complex connection graphs for pathfinding tests

### Error Conditions
- Invalid entity IDs
- Constraint violation data
- Malformed input data
- Resource exhaustion scenarios

## Success Criteria

### Transaction Integrity
- All operations are atomic (all succeed or all fail)
- No partial state exists after rollback
- Database constraints are properly enforced

### Concurrency Handling
- Proper isolation between transactions
- No race conditions in critical operations
- Consistent behavior under concurrent load

### Error Recovery
- Graceful handling of all error conditions
- Proper cleanup after failures
- System remains stable after errors

### Performance
- Transaction overhead is minimal
- Connection pool efficiency maintained
- No resource leaks under normal or error conditions