# Multi-stage build for development and production
FROM python:3.11-slim AS base

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Development stage - optimized for hot reload
FROM base AS development

# Install additional development dependencies if needed
# RUN pip install --no-cache-dir debugpy

# Copy application code (will be overridden by volume mounts in dev)
COPY src/ ./src/
COPY tests/ ./tests/

# Development command with hot reload
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Production stage - optimized for deployment
FROM base AS production

# Copy application code
COPY src/ ./src/
COPY tests/ ./tests/

# Production command without reload
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]