#!/usr/bin/env python3
"""
Final comprehensive fix for all remaining flake8 issues.
"""

import re

def final_fix_all_issues(file_path):
    """Fix all remaining flake8 issues."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix E303 - too many blank lines (reduce to 2)
    content = re.sub(r'\n\n\n\n+', '\n\n\n', content)
    content = re.sub(r'\n\n\n(?=class)', '\n\n\nclass', content)
    
    # Fix E122 - continuation line missing indentation
    # Pattern: function call with missing indentation
    content = re.sub(
        r'(\s+)(create_response = await test_client\.post\(\n)(\s+)("/api/v1/entities/", json=entity_data\))',
        r'\1\2\1    \4',
        content
    )
    
    # Fix E122 for other similar patterns
    content = re.sub(
        r'(\s+)(response = await test_client\.get\(\n)(\s+)(f"/api/v1/)',
        r'\1\2\1    \4',
        content
    )
    
    # Fix E501 - line too long issues
    # Pattern 1: Long create_test_entity_name calls
    content = re.sub(
        r'create_test_entity_name\(\s*\n\s*f"([^"]+)"\)',
        r'create_test_entity_name(\n            f"\1")',
        content
    )
    
    # Pattern 2: Long entity creation
    content = re.sub(
        r'(\s+)entity_name = create_test_entity_name\(\n\s+f"([^"]+)"\)',
        r'\1entity_name = create_test_entity_name(\n\1    f"\2")',
        content
    )
    
    # Pattern 3: Long connection creation from entity arrays - skip complex f-strings
    
    # Pattern 4: Long dictionary definitions - skip complex f-strings
    
    # Fix remaining E122 errors by ensuring proper indentation
    content = re.sub(
        r'(\s+create_response = await test_client\.post\(\n)(\s+)("/api/v1/entities/", json=entity_data\))',
        r'\1            \3',
        content
    )
    
    # Fix remaining E122 patterns
    content = re.sub(
        r'(\s+response = await test_client\.get\(\n)(\s+)(f"/api/v1/)',
        r'\1            \3',
        content
    )
    
    # Fix specific long lines that are still causing issues
    content = re.sub(
        r'entity_name = create_test_entity_name\(\n\s+f"MaxPath\{chr\(65\+i\)\}"\)',
        r'entity_name = create_test_entity_name(\n            f"MaxPath{chr(65+i)}")',
        content
    )
    
    content = re.sub(
        r'entity_name = create_test_entity_name\(\n\s+f"ExceedPath\{chr\(65\+i\)\}"\)',
        r'entity_name = create_test_entity_name(\n            f"ExceedPath{chr(65+i)}")',
        content
    )
    
    # Fix response.get calls that are missing proper indentation
    content = re.sub(
        r'(\s+)response = await test_client\.get\(\n\s+\n\s+(f"/api/v1/[^"]+"\n\s+f"[^"]+"\))',
        r'\1response = await test_client.get(\n\1    \2',
        content
    )
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Applied final comprehensive fix to {file_path}")

if __name__ == "__main__":
    files_to_fix = [
        "tests/test_compare_comprehensive.py",
        "tests/test_compare_error_scenarios.py", 
        "tests/test_comprehensive_connections.py",
        "tests/test_comprehensive_entities.py",
        "tests/test_comprehensive_pathfinding.py"
    ]
    
    for file_path in files_to_fix:
        try:
            final_fix_all_issues(file_path)
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")