#!/usr/bin/env python3
"""Script to fix long lines in test files."""

import re
from pathlib import Path

def fix_file(file_path):
    """Fix long lines in a file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Common patterns to fix
    patterns = [
        # Fix function signatures
        (r'async def (test_[a-zA-Z_]+)\(self, test_client: AsyncClient\):',
         r'async def \1(\n            self, test_client: AsyncClient):'),
        
        # Fix HTTP client calls
        (r'response = await test_client\.get\(f"([^"]+)"\)',
         r'response = await test_client.get(\n            f"\1")'),
        
        # Fix HTTP client calls with regular strings
        (r'response = await test_client\.get\("([^"]+)"\)',
         r'response = await test_client.get(\n            "\1")'),
        
        # Fix POST calls
        (r'response = await test_client\.post\("([^"]+)", json=([^)]+)\)',
         r'response = await test_client.post(\n            "\1", json=\2)'),
        
        # Fix create_response calls
        (r'create_response = await test_client\.post\("([^"]+)", json=([^)]+)\)',
         r'create_response = await test_client.post(\n            "\1", json=\2)'),
        
        # Fix assert statements with long strings
        (r'assert "([^"]{30,})" in ([^"]+)',
         r'assert "\1" in \\\n            \2'),
        
        # Fix long assignment lines
        (r'([a-zA-Z_][a-zA-Z0-9_]*) = (create_test_entity_name\("[^"]+"\))',
         r'\1 = \\\n            \2'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    # Additional manual fixes for specific patterns
    content = content.replace(
        'async def test_compare_same_entity_returns_multiplier_one(self, test_client: AsyncClient):',
        'async def test_compare_same_entity_returns_multiplier_one(\n            self, test_client: AsyncClient):'
    )
    
    content = content.replace(
        'async def test_compare_same_entity_all_units(self, test_client: AsyncClient):',
        'async def test_compare_same_entity_all_units(\n            self, test_client: AsyncClient):'
    )
    
    content = content.replace(
        'create_response = await test_client.post("/api/v1/entities/", json=entity_data)',
        'create_response = await test_client.post(\n            "/api/v1/entities/", json=entity_data)'
    )
    
    # Fix specific long lines
    content = content.replace(
        'response = await test_client.get(f"/api/v1/compare/?from={entity_id}&to={entity_id}&unit={unit_id}")',
        'response = await test_client.get(\n            f"/api/v1/compare/?from={entity_id}&to={entity_id}&unit={unit_id}")'
    )
    
    content = content.replace(
        'response = await test_client.get(f"/api/v1/compare/?from={entity_id}&to={entity_id}&unit={unit_id}")',
        'response = await test_client.get(\n            f"/api/v1/compare/?from={entity_id}&to={entity_id}&unit={unit_id}")'
    )
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed long lines in {file_path}")

if __name__ == "__main__":
    test_files = [
        "tests/test_compare_comprehensive.py",
        "tests/test_compare_error_scenarios.py", 
        "tests/test_comprehensive_connections.py",
        "tests/test_comprehensive_entities.py",
        "tests/test_comprehensive_pathfinding.py"
    ]
    
    for file_path in test_files:
        if Path(file_path).exists():
            fix_file(file_path)
        else:
            print(f"File not found: {file_path}")