[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_functions = ["test_*"]
addopts = "-ra -q --strict-markers --cov=src --cov-report=term-missing"
asyncio_mode = "strict"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "performance: marks tests as performance benchmarks",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "database: marks tests that require database",
    "concurrent: marks tests that test concurrent operations"
]