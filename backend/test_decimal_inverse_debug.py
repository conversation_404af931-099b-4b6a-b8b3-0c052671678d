import asyncio
import httpx
from src.app_factory import create_app
from decimal import Decimal
import time
import logging

# Enable logging to see debug messages
logging.basicConfig(level=logging.INFO)

async def test_decimal_inverse_connection():
    app = create_app()
    async with httpx.AsyncClient(app=app, base_url='http://test') as client:
        # Create entities with valid names (letters and spaces only)
        entity1 = await client.post('/api/v1/entities/', json={'name': 'Decimal Inv Ent Gamma'})
        entity2 = await client.post('/api/v1/entities/', json={'name': 'Decimal Inv Ent Delta'})
        
        if entity1.status_code != 201:
            print(f'Entity 1 failed: {entity1.status_code} - {entity1.text}')
            return
        if entity2.status_code != 201:
            print(f'Entity 2 failed: {entity2.status_code} - {entity2.text}')
            return
            
        entity1_id = entity1.json()["id"]
        entity2_id = entity2.json()["id"]
        print(f'Entity 1 ID: {entity1_id}')
        print(f'Entity 2 ID: {entity2_id}')
        
        # Get units
        units = await client.get('/api/v1/units/')
        unit_id = units.json()[0]['id']
        print(f'Using unit ID: {unit_id}')
        
        # Create connection with multiplier 3.0 -> inverse should be 0.3
        print('Creating connection...')
        connection_response = await client.post('/api/v1/connections/', json={
            'from_entity_id': entity1_id,
            'to_entity_id': entity2_id,
            'unit_id': unit_id,
            'multiplier': 3.0
        })
        
        if connection_response.status_code != 201:
            print(f'Connection creation failed: {connection_response.status_code} - {connection_response.text}')
            return
            
        print(f'Connection created successfully: {connection_response.json()}')
        
        # Check all connections for these specific entities
        all_connections = await client.get('/api/v1/connections/')
        print(f'Connections for our entities:')
        for conn in all_connections.json():
            if conn['from_entity_id'] in [entity1_id, entity2_id] and conn['to_entity_id'] in [entity1_id, entity2_id]:
                print(f'  {conn["id"]}: {conn["from_entity_id"]} -> {conn["to_entity_id"]} (multiplier: {conn["multiplier"]})')
        
        # Look for the inverse connection
        inverse_connections = [
            c for c in all_connections.json()
            if c['from_entity_id'] == entity2_id and c['to_entity_id'] == entity1_id
        ]
        
        if inverse_connections:
            inverse = inverse_connections[0]
            print(f'Found inverse connection: {inverse["multiplier"]} (expected: 0.3)')
            print(f'Inverse multiplier type: {type(inverse["multiplier"])}')
            print(f'Inverse multiplier == 0.3: {float(inverse["multiplier"]) == 0.3}')
        else:
            print('ERROR: No inverse connection found!')
            
        # Check for the specific entities and connections in the database
        print(f'Total connections found: {len(all_connections.json())}')

if __name__ == "__main__":
    asyncio.run(test_decimal_inverse_connection())