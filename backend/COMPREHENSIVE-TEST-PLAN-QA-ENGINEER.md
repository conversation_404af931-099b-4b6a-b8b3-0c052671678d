# Comprehensive QA Test Plan for SIMILE Backend

## Executive Summary

This test plan addresses critical coverage gaps in the SIMILE backend, focusing on achieving 80% test coverage with emphasis on error paths, edge cases, and integration scenarios. Current coverage stands at 63% with significant gaps in critical modules.

## Coverage Gap Analysis

### Critical Priority: connections.py (21% coverage)
**Missing Coverage Lines**: 35-50, 52-188, 210-218, 230-233, 247-300, 312-334

### High Priority: compare.py (40% coverage)  
**Missing Coverage Lines**: 29-74

### Medium Priority: entities.py (47% coverage)
**Missing Coverage Lines**: Various error paths and edge cases

### Low Priority: units.py (68% coverage)
**Missing Coverage Lines**: Error handling and edge cases

## Detailed Test Case Specifications

### 1. CONNECTIONS MODULE - Critical Priority Test Cases

#### 1.1 Error Path Testing - Database Failures

**Test Case ID**: TC-CONN-ERR-001  
**Priority**: CRITICAL  
**Description**: Test connection creation when database is unavailable  
**Preconditions**: Mock database to simulate connection failure  
**Test Steps**:
1. Attempt to create a connection
2. Simulate database connection timeout
3. Verify proper error handling

**Expected Result**: 
- HTTPException with status_code=500
- Error message: "Database connection failed"
- No partial data committed
- Proper cleanup of any resources

**Implementation**:
```python
@pytest.mark.asyncio
async def test_create_connection_database_failure(test_client, mocker):
    """Test connection creation with database failure."""
    # Mock database execute to raise exception
    mocker.patch('sqlalchemy.ext.asyncio.AsyncSession.execute', 
                 side_effect=Exception("Database connection failed"))
    
    connection_data = {
        "from_entity_id": 1,
        "to_entity_id": 2,
        "unit_id": 1,
        "multiplier": 2.5
    }
    
    response = await test_client.post("/api/v1/connections/", json=connection_data)
    assert response.status_code == 500
    assert "Database connection failed" in response.json()["detail"]
```

#### 1.2 Constraint Violation Testing

**Test Case ID**: TC-CONN-ERR-002  
**Priority**: CRITICAL  
**Description**: Test all database constraint violations  
**Test Scenarios**:

1. **Self-Reference Constraint**
```python
async def test_create_connection_self_reference(test_client):
    """Test creating connection from entity to itself."""
    entity_id = await create_test_entity()
    connection_data = {
        "from_entity_id": entity_id,
        "to_entity_id": entity_id,  # Same as from_entity_id
        "unit_id": 1,
        "multiplier": 1.0
    }
    response = await test_client.post("/api/v1/connections/", json=connection_data)
    assert response.status_code == 400
    assert "Cannot create connection from entity to itself" in response.json()["detail"]
```

2. **Negative Multiplier Constraint**
```python
async def test_create_connection_negative_multiplier(test_client):
    """Test creating connection with negative multiplier."""
    connection_data = {
        "from_entity_id": 1,
        "to_entity_id": 2,
        "unit_id": 1,
        "multiplier": -1.5  # Negative value
    }
    response = await test_client.post("/api/v1/connections/", json=connection_data)
    assert response.status_code == 400
    assert "Multiplier must be positive" in response.json()["detail"]
```

3. **Zero Multiplier Constraint**
```python
async def test_create_connection_zero_multiplier(test_client):
    """Test creating connection with zero multiplier."""
    connection_data = {
        "from_entity_id": 1,
        "to_entity_id": 2,
        "unit_id": 1,
        "multiplier": 0.0  # Zero value
    }
    response = await test_client.post("/api/v1/connections/", json=connection_data)
    assert response.status_code == 400
    assert "Multiplier must be positive" in response.json()["detail"]
```

#### 1.3 Edge Cases - Decimal Precision

**Test Case ID**: TC-CONN-EDGE-001  
**Priority**: HIGH  
**Description**: Test decimal precision edge cases with banker's rounding

**Test Scenarios**:

1. **Extreme Small Values**
```python
async def test_connection_extreme_small_multiplier(test_client):
    """Test connection with multiplier that rounds to 0.1."""
    connection_data = {
        "from_entity_id": 1,
        "to_entity_id": 2,
        "unit_id": 1,
        "multiplier": 0.05  # Should round to 0.1
    }
    response = await test_client.post("/api/v1/connections/", json=connection_data)
    assert response.status_code == 201
    
    # Check inverse is capped at 0.1 (not infinity)
    connections = await test_client.get("/api/v1/connections/")
    inverse = next(c for c in connections.json() 
                   if c["from_entity_id"] == 2 and c["to_entity_id"] == 1)
    assert inverse["multiplier"] == "0.1"  # Maximum inverse value
```

2. **Banker's Rounding (ROUND_HALF_EVEN)**
```python
async def test_connection_bankers_rounding(test_client):
    """Test banker's rounding for .5 values."""
    test_cases = [
        (1.15, "1.2"),   # 0.5 rounds to even (up)
        (1.25, "1.2"),   # 0.5 rounds to even (down)
        (1.35, "1.4"),   # 0.5 rounds to even (up)
        (1.45, "1.4"),   # 0.5 rounds to even (down)
    ]
    
    for input_val, expected in test_cases:
        connection_data = {
            "from_entity_id": 1,
            "to_entity_id": 2,
            "unit_id": 1,
            "multiplier": input_val
        }
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        assert response.json()["multiplier"] == expected
```

#### 1.4 Integration Scenarios - Transaction Rollback

**Test Case ID**: TC-CONN-INT-001  
**Priority**: HIGH  
**Description**: Test transaction rollback on inverse creation failure

```python
async def test_connection_rollback_on_inverse_failure(test_client, mocker):
    """Test that primary connection is rolled back if inverse creation fails."""
    # Create a spy on the Connection model to fail on second creation
    creation_count = 0
    
    def mock_add(self, instance):
        nonlocal creation_count
        creation_count += 1
        if creation_count == 2:  # Fail on inverse creation
            raise IntegrityError("Simulated failure", None, None)
        return original_add(self, instance)
    
    original_add = AsyncSession.add
    mocker.patch('sqlalchemy.ext.asyncio.AsyncSession.add', mock_add)
    
    connection_data = {
        "from_entity_id": 1,
        "to_entity_id": 2,
        "unit_id": 1,
        "multiplier": 2.0
    }
    
    response = await test_client.post("/api/v1/connections/", json=connection_data)
    assert response.status_code == 400
    
    # Verify no connections were created
    connections = await test_client.get("/api/v1/connections/")
    assert len(connections.json()) == 0
```

#### 1.5 Concurrent Operations Testing

**Test Case ID**: TC-CONN-CONC-001  
**Priority**: HIGH  
**Description**: Test concurrent connection creation

```python
async def test_concurrent_connection_creation(test_client):
    """Test creating same connection from multiple concurrent requests."""
    import asyncio
    
    connection_data = {
        "from_entity_id": 1,
        "to_entity_id": 2,
        "unit_id": 1,
        "multiplier": 2.0
    }
    
    # Create 5 concurrent requests
    tasks = [
        test_client.post("/api/v1/connections/", json=connection_data)
        for _ in range(5)
    ]
    
    responses = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Exactly one should succeed with 201
    success_count = sum(1 for r in responses if not isinstance(r, Exception) and r.status_code == 201)
    assert success_count == 1
    
    # Others should get appropriate error
    error_count = sum(1 for r in responses if not isinstance(r, Exception) and r.status_code == 400)
    assert error_count == 4
```

### 2. COMPARE MODULE - High Priority Test Cases

#### 2.1 Error Path Testing

**Test Case ID**: TC-COMP-ERR-001  
**Priority**: HIGH  
**Description**: Test all entity validation error paths

```python
async def test_compare_all_validation_errors(test_client):
    """Test all validation error scenarios in sequence."""
    # Test missing from_entity
    response = await test_client.get("/api/v1/compare/?from=9999&to=1&unit=1")
    assert response.status_code == 404
    assert "From entity not found" in response.json()["detail"]
    
    # Test missing to_entity
    response = await test_client.get("/api/v1/compare/?from=1&to=9999&unit=1")
    assert response.status_code == 404
    assert "To entity not found" in response.json()["detail"]
    
    # Test missing unit
    response = await test_client.get("/api/v1/compare/?from=1&to=2&unit=9999")
    assert response.status_code == 404
    assert "Unit not found" in response.json()["detail"]
```

#### 2.2 Edge Cases - Unicode and Special Characters

**Test Case ID**: TC-COMP-EDGE-001  
**Priority**: MEDIUM  
**Description**: Test entity comparison with unicode names

```python
async def test_compare_unicode_entity_names(test_client):
    """Test comparison with unicode entity names in error messages."""
    # Create entities with unicode names
    entity1 = await create_entity("Café")
    entity2 = await create_entity("Naïve")
    
    # Create connection in different unit
    await create_connection(entity1.id, entity2.id, unit_id=1, multiplier=2.0)
    
    # Try to compare in different unit
    response = await test_client.get(f"/api/v1/compare/?from={entity1.id}&to={entity2.id}&unit=2")
    assert response.status_code == 404
    error_msg = response.json()["detail"]
    assert "Café" in error_msg
    assert "Naïve" in error_msg
    assert "No path found" in error_msg
```

#### 2.3 Integration - Path Enrichment Failures

**Test Case ID**: TC-COMP-INT-001  
**Priority**: MEDIUM  
**Description**: Test path enrichment when entities are deleted mid-request

```python
async def test_compare_entity_deleted_during_enrichment(test_client, mocker):
    """Test comparison when entity is deleted during path enrichment."""
    # Create path: A -> B -> C
    entity_a = await create_entity("A")
    entity_b = await create_entity("B")
    entity_c = await create_entity("C")
    
    await create_connection(entity_a.id, entity_b.id, 1, 2.0)
    await create_connection(entity_b.id, entity_c.id, 1, 3.0)
    
    # Mock enrichment to delete entity B mid-process
    original_enrich = get_entity_names_for_path
    async def mock_enrich(db, path):
        # Delete entity B
        await test_client.delete(f"/api/v1/entities/{entity_b.id}")
        return await original_enrich(db, path)
    
    mocker.patch('src.services.get_entity_names_for_path', mock_enrich)
    
    response = await test_client.get(f"/api/v1/compare/?from={entity_a.id}&to={entity_c.id}&unit=1")
    # Should still return result but with missing entity handled gracefully
    assert response.status_code == 200
```

### 3. ENTITIES MODULE - Medium Priority Test Cases

#### 3.1 Cascade Deletion Effects

**Test Case ID**: TC-ENT-CASC-001  
**Priority**: HIGH  
**Description**: Test entity deletion with complex connection networks

```python
async def test_entity_deletion_complex_cascade(test_client):
    """Test deleting entity with multiple incoming and outgoing connections."""
    # Create network: A <-> B <-> C, B <-> D
    entities = await create_entity_network(["A", "B", "C", "D"])
    
    # Create connections
    await create_bidirectional_connection(entities["A"], entities["B"], 1, 2.0)
    await create_bidirectional_connection(entities["B"], entities["C"], 1, 3.0)
    await create_bidirectional_connection(entities["B"], entities["D"], 1, 4.0)
    
    # Delete central entity B
    response = await test_client.delete(f"/api/v1/entities/{entities['B'].id}")
    assert response.status_code == 200
    
    # Verify all connections involving B are deleted
    connections = await test_client.get("/api/v1/connections/")
    remaining = connections.json()
    assert len(remaining) == 0  # All connections should be cascade deleted
    
    # Verify other entities still exist
    for name in ["A", "C", "D"]:
        entity_response = await test_client.get(f"/api/v1/entities/{entities[name].id}")
        assert entity_response.status_code == 200
```

#### 3.2 Unicode and Internationalization

**Test Case ID**: TC-ENT-I18N-001  
**Priority**: MEDIUM  
**Description**: Test entity names with various Unicode characters

```python
async def test_entity_unicode_names(test_client):
    """Test creating entities with various Unicode characters."""
    test_names = [
        "Café",
        "Naïve",
        "Über",
        "北京",  # Chinese
        "Москва",  # Russian
        "مصر",  # Arabic
        "🚀 Rocket"  # Emoji
    ]
    
    for name in test_names:
        response = await test_client.post("/api/v1/entities/", json={"name": name})
        # Should fail due to validation pattern [a-zA-Z\s]+
        assert response.status_code == 422
        assert "String should match pattern" in response.json()["detail"][0]["msg"]
```

### 4. UNITS MODULE - Low Priority Test Cases

#### 4.1 Boundary Testing

**Test Case ID**: TC-UNIT-BOUND-001  
**Priority**: LOW  
**Description**: Test unit creation with boundary values

```python
async def test_unit_boundary_values(test_client):
    """Test unit creation with maximum length names and symbols."""
    # Test maximum lengths (assuming reasonable limits)
    unit_data = {
        "name": "A" * 50,  # Long name
        "symbol": "X" * 10  # Long symbol
    }
    
    response = await test_client.post("/api/v1/units/", json=unit_data)
    assert response.status_code in [201, 422]  # Either succeeds or validates length
```

## Test Execution Strategy

### Phase 1: Critical Path Coverage (Week 1)
1. Implement all CRITICAL priority test cases
2. Focus on connections.py error paths
3. Achieve 50% coverage on connections module

### Phase 2: Error Scenario Coverage (Week 2)
1. Implement all HIGH priority test cases
2. Complete compare.py error coverage
3. Add transaction rollback tests

### Phase 3: Edge Case Coverage (Week 3)
1. Implement MEDIUM priority test cases
2. Add unicode/internationalization tests
3. Complete cascade deletion scenarios

### Phase 4: Polish and Optimization (Week 4)
1. Implement LOW priority test cases
2. Add performance benchmarks
3. Ensure 80% overall coverage

## Test Data Management

### Fixture Enhancements Needed

```python
@pytest.fixture
async def mock_db_failure():
    """Fixture to simulate database failures."""
    with patch('sqlalchemy.ext.asyncio.AsyncSession.execute') as mock:
        mock.side_effect = Exception("Database connection failed")
        yield mock

@pytest.fixture
async def entity_network():
    """Create a complex entity network for testing."""
    async def _create_network(names: List[str]):
        entities = {}
        for name in names:
            response = await test_client.post("/api/v1/entities/", 
                                            json={"name": create_test_entity_name(name)})
            entities[name] = response.json()
        return entities
    return _create_network

@pytest.fixture
async def concurrent_test_client(test_client):
    """Provide multiple client instances for concurrent testing."""
    return [test_client for _ in range(5)]
```

## Success Metrics

### Coverage Targets by Module
- **connections.py**: 21% → 85% (64% improvement)
- **compare.py**: 40% → 90% (50% improvement)
- **entities.py**: 47% → 80% (33% improvement)
- **units.py**: 68% → 85% (17% improvement)
- **Overall**: 63% → 80%+ (17%+ improvement)

### Quality Metrics
- Zero test flakiness
- All error paths covered
- All business rules validated
- All edge cases tested
- Performance benchmarks established

## Risk Mitigation

### Technical Risks
1. **Database Mocking Complexity**: Use pytest-mock and careful fixture design
2. **Async Test Challenges**: Ensure proper async context management
3. **Test Data Conflicts**: Enhanced UUID naming and cleanup verification

### Process Risks
1. **Test Maintenance**: Clear documentation and naming conventions
2. **False Positives**: Thorough assertion verification
3. **Performance Impact**: Parallel test execution support

## Deliverables

### Week 1 Deliverables
1. 15+ new test cases for connections.py
2. Updated coverage report showing 50%+ connections coverage
3. Test execution documentation

### Week 2 Deliverables
1. 10+ new test cases for compare.py
2. Complete error path coverage
3. Integration test suite

### Week 3 Deliverables
1. Edge case test suite
2. Unicode/I18N test coverage
3. Performance benchmarks

### Week 4 Deliverables
1. 80%+ overall test coverage
2. Complete test documentation
3. CI/CD integration with coverage gates

## Appendix: Test Implementation Templates

### Error Path Test Template
```python
@pytest.mark.asyncio
async def test_{module}_{error_scenario}(test_client, test_data_isolation):
    """Test {description of error scenario}."""
    # Arrange
    test_data = create_test_data()
    
    # Act
    response = await test_client.{method}("/api/v1/{endpoint}", json=test_data)
    
    # Assert
    assert response.status_code == {expected_error_code}
    error_detail = response.json()["detail"]
    assert "{expected_error_message}" in error_detail
    
    # Verify no side effects
    verify_no_data_corruption()
```

### Edge Case Test Template
```python
@pytest.mark.asyncio
async def test_{module}_{edge_case}(test_client, cleanup_verification):
    """Test {description of edge case}."""
    # Arrange boundary values
    edge_case_data = {
        "field": BOUNDARY_VALUE
    }
    
    # Act
    response = await test_client.post("/api/v1/{endpoint}", json=edge_case_data)
    
    # Assert expected behavior at boundary
    assert response.status_code == 201
    assert response.json()["field"] == EXPECTED_NORMALIZED_VALUE
```

### Integration Test Template
```python
@pytest.mark.asyncio
async def test_{module}_integration_{scenario}(test_client, db_session):
    """Test {integration scenario description}."""
    # Setup complex state
    await setup_integration_scenario()
    
    # Execute integration flow
    responses = await execute_integration_flow()
    
    # Verify end-to-end behavior
    assert_integration_success(responses)
    
    # Verify data consistency
    await verify_database_consistency()
```

---

*QA Test Plan Version 1.0*  
*Created: January 7, 2025*  
*Target Completion: 4 weeks*  
*Target Coverage: 80%+*