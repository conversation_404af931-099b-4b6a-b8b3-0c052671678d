#!/bin/bash

# Comprehensive Database Transaction Test Runner for SIMILE Backend
# This script runs all database transaction and rollback tests with proper setup and validation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check test database connectivity
check_test_database() {
    print_status "Checking test database connectivity..."
    
    # Check if we can connect to test database
    TEST_DATABASE_HOST=${TEST_DATABASE_HOST:-localhost}
    TEST_DB_URL="postgresql://postgres:postgres@${TEST_DATABASE_HOST}:5432/simile_test"
    
    if command_exists psql; then
        if psql "$TEST_DB_URL" -c "SELECT 1;" >/dev/null 2>&1; then
            print_success "Test database is accessible"
            return 0
        else
            print_error "Cannot connect to test database at $TEST_DB_URL"
            return 1
        fi
    else
        print_warning "psql not found, skipping database connectivity check"
        return 0
    fi
}

# Function to setup test environment
setup_test_environment() {
    print_status "Setting up test environment..."
    
    # Check if we're in the backend directory
    if [ ! -f "requirements.txt" ] || [ ! -d "src" ]; then
        print_error "Please run this script from the backend directory"
        exit 1
    fi
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3.11 -m venv venv
    fi
    
    # Activate virtual environment
    print_status "Activating virtual environment..."
    source venv/bin/activate
    
    # Install/upgrade dependencies
    print_status "Installing dependencies..."
    pip install -r requirements.txt
    
    # Install additional test dependencies if needed
    pip install pytest-cov pytest-asyncio pytest-mock
    
    print_success "Test environment setup complete"
}

# Function to run specific test category
run_test_category() {
    local test_file=$1
    local category_name=$2
    
    print_status "Running $category_name tests..."
    
    # Set environment variables for testing
    export TEST_DATABASE_HOST=${TEST_DATABASE_HOST:-localhost}
    export PYTHONPATH=.
    
    # Run tests with coverage
    if pytest "$test_file" -v --tb=short --cov=src --cov-report=term-missing; then
        print_success "$category_name tests passed"
        return 0
    else
        print_error "$category_name tests failed"
        return 1
    fi
}

# Function to run all transaction tests
run_all_transaction_tests() {
    print_status "Running comprehensive database transaction test suite..."
    
    local failed_tests=0
    
    # Test categories
    declare -A test_categories=(
        ["tests/test_database_transactions.py"]="Database Transaction Integrity"
        ["tests/test_concurrent_operations.py"]="Concurrent Operations & Race Conditions"
        ["tests/test_error_recovery_cleanup.py"]="Error Recovery & Cleanup"
    )
    
    # Run each test category
    for test_file in "${!test_categories[@]}"; do
        category_name="${test_categories[$test_file]}"
        
        if [ -f "$test_file" ]; then
            if ! run_test_category "$test_file" "$category_name"; then
                ((failed_tests++))
            fi
        else
            print_warning "Test file $test_file not found, skipping $category_name tests"
        fi
        
        echo "----------------------------------------"
    done
    
    return $failed_tests
}

# Function to generate test report
generate_test_report() {
    print_status "Generating comprehensive test report..."
    
    # Create reports directory
    mkdir -p test_reports
    
    # Generate detailed coverage report
    if command_exists pytest; then
        export TEST_DATABASE_HOST=${TEST_DATABASE_HOST:-localhost}
        export PYTHONPATH=.
        
        # Run all transaction tests with detailed reporting
        pytest tests/test_database_transactions.py \
               tests/test_concurrent_operations.py \
               tests/test_error_recovery_cleanup.py \
               -v --tb=long \
               --cov=src \
               --cov-report=html:test_reports/transaction_coverage \
               --cov-report=xml:test_reports/transaction_coverage.xml \
               --junitxml=test_reports/transaction_results.xml \
               > test_reports/transaction_test_output.txt 2>&1
        
        if [ $? -eq 0 ]; then
            print_success "Test report generated in test_reports/ directory"
        else
            print_warning "Test report generation completed with some failures"
        fi
    fi
}

# Function to validate test isolation
validate_test_isolation() {
    print_status "Validating test isolation..."
    
    # Check that tests clean up after themselves
    export TEST_DATABASE_HOST=${TEST_DATABASE_HOST:-localhost}
    export PYTHONPATH=.
    
    # Run a simple isolation validation
    if python3 -c "
import asyncio
import sys
sys.path.insert(0, '.')
from tests.conftest import clean_test_database

async def main():
    try:
        await clean_test_database()
        print('Test isolation validation: PASSED')
        return 0
    except Exception as e:
        print(f'Test isolation validation: FAILED - {e}')
        return 1

exit(asyncio.run(main()))
"; then
        print_success "Test isolation validation passed"
    else
        print_warning "Test isolation validation failed - tests may interfere with each other"
    fi
}

# Function to run performance baseline
run_performance_baseline() {
    print_status "Running transaction performance baseline..."
    
    # Create a simple performance test
    export TEST_DATABASE_HOST=${TEST_DATABASE_HOST:-localhost}
    export PYTHONPATH=.
    
    python3 -c "
import asyncio
import time
import sys
sys.path.insert(0, '.')
from src.database import async_session
from src.models import Entity

async def performance_test():
    start_time = time.time()
    
    # Create 100 entities to test transaction performance
    async with async_session() as db:
        for i in range(100):
            entity = Entity(name=f'PerfTest{i}_{int(time.time())}')
            db.add(entity)
        await db.commit()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f'Transaction performance baseline: {duration:.2f}s for 100 entity creations')
    
    if duration < 5.0:
        print('Performance: GOOD')
        return 0
    elif duration < 10.0:
        print('Performance: ACCEPTABLE')
        return 0
    else:
        print('Performance: POOR - may indicate issues')
        return 1

try:
    exit(asyncio.run(performance_test()))
except Exception as e:
    print(f'Performance test failed: {e}')
    exit(1)
"
    
    if [ $? -eq 0 ]; then
        print_success "Performance baseline completed"
    else
        print_warning "Performance baseline indicates potential issues"
    fi
}

# Main execution
main() {
    echo "================================="
    echo "SIMILE Database Transaction Tests"
    echo "================================="
    echo
    
    # Check prerequisites
    if ! command_exists python3.11; then
        print_error "Python 3.11 is required but not found"
        exit 1
    fi
    
    if ! command_exists pytest; then
        print_warning "pytest not found in PATH, will install via pip"
    fi
    
    # Setup test environment
    setup_test_environment
    
    # Activate virtual environment for the rest of the script
    source venv/bin/activate
    
    # Check database connectivity
    if ! check_test_database; then
        print_error "Database connectivity check failed"
        print_error "Please ensure PostgreSQL is running and accessible"
        exit 1
    fi
    
    # Validate test isolation
    validate_test_isolation
    
    # Run performance baseline
    run_performance_baseline
    
    # Run all transaction tests
    if run_all_transaction_tests; then
        print_success "All transaction tests completed successfully!"
        
        # Generate test report
        generate_test_report
        
        echo
        print_success "Transaction test suite execution completed!"
        print_status "Check test_reports/ directory for detailed results"
        
        exit 0
    else
        print_error "Some transaction tests failed"
        
        # Still generate report for analysis
        generate_test_report
        
        echo
        print_error "Transaction test suite execution completed with failures"
        print_status "Check test_reports/ directory for detailed results"
        
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --setup-only        Only setup test environment, don't run tests"
        echo "  --tests-only        Only run tests, skip setup"
        echo "  --report-only       Only generate test report"
        echo
        echo "Environment Variables:"
        echo "  TEST_DATABASE_HOST  Database host for testing (default: localhost)"
        echo
        exit 0
        ;;
    --setup-only)
        setup_test_environment
        print_success "Test environment setup completed"
        exit 0
        ;;
    --tests-only)
        source venv/bin/activate 2>/dev/null || true
        check_test_database
        run_all_transaction_tests
        exit $?
        ;;
    --report-only)
        source venv/bin/activate 2>/dev/null || true
        generate_test_report
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac