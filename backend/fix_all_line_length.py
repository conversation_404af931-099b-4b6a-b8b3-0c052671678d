#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix all line length issues in test files.
This script will fix common E501 patterns in the test files.
"""

import re

def fix_line_length(file_path):
    """Fix line length issues in a file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix long f-strings with API calls
    content = re.sub(
        r'f"(/api/v1/[^"]*from={[^}]+}&to={[^}]+}&unit={[^}]+})"',
        lambda m: f'f"{m.group(1)[:50]}"\n            f"{m.group(1)[50:]}"' if len(m.group(1)) > 79 else m.group(0),
        content
    )
    
    # Fix long lines with /api/v1/compare/?from=...&to=...&unit=...
    content = re.sub(
        r'f"/api/v1/compare/\?from={([^}]+)}&to={([^}]+)}&unit={([^}]+)}"',
        r'f"/api/v1/compare/?from={\1}&to={\2}&"\n            f"unit={\3}"',
        content
    )
    
    # Fix long lines with create_test_entity_name
    content = re.sub(
        r'create_test_entity_name\("([^"]+)"\)',
        lambda m: f'create_test_entity_name(\n            "{m.group(1)}")' if len(m.group(1)) > 40 else m.group(0),
        content
    )
    
    # Fix long assertion lines
    content = re.sub(
        r'assert response\.status_code == 200, f"Expected 200, got {response\.status_code}: {response\.text}"',
        r'assert response.status_code == 200, (\n            f"Expected 200, got {response.status_code}: {response.text}")',
        content
    )
    
    # Fix long lines with json()["detail"]
    content = re.sub(
        r'assert "([^"]+)" in response\.json\(\)\["detail"\]',
        lambda m: f'assert "{m.group(1)}" in response.json()["detail"]' if len(m.group(1)) < 40 else f'assert (\n            "{m.group(1)}" in response.json()["detail"])',
        content
    )
    
    # Fix class definitions that need more blank lines
    content = re.sub(
        r'\n\nclass ([^:]+):',
        r'\n\n\nclass \1:',
        content
    )
    
    # Fix continuation line indentation - fix specific E122 errors
    content = re.sub(
        r'            response = await test_client\.get\(\n            f',
        r'            response = await test_client.get(\n                f',
        content
    )
    
    # Fix continuation line indentation for other patterns
    content = re.sub(
        r'            (await test_client\.(get|post|put|delete))\(\n            ',
        r'            \1(\n                ',
        content
    )
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed line length issues in {file_path}")

if __name__ == "__main__":
    files_to_fix = [
        "tests/test_compare_comprehensive.py",
        "tests/test_compare_error_scenarios.py", 
        "tests/test_comprehensive_connections.py",
        "tests/test_comprehensive_entities.py",
        "tests/test_comprehensive_pathfinding.py"
    ]
    
    for file_path in files_to_fix:
        try:
            fix_line_length(file_path)
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")