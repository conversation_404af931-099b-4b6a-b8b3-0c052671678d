#!/usr/bin/env python3
"""
Comprehensive test file cleanup script to fix flake8 issues:
- W293: blank line contains whitespace
- W292: no newline at end of file
- F401: unused imports
- E501: line too long (simple cases)
"""

import os
import re
import subprocess
import sys
from pathlib import Path


def get_files_with_issues(error_codes):
    """Get list of files with specific flake8 issues."""
    cmd = ["flake8", "tests", "--select=" + ",".join(error_codes)]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    files = set()
    for line in result.stdout.split('\n'):
        if line.strip():
            file_path = line.split(':')[0]
            files.add(file_path)
    
    return sorted(files)


def fix_whitespace_issues(file_path):
    """Fix W293 (blank line whitespace) issues."""
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    # Fix blank lines with whitespace
    fixed_lines = []
    for line in lines:
        if line.strip() == '' and line != '\n':
            fixed_lines.append('\n')
        else:
            fixed_lines.append(line)
    
    with open(file_path, 'w') as f:
        f.writelines(fixed_lines)


def fix_newline_at_end(file_path):
    """Fix W292 (no newline at end of file) issues."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    if content and not content.endswith('\n'):
        with open(file_path, 'w') as f:
            f.write(content + '\n')


def get_unused_imports(file_path):
    """Get list of unused imports for a file."""
    cmd = ["flake8", file_path, "--select=F401"]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    unused_imports = []
    for line in result.stdout.split('\n'):
        if 'F401' in line:
            # Extract the import name from the message
            match = re.search(r"'([^']+)' imported but unused", line)
            if match:
                import_name = match.group(1)
                line_num = int(line.split(':')[1])
                unused_imports.append((line_num, import_name))
    
    return unused_imports


def fix_unused_imports(file_path):
    """Fix F401 (unused imports) issues."""
    unused_imports = get_unused_imports(file_path)
    if not unused_imports:
        return
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    # Sort by line number in descending order to avoid index issues
    unused_imports.sort(reverse=True)
    
    for line_num, import_name in unused_imports:
        line_idx = line_num - 1
        if line_idx < len(lines):
            line = lines[line_idx]
            
            # Handle different import patterns
            if line.strip().startswith('import '):
                # Simple import: import module
                if f'import {import_name}' in line:
                    # Check if it's the only import on the line
                    if line.strip() == f'import {import_name}':
                        lines.pop(line_idx)
                    else:
                        # Multiple imports on same line
                        line = line.replace(f', {import_name}', '')
                        line = line.replace(f'{import_name}, ', '')
                        line = line.replace(f'import {import_name}', 'import')
                        if line.strip() != 'import':
                            lines[line_idx] = line
                        else:
                            lines.pop(line_idx)
            
            elif line.strip().startswith('from '):
                # From import: from module import name
                if f'import {import_name}' in line:
                    # Check if it's the only import
                    if line.strip().endswith(f'import {import_name}'):
                        lines.pop(line_idx)
                    else:
                        # Multiple imports
                        line = line.replace(f', {import_name}', '')
                        line = line.replace(f'{import_name}, ', '')
                        if not line.strip().endswith('import'):
                            lines[line_idx] = line
                        else:
                            lines.pop(line_idx)
    
    with open(file_path, 'w') as f:
        f.writelines(lines)


def fix_simple_line_length(file_path):
    """Fix simple E501 (line too long) issues."""
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    fixed_lines = []
    for line in lines:
        if len(line.rstrip()) > 79:
            # Only fix simple cases that are easy to split
            stripped = line.rstrip()
            
            # Handle long string literals
            if ('"""' in stripped or "'''" in stripped or 
                stripped.count('"') == 2 or stripped.count("'") == 2):
                # Skip complex string cases
                fixed_lines.append(line)
                continue
            
            # Handle long function calls with multiple parameters
            if '(' in stripped and ')' in stripped and ',' in stripped:
                # Try to split on comma if it's a function call
                indent = len(line) - len(line.lstrip())
                if stripped.count('(') == stripped.count(')') == 1:
                    # Simple function call
                    before_paren = stripped[:stripped.find('(') + 1]
                    after_paren = stripped[stripped.find('(') + 1:stripped.rfind(')')]
                    end_paren = stripped[stripped.rfind(')'):]
                    
                    if ',' in after_paren and len(before_paren) + len(end_paren) < 60:
                        # Split on commas
                        params = [p.strip() for p in after_paren.split(',') if p.strip()]
                        if len(params) > 1:
                            new_line = ' ' * indent + before_paren + '\n'
                            for i, param in enumerate(params):
                                if i == len(params) - 1:
                                    new_line += ' ' * (indent + 4) + param + '\n'
                                else:
                                    new_line += ' ' * (indent + 4) + param + ',\n'
                            new_line += ' ' * indent + end_paren + '\n'
                            fixed_lines.append(new_line)
                            continue
            
            # Handle long import statements
            if line.strip().startswith('from ') and ' import ' in line:
                from_part = line[:line.find(' import ')]
                import_part = line[line.find(' import ') + 8:].strip()
                
                if ',' in import_part:
                    imports = [imp.strip() for imp in import_part.split(',')]
                    if len(imports) > 2:
                        indent = len(line) - len(line.lstrip())
                        new_line = from_part + ' import (\n'
                        for i, imp in enumerate(imports):
                            if i == len(imports) - 1:
                                new_line += ' ' * (indent + 4) + imp + '\n'
                            else:
                                new_line += ' ' * (indent + 4) + imp + ',\n'
                        new_line += ' ' * indent + ')\n'
                        fixed_lines.append(new_line)
                        continue
            
            # If we can't fix it easily, keep the original line
            fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    with open(file_path, 'w') as f:
        f.writelines(fixed_lines)


def main():
    """Main function to run all fixes."""
    print("Starting comprehensive test file cleanup...")
    
    # Get initial count
    result = subprocess.run(["flake8", "tests", "--select=W293,W292,F401,E501", "--count"], 
                          capture_output=True, text=True)
    initial_count = int(result.stdout.strip().split('\n')[-1])
    print(f"Initial issues: {initial_count}")
    
    # Fix W293 (blank line whitespace) - easiest to fix
    print("\n1. Fixing W293 (blank line whitespace)...")
    files_w293 = get_files_with_issues(["W293"])
    for file_path in files_w293:
        fix_whitespace_issues(file_path)
    
    # Fix W292 (no newline at end) - also easy
    print("\n2. Fixing W292 (no newline at end)...")
    files_w292 = get_files_with_issues(["W292"])
    for file_path in files_w292:
        fix_newline_at_end(file_path)
    
    # Fix F401 (unused imports) - moderate difficulty
    print("\n3. Fixing F401 (unused imports)...")
    files_f401 = get_files_with_issues(["F401"])
    for file_path in files_f401:
        print(f"  Processing {file_path}...")
        fix_unused_imports(file_path)
    
    # Fix simple E501 (line too long) cases
    print("\n4. Fixing simple E501 (line too long) cases...")
    files_e501 = get_files_with_issues(["E501"])
    for file_path in files_e501:
        print(f"  Processing {file_path}...")
        fix_simple_line_length(file_path)
    
    # Get final count
    result = subprocess.run(["flake8", "tests", "--select=W293,W292,F401,E501", "--count"], 
                          capture_output=True, text=True)
    final_count = int(result.stdout.strip().split('\n')[-1])
    
    print(f"\nResults:")
    print(f"Initial issues: {initial_count}")
    print(f"Final issues: {final_count}")
    print(f"Issues fixed: {initial_count - final_count}")
    print(f"Reduction: {((initial_count - final_count) / initial_count) * 100:.1f}%")
    
    if final_count < 1000:
        print("\n✅ SUCCESS: Reduced issues to under 1,000!")
    else:
        print(f"\n⚠️  Still {final_count - 1000} issues over target of 1,000")


if __name__ == "__main__":
    main()