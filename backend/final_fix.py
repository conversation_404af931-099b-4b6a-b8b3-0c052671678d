#!/usr/bin/env python3
"""Final fix for remaining flake8 issues."""

import re
from pathlib import Path

def fix_file(file_path):
    """Fix remaining issues in a file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Remove unused imports
    content = re.sub(r'import pytest_asyncio\n', '', content)
    content = re.sub(r'from decimal import Decimal\n', '', content)
    content = re.sub(r'from sqlalchemy import select, func\n', '', content)
    content = re.sub(r'from src.models import Entity\n', '', content)
    
    # Fix continuation line issues
    content = re.sub(r'(\s+)"/api/v1/entities/", json=entity_data\)', r'\1"/api/v1/entities/", json=entity_data)', content)
    content = re.sub(r'(\s+)"/api/v1/connections/", json=conn_data\)', r'\1"/api/v1/connections/", json=conn_data)', content)
    
    # Fix E303 too many blank lines
    content = re.sub(r'\n\n\n+', '\n\n', content)
    
    # Fix line continuation issues
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        if '/api/v1/entities/", json=entity_data)' in line or '/api/v1/connections/", json=conn_data)' in line:
            # Fix indentation for continuation lines
            indent = len(line) - len(line.lstrip())
            if indent < 12:  # Adjust indentation
                line = ' ' * 12 + line.lstrip()
        fixed_lines.append(line)
    
    content = '\n'.join(fixed_lines)
    
    # Fix specific long lines that are still problematic
    replacements = [
        # Function signatures
        ('async def test_compare_missing_query_parameters(self, test_client: AsyncClient):', 
         'async def test_compare_missing_query_parameters(\n            self, test_client: AsyncClient):'),
        
        # Long assertions
        ('assert "cannot create connection from entity to itself" in error_msg', 
         'assert "cannot create connection from entity to itself" in \\\n            error_msg'),
        
        # Long strings
        ('assert "Consider creating a direct connection" in error_detail["detail"]',
         'assert "Consider creating a direct connection" in \\\n            error_detail["detail"]'),
    ]
    
    for old, new in replacements:
        content = content.replace(old, new)
    
    # Fix specific E501 issues with f-strings
    content = re.sub(
        r'entity_name = create_test_entity_name\(f"([^"]+)"\)',
        r'entity_name = create_test_entity_name(\n            f"\1")', 
        content
    )
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Applied final fixes to {file_path}")

if __name__ == "__main__":
    test_files = [
        "tests/test_compare_comprehensive.py",
        "tests/test_compare_error_scenarios.py", 
        "tests/test_comprehensive_connections.py",
        "tests/test_comprehensive_entities.py",
        "tests/test_comprehensive_pathfinding.py"
    ]
    
    for file_path in test_files:
        if Path(file_path).exists():
            fix_file(file_path)
        else:
            print(f"File not found: {file_path}")