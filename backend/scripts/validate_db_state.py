#!/usr/bin/env python3
"""
Database state validation script for SIMILE testing.
Captures and compares database states before and after tests.
"""
import asyncio
import asyncpg
import json
import argparse
import sys
import os
from datetime import datetime
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseStateValidator:
    def __init__(self, connection_string):
        self.connection_string = connection_string
        
    async def capture_state(self):
        """Capture current database state."""
        conn = await asyncpg.connect(self.connection_string)
        try:
            state = {
                "timestamp": datetime.utcnow().isoformat(),
                "tables": {},
                "constraints": [],
                "indexes": [],
                "sequences": [],
                "functions": [],
                "database_info": {}
            }
            
            # Database information
            db_info = await conn.fetchrow("""
                SELECT 
                    current_database() as database_name,
                    current_user as current_user,
                    version() as version,
                    pg_database_size(current_database()) as database_size
            """)
            state["database_info"] = dict(db_info)
            
            # Get all tables
            tables = await conn.fetch("""
                SELECT tablename 
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY tablename
            """)
            
            # Capture table information
            for table_row in tables:
                table_name = table_row['tablename']
                
                # Row count
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                
                # Table size
                table_size = await conn.fetchval(f"SELECT pg_total_relation_size('{table_name}')")
                
                # Sample data (first 5 rows)
                sample_rows = await conn.fetch(f"SELECT * FROM {table_name} LIMIT 5")
                sample_data = [dict(row) for row in sample_rows]
                
                # Column information
                columns = await conn.fetch("""
                    SELECT 
                        column_name,
                        data_type,
                        is_nullable,
                        column_default
                    FROM information_schema.columns
                    WHERE table_schema = 'public' AND table_name = $1
                    ORDER BY ordinal_position
                """, table_name)
                
                state["tables"][table_name] = {
                    "row_count": count,
                    "table_size": table_size,
                    "sample_data": sample_data,
                    "columns": [dict(col) for col in columns]
                }
            
            # Capture constraints
            constraints = await conn.fetch("""
                SELECT 
                    tc.table_name,
                    tc.constraint_name,
                    tc.constraint_type,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints tc
                LEFT JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name
                LEFT JOIN information_schema.constraint_column_usage ccu 
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.table_schema = 'public'
                ORDER BY tc.table_name, tc.constraint_name
            """)
            state["constraints"] = [dict(c) for c in constraints]
            
            # Capture indexes
            indexes = await conn.fetch("""
                SELECT 
                    indexname,
                    tablename,
                    indexdef,
                    pg_relation_size(indexname::regclass) as index_size
                FROM pg_indexes
                WHERE schemaname = 'public'
                ORDER BY tablename, indexname
            """)
            state["indexes"] = [dict(i) for i in indexes]
            
            # Capture sequences
            sequences = await conn.fetch("""
                SELECT 
                    sequence_name,
                    start_value,
                    minimum_value,
                    maximum_value,
                    increment,
                    cycle_option
                FROM information_schema.sequences
                WHERE sequence_schema = 'public'
                ORDER BY sequence_name
            """)
            state["sequences"] = [dict(s) for s in sequences]
            
            # Capture functions
            functions = await conn.fetch("""
                SELECT 
                    routine_name,
                    routine_type,
                    data_type,
                    routine_definition
                FROM information_schema.routines
                WHERE routine_schema = 'public'
                ORDER BY routine_name
            """)
            state["functions"] = [dict(f) for f in functions]
            
            # Database statistics
            db_stats = await conn.fetchrow("""
                SELECT 
                    numbackends,
                    xact_commit,
                    xact_rollback,
                    blks_read,
                    blks_hit,
                    tup_returned,
                    tup_fetched,
                    tup_inserted,
                    tup_updated,
                    tup_deleted,
                    conflicts,
                    temp_files,
                    temp_bytes,
                    deadlocks
                FROM pg_stat_database
                WHERE datname = current_database()
            """)
            state["database_stats"] = dict(db_stats) if db_stats else {}
            
            return state
            
        finally:
            await conn.close()
    
    async def compare_states(self, state1, state2):
        """Compare two database states and return differences."""
        differences = {
            "timestamp": datetime.utcnow().isoformat(),
            "comparison": {
                "before": state1.get("timestamp"),
                "after": state2.get("timestamp")
            },
            "table_differences": {},
            "constraint_differences": [],
            "index_differences": [],
            "sequence_differences": [],
            "function_differences": [],
            "database_info_differences": {},
            "stats_differences": {}
        }
        
        # Compare database info
        info1 = state1.get("database_info", {})
        info2 = state2.get("database_info", {})
        
        for key in set(info1.keys()) | set(info2.keys()):
            val1 = info1.get(key)
            val2 = info2.get(key)
            if val1 != val2:
                differences["database_info_differences"][key] = {
                    "before": val1,
                    "after": val2
                }
        
        # Compare tables
        tables1 = state1.get("tables", {})
        tables2 = state2.get("tables", {})
        
        all_tables = set(tables1.keys()) | set(tables2.keys())
        
        for table in all_tables:
            table_diff = {}
            
            if table not in tables1:
                table_diff["status"] = "added"
                table_diff["details"] = tables2[table]
            elif table not in tables2:
                table_diff["status"] = "removed"
                table_diff["details"] = tables1[table]
            else:
                # Compare existing table
                t1 = tables1[table]
                t2 = tables2[table]
                
                # Row count difference
                count_diff = t2["row_count"] - t1["row_count"]
                if count_diff != 0:
                    table_diff["row_count_change"] = count_diff
                
                # Size difference
                size_diff = t2["table_size"] - t1["table_size"]
                if size_diff != 0:
                    table_diff["size_change"] = size_diff
                
                # Column differences
                cols1 = {col["column_name"]: col for col in t1["columns"]}
                cols2 = {col["column_name"]: col for col in t2["columns"]}
                
                col_changes = {}
                for col_name in set(cols1.keys()) | set(cols2.keys()):
                    if col_name not in cols1:
                        col_changes[col_name] = {"status": "added", "definition": cols2[col_name]}
                    elif col_name not in cols2:
                        col_changes[col_name] = {"status": "removed", "definition": cols1[col_name]}
                    elif cols1[col_name] != cols2[col_name]:
                        col_changes[col_name] = {
                            "status": "modified",
                            "before": cols1[col_name],
                            "after": cols2[col_name]
                        }
                
                if col_changes:
                    table_diff["column_changes"] = col_changes
            
            if table_diff:
                differences["table_differences"][table] = table_diff
        
        # Compare constraints
        constraints1 = set(c["constraint_name"] for c in state1.get("constraints", []))
        constraints2 = set(c["constraint_name"] for c in state2.get("constraints", []))
        
        added_constraints = constraints2 - constraints1
        removed_constraints = constraints1 - constraints2
        
        for constraint in added_constraints:
            constraint_detail = next(c for c in state2["constraints"] if c["constraint_name"] == constraint)
            differences["constraint_differences"].append({
                "status": "added",
                "constraint": constraint_detail
            })
        
        for constraint in removed_constraints:
            constraint_detail = next(c for c in state1["constraints"] if c["constraint_name"] == constraint)
            differences["constraint_differences"].append({
                "status": "removed",
                "constraint": constraint_detail
            })
        
        # Compare indexes
        indexes1 = set(i["indexname"] for i in state1.get("indexes", []))
        indexes2 = set(i["indexname"] for i in state2.get("indexes", []))
        
        added_indexes = indexes2 - indexes1
        removed_indexes = indexes1 - indexes2
        
        for index in added_indexes:
            index_detail = next(i for i in state2["indexes"] if i["indexname"] == index)
            differences["index_differences"].append({
                "status": "added",
                "index": index_detail
            })
        
        for index in removed_indexes:
            index_detail = next(i for i in state1["indexes"] if i["indexname"] == index)
            differences["index_differences"].append({
                "status": "removed",
                "index": index_detail
            })
        
        # Compare database statistics
        stats1 = state1.get("database_stats", {})
        stats2 = state2.get("database_stats", {})
        
        for key in set(stats1.keys()) | set(stats2.keys()):
            val1 = stats1.get(key, 0)
            val2 = stats2.get(key, 0)
            if val1 != val2:
                differences["stats_differences"][key] = {
                    "before": val1,
                    "after": val2,
                    "change": val2 - val1
                }
        
        return differences
    
    async def validate_integrity(self):
        """Validate database integrity."""
        conn = await asyncpg.connect(self.connection_string)
        try:
            integrity_report = {
                "timestamp": datetime.utcnow().isoformat(),
                "checks": {}
            }
            
            # Check foreign key constraints
            fk_violations = await conn.fetch("""
                SELECT 
                    tc.table_name,
                    tc.constraint_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage ccu 
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_schema = 'public'
            """)
            
            fk_issues = []
            for fk in fk_violations:
                # Check if there are any orphaned records
                orphan_count = await conn.fetchval(f"""
                    SELECT COUNT(*) 
                    FROM {fk['table_name']} t1
                    LEFT JOIN {fk['foreign_table_name']} t2 
                        ON t1.{fk['column_name']} = t2.{fk['foreign_column_name']}
                    WHERE t1.{fk['column_name']} IS NOT NULL 
                        AND t2.{fk['foreign_column_name']} IS NULL
                """)
                
                if orphan_count > 0:
                    fk_issues.append({
                        "constraint": fk['constraint_name'],
                        "table": fk['table_name'],
                        "column": fk['column_name'],
                        "foreign_table": fk['foreign_table_name'],
                        "foreign_column": fk['foreign_column_name'],
                        "orphan_count": orphan_count
                    })
            
            integrity_report["checks"]["foreign_key_violations"] = {
                "passed": len(fk_issues) == 0,
                "issues": fk_issues
            }
            
            # Check for duplicate primary keys (shouldn't happen but good to verify)
            tables = await conn.fetch("""
                SELECT tablename 
                FROM pg_tables 
                WHERE schemaname = 'public'
            """)
            
            duplicate_pk_issues = []
            for table_row in tables:
                table_name = table_row['tablename']
                
                # Get primary key columns
                pk_columns = await conn.fetch("""
                    SELECT kcu.column_name
                    FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu 
                        ON tc.constraint_name = kcu.constraint_name
                    WHERE tc.table_schema = 'public' 
                        AND tc.table_name = $1
                        AND tc.constraint_type = 'PRIMARY KEY'
                """, table_name)
                
                if pk_columns:
                    pk_column_list = [col['column_name'] for col in pk_columns]
                    pk_columns_str = ', '.join(pk_column_list)
                    
                    # Check for duplicates
                    duplicate_count = await conn.fetchval(f"""
                        SELECT COUNT(*) - COUNT(DISTINCT {pk_columns_str})
                        FROM {table_name}
                    """)
                    
                    if duplicate_count > 0:
                        duplicate_pk_issues.append({
                            "table": table_name,
                            "primary_key_columns": pk_column_list,
                            "duplicate_count": duplicate_count
                        })
            
            integrity_report["checks"]["primary_key_duplicates"] = {
                "passed": len(duplicate_pk_issues) == 0,
                "issues": duplicate_pk_issues
            }
            
            # Check for null values in not-null columns
            null_violations = []
            for table_row in tables:
                table_name = table_row['tablename']
                
                # Get not-null columns
                not_null_columns = await conn.fetch("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = 'public' 
                        AND table_name = $1
                        AND is_nullable = 'NO'
                """, table_name)
                
                for col_row in not_null_columns:
                    col_name = col_row['column_name']
                    
                    null_count = await conn.fetchval(f"""
                        SELECT COUNT(*) 
                        FROM {table_name} 
                        WHERE {col_name} IS NULL
                    """)
                    
                    if null_count > 0:
                        null_violations.append({
                            "table": table_name,
                            "column": col_name,
                            "null_count": null_count
                        })
            
            integrity_report["checks"]["null_violations"] = {
                "passed": len(null_violations) == 0,
                "issues": null_violations
            }
            
            return integrity_report
            
        finally:
            await conn.close()

async def main():
    parser = argparse.ArgumentParser(description="Database state validation for SIMILE")
    parser.add_argument("--capture", type=str, help="Capture current state to file")
    parser.add_argument("--compare", nargs=2, metavar=("STATE1", "STATE2"), 
                       help="Compare two state files")
    parser.add_argument("--integrity", action="store_true", 
                       help="Validate database integrity")
    parser.add_argument("--connection-string", type=str, 
                       help="PostgreSQL connection string")
    
    args = parser.parse_args()
    
    # Determine connection string
    if args.connection_string:
        connection_string = args.connection_string
    else:
        # Use environment variables
        TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        connection_string = f"postgresql://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"
    
    validator = DatabaseStateValidator(connection_string)
    
    try:
        if args.capture:
            logger.info("Capturing database state...")
            state = await validator.capture_state()
            
            output_path = Path(args.capture)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w') as f:
                json.dump(state, f, indent=2, default=str)
            
            logger.info(f"Database state captured to {output_path}")
            
            # Print summary
            tables = state.get("tables", {})
            total_rows = sum(t["row_count"] for t in tables.values())
            print(f"Captured state: {len(tables)} tables, {total_rows} total rows")
            
        elif args.compare:
            state1_file, state2_file = args.compare
            
            logger.info(f"Comparing states: {state1_file} vs {state2_file}")
            
            with open(state1_file, 'r') as f:
                state1 = json.load(f)
            
            with open(state2_file, 'r') as f:
                state2 = json.load(f)
            
            differences = await validator.compare_states(state1, state2)
            
            # Print comparison results
            print("Database State Comparison")
            print("=" * 50)
            
            if differences["table_differences"]:
                print("\nTable Differences:")
                for table, diff in differences["table_differences"].items():
                    print(f"  {table}:")
                    if "status" in diff:
                        print(f"    Status: {diff['status']}")
                    if "row_count_change" in diff:
                        print(f"    Row count change: {diff['row_count_change']:+d}")
                    if "size_change" in diff:
                        print(f"    Size change: {diff['size_change']:+d} bytes")
                    if "column_changes" in diff:
                        print(f"    Column changes: {len(diff['column_changes'])} columns")
            
            if differences["constraint_differences"]:
                print(f"\nConstraint Differences: {len(differences['constraint_differences'])} changes")
            
            if differences["index_differences"]:
                print(f"\nIndex Differences: {len(differences['index_differences'])} changes")
            
            if differences["stats_differences"]:
                print("\nDatabase Statistics Changes:")
                for stat, change in differences["stats_differences"].items():
                    print(f"  {stat}: {change['before']} → {change['after']} ({change['change']:+d})")
            
            # Save detailed comparison
            comparison_file = f"state_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(comparison_file, 'w') as f:
                json.dump(differences, f, indent=2, default=str)
            
            print(f"\nDetailed comparison saved to: {comparison_file}")
            
        elif args.integrity:
            logger.info("Validating database integrity...")
            integrity_report = await validator.validate_integrity()
            
            print("Database Integrity Report")
            print("=" * 50)
            
            all_passed = True
            for check_name, check_result in integrity_report["checks"].items():
                status = "✅ PASS" if check_result["passed"] else "❌ FAIL"
                print(f"{check_name}: {status}")
                
                if not check_result["passed"]:
                    all_passed = False
                    for issue in check_result["issues"]:
                        print(f"  Issue: {issue}")
            
            if all_passed:
                print("\n✅ All integrity checks passed!")
            else:
                print("\n❌ Some integrity checks failed!")
                sys.exit(1)
            
            # Save integrity report
            integrity_file = f"integrity_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(integrity_file, 'w') as f:
                json.dump(integrity_report, f, indent=2, default=str)
            
            print(f"Integrity report saved to: {integrity_file}")
            
        else:
            parser.print_help()
            
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())