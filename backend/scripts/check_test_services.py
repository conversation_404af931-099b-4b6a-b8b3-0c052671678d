#!/usr/bin/env python3
"""Check that all required services are running before tests."""
import sys
import os
import asyncpg
import asyncio

def check_venv():
    """Check if running in virtual environment."""
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) or
        os.environ.get('VIRTUAL_ENV') is not None
    )
    
    if in_venv:
        print(f"✅ Running in virtual environment")
        return True
    else:
        print(f"❌ Not in virtual environment")
        return False

async def check_postgres():
    """Check PostgreSQL is accessible."""
    try:
        conn = await asyncpg.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres',
            database='postgres'
        )
        await conn.close()
        print("✅ PostgreSQL is running on localhost:5432")
        return True
    except Exception as e:
        print(f"❌ PostgreSQL not accessible: {e}")
        return False

async def main():
    """Run all service checks."""
    print("Checking test environment...")
    print("-" * 40)
    
    # Check virtual environment first
    if not check_venv():
        print("-" * 40)
        print("❌ Virtual environment not active. Cannot proceed.")
        print("   Run: source venv/bin/activate")
        return 1
    
    # Check services
    checks = [
        check_postgres(),
    ]
    
    results = await asyncio.gather(*checks)
    
    if all(results):
        print("-" * 40)
        print("✅ All checks passed!")
        return 0
    else:
        print("-" * 40)
        print("❌ Some checks failed. Please fix before running tests.")
        print("   For PostgreSQL: podman-compose up -d database")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))