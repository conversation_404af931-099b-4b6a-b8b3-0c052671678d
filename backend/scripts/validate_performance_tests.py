#!/usr/bin/env python3
"""
Validation script for performance test suite.
Runs basic checks to ensure the performance testing infrastructure is working.
"""
import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Any

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_dependencies() -> Dict[str, bool]:
    """Check if required dependencies are available."""
    dependencies = {
        "pytest": False,
        "pytest-asyncio": False,
        "psutil": False,
        "fastapi": False,
        "sqlalchemy": False,
        "asyncpg": False,
        "pytest-json-report": False,
        "pytest-benchmark": False
    }
    
    for dep in dependencies.keys():
        try:
            __import__(dep.replace("-", "_"))
            dependencies[dep] = True
        except ImportError:
            logger.warning(f"Missing dependency: {dep}")
    
    return dependencies


def check_environment() -> Dict[str, Any]:
    """Check environment configuration."""
    env_info = {
        "TEST_DATABASE_HOST": os.getenv("TEST_DATABASE_HOST", "Not set"),
        "DATABASE_URL": os.getenv("DATABASE_URL", "Not set"),
        "PYTHONPATH": os.getenv("PYTHONPATH", "Not set"),
        "python_version": sys.version,
        "working_directory": os.getcwd(),
        "virtual_env": getattr(sys, 'real_prefix', None) is not None or (
            hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
        )
    }
    
    return env_info


def check_test_files() -> Dict[str, bool]:
    """Check if test files exist and are accessible."""
    backend_root = Path(__file__).parent.parent
    
    test_files = {
        "performance_benchmarks": backend_root / "tests" / "test_performance_benchmarks.py",
        "performance_integration": backend_root / "tests" / "test_performance_integration.py",
        "benchmark_runner": backend_root / "scripts" / "run_performance_benchmark.py",
        "conftest": backend_root / "tests" / "conftest.py",
        "makefile": backend_root / "Makefile",
        "pyproject_toml": backend_root / "pyproject.toml"
    }
    
    file_status = {}
    for name, file_path in test_files.items():
        file_status[name] = file_path.exists()
        if not file_path.exists():
            logger.warning(f"Missing file: {file_path}")
    
    return file_status


def run_integration_tests() -> Dict[str, Any]:
    """Run integration tests to validate performance test infrastructure."""
    backend_root = Path(__file__).parent.parent
    
    # Set up environment
    env = os.environ.copy()
    env["PYTHONPATH"] = str(backend_root)
    env["TEST_DATABASE_HOST"] = env.get("TEST_DATABASE_HOST", "localhost")
    
    # Run integration tests
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_performance_integration.py",
        "-v",
        "--tb=short",
        "-x"  # Stop on first failure
    ]
    
    logger.info(f"Running integration tests: {' '.join(cmd)}")
    
    result = subprocess.run(
        cmd,
        cwd=backend_root,
        env=env,
        capture_output=True,
        text=True
    )
    
    return {
        "return_code": result.returncode,
        "stdout": result.stdout,
        "stderr": result.stderr,
        "success": result.returncode == 0
    }


def run_syntax_check() -> Dict[str, Any]:
    """Run syntax check on performance test files."""
    backend_root = Path(__file__).parent.parent
    
    test_files = [
        "tests/test_performance_benchmarks.py",
        "tests/test_performance_integration.py",
        "scripts/run_performance_benchmark.py"
    ]
    
    results = {}
    
    for test_file in test_files:
        file_path = backend_root / test_file
        if file_path.exists():
            cmd = [sys.executable, "-m", "py_compile", str(file_path)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            results[test_file] = {
                "success": result.returncode == 0,
                "error": result.stderr if result.returncode != 0 else None
            }
        else:
            results[test_file] = {
                "success": False,
                "error": "File not found"
            }
    
    return results


def validate_performance_tests() -> Dict[str, Any]:
    """Run complete validation of performance test suite."""
    logger.info("Validating performance test suite...")
    
    validation_results = {
        "dependencies": check_dependencies(),
        "environment": check_environment(),
        "test_files": check_test_files(),
        "syntax_check": run_syntax_check(),
        "integration_tests": None  # Will be set below
    }
    
    # Check if basic requirements are met
    required_deps = ["pytest", "pytest-asyncio", "psutil", "fastapi", "sqlalchemy"]
    missing_deps = [dep for dep in required_deps if not validation_results["dependencies"][dep]]
    
    if missing_deps:
        logger.error(f"Missing required dependencies: {missing_deps}")
        validation_results["integration_tests"] = {
            "success": False,
            "error": f"Missing dependencies: {missing_deps}"
        }
    else:
        # Run integration tests
        validation_results["integration_tests"] = run_integration_tests()
    
    return validation_results


def print_validation_report(results: Dict[str, Any]):
    """Print a formatted validation report."""
    print("\n" + "="*60)
    print("PERFORMANCE TEST SUITE VALIDATION REPORT")
    print("="*60)
    
    # Dependencies
    print("\n📦 DEPENDENCIES:")
    for dep, available in results["dependencies"].items():
        status = "✅" if available else "❌"
        print(f"  {status} {dep}")
    
    # Environment
    print("\n🌍 ENVIRONMENT:")
    env_info = results["environment"]
    print(f"  Python Version: {env_info['python_version'].split()[0]}")
    print(f"  Virtual Environment: {'✅' if env_info['virtual_env'] else '❌'}")
    print(f"  Test Database Host: {env_info['TEST_DATABASE_HOST']}")
    print(f"  Working Directory: {env_info['working_directory']}")
    
    # Test Files
    print("\n📁 TEST FILES:")
    for file_name, exists in results["test_files"].items():
        status = "✅" if exists else "❌"
        print(f"  {status} {file_name}")
    
    # Syntax Check
    print("\n🔍 SYNTAX CHECK:")
    syntax_results = results["syntax_check"]
    all_syntax_ok = all(result["success"] for result in syntax_results.values())
    print(f"  Overall: {'✅' if all_syntax_ok else '❌'}")
    
    for file_name, result in syntax_results.items():
        status = "✅" if result["success"] else "❌"
        print(f"  {status} {file_name}")
        if not result["success"] and result["error"]:
            print(f"    Error: {result['error']}")
    
    # Integration Tests
    print("\n🧪 INTEGRATION TESTS:")
    integration_result = results["integration_tests"]
    if integration_result is None:
        print("  ⏭️  Skipped (missing dependencies)")
    else:
        status = "✅" if integration_result["success"] else "❌"
        print(f"  {status} Test execution")
        
        if not integration_result["success"]:
            print(f"    Return code: {integration_result['return_code']}")
            if integration_result.get("error"):
                print(f"    Error: {integration_result['error']}")
            if integration_result.get("stderr"):
                print(f"    Stderr: {integration_result['stderr']}")
    
    # Overall Status
    print("\n🎯 OVERALL STATUS:")
    
    # Check if all components are working
    deps_ok = all(results["dependencies"][dep] for dep in ["pytest", "pytest-asyncio", "psutil", "fastapi", "sqlalchemy"])
    files_ok = all(results["test_files"].values())
    syntax_ok = all(result["success"] for result in results["syntax_check"].values())
    integration_ok = results["integration_tests"] and results["integration_tests"]["success"]
    
    overall_status = deps_ok and files_ok and syntax_ok and integration_ok
    
    print(f"  {'✅ READY' if overall_status else '❌ ISSUES FOUND'}")
    
    if not overall_status:
        print("\n🔧 NEXT STEPS:")
        if not deps_ok:
            print("  1. Install missing dependencies: make perf-deps")
        if not files_ok:
            print("  2. Ensure all test files are present")
        if not syntax_ok:
            print("  3. Fix syntax errors in test files")
        if not integration_ok:
            print("  4. Debug integration test failures")
    else:
        print("\n🚀 READY TO RUN PERFORMANCE TESTS:")
        print("  make perf-benchmark       # Run full performance benchmark")
        print("  make perf-benchmark-ci    # Run CI-friendly benchmark")
    
    print("\n" + "="*60)


def main():
    """Main entry point."""
    try:
        results = validate_performance_tests()
        print_validation_report(results)
        
        # Exit with appropriate code
        overall_success = (
            all(results["dependencies"][dep] for dep in ["pytest", "pytest-asyncio", "psutil", "fastapi", "sqlalchemy"]) and
            all(results["test_files"].values()) and
            all(result["success"] for result in results["syntax_check"].values()) and
            results["integration_tests"] and results["integration_tests"]["success"]
        )
        
        sys.exit(0 if overall_success else 1)
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()