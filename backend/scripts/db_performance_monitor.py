#!/usr/bin/env python3
"""
Database performance monitoring script for SIMILE.
Monitors PostgreSQL performance metrics during tests.
"""
import asyncio
import asyncpg
import json
import time
import argparse
import signal
import sys
import os
from datetime import datetime
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabasePerformanceMonitor:
    def __init__(self, connection_string, output_file=None, interval=1):
        self.connection_string = connection_string
        self.output_file = output_file
        self.interval = interval
        self.metrics = []
        self.running = False
        self.start_time = None
        
    async def collect_database_metrics(self):
        """Collect comprehensive database performance metrics."""
        conn = await asyncpg.connect(self.connection_string)
        
        try:
            metrics = {
                'timestamp': datetime.utcnow().isoformat(),
                'elapsed_time': time.time() - self.start_time if self.start_time else 0
            }
            
            # Connection statistics
            connections = await conn.fetch("""
                SELECT 
                    state,
                    count(*) as count
                FROM pg_stat_activity 
                WHERE datname = current_database()
                GROUP BY state
            """)
            
            metrics['connections'] = {}
            total_connections = 0
            for conn_stat in connections:
                state = conn_stat['state'] or 'idle'
                count = conn_stat['count']
                metrics['connections'][state] = count
                total_connections += count
            
            metrics['connections']['total'] = total_connections
            
            # Database size
            db_size = await conn.fetchval("SELECT pg_database_size(current_database())")
            metrics['database_size'] = db_size
            
            # Transaction statistics
            db_stats = await conn.fetchrow("""
                SELECT 
                    xact_commit,
                    xact_rollback,
                    blks_read,
                    blks_hit,
                    tup_returned,
                    tup_fetched,
                    tup_inserted,
                    tup_updated,
                    tup_deleted,
                    conflicts,
                    temp_files,
                    temp_bytes,
                    deadlocks
                FROM pg_stat_database 
                WHERE datname = current_database()
            """)
            
            if db_stats:
                metrics['database_stats'] = dict(db_stats)
                
                # Calculate cache hit ratio
                total_reads = db_stats['blks_read'] + db_stats['blks_hit']
                if total_reads > 0:
                    metrics['cache_hit_ratio'] = db_stats['blks_hit'] / total_reads
                else:
                    metrics['cache_hit_ratio'] = 0
            
            # Table statistics
            table_stats = await conn.fetch("""
                SELECT 
                    schemaname,
                    tablename,
                    seq_scan,
                    seq_tup_read,
                    idx_scan,
                    idx_tup_fetch,
                    n_tup_ins,
                    n_tup_upd,
                    n_tup_del,
                    n_tup_hot_upd,
                    n_live_tup,
                    n_dead_tup,
                    vacuum_count,
                    autovacuum_count,
                    analyze_count,
                    autoanalyze_count
                FROM pg_stat_user_tables
                ORDER BY n_live_tup DESC
            """)
            
            metrics['table_stats'] = [dict(stat) for stat in table_stats]
            
            # Index statistics
            index_stats = await conn.fetch("""
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    idx_scan,
                    idx_tup_read,
                    idx_tup_fetch
                FROM pg_stat_user_indexes
                WHERE idx_scan > 0
                ORDER BY idx_scan DESC
                LIMIT 10
            """)
            
            metrics['index_stats'] = [dict(stat) for stat in index_stats]
            
            # Query statistics (requires pg_stat_statements extension)
            try:
                query_stats = await conn.fetch("""
                    SELECT 
                        query,
                        calls,
                        total_exec_time,
                        mean_exec_time,
                        stddev_exec_time,
                        rows,
                        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
                    FROM pg_stat_statements 
                    WHERE query NOT LIKE '%pg_stat%' 
                    ORDER BY total_exec_time DESC
                    LIMIT 10
                """)
                
                metrics['query_stats'] = [dict(stat) for stat in query_stats]
                
            except asyncpg.exceptions.UndefinedTableError:
                logger.debug("pg_stat_statements not available")
                metrics['query_stats'] = []
            
            # Lock statistics
            locks = await conn.fetch("""
                SELECT 
                    mode,
                    granted,
                    count(*) as count
                FROM pg_locks 
                WHERE pid != pg_backend_pid()
                GROUP BY mode, granted
                ORDER BY count DESC
            """)
            
            metrics['locks'] = [dict(lock) for lock in locks]
            
            # Long running queries
            long_queries = await conn.fetch("""
                SELECT 
                    pid,
                    state,
                    query_start,
                    state_change,
                    wait_event_type,
                    wait_event,
                    query
                FROM pg_stat_activity 
                WHERE 
                    state = 'active' 
                    AND query_start < NOW() - INTERVAL '1 second'
                    AND pid != pg_backend_pid()
                ORDER BY query_start
            """)
            
            metrics['long_queries'] = []
            for query in long_queries:
                metrics['long_queries'].append({
                    'pid': query['pid'],
                    'state': query['state'],
                    'duration': (datetime.utcnow() - query['query_start']).total_seconds(),
                    'wait_event_type': query['wait_event_type'],
                    'wait_event': query['wait_event'],
                    'query': query['query'][:200] + '...' if len(query['query']) > 200 else query['query']
                })
            
            # Replication lag (if applicable)
            try:
                replication_lag = await conn.fetchval("""
                    SELECT 
                        CASE 
                            WHEN pg_last_wal_receive_lsn() = pg_last_wal_replay_lsn() THEN 0
                            ELSE EXTRACT(EPOCH FROM NOW() - pg_last_xact_replay_timestamp())
                        END AS replication_lag
                """)
                metrics['replication_lag'] = replication_lag
            except:
                metrics['replication_lag'] = None
            
            # WAL statistics
            try:
                wal_stats = await conn.fetchrow("""
                    SELECT 
                        wal_records,
                        wal_fpi,
                        wal_bytes,
                        wal_buffers_full,
                        wal_write,
                        wal_sync,
                        wal_write_time,
                        wal_sync_time
                    FROM pg_stat_wal
                """)
                
                if wal_stats:
                    metrics['wal_stats'] = dict(wal_stats)
                    
            except asyncpg.exceptions.UndefinedTableError:
                logger.debug("pg_stat_wal not available (PostgreSQL < 14)")
                metrics['wal_stats'] = {}
            
            return metrics
            
        finally:
            await conn.close()
    
    async def monitor_loop(self):
        """Main monitoring loop."""
        self.start_time = time.time()
        self.running = True
        
        logger.info(f"Starting database performance monitoring (interval: {self.interval}s)")
        
        while self.running:
            try:
                metrics = await self.collect_database_metrics()
                self.metrics.append(metrics)
                
                # Log periodic summary
                if len(self.metrics) % 30 == 0:  # Every 30 seconds
                    connections = metrics.get('connections', {})
                    cache_ratio = metrics.get('cache_hit_ratio', 0)
                    logger.info(f"Collected {len(self.metrics)} DB metric samples. "
                              f"Connections: {connections.get('total', 0)}, "
                              f"Cache hit ratio: {cache_ratio:.3f}")
                
                await asyncio.sleep(self.interval)
                
            except Exception as e:
                logger.error(f"Error collecting database metrics: {e}")
                await asyncio.sleep(self.interval)
    
    def stop(self):
        """Stop monitoring."""
        self.running = False
        logger.info("Stopping database performance monitoring")
    
    def save_metrics(self, output_file=None):
        """Save collected metrics to file."""
        if not output_file:
            output_file = self.output_file
        
        if not output_file:
            output_file = f"db_performance_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Calculate summary statistics
        if self.metrics:
            summary = self.calculate_summary()
        else:
            summary = {}
        
        output_data = {
            'monitoring_info': {
                'start_time': self.start_time,
                'end_time': time.time(),
                'duration': time.time() - self.start_time if self.start_time else 0,
                'interval': self.interval,
                'sample_count': len(self.metrics)
            },
            'summary': summary,
            'metrics': self.metrics
        }
        
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        logger.info(f"Database performance metrics saved to {output_path}")
        return output_path
    
    def calculate_summary(self):
        """Calculate summary statistics from collected metrics."""
        if not self.metrics:
            return {}
        
        import statistics
        
        # Extract time series data
        total_connections = [m.get('connections', {}).get('total', 0) for m in self.metrics]
        active_connections = [m.get('connections', {}).get('active', 0) for m in self.metrics]
        cache_hit_ratios = [m.get('cache_hit_ratio', 0) for m in self.metrics if m.get('cache_hit_ratio') is not None]
        
        summary = {
            'connections': {
                'total': {
                    'mean': statistics.mean(total_connections),
                    'max': max(total_connections),
                    'min': min(total_connections)
                },
                'active': {
                    'mean': statistics.mean(active_connections),
                    'max': max(active_connections),
                    'min': min(active_connections)
                }
            }
        }
        
        if cache_hit_ratios:
            summary['cache_hit_ratio'] = {
                'mean': statistics.mean(cache_hit_ratios),
                'min': min(cache_hit_ratios),
                'max': max(cache_hit_ratios)
            }
        
        # Database size growth
        db_sizes = [m.get('database_size', 0) for m in self.metrics if m.get('database_size')]
        if db_sizes:
            summary['database_size'] = {
                'start': db_sizes[0],
                'end': db_sizes[-1],
                'growth': db_sizes[-1] - db_sizes[0],
                'max': max(db_sizes)
            }
        
        # Transaction statistics
        if self.metrics and 'database_stats' in self.metrics[0]:
            first_stats = self.metrics[0]['database_stats']
            last_stats = self.metrics[-1]['database_stats']
            
            summary['transactions'] = {
                'commits': last_stats.get('xact_commit', 0) - first_stats.get('xact_commit', 0),
                'rollbacks': last_stats.get('xact_rollback', 0) - first_stats.get('xact_rollback', 0),
                'tuples_inserted': last_stats.get('tup_inserted', 0) - first_stats.get('tup_inserted', 0),
                'tuples_updated': last_stats.get('tup_updated', 0) - first_stats.get('tup_updated', 0),
                'tuples_deleted': last_stats.get('tup_deleted', 0) - first_stats.get('tup_deleted', 0)
            }
        
        # Long running queries
        long_query_counts = [len(m.get('long_queries', [])) for m in self.metrics]
        if long_query_counts:
            summary['long_queries'] = {
                'max_count': max(long_query_counts),
                'avg_count': statistics.mean(long_query_counts)
            }
        
        return summary

# Global monitor instance for signal handling
monitor = None

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    global monitor
    if monitor:
        monitor.stop()
    sys.exit(0)

async def main():
    global monitor
    
    parser = argparse.ArgumentParser(description="Database performance monitoring for SIMILE")
    parser.add_argument("--output", type=str, help="Output file for metrics")
    parser.add_argument("--interval", type=float, default=1.0, 
                       help="Monitoring interval in seconds (default: 1.0)")
    parser.add_argument("--duration", type=float, help="Maximum monitoring duration in seconds")
    parser.add_argument("--connection-string", type=str, 
                       help="PostgreSQL connection string")
    
    args = parser.parse_args()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Determine connection string
    if args.connection_string:
        connection_string = args.connection_string
    else:
        # Use environment variables
        TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        connection_string = f"postgresql://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"
    
    # Create monitor
    monitor = DatabasePerformanceMonitor(
        connection_string=connection_string,
        output_file=args.output,
        interval=args.interval
    )
    
    try:
        # Test connection
        conn = await asyncpg.connect(connection_string)
        await conn.close()
        logger.info(f"Connected to database successfully")
        
        # Start monitoring
        if args.duration:
            # Run for specified duration
            monitor_task = asyncio.create_task(monitor.monitor_loop())
            await asyncio.sleep(args.duration)
            monitor.stop()
            await monitor_task
        else:
            # Run until interrupted
            await monitor.monitor_loop()
    
    except asyncpg.exceptions.InvalidCatalogNameError:
        logger.error("Database does not exist. Please create the test database first.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        sys.exit(1)
    finally:
        # Save metrics
        if monitor and monitor.metrics:
            output_path = monitor.save_metrics()
            print(f"Database performance metrics saved to: {output_path}")
        else:
            print("No metrics collected")

if __name__ == "__main__":
    asyncio.run(main())