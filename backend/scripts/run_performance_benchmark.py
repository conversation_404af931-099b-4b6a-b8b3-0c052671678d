#!/usr/bin/env python3
"""
Performance benchmark runner for SIMILE backend.
Executes performance tests and generates comprehensive reports.
"""
import os
import sys
import json
import time
import subprocess
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import argparse

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PerformanceBenchmarkRunner:
    """Runs performance benchmarks and generates reports."""
    
    def __init__(self, output_dir: str = "performance_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def run_benchmark_tests(self, test_pattern: str = "test_performance_benchmarks.py") -> Dict[str, Any]:
        """Run performance benchmark tests and collect results."""
        logger.info(f"Running performance benchmark tests: {test_pattern}")
        
        # Ensure test environment is ready
        self._ensure_test_environment()
        
        # Run pytest with performance markers
        cmd = [
            sys.executable, "-m", "pytest",
            f"tests/{test_pattern}",
            "-v",
            "--tb=short",
            "--durations=10",
            "--json-report",
            f"--json-report-file={self.output_dir}/performance_test_results_{self.timestamp}.json",
            "-m", "performance or not slow"
        ]
        
        logger.info(f"Executing: {' '.join(cmd)}")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent.parent)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Parse results
        results = {
            "timestamp": self.timestamp,
            "execution_time_seconds": execution_time,
            "return_code": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "success": result.returncode == 0
        }
        
        # Try to load JSON report if available
        json_report_path = self.output_dir / f"performance_test_results_{self.timestamp}.json"
        if json_report_path.exists():
            try:
                with open(json_report_path, 'r') as f:
                    results["json_report"] = json.load(f)
            except Exception as e:
                logger.warning(f"Could not load JSON report: {e}")
        
        return results
    
    def _ensure_test_environment(self):
        """Ensure test environment is properly set up."""
        # Check if test database is available
        test_db_host = os.getenv("TEST_DATABASE_HOST", "localhost")
        logger.info(f"Using test database host: {test_db_host}")
        
        # Check if virtual environment is active
        if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            logger.warning("Virtual environment may not be active")
        
        # Check if required packages are installed
        required_packages = ["pytest", "pytest-asyncio", "psutil", "fastapi", "sqlalchemy"]
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"Missing required packages: {missing_packages}")
            raise RuntimeError(f"Missing packages: {missing_packages}")
    
    def generate_performance_report(self, results: Dict[str, Any]) -> str:
        """Generate a comprehensive performance report."""
        report_path = self.output_dir / f"performance_report_{self.timestamp}.md"
        
        with open(report_path, 'w') as f:
            f.write(f"# SIMILE Backend Performance Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Test Run:** {self.timestamp}\n\n")
            
            # Executive Summary
            f.write("## Executive Summary\n\n")
            f.write(f"- **Test Status:** {'PASSED' if results['success'] else 'FAILED'}\n")
            f.write(f"- **Execution Time:** {results['execution_time_seconds']:.2f} seconds\n")
            f.write(f"- **Return Code:** {results['return_code']}\n\n")
            
            # Test Results Summary
            if "json_report" in results:
                json_report = results["json_report"]
                summary = json_report.get("summary", {})
                
                f.write("## Test Results Summary\n\n")
                f.write(f"- **Total Tests:** {summary.get('total', 0)}\n")
                f.write(f"- **Passed:** {summary.get('passed', 0)}\n")
                f.write(f"- **Failed:** {summary.get('failed', 0)}\n")
                f.write(f"- **Skipped:** {summary.get('skipped', 0)}\n")
                f.write(f"- **Errors:** {summary.get('error', 0)}\n\n")
                
                # Performance Metrics
                f.write("## Performance Metrics\n\n")
                
                # Parse test durations
                if "durations" in json_report:
                    f.write("### Test Execution Times\n\n")
                    f.write("| Test | Duration (seconds) |\n")
                    f.write("|------|-------------------|\n")
                    
                    for test_info in json_report["durations"]:
                        test_name = test_info.get("name", "Unknown")
                        duration = test_info.get("duration", 0)
                        f.write(f"| {test_name} | {duration:.3f} |\n")
                    f.write("\n")
                
                # Test Details
                f.write("## Test Details\n\n")
                
                tests = json_report.get("tests", [])
                for test in tests:
                    test_name = test.get("name", "Unknown")
                    outcome = test.get("outcome", "unknown")
                    duration = test.get("duration", 0)
                    
                    f.write(f"### {test_name}\n\n")
                    f.write(f"- **Outcome:** {outcome}\n")
                    f.write(f"- **Duration:** {duration:.3f} seconds\n")
                    
                    if outcome == "failed":
                        f.write(f"- **Error:** {test.get('call', {}).get('longrepr', 'No error details')}\n")
                    
                    f.write("\n")
            
            # Console Output
            f.write("## Console Output\n\n")
            f.write("### Standard Output\n\n")
            f.write("```\n")
            f.write(results['stdout'])
            f.write("\n```\n\n")
            
            if results['stderr']:
                f.write("### Standard Error\n\n")
                f.write("```\n")
                f.write(results['stderr'])
                f.write("\n```\n\n")
            
            # Recommendations
            f.write("## Performance Recommendations\n\n")
            
            if results['success']:
                f.write("✅ **All performance tests passed successfully!**\n\n")
                f.write("### Recommendations:\n")
                f.write("- Continue monitoring performance metrics in CI/CD\n")
                f.write("- Consider adding more edge case performance tests\n")
                f.write("- Monitor memory usage patterns in production\n")
                f.write("- Set up performance alerts for production metrics\n")
            else:
                f.write("❌ **Some performance tests failed.**\n\n")
                f.write("### Immediate Actions Required:\n")
                f.write("- Review failed test details above\n")
                f.write("- Investigate performance bottlenecks\n")
                f.write("- Consider optimizing database queries\n")
                f.write("- Review application resource usage\n")
                f.write("- Check for memory leaks or excessive CPU usage\n")
            
            # Environment Information
            f.write("\n## Environment Information\n\n")
            f.write(f"- **Python Version:** {sys.version}\n")
            f.write(f"- **Working Directory:** {os.getcwd()}\n")
            f.write(f"- **Test Database Host:** {os.getenv('TEST_DATABASE_HOST', 'localhost')}\n")
            f.write(f"- **Virtual Environment:** {'Active' if hasattr(sys, 'real_prefix') else 'Not detected'}\n")
        
        logger.info(f"Performance report generated: {report_path}")
        return str(report_path)
    
    def generate_json_summary(self, results: Dict[str, Any]) -> str:
        """Generate a JSON summary for CI/CD integration."""
        summary_path = self.output_dir / f"performance_summary_{self.timestamp}.json"
        
        # Extract key metrics
        summary = {
            "timestamp": self.timestamp,
            "success": results["success"],
            "execution_time_seconds": results["execution_time_seconds"],
            "return_code": results["return_code"]
        }
        
        # Add test summary if available
        if "json_report" in results:
            json_report = results["json_report"]
            test_summary = json_report.get("summary", {})
            summary.update({
                "total_tests": test_summary.get("total", 0),
                "passed_tests": test_summary.get("passed", 0),
                "failed_tests": test_summary.get("failed", 0),
                "skipped_tests": test_summary.get("skipped", 0),
                "error_tests": test_summary.get("error", 0)
            })
            
            # Add performance metrics
            if "durations" in json_report:
                durations = [d["duration"] for d in json_report["durations"]]
                if durations:
                    summary["performance_metrics"] = {
                        "avg_test_duration": sum(durations) / len(durations),
                        "max_test_duration": max(durations),
                        "min_test_duration": min(durations),
                        "total_test_duration": sum(durations)
                    }
        
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"Performance summary generated: {summary_path}")
        return str(summary_path)
    
    def run_full_benchmark(self, test_pattern: str = "test_performance_benchmarks.py") -> Dict[str, str]:
        """Run complete benchmark suite and generate all reports."""
        logger.info("Starting full performance benchmark run")
        
        # Run tests
        results = self.run_benchmark_tests(test_pattern)
        
        # Generate reports
        report_path = self.generate_performance_report(results)
        summary_path = self.generate_json_summary(results)
        
        # Log summary
        if results["success"]:
            logger.info("✅ Performance benchmark completed successfully")
        else:
            logger.error("❌ Performance benchmark failed")
            
        logger.info(f"Reports generated in: {self.output_dir}")
        
        return {
            "report_path": report_path,
            "summary_path": summary_path,
            "success": results["success"]
        }


def main():
    """Main entry point for performance benchmark runner."""
    parser = argparse.ArgumentParser(description="Run SIMILE backend performance benchmarks")
    parser.add_argument(
        "--test-pattern", 
        default="test_performance_benchmarks.py",
        help="Test file pattern to run (default: test_performance_benchmarks.py)"
    )
    parser.add_argument(
        "--output-dir", 
        default="performance_reports",
        help="Output directory for reports (default: performance_reports)"
    )
    parser.add_argument(
        "--json-only", 
        action="store_true",
        help="Only generate JSON summary (for CI/CD integration)"
    )
    
    args = parser.parse_args()
    
    try:
        runner = PerformanceBenchmarkRunner(args.output_dir)
        
        if args.json_only:
            # Run tests and generate JSON summary only
            results = runner.run_benchmark_tests(args.test_pattern)
            summary_path = runner.generate_json_summary(results)
            print(f"JSON summary: {summary_path}")
            
            # Exit with appropriate code
            sys.exit(0 if results["success"] else 1)
        else:
            # Run full benchmark suite
            report_results = runner.run_full_benchmark(args.test_pattern)
            
            print(f"Performance report: {report_results['report_path']}")
            print(f"JSON summary: {report_results['summary_path']}")
            
            # Exit with appropriate code
            sys.exit(0 if report_results["success"] else 1)
            
    except Exception as e:
        logger.error(f"Benchmark runner failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()