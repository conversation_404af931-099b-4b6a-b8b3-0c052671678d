#!/usr/bin/env python3
"""
Test Database Isolation Validation Script

This script validates that tests are properly isolated and using the correct database.
It checks configuration, environment variables, and database connectivity.
"""
import asyncio
import os
import sys
from pathlib import Path
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.config import Settings, get_settings


async def validate_database_connectivity(database_url: str, database_name: str):
    """Validate connectivity to a specific database."""
    try:
        engine = create_async_engine(database_url)
        async with engine.connect() as conn:
            result = await conn.execute(text("SELECT current_database(), version()"))
            row = result.fetchone()
            print(f"✅ {database_name}: Connected successfully")
            print(f"   Database: {row[0]}")
            print(f"   Version: {row[1][:50]}...")
            
            # Check if tables exist
            tables_result = await conn.execute(text("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """))
            tables = [row[0] for row in tables_result]
            print(f"   Tables: {', '.join(tables) if tables else 'None'}")
            
            if 'entities' in tables:
                entities_result = await conn.execute(text("SELECT COUNT(*) FROM entities"))
                entities_count = entities_result.scalar()
                print(f"   Entities: {entities_count}")
        
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ {database_name}: Connection failed - {e}")
        return False


async def validate_test_isolation():
    """Comprehensive test isolation validation."""
    print("🔍 SIMILE Test Isolation Validation")
    print("=" * 50)
    print()
    
    # Check environment variables
    print("1. Environment Variables:")
    test_host = os.getenv("TEST_DATABASE_HOST")
    database_url = os.getenv("DATABASE_URL")
    
    print(f"   TEST_DATABASE_HOST: {test_host or 'NOT SET'}")
    print(f"   DATABASE_URL: {database_url or 'NOT SET'}")
    print(f"   PYTHONPATH: {os.getenv('PYTHONPATH', 'NOT SET')}")
    print()
    
    # Check settings configuration
    print("2. Settings Configuration:")
    
    # Test without TEST_DATABASE_HOST
    if "TEST_DATABASE_HOST" in os.environ:
        del os.environ["TEST_DATABASE_HOST"]
    
    settings_production = Settings()
    print(f"   Production settings: {settings_production.database_url}")
    
    # Test with TEST_DATABASE_HOST
    os.environ["TEST_DATABASE_HOST"] = "localhost"
    settings_test = Settings()
    print(f"   Test settings: {settings_test.database_url}")
    
    # Test get_settings function
    settings_function = get_settings()
    print(f"   get_settings(): {settings_function.database_url}")
    print()
    
    # Validate database connectivity
    print("3. Database Connectivity:")
    
    # Main database
    main_db_url = "postgresql+asyncpg://postgres:postgres@localhost:5432/simile"
    main_db_ok = await validate_database_connectivity(main_db_url, "Main Database (simile)")
    
    # Test database
    test_db_url = "postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test"
    test_db_ok = await validate_database_connectivity(test_db_url, "Test Database (simile_test)")
    
    print()
    
    # Check for contamination
    print("4. Contamination Check:")
    if main_db_ok:
        try:
            engine = create_async_engine(main_db_url)
            async with engine.connect() as conn:
                # Check for test entity patterns
                test_patterns_query = text("""
                    SELECT COUNT(*) FROM entities 
                    WHERE name LIKE '%Test%' 
                       OR name LIKE '%Cleanup%'
                       OR name LIKE '%Cleanu W D%'
                       OR name LIKE '%ConnectionTest%'
                       OR name LIKE '%ConcurrentTest%'
                """)
                contamination_result = await conn.execute(test_patterns_query)
                contamination_count = contamination_result.scalar()
                
                if contamination_count > 0:
                    print(f"   ⚠️  CONTAMINATION DETECTED: {contamination_count} test entities in main database!")
                    
                    # Show examples
                    examples_query = text("""
                        SELECT name FROM entities 
                        WHERE name LIKE '%Test%' 
                           OR name LIKE '%Cleanup%'
                           OR name LIKE '%Cleanu W D%'
                           OR name LIKE '%ConnectionTest%'
                           OR name LIKE '%ConcurrentTest%'
                        LIMIT 5
                    """)
                    examples_result = await conn.execute(examples_query)
                    print("   Examples:")
                    for row in examples_result:
                        print(f"     - {row[0]}")
                else:
                    print("   ✅ Main database is clean (no test entities found)")
            
            await engine.dispose()
            
        except Exception as e:
            print(f"   ❌ Could not check contamination: {e}")
    
    print()
    
    # Summary and recommendations
    print("5. Summary and Recommendations:")
    
    if not test_db_ok:
        print("   ❌ Test database is not accessible")
        print("   📋 Action: Create test database or check connection")
    
    if main_db_ok and not test_db_ok:
        print("   ⚠️  Only main database is accessible - tests may contaminate production data")
    
    if contamination_count > 0:
        print("   ❌ Main database is contaminated with test data")
        print("   📋 Action: Run cleanup script to remove test entities")
        print("   📋 Command: python scripts/cleanup_main_database.py")
    
    if test_db_ok and main_db_ok and contamination_count == 0:
        print("   ✅ Test isolation appears to be working correctly")
    
    print()
    print("6. Test Isolation Checklist:")
    print("   □ Set TEST_DATABASE_HOST=localhost when running tests")
    print("   □ Ensure simile_test database exists")
    print("   □ Use get_settings() function instead of global settings")
    print("   □ Run this validation before and after test runs")
    print("   □ Monitor main database for test contamination")


if __name__ == "__main__":
    asyncio.run(validate_test_isolation())