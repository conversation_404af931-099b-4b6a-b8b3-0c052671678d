#!/usr/bin/env python3
"""
Script to ensure the test database exists before running tests.
Can be run manually or as part of a test setup process.
"""
import asyncio
import os
import sys
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
ADMIN_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/postgres"


async def create_test_database():
    """Create the test database if it doesn't exist."""
    engine = create_async_engine(ADMIN_DATABASE_URL, isolation_level="AUTOCOMMIT")
    
    try:
        async with engine.connect() as conn:
            # Check if database exists
            result = await conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = 'simile_test'")
            )
            exists = result.scalar() is not None
            
            if not exists:
                logger.info("Creating simile_test database...")
                await conn.execute(text("CREATE DATABASE simile_test"))
                logger.info("simile_test database created successfully")
            else:
                logger.info("simile_test database already exists")
                
    except Exception as e:
        logger.error(f"Error creating test database: {e}")
        sys.exit(1)
    finally:
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(create_test_database())