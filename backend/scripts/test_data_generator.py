#!/usr/bin/env python3
"""
Advanced test data generator for SIMILE performance testing.
Generates realistic entities, connections, and graph structures for stress testing.
"""
import asyncio
import asyncpg
import random
import faker
import uuid
import json
import argparse
from datetime import datetime
from pathlib import Path
import sys
import os

# Add backend to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestDataGenerator:
    def __init__(self, connection_string):
        self.connection_string = connection_string
        self.fake = faker.Faker()
        self.units = ["length", "mass", "time", "volume", "area", "count"]
        
    async def generate_entities(self, count=100):
        """Generate test entities with realistic names."""
        entities = []
        
        # Predefined categories for realistic names
        categories = {
            "length": ["Bridge", "Tower", "Building", "Road", "River", "Cable", "Pipe", "Beam", "Track", "Rail"],
            "mass": ["Vehicle", "Aircraft", "Ship", "Package", "Container", "Truck", "Train", "Cargo", "Load", "Weight"],
            "time": ["Process", "Journey", "Task", "Event", "Duration", "Period", "Phase", "Cycle", "Schedule", "Timer"],
            "volume": ["Tank", "Pool", "Reservoir", "Container", "Lake", "Vessel", "Bottle", "Barrel", "Storage", "Chamber"],
            "area": ["Field", "Park", "City", "Country", "Forest", "Zone", "Region", "Territory", "District", "Sector"],
            "count": ["Population", "Items", "Units", "Batch", "Group", "Collection", "Set", "Series", "Lot", "Bundle"]
        }
        
        conn = await asyncpg.connect(self.connection_string)
        try:
            for i in range(count):
                unit = random.choice(self.units)
                category = random.choice(categories[unit])
                
                # Generate more realistic names
                if i % 3 == 0:
                    name = f"{category}_{self.fake.city().replace(' ', '_')}"[:20]
                elif i % 3 == 1:
                    name = f"{category}_{self.fake.word().capitalize()}"[:20]
                else:
                    name = f"{category}_{random.randint(1, 999)}"[:20]
                
                try:
                    entity_id = await conn.fetchval(
                        "INSERT INTO entities (name, unit_id) VALUES ($1, $2) RETURNING id",
                        name, unit
                    )
                    entities.append({
                        "id": entity_id,
                        "name": name,
                        "unit_id": unit
                    })
                except asyncpg.exceptions.UniqueViolationError:
                    # Skip duplicate names
                    continue
                    
        finally:
            await conn.close()
            
        return entities
    
    async def generate_connections(self, entities, connection_count=500):
        """Generate realistic connections between entities."""
        connections = []
        conn = await asyncpg.connect(self.connection_string)
        
        try:
            # Group entities by unit
            entities_by_unit = {}
            for entity in entities:
                unit = entity["unit_id"]
                if unit not in entities_by_unit:
                    entities_by_unit[unit] = []
                entities_by_unit[unit].append(entity)
            
            # Generate connections within same unit
            created_connections = 0
            attempts = 0
            max_attempts = connection_count * 3
            
            while created_connections < connection_count and attempts < max_attempts:
                attempts += 1
                
                # Choose a unit with enough entities
                available_units = [u for u, entities in entities_by_unit.items() if len(entities) >= 2]
                if not available_units:
                    break
                    
                unit = random.choice(available_units)
                from_entity, to_entity = random.sample(entities_by_unit[unit], 2)
                
                # Generate realistic connection values based on unit type
                value_ranges = {
                    "length": (0.1, 1000.0),
                    "mass": (0.1, 10000.0),
                    "time": (0.1, 3600.0),
                    "volume": (0.1, 1000000.0),
                    "area": (0.1, 1000000.0),
                    "count": (1.0, 10000.0)
                }
                
                min_val, max_val = value_ranges.get(unit, (0.1, 100.0))
                
                # Use weighted random for more realistic distributions
                if random.random() < 0.7:  # 70% of values in lower range
                    connection_value = round(random.uniform(min_val, min_val + (max_val - min_val) * 0.3), 1)
                else:  # 30% in higher range
                    connection_value = round(random.uniform(min_val + (max_val - min_val) * 0.3, max_val), 1)
                
                try:
                    conn_id = await conn.fetchval("""
                        INSERT INTO connections (from_entity_id, to_entity_id, connection_value)
                        VALUES ($1, $2, $3)
                        ON CONFLICT (from_entity_id, to_entity_id) DO NOTHING
                        RETURNING id
                    """, from_entity["id"], to_entity["id"], connection_value)
                    
                    if conn_id:
                        connections.append({
                            "id": conn_id,
                            "from_entity_id": from_entity["id"],
                            "to_entity_id": to_entity["id"],
                            "connection_value": connection_value
                        })
                        created_connections += 1
                        
                except Exception as e:
                    print(f"Failed to create connection: {e}")
                    
        finally:
            await conn.close()
            
        return connections
    
    async def generate_complex_graph(self, num_clusters=5, entities_per_cluster=20):
        """Generate a complex graph structure for pathfinding tests."""
        all_entities = []
        all_connections = []
        
        print(f"Generating complex graph: {num_clusters} clusters, {entities_per_cluster} entities each")
        
        # Generate clusters
        for cluster_id in range(num_clusters):
            print(f"Creating cluster {cluster_id + 1}/{num_clusters}")
            
            # Generate entities for this cluster
            cluster_entities = await self.generate_entities(entities_per_cluster)
            
            # Create dense connections within cluster
            cluster_connections = await self.generate_connections(
                cluster_entities, 
                connection_count=min(entities_per_cluster * 3, len(cluster_entities) * (len(cluster_entities) - 1) // 4)
            )
            
            # Add bridge connections to previous cluster
            if cluster_id > 0 and all_entities:
                bridge_count = random.randint(2, 5)
                print(f"Creating {bridge_count} bridge connections to previous cluster")
                
                for _ in range(bridge_count):
                    from_entity = random.choice(cluster_entities)
                    # Get entities from previous cluster
                    prev_cluster_start = (cluster_id - 1) * entities_per_cluster
                    prev_cluster_end = cluster_id * entities_per_cluster
                    prev_cluster_entities = all_entities[prev_cluster_start:prev_cluster_end]
                    
                    if prev_cluster_entities:
                        to_entity = random.choice(prev_cluster_entities)
                        
                        # Only create connection if same unit
                        if from_entity["unit_id"] == to_entity["unit_id"]:
                            try:
                                conn = await asyncpg.connect(self.connection_string)
                                try:
                                    conn_id = await conn.fetchval("""
                                        INSERT INTO connections (from_entity_id, to_entity_id, connection_value)
                                        VALUES ($1, $2, $3)
                                        ON CONFLICT (from_entity_id, to_entity_id) DO NOTHING
                                        RETURNING id
                                    """, from_entity["id"], to_entity["id"], round(random.uniform(1.0, 10.0), 1))
                                    
                                    if conn_id:
                                        all_connections.append({
                                            "id": conn_id,
                                            "from_entity_id": from_entity["id"],
                                            "to_entity_id": to_entity["id"],
                                            "connection_value": round(random.uniform(1.0, 10.0), 1)
                                        })
                                finally:
                                    await conn.close()
                            except Exception as e:
                                print(f"Failed to create bridge connection: {e}")
            
            all_entities.extend(cluster_entities)
            all_connections.extend(cluster_connections)
            
        print(f"Generated {len(all_entities)} entities and {len(all_connections)} connections")
        return all_entities, all_connections
    
    async def generate_pathfinding_test_data(self, path_length=6):
        """Generate specific data for pathfinding performance tests."""
        print(f"Generating pathfinding test data with max path length {path_length}")
        
        # Create a chain of entities for guaranteed paths
        chain_entities = []
        conn = await asyncpg.connect(self.connection_string)
        
        try:
            # Create chain of entities in same unit
            unit = random.choice(self.units)
            
            for i in range(path_length + 2):  # +2 for start and end
                entity_id = await conn.fetchval(
                    "INSERT INTO entities (name, unit_id) VALUES ($1, $2) RETURNING id",
                    f"PathTest_{unit}_{i}", unit
                )
                chain_entities.append(entity_id)
            
            # Create connections along the chain
            chain_connections = []
            for i in range(len(chain_entities) - 1):
                conn_id = await conn.fetchval("""
                    INSERT INTO connections (from_entity_id, to_entity_id, connection_value)
                    VALUES ($1, $2, $3)
                    RETURNING id
                """, chain_entities[i], chain_entities[i + 1], round(random.uniform(1.0, 5.0), 1))
                chain_connections.append(conn_id)
            
            # Add some branching paths for complexity
            branch_entities = []
            for i in range(path_length):
                branch_entity_id = await conn.fetchval(
                    "INSERT INTO entities (name, unit_id) VALUES ($1, $2) RETURNING id",
                    f"PathBranch_{unit}_{i}", unit
                )
                branch_entities.append(branch_entity_id)
                
                # Connect to random point in main chain
                chain_point = random.choice(chain_entities[:-1])  # Not the last one
                await conn.execute("""
                    INSERT INTO connections (from_entity_id, to_entity_id, connection_value)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (from_entity_id, to_entity_id) DO NOTHING
                """, chain_point, branch_entity_id, round(random.uniform(1.0, 5.0), 1))
            
            print(f"Created pathfinding test data: {len(chain_entities)} chain entities, {len(branch_entities)} branch entities")
            return chain_entities, branch_entities
            
        finally:
            await conn.close()
    
    async def cleanup_test_data(self):
        """Clean up all generated test data."""
        conn = await asyncpg.connect(self.connection_string)
        try:
            # Delete in order of dependencies
            await conn.execute("DELETE FROM connections")
            await conn.execute("DELETE FROM entities WHERE name LIKE '%Test%' OR name LIKE '%PathTest%' OR name LIKE '%PathBranch%'")
            print("Cleaned up test data")
        finally:
            await conn.close()
    
    async def get_stats(self):
        """Get current database statistics."""
        conn = await asyncpg.connect(self.connection_string)
        try:
            stats = {}
            for table in ['entities', 'connections']:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                stats[table] = count
            return stats
        finally:
            await conn.close()

async def main():
    parser = argparse.ArgumentParser(description="Generate test data for SIMILE performance testing")
    parser.add_argument("--entities", type=int, default=100, help="Number of entities to generate")
    parser.add_argument("--connections", type=int, default=500, help="Number of connections to generate")
    parser.add_argument("--complexity", choices=["simple", "moderate", "complex"], default="simple",
                       help="Complexity level of generated data")
    parser.add_argument("--output", type=str, help="Output file for generated data metadata")
    parser.add_argument("--cleanup", action="store_true", help="Clean up existing test data")
    parser.add_argument("--pathfinding", action="store_true", help="Generate pathfinding test data")
    parser.add_argument("--stats", action="store_true", help="Show current database statistics")
    
    args = parser.parse_args()
    
    # Database connection
    TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
    connection_string = f"postgresql://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"
    
    generator = TestDataGenerator(connection_string)
    
    try:
        if args.cleanup:
            await generator.cleanup_test_data()
            return
        
        if args.stats:
            stats = await generator.get_stats()
            print("Current database statistics:")
            for table, count in stats.items():
                print(f"  {table}: {count} records")
            return
        
        # Adjust parameters based on complexity
        if args.complexity == "moderate":
            entities_count = args.entities * 2
            connections_count = args.connections * 3
        elif args.complexity == "complex":
            entities_count = args.entities * 5
            connections_count = args.connections * 10
        else:
            entities_count = args.entities
            connections_count = args.connections
        
        print(f"Generating {args.complexity} test data...")
        print(f"Target: {entities_count} entities, {connections_count} connections")
        
        start_time = datetime.utcnow()
        
        if args.pathfinding:
            chain_entities, branch_entities = await generator.generate_pathfinding_test_data()
            entities = chain_entities + branch_entities
            connections = []
        elif args.complexity == "complex":
            entities, connections = await generator.generate_complex_graph(
                num_clusters=5, 
                entities_per_cluster=entities_count // 5
            )
        else:
            entities = await generator.generate_entities(entities_count)
            connections = await generator.generate_connections(entities, connections_count)
        
        end_time = datetime.utcnow()
        generation_time = (end_time - start_time).total_seconds()
        
        # Get final statistics
        final_stats = await generator.get_stats()
        
        metadata = {
            "generation_time": generation_time,
            "complexity": args.complexity,
            "generated_entities": len(entities) if isinstance(entities, list) else entities_count,
            "generated_connections": len(connections) if isinstance(connections, list) else connections_count,
            "final_stats": final_stats,
            "timestamp": end_time.isoformat()
        }
        
        if args.output:
            output_path = Path(args.output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            print(f"Metadata saved to {output_path}")
        
        print(f"\nGeneration complete!")
        print(f"Time taken: {generation_time:.2f} seconds")
        print(f"Final database stats: {final_stats}")
        
    except Exception as e:
        print(f"Error generating test data: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())