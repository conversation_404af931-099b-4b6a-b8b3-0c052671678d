#!/usr/bin/env python3
"""
Test infrastructure validation script for SIMILE backend.
Validates database connectivity, test isolation, and dependency requirements.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any
import importlib.util

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestInfrastructureValidator:
    """Validates test infrastructure components."""
    
    def __init__(self):
        self.results = {}
        
    def validate_dependencies(self) -> Dict[str, Any]:
        """Validate all required dependencies are installed."""
        logger.info("Validating dependencies...")
        
        required_packages = [
            'pytest',
            'pytest_asyncio',
            'pytest_cov',
            'httpx',
            'psutil',
            'fastapi',
            'sqlalchemy',
            'asyncpg',
            'greenlet',
            'pydantic',
            'pytest_benchmark',
            'pytest_jsonreport'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                spec = importlib.util.find_spec(package)
                if spec is None:
                    missing_packages.append(package)
                else:
                    logger.info(f"✓ {package} is installed")
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"Missing packages: {missing_packages}")
            return {'status': 'FAILED', 'missing_packages': missing_packages}
        
        logger.info("All dependencies are installed")
        return {'status': 'PASSED', 'missing_packages': []}
    
    async def validate_database_connectivity(self) -> Dict[str, Any]:
        """Validate database connectivity and setup."""
        logger.info("Validating database connectivity...")
        
        TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        TEST_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"
        
        try:
            engine = create_async_engine(TEST_DATABASE_URL)
            async with engine.connect() as conn:
                # Test basic connectivity
                result = await conn.execute(text("SELECT 1"))
                assert result.scalar() == 1
                
                # Check if required tables exist
                tables_result = await conn.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """))
                tables = [row[0] for row in tables_result.fetchall()]
                
                required_tables = ['entities', 'connections', 'units']
                missing_tables = [t for t in required_tables if t not in tables]
                
                if missing_tables:
                    logger.error(f"Missing tables: {missing_tables}")
                    return {'status': 'FAILED', 'missing_tables': missing_tables}
                
                # Check if basic units exist
                units_result = await conn.execute(text("SELECT COUNT(*) FROM units"))
                units_count = units_result.scalar()
                
                if units_count == 0:
                    logger.warning("No units found in database, may need initialization")
                    return {'status': 'WARNING', 'units_count': units_count}
                
                logger.info(f"✓ Database connectivity validated with {units_count} units")
                return {'status': 'PASSED', 'units_count': units_count}
                
        except Exception as e:
            logger.error(f"Database connectivity failed: {e}")
            return {'status': 'FAILED', 'error': str(e)}
        finally:
            await engine.dispose()
    
    async def validate_test_isolation(self) -> Dict[str, Any]:
        """Validate test isolation mechanisms."""
        logger.info("Validating test isolation...")
        
        TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        TEST_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"
        
        try:
            engine = create_async_engine(TEST_DATABASE_URL)
            async with engine.begin() as conn:
                # Create test entity
                await conn.execute(text("""
                    INSERT INTO entities (name, created_at, updated_at) 
                    VALUES ('Test Entity', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """))
                
                # Verify entity exists
                result = await conn.execute(text("SELECT COUNT(*) FROM entities WHERE name = 'Test Entity'"))
                count = result.scalar()
                
                if count != 1:
                    logger.error(f"Expected 1 test entity, found {count}")
                    return {'status': 'FAILED', 'entity_count': count}
                
                # Clean up test entity
                await conn.execute(text("DELETE FROM entities WHERE name = 'Test Entity'"))
                
                # Verify cleanup
                result = await conn.execute(text("SELECT COUNT(*) FROM entities WHERE name = 'Test Entity'"))
                count = result.scalar()
                
                if count != 0:
                    logger.error(f"Cleanup failed, {count} entities remaining")
                    return {'status': 'FAILED', 'cleanup_failed': True}
                
                logger.info("✓ Test isolation validation passed")
                return {'status': 'PASSED'}
                
        except Exception as e:
            logger.error(f"Test isolation validation failed: {e}")
            return {'status': 'FAILED', 'error': str(e)}
        finally:
            await engine.dispose()
    
    def validate_environment_variables(self) -> Dict[str, Any]:
        """Validate required environment variables."""
        logger.info("Validating environment variables...")
        
        required_vars = {
            'TEST_DATABASE_HOST': os.getenv('TEST_DATABASE_HOST', 'localhost'),
            'PYTHONPATH': os.getenv('PYTHONPATH', ''),
        }
        
        missing_vars = []
        for var_name, var_value in required_vars.items():
            if not var_value:
                missing_vars.append(var_name)
            else:
                logger.info(f"✓ {var_name} = {var_value}")
        
        if missing_vars:
            logger.warning(f"Missing environment variables: {missing_vars}")
            return {'status': 'WARNING', 'missing_vars': missing_vars}
        
        logger.info("Environment variables validated")
        return {'status': 'PASSED'}
    
    async def run_all_validations(self) -> Dict[str, Any]:
        """Run all validation checks."""
        logger.info("Starting comprehensive test infrastructure validation...")
        
        # Run dependency validation
        self.results['dependencies'] = self.validate_dependencies()
        
        # Run environment validation
        self.results['environment'] = self.validate_environment_variables()
        
        # Run database connectivity validation
        self.results['database_connectivity'] = await self.validate_database_connectivity()
        
        # Run test isolation validation
        self.results['test_isolation'] = await self.validate_test_isolation()
        
        # Overall status
        all_passed = all(result['status'] == 'PASSED' for result in self.results.values())
        has_warnings = any(result['status'] == 'WARNING' for result in self.results.values())
        
        if all_passed:
            overall_status = 'PASSED'
        elif has_warnings:
            overall_status = 'PASSED_WITH_WARNINGS'
        else:
            overall_status = 'FAILED'
        
        self.results['overall_status'] = overall_status
        
        return self.results
    
    def print_results(self):
        """Print validation results in a formatted way."""
        logger.info("\n" + "="*60)
        logger.info("TEST INFRASTRUCTURE VALIDATION RESULTS")
        logger.info("="*60)
        
        for check_name, result in self.results.items():
            if check_name == 'overall_status':
                continue
                
            status_symbol = {
                'PASSED': '✓',
                'WARNING': '⚠',
                'FAILED': '✗'
            }.get(result['status'], '?')
            
            logger.info(f"{status_symbol} {check_name.replace('_', ' ').title()}: {result['status']}")
            
            if result['status'] == 'FAILED' and 'error' in result:
                logger.error(f"  Error: {result['error']}")
            elif result['status'] == 'WARNING' and 'missing_vars' in result:
                logger.warning(f"  Missing variables: {result['missing_vars']}")
        
        logger.info("="*60)
        logger.info(f"OVERALL STATUS: {self.results['overall_status']}")
        logger.info("="*60)

async def main():
    """Main function to run validation."""
    validator = TestInfrastructureValidator()
    
    try:
        results = await validator.run_all_validations()
        validator.print_results()
        
        # Return appropriate exit code
        if results['overall_status'] == 'PASSED':
            logger.info("✅ All validations passed!")
            sys.exit(0)
        elif results['overall_status'] == 'PASSED_WITH_WARNINGS':
            logger.info("⚠️  Validations passed with warnings")
            sys.exit(0)
        else:
            logger.error("❌ Validation failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Validation script failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())