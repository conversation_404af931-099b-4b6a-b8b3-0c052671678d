#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reset the test database by cleaning all data but preserving structure.
Useful for manual cleanup when tests leave the database in a dirty state.
"""
import asyncio
import os
import sys
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
TEST_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"


async def reset_test_database():
    """Reset the test database by removing all data but preserving structure."""
    logger.info("Connecting to test database...")
    engine = create_async_engine(TEST_DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            # Check current state
            entities_result = await conn.execute(text("SELECT COUNT(*) FROM entities"))
            connections_result = await conn.execute(text("SELECT COUNT(*) FROM connections"))
            
            entities_count = entities_result.scalar()
            connections_count = connections_result.scalar()
            
            logger.info(f"Found {entities_count} entities and {connections_count} connections")
            
            if entities_count == 0 and connections_count == 0:
                logger.info("Database is already clean!")
                return
            
            logger.info("Cleaning test database...")
            
            # Delete all data in the correct order to avoid foreign key conflicts
            await conn.execute(text("DELETE FROM connections"))
            await conn.execute(text("DELETE FROM entities"))
            
            # Reset sequences to start from 1
            await conn.execute(text("ALTER SEQUENCE entities_id_seq RESTART WITH 1"))
            await conn.execute(text("ALTER SEQUENCE connections_id_seq RESTART WITH 1"))
            
            logger.info("Database cleaned successfully!")
            
            # Verify cleanup
            entities_result = await conn.execute(text("SELECT COUNT(*) FROM entities"))
            connections_result = await conn.execute(text("SELECT COUNT(*) FROM connections"))
            units_result = await conn.execute(text("SELECT COUNT(*) FROM units"))
            
            entities_count = entities_result.scalar()
            connections_count = connections_result.scalar()
            units_count = units_result.scalar()
            
            logger.info(f"After cleanup: {entities_count} entities, {connections_count} connections, {units_count} units")
            
    except Exception as e:
        logger.error(f"Error resetting test database: {e}")
        sys.exit(1)
    finally:
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(reset_test_database())