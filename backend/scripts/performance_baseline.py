#!/usr/bin/env python3
"""
Performance baseline tracking system for SIMILE backend.
Tracks performance metrics over time and detects regressions.
"""
import json
import statistics
import argparse
import sys
from datetime import datetime
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceBaselineTracker:
    def __init__(self, baseline_file="performance_baseline.json"):
        self.baseline_file = Path(baseline_file)
        self.baselines = self.load_baselines()
        
    def load_baselines(self):
        """Load existing baselines."""
        if self.baseline_file.exists():
            try:
                with open(self.baseline_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                logger.warning(f"Could not load baseline file {self.baseline_file}, starting fresh")
                return {}
        return {}
    
    def save_baselines(self):
        """Save baselines to file."""
        try:
            self.baseline_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.baseline_file, 'w') as f:
                json.dump(self.baselines, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save baselines: {e}")
    
    def record_metric(self, test_name, metric_name, value, metadata=None):
        """Record a performance metric."""
        if test_name not in self.baselines:
            self.baselines[test_name] = {}
            
        if metric_name not in self.baselines[test_name]:
            self.baselines[test_name][metric_name] = {
                "history": [],
                "baseline": None,
                "metadata": metadata or {}
            }
        
        # Add to history
        self.baselines[test_name][metric_name]["history"].append({
            "timestamp": datetime.utcnow().isoformat(),
            "value": value,
            "metadata": metadata or {}
        })
        
        # Keep only last 100 measurements
        history = self.baselines[test_name][metric_name]["history"]
        if len(history) > 100:
            self.baselines[test_name][metric_name]["history"] = history[-100:]
        
        # Update baseline (using median of last 20 runs)
        recent_values = [h["value"] for h in history[-20:]]
        if len(recent_values) >= 5:
            self.baselines[test_name][metric_name]["baseline"] = {
                "value": statistics.median(recent_values),
                "mean": statistics.mean(recent_values),
                "stddev": statistics.stdev(recent_values) if len(recent_values) > 1 else 0,
                "min": min(recent_values),
                "max": max(recent_values),
                "samples": len(recent_values),
                "last_updated": datetime.utcnow().isoformat()
            }
        
        self.save_baselines()
        logger.info(f"Recorded metric {test_name}.{metric_name}: {value}")
    
    def check_regression(self, test_name, metric_name, current_value, threshold=1.2):
        """Check if current value indicates a performance regression."""
        if test_name not in self.baselines:
            return False, "No baseline established", {}
            
        if metric_name not in self.baselines[test_name]:
            return False, "No baseline for this metric", {}
            
        baseline = self.baselines[test_name][metric_name].get("baseline")
        if not baseline:
            return False, "Insufficient baseline data", {}
        
        baseline_value = baseline["value"]
        baseline_mean = baseline["mean"]
        stddev = baseline["stddev"]
        
        analysis = {
            "current_value": current_value,
            "baseline_value": baseline_value,
            "baseline_mean": baseline_mean,
            "stddev": stddev,
            "threshold": threshold,
            "deviation_factor": current_value / baseline_value if baseline_value > 0 else float('inf'),
            "std_deviations": (current_value - baseline_mean) / stddev if stddev > 0 else 0
        }
        
        # Check if current value exceeds threshold
        if current_value > baseline_value * threshold:
            return True, f"Performance regression detected: {current_value:.2f} > {baseline_value * threshold:.2f} (baseline: {baseline_value:.2f})", analysis
        
        # Check if value is more than 3 standard deviations away
        if stddev > 0 and current_value > baseline_mean + (3 * stddev):
            return True, f"Performance anomaly detected: {current_value:.2f} is {analysis['std_deviations']:.1f} standard deviations from baseline", analysis
        
        return False, f"Performance within acceptable range: {current_value:.2f} (baseline: {baseline_value:.2f})", analysis
    
    def get_baseline_summary(self, test_name=None):
        """Get summary of all baselines."""
        if test_name:
            if test_name not in self.baselines:
                return {}
            return {test_name: self.baselines[test_name]}
        
        summary = {}
        for test, metrics in self.baselines.items():
            summary[test] = {}
            for metric, data in metrics.items():
                baseline = data.get("baseline")
                if baseline:
                    summary[test][metric] = {
                        "baseline_value": baseline["value"],
                        "samples": baseline["samples"],
                        "last_updated": baseline["last_updated"],
                        "stddev": baseline["stddev"]
                    }
        
        return summary
    
    def analyze_trends(self, test_name, metric_name, window=20):
        """Analyze performance trends over time."""
        if test_name not in self.baselines or metric_name not in self.baselines[test_name]:
            return None
        
        history = self.baselines[test_name][metric_name]["history"]
        if len(history) < window:
            return None
        
        # Get recent values
        recent_values = [h["value"] for h in history[-window:]]
        timestamps = [h["timestamp"] for h in history[-window:]]
        
        # Calculate trend
        n = len(recent_values)
        x = list(range(n))
        
        # Simple linear regression
        x_mean = statistics.mean(x)
        y_mean = statistics.mean(recent_values)
        
        numerator = sum((x[i] - x_mean) * (recent_values[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            slope = 0
        else:
            slope = numerator / denominator
        
        # Determine trend direction
        if abs(slope) < 0.01:
            trend = "stable"
        elif slope > 0:
            trend = "increasing"
        else:
            trend = "decreasing"
        
        return {
            "trend": trend,
            "slope": slope,
            "recent_mean": y_mean,
            "recent_stddev": statistics.stdev(recent_values) if len(recent_values) > 1 else 0,
            "sample_count": n,
            "time_range": {
                "start": timestamps[0],
                "end": timestamps[-1]
            }
        }
    
    def export_report(self, output_file=None):
        """Export performance report."""
        if output_file is None:
            output_file = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "generated_at": datetime.utcnow().isoformat(),
            "summary": self.get_baseline_summary(),
            "trends": {},
            "recommendations": []
        }
        
        # Analyze trends for all metrics
        for test_name, metrics in self.baselines.items():
            report["trends"][test_name] = {}
            for metric_name in metrics:
                trend = self.analyze_trends(test_name, metric_name)
                if trend:
                    report["trends"][test_name][metric_name] = trend
                    
                    # Generate recommendations
                    if trend["trend"] == "increasing" and trend["slope"] > 1:
                        report["recommendations"].append({
                            "test": test_name,
                            "metric": metric_name,
                            "type": "performance_degradation",
                            "message": f"Performance degradation detected in {test_name}.{metric_name}",
                            "severity": "high" if trend["slope"] > 2 else "medium"
                        })
        
        # Save report
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Performance report exported to {output_path}")
        return output_path

def main():
    parser = argparse.ArgumentParser(description="Performance baseline tracker")
    parser.add_argument("--baseline-file", default="performance_baseline.json", 
                       help="Baseline file path")
    parser.add_argument("--record", nargs=3, metavar=("TEST", "METRIC", "VALUE"),
                       help="Record a metric: test_name metric_name value")
    parser.add_argument("--check", nargs=3, metavar=("TEST", "METRIC", "VALUE"),
                       help="Check for regression: test_name metric_name value")
    parser.add_argument("--threshold", type=float, default=1.2,
                       help="Regression threshold (default: 1.2)")
    parser.add_argument("--summary", action="store_true",
                       help="Show baseline summary")
    parser.add_argument("--trend", nargs=2, metavar=("TEST", "METRIC"),
                       help="Analyze trend: test_name metric_name")
    parser.add_argument("--report", nargs="?", const="auto", 
                       help="Export performance report")
    parser.add_argument("--import-locust", metavar="LOCUST_STATS_FILE",
                       help="Import Locust statistics file")
    
    args = parser.parse_args()
    
    tracker = PerformanceBaselineTracker(args.baseline_file)
    
    if args.record:
        test_name, metric_name, value = args.record
        try:
            value = float(value)
            tracker.record_metric(test_name, metric_name, value)
            print(f"Recorded {test_name}.{metric_name} = {value}")
        except ValueError:
            print(f"Error: Invalid value '{value}', must be a number")
            sys.exit(1)
    
    elif args.check:
        test_name, metric_name, value = args.check
        try:
            value = float(value)
            is_regression, message, analysis = tracker.check_regression(
                test_name, metric_name, value, args.threshold
            )
            
            print(f"Regression check for {test_name}.{metric_name}:")
            print(f"  Current value: {value}")
            print(f"  Result: {message}")
            
            if analysis:
                print(f"  Analysis:")
                print(f"    Deviation factor: {analysis['deviation_factor']:.2f}")
                print(f"    Standard deviations: {analysis['std_deviations']:.2f}")
            
            if is_regression:
                print("  STATUS: REGRESSION DETECTED")
                sys.exit(1)
            else:
                print("  STATUS: OK")
                
        except ValueError:
            print(f"Error: Invalid value '{value}', must be a number")
            sys.exit(1)
    
    elif args.summary:
        summary = tracker.get_baseline_summary()
        if summary:
            print("Performance Baseline Summary:")
            print("=" * 40)
            for test_name, metrics in summary.items():
                print(f"\n{test_name}:")
                for metric_name, data in metrics.items():
                    print(f"  {metric_name}:")
                    print(f"    Baseline: {data['baseline_value']:.2f}")
                    print(f"    Samples: {data['samples']}")
                    print(f"    Std Dev: {data['stddev']:.2f}")
                    print(f"    Updated: {data['last_updated']}")
        else:
            print("No baseline data available")
    
    elif args.trend:
        test_name, metric_name = args.trend
        trend = tracker.analyze_trends(test_name, metric_name)
        if trend:
            print(f"Trend analysis for {test_name}.{metric_name}:")
            print(f"  Trend: {trend['trend']}")
            print(f"  Slope: {trend['slope']:.4f}")
            print(f"  Recent mean: {trend['recent_mean']:.2f}")
            print(f"  Recent std dev: {trend['recent_stddev']:.2f}")
            print(f"  Sample count: {trend['sample_count']}")
        else:
            print(f"No trend data available for {test_name}.{metric_name}")
    
    elif args.report:
        output_file = args.report if args.report != "auto" else None
        report_path = tracker.export_report(output_file)
        print(f"Performance report exported to: {report_path}")
    
    elif args.import_locust:
        # Import Locust statistics
        try:
            with open(args.import_locust, 'r') as f:
                stats = json.load(f)
            
            # Extract key metrics
            if 'stats' in stats:
                for endpoint in stats['stats']:
                    if endpoint['name'] != 'Aggregated':
                        test_name = f"locust_{endpoint['name'].replace('/', '_').replace(' ', '_')}"
                        
                        # Record various metrics
                        tracker.record_metric(test_name, "avg_response_time", endpoint['avg_response_time'])
                        tracker.record_metric(test_name, "median_response_time", endpoint['median_response_time'])
                        tracker.record_metric(test_name, "95th_percentile", endpoint['95th_percentile'])
                        tracker.record_metric(test_name, "requests_per_sec", endpoint['current_rps'])
                        tracker.record_metric(test_name, "failure_rate", endpoint['failure_rate'])
                
                print(f"Imported Locust statistics from {args.import_locust}")
            else:
                print("Error: Invalid Locust statistics file format")
                sys.exit(1)
                
        except FileNotFoundError:
            print(f"Error: File {args.import_locust} not found")
            sys.exit(1)
        except json.JSONDecodeError:
            print(f"Error: Invalid JSON in {args.import_locust}")
            sys.exit(1)
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()