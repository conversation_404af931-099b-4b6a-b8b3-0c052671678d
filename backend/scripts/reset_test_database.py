#!/usr/bin/env python3
"""
Test database reset script for SIMILE backend.
Completely resets the test database to a clean state.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from src.models import Base

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestDatabaseManager:
    """Manages test database operations."""
    
    def __init__(self):
        self.TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        self.ADMIN_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{self.TEST_DATABASE_HOST}:5432/postgres"
        self.TEST_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{self.TEST_DATABASE_HOST}:5432/simile_test"
    
    async def drop_test_database(self):
        """Drop the test database completely."""
        logger.info("Dropping test database...")
        
        admin_engine = create_async_engine(self.ADMIN_DATABASE_URL, isolation_level="AUTOCOMMIT")
        try:
            async with admin_engine.connect() as conn:
                # Terminate all connections to the test database
                await conn.execute(text("""
                    SELECT pg_terminate_backend(pg_stat_activity.pid)
                    FROM pg_stat_activity
                    WHERE pg_stat_activity.datname = 'simile_test'
                        AND pid <> pg_backend_pid()
                """))
                
                # Drop database
                await conn.execute(text("DROP DATABASE IF EXISTS simile_test"))
                logger.info("✓ Test database dropped")
        finally:
            await admin_engine.dispose()
    
    async def create_test_database(self):
        """Create a fresh test database."""
        logger.info("Creating test database...")
        
        admin_engine = create_async_engine(self.ADMIN_DATABASE_URL, isolation_level="AUTOCOMMIT")
        try:
            async with admin_engine.connect() as conn:
                # Create database
                await conn.execute(text("CREATE DATABASE simile_test"))
                logger.info("✓ Test database created")
        finally:
            await admin_engine.dispose()
    
    async def setup_test_schema(self):
        """Set up the test database schema and initial data."""
        logger.info("Setting up test schema...")
        
        test_engine = create_async_engine(self.TEST_DATABASE_URL)
        try:
            async with test_engine.begin() as conn:
                # Create all tables
                await conn.run_sync(Base.metadata.create_all)
                logger.info("✓ Tables created")
                
                # Insert initial units
                await conn.execute(text("""
                    INSERT INTO units (name, symbol, created_at, updated_at) VALUES
                    ('Length', 'm', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                    ('Mass', 'kg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                    ('Time', 's', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                    ('Count', '#', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                    ('Volume', 'L', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    ON CONFLICT (name) DO NOTHING
                """))
                
                # Verify units were created
                result = await conn.execute(text("SELECT COUNT(*) FROM units"))
                count = result.scalar()
                logger.info(f"✓ {count} units created")
        finally:
            await test_engine.dispose()
    
    async def verify_test_database(self):
        """Verify the test database is properly set up."""
        logger.info("Verifying test database...")
        
        test_engine = create_async_engine(self.TEST_DATABASE_URL)
        try:
            async with test_engine.connect() as conn:
                # Check tables exist
                tables_result = await conn.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """))
                tables = [row[0] for row in tables_result.fetchall()]
                
                required_tables = ['entities', 'connections', 'units']
                missing_tables = [t for t in required_tables if t not in tables]
                
                if missing_tables:
                    raise Exception(f"Missing tables: {missing_tables}")
                
                # Check data counts
                entities_result = await conn.execute(text("SELECT COUNT(*) FROM entities"))
                connections_result = await conn.execute(text("SELECT COUNT(*) FROM connections"))
                units_result = await conn.execute(text("SELECT COUNT(*) FROM units"))
                
                entities_count = entities_result.scalar()
                connections_count = connections_result.scalar()
                units_count = units_result.scalar()
                
                logger.info(f"✓ Database verified: {entities_count} entities, {connections_count} connections, {units_count} units")
                
                if entities_count != 0 or connections_count != 0:
                    logger.warning(f"Database not clean: {entities_count} entities, {connections_count} connections")
                
                if units_count < 5:
                    logger.warning(f"Expected at least 5 units, found {units_count}")
                    
        finally:
            await test_engine.dispose()
    
    async def reset_database(self):
        """Completely reset the test database."""
        logger.info("Starting test database reset...")
        
        try:
            await self.drop_test_database()
            await self.create_test_database()
            await self.setup_test_schema()
            await self.verify_test_database()
            
            logger.info("✅ Test database reset completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Database reset failed: {e}")
            raise
    
    async def clean_database(self):
        """Clean the database without dropping it."""
        logger.info("Starting database cleanup...")
        
        test_engine = create_async_engine(self.TEST_DATABASE_URL)
        try:
            async with test_engine.begin() as conn:
                # Get counts before cleanup
                entities_result = await conn.execute(text("SELECT COUNT(*) FROM entities"))
                connections_result = await conn.execute(text("SELECT COUNT(*) FROM connections"))
                
                entities_count = entities_result.scalar()
                connections_count = connections_result.scalar()
                
                if entities_count > 0 or connections_count > 0:
                    logger.info(f"Cleaning: {entities_count} entities, {connections_count} connections")
                
                # Delete all data in the correct order
                await conn.execute(text("DELETE FROM connections"))
                await conn.execute(text("DELETE FROM entities"))
                
                # Clean custom units (keep predefined ones)
                await conn.execute(text("""
                    DELETE FROM units 
                    WHERE name NOT IN ('Length', 'Mass', 'Time', 'Count', 'Volume')
                """))
                
                # Reset sequences
                await conn.execute(text("ALTER SEQUENCE entities_id_seq RESTART WITH 1"))
                await conn.execute(text("ALTER SEQUENCE connections_id_seq RESTART WITH 1"))
                await conn.execute(text("ALTER SEQUENCE units_id_seq RESTART WITH 10"))
                
                # Verify cleanup
                entities_after = await conn.execute(text("SELECT COUNT(*) FROM entities"))
                connections_after = await conn.execute(text("SELECT COUNT(*) FROM connections"))
                
                entities_remaining = entities_after.scalar()
                connections_remaining = connections_after.scalar()
                
                if entities_remaining != 0 or connections_remaining != 0:
                    logger.error(f"Cleanup failed: {entities_remaining} entities, {connections_remaining} connections remaining")
                    raise Exception(f"Database cleanup failed: {entities_remaining} entities, {connections_remaining} connections remaining")
                
                logger.info("✅ Database cleanup completed successfully!")
                
        finally:
            await test_engine.dispose()

async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test database management')
    parser.add_argument('action', choices=['reset', 'clean', 'verify'], 
                       help='Action to perform')
    
    args = parser.parse_args()
    
    manager = TestDatabaseManager()
    
    try:
        if args.action == 'reset':
            await manager.reset_database()
        elif args.action == 'clean':
            await manager.clean_database()
        elif args.action == 'verify':
            await manager.verify_test_database()
            
    except Exception as e:
        logger.error(f"Operation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())