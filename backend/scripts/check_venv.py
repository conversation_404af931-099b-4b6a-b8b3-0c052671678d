#!/usr/bin/env python3
"""Check that we're running in a virtual environment."""
import sys
import os

def check_venv():
    """Verify we're in a virtual environment."""
    # Check multiple indicators of virtual environment
    in_venv = (
        hasattr(sys, 'real_prefix') or  # virtualenv
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) or  # venv
        os.environ.get('VIRTUAL_ENV') is not None  # Common env var
    )
    
    if in_venv:
        venv_path = os.environ.get('VIRTUAL_ENV', sys.prefix)
        print(f"✅ Running in virtual environment: {venv_path}")
        return True
    else:
        print("❌ Not running in a virtual environment!")
        print("   Please activate the virtual environment first:")
        print("   source venv/bin/activate")
        return False

if __name__ == "__main__":
    sys.exit(0 if check_venv() else 1)