#!/usr/bin/env python3
"""
Main Database Cleanup Script

This script cleans up test contamination from the main 'simile' database.
It removes test entities and connections that were accidentally created during tests.

⚠️  WARNING: This script modifies the PRODUCTION database! ⚠️
Use with extreme caution and ensure you have backups.
"""
import asyncio
import os
import sys
from pathlib import Path
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

async def cleanup_main_database():
    """Clean up test contamination from the main database."""
    
    # Get the main database URL (NOT the test database)
    DATABASE_HOST = os.getenv("DATABASE_HOST", "localhost")
    MAIN_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{DATABASE_HOST}:5432/simile"
    
    print("⚠️  WARNING: About to clean up the MAIN database!")
    print(f"Database URL: {MAIN_DATABASE_URL}")
    print()
    
    # Safety confirmation
    confirm = input("Are you sure you want to clean the MAIN database? Type 'YES' to continue: ")
    if confirm != "YES":
        print("Operation cancelled.")
        return
    
    engine = create_async_engine(MAIN_DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            print("Analyzing database contents...")
            
            # Get current entity count
            entities_result = await conn.execute(text("SELECT COUNT(*) FROM entities"))
            total_entities = entities_result.scalar()
            
            # Get test entity patterns to remove
            test_patterns = [
                "Cleanu W D AAB%",  # Test cleanup entities
                "Cleanu W D AAC%",
                "Cleanu W D AAD%",
                "%Test%Entity%",    # Test entities
                "%TestEntity%",
                "%Entity Test%",
                "%ConnectionTest%", # Connection test entities
                "%ConcurrentTest%", # Concurrent test entities
                "%DuplicateTest%",  # Duplicate test entities
                "%Cleanup%",        # Other cleanup entities
            ]
            
            # Count test entities to remove
            test_count_query = "SELECT COUNT(*) FROM entities WHERE " + " OR ".join([f"name LIKE '{pattern}'" for pattern in test_patterns])
            test_entities_result = await conn.execute(text(test_count_query))
            test_entities_count = test_entities_result.scalar()
            
            print(f"Found {total_entities} total entities")
            print(f"Found {test_entities_count} test entities to remove")
            
            if test_entities_count == 0:
                print("No test entities found. Database is clean.")
                return
            
            # Show what will be removed
            preview_query = "SELECT id, name FROM entities WHERE " + " OR ".join([f"name LIKE '{pattern}'" for pattern in test_patterns]) + " ORDER BY name"
            preview_result = await conn.execute(text(preview_query))
            
            print("\nEntities to be removed:")
            entities_to_remove = []
            for row in preview_result:
                entities_to_remove.append(row[0])
                print(f"  ID {row[0]}: {row[1]}")
            
            if entities_to_remove:
                final_confirm = input(f"\nProceed with removing {len(entities_to_remove)} test entities? Type 'DELETE' to confirm: ")
                if final_confirm != "DELETE":
                    print("Operation cancelled.")
                    return
                
                print("\nRemoving test entities and their connections...")
                
                # Remove connections associated with test entities
                entity_ids_str = ",".join(map(str, entities_to_remove))
                connections_removed = await conn.execute(text(f"""
                    DELETE FROM connections 
                    WHERE from_entity_id IN ({entity_ids_str}) 
                       OR to_entity_id IN ({entity_ids_str})
                """))
                
                print(f"Removed {connections_removed.rowcount} connections")
                
                # Remove the test entities
                entities_removed = await conn.execute(text(f"""
                    DELETE FROM entities WHERE id IN ({entity_ids_str})
                """))
                
                print(f"Removed {entities_removed.rowcount} test entities")
                
                # Verify cleanup
                final_count_result = await conn.execute(text("SELECT COUNT(*) FROM entities"))
                final_count = final_count_result.scalar()
                
                print(f"\nCleanup complete!")
                print(f"Entities before: {total_entities}")
                print(f"Entities after: {final_count}")
                print(f"Entities removed: {total_entities - final_count}")
                
                # Show remaining entities
                remaining_result = await conn.execute(text("SELECT id, name FROM entities ORDER BY name"))
                print(f"\nRemaining entities ({final_count}):")
                for row in remaining_result:
                    print(f"  ID {row[0]}: {row[1]}")
            
    except Exception as e:
        print(f"Error during cleanup: {e}")
        raise
    finally:
        await engine.dispose()


async def backup_main_database():
    """Create a backup of the main database before cleanup."""
    DATABASE_HOST = os.getenv("DATABASE_HOST", "localhost")
    
    print("Creating database backup...")
    
    # Use pg_dump to create a backup
    backup_filename = f"simile_backup_{os.getpid()}.sql"
    backup_command = f"pg_dump -h {DATABASE_HOST} -U postgres simile > {backup_filename}"
    
    print(f"Backup command: {backup_command}")
    print("Please run this command manually if you want a backup before cleanup.")
    print()


if __name__ == "__main__":
    print("🧹 SIMILE Main Database Cleanup Tool")
    print("=" * 50)
    print()
    
    # Show backup recommendation
    backup_choice = input("Do you want backup instructions? (y/n): ")
    if backup_choice.lower() == 'y':
        asyncio.run(backup_main_database())
    
    # Run cleanup
    asyncio.run(cleanup_main_database())