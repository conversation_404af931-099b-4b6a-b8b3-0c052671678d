#!/usr/bin/env python3
"""
Tear down the test database after tests complete.
"""
import asyncio
import os
import sys
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Check virtual environment
def check_venv():
    """Verify we're in a virtual environment."""
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) or
        os.environ.get('VIRTUAL_ENV') is not None
    )
    
    if not in_venv:
        logger.error("Not running in a virtual environment!")
        logger.error("Please activate the virtual environment first: source venv/bin/activate")
        sys.exit(1)

check_venv()

TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
ADMIN_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/postgres"


async def drop_test_database():
    """Drop the test database."""
    engine = create_async_engine(ADMIN_DATABASE_URL, isolation_level="AUTOCOMMIT")
    
    try:
        async with engine.connect() as conn:
            # Check if database exists
            result = await conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = 'simile_test'")
            )
            exists = result.scalar() is not None
            
            if exists:
                # Terminate all connections to the test database
                logger.info("Terminating connections to simile_test...")
                await conn.execute(text("""
                    SELECT pg_terminate_backend(pg_stat_activity.pid)
                    FROM pg_stat_activity
                    WHERE pg_stat_activity.datname = 'simile_test'
                    AND pid <> pg_backend_pid()
                """))
                
                # Drop the database
                logger.info("Dropping simile_test database...")
                await conn.execute(text("DROP DATABASE simile_test"))
                logger.info("✅ simile_test database dropped successfully")
            else:
                logger.info("simile_test database does not exist, nothing to clean up")
                
    except Exception as e:
        logger.error(f"Error dropping test database: {e}")
        # Don't raise - we want cleanup to be lenient
        return 1
    finally:
        await engine.dispose()
    
    return 0


async def main():
    """Main teardown function."""
    try:
        result = await drop_test_database()
        
        if result == 0:
            logger.info("✅ Test database teardown completed successfully!")
        else:
            logger.warning("⚠️  Test database teardown completed with warnings")
            
        return result
        
    except Exception as e:
        logger.error(f"Test database teardown failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))