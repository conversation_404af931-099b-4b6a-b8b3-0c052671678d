#!/bin/bash

# SIMILE Performance Test Suite
# Comprehensive performance testing with monitoring and analysis

set -e

# Configuration
RESULTS_DIR="performance_results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RESULT_FILE="$RESULTS_DIR/perf_test_$TIMESTAMP.json"
LOCUST_REPORT="$RESULTS_DIR/locust_report_$TIMESTAMP.html"
LOCUST_STATS="$RESULTS_DIR/locust_stats_$TIMESTAMP.json"

# Test configuration
USERS=${USERS:-50}
SPAWN_RATE=${SPAWN_RATE:-5}
RUN_TIME=${RUN_TIME:-5m}
HOST=${HOST:-http://localhost:8000}

echo "🚀 SIMILE Performance Test Suite"
echo "================================="
echo "Users: $USERS"
echo "Spawn Rate: $SPAWN_RATE"
echo "Run Time: $RUN_TIME"
echo "Host: $HOST"
echo "Results: $RESULTS_DIR"
echo "Timestamp: $TIMESTAMP"
echo ""

# Create results directory
mkdir -p $RESULTS_DIR

# Function to cleanup on exit
cleanup() {
    echo "🧹 Cleaning up..."
    # Kill any background processes
    jobs -p | xargs -r kill 2>/dev/null || true
    
    # Clean up test data
    if [ -f "scripts/test_data_generator.py" ]; then
        python scripts/test_data_generator.py --cleanup 2>/dev/null || true
    fi
}

# Set up cleanup trap
trap cleanup EXIT

# Function to check if service is running
check_service() {
    local service=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    echo "🔍 Checking $service on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://localhost:$port" >/dev/null 2>&1; then
            echo "✅ $service is running"
            return 0
        fi
        
        echo "⏳ Waiting for $service... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service is not responding after $max_attempts attempts"
    return 1
}

# 1. Pre-flight checks
echo "1. 🔍 Pre-flight checks"
echo "----------------------"

# Check if virtual environment is active
if [ -z "$VIRTUAL_ENV" ]; then
    echo "❌ Virtual environment not active"
    echo "   Please run: source venv/bin/activate"
    exit 1
fi

# Check required dependencies
echo "📦 Checking dependencies..."
python -c "import locust, psutil, asyncpg" 2>/dev/null || {
    echo "❌ Missing dependencies. Installing..."
    pip install -r requirements-perf.txt
}

# Check services
echo "🔍 Checking services..."
python scripts/check_test_services.py

# Check API is responding
if ! check_service "API" 8000; then
    echo "❌ API service not available"
    echo "   Please start backend services: docker-compose up -d"
    exit 1
fi

echo "✅ All pre-flight checks passed!"
echo ""

# 2. Test environment setup
echo "2. 🛠️  Test environment setup"
echo "-----------------------------"

# Set up test database
echo "🗄️  Setting up test database..."
python scripts/test_db_setup.py

# Generate test data
echo "📊 Generating test data..."
python scripts/test_data_generator.py \
    --entities 500 \
    --connections 2000 \
    --complexity moderate \
    --output "$RESULTS_DIR/test_data_$TIMESTAMP.json"

# Capture initial database state
echo "📸 Capturing initial database state..."
python scripts/validate_db_state.py --capture "$RESULTS_DIR/db_state_before_$TIMESTAMP.json" || echo "Warning: Could not capture initial state"

echo "✅ Test environment ready!"
echo ""

# 3. Start monitoring
echo "3. 📊 Starting monitoring"
echo "-------------------------"

# Start resource monitoring
echo "🔍 Starting resource monitoring..."
python scripts/resource_monitor.py --output "$RESULTS_DIR/resources_$TIMESTAMP.json" &
MONITOR_PID=$!
echo "Resource monitor started (PID: $MONITOR_PID)"

# Start database performance monitoring
echo "🗄️  Starting database monitoring..."
python scripts/db_performance_monitor.py --output "$RESULTS_DIR/db_perf_$TIMESTAMP.json" &
DB_MONITOR_PID=$!
echo "Database monitor started (PID: $DB_MONITOR_PID)"

echo "✅ Monitoring active!"
echo ""

# 4. Run performance tests
echo "4. 🚀 Running performance tests"
echo "-------------------------------"

# Run Locust tests
echo "🦗 Starting Locust performance tests..."
echo "   Users: $USERS"
echo "   Spawn Rate: $SPAWN_RATE/sec"
echo "   Duration: $RUN_TIME"
echo "   Target: $HOST"

# Run Locust with detailed output
locust \
    -f tests/performance/locustfile.py \
    --headless \
    --users $USERS \
    --spawn-rate $SPAWN_RATE \
    --run-time $RUN_TIME \
    --host $HOST \
    --html "$LOCUST_REPORT" \
    --csv "$RESULTS_DIR/locust_$TIMESTAMP" \
    --loglevel INFO \
    --logfile "$RESULTS_DIR/locust_log_$TIMESTAMP.txt" \
    --exit-code-on-error 0

# Check if Locust completed successfully
if [ $? -eq 0 ]; then
    echo "✅ Locust tests completed successfully!"
else
    echo "⚠️  Locust tests completed with warnings"
fi

# Wait a moment for stats to be written
sleep 2

echo ""

# 5. Stop monitoring
echo "5. 🛑 Stopping monitoring"
echo "-------------------------"

# Stop resource monitoring
if kill -0 $MONITOR_PID 2>/dev/null; then
    echo "🔍 Stopping resource monitor..."
    kill $MONITOR_PID
    wait $MONITOR_PID 2>/dev/null || true
fi

# Stop database monitoring
if kill -0 $DB_MONITOR_PID 2>/dev/null; then
    echo "🗄️  Stopping database monitor..."
    kill $DB_MONITOR_PID
    wait $DB_MONITOR_PID 2>/dev/null || true
fi

echo "✅ Monitoring stopped!"
echo ""

# 6. Post-test analysis
echo "6. 📊 Post-test analysis"
echo "------------------------"

# Capture final database state
echo "📸 Capturing final database state..."
python scripts/validate_db_state.py --capture "$RESULTS_DIR/db_state_after_$TIMESTAMP.json" || echo "Warning: Could not capture final state"

# Generate performance report
echo "📋 Generating performance report..."
python scripts/analyze_performance_results.py \
    --locust-stats "$RESULTS_DIR/locust_${TIMESTAMP}_stats.csv" \
    --resources "$RESULTS_DIR/resources_$TIMESTAMP.json" \
    --db-perf "$RESULTS_DIR/db_perf_$TIMESTAMP.json" \
    --output "$RESULT_FILE" \
    --html "$RESULTS_DIR/report_$TIMESTAMP.html" \
    2>/dev/null || echo "Warning: Could not generate detailed report"

# Update baselines and check for regressions
echo "🎯 Checking for performance regressions..."
if [ -f "$RESULTS_DIR/locust_${TIMESTAMP}_stats.csv" ]; then
    # Extract key metrics from CSV and check baselines
    python - <<EOF
import csv
import sys
sys.path.append('scripts')
from performance_baseline import PerformanceBaselineTracker

tracker = PerformanceBaselineTracker('$RESULTS_DIR/performance_baseline.json')

# Read Locust stats
try:
    with open('$RESULTS_DIR/locust_${TIMESTAMP}_stats.csv', 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row['Type'] == 'GET' and row['Name'] != 'Aggregated':
                test_name = f"api_{row['Name'].replace('/', '_').strip('_')}"
                
                # Record metrics
                tracker.record_metric(test_name, 'avg_response_time', float(row['Average Response Time']))
                tracker.record_metric(test_name, 'median_response_time', float(row['Median Response Time']))
                tracker.record_metric(test_name, 'requests_per_sec', float(row['Requests/s']))
                
                # Check for regressions
                is_regression, message, analysis = tracker.check_regression(
                    test_name, 'avg_response_time', float(row['Average Response Time'])
                )
                
                if is_regression:
                    print(f"⚠️  REGRESSION: {message}")
                else:
                    print(f"✅ OK: {test_name} - {message}")
                    
except Exception as e:
    print(f"Warning: Could not process Locust stats: {e}")
EOF
fi

# Clean up test data
echo "🧹 Cleaning up test data..."
python scripts/test_data_generator.py --cleanup 2>/dev/null || echo "Warning: Could not clean up test data"

echo "✅ Analysis complete!"
echo ""

# 7. Summary
echo "7. 📊 Test Summary"
echo "=================="

# Display key results
if [ -f "$LOCUST_REPORT" ]; then
    echo "📄 Locust Report: $LOCUST_REPORT"
fi

if [ -f "$RESULT_FILE" ]; then
    echo "📊 Performance Results: $RESULT_FILE"
fi

# Show quick stats if available
if [ -f "$RESULTS_DIR/locust_${TIMESTAMP}_stats.csv" ]; then
    echo ""
    echo "🎯 Quick Stats:"
    echo "---------------"
    
    # Extract and display key metrics
    python - <<EOF
import csv
try:
    with open('$RESULTS_DIR/locust_${TIMESTAMP}_stats.csv', 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row['Name'] == 'Aggregated':
                print(f"Total Requests: {row['Request Count']}")
                print(f"Failure Rate: {row['Failure Count']}/{row['Request Count']} ({float(row['Failure Count'])/float(row['Request Count'])*100:.1f}%)")
                print(f"Average Response Time: {row['Average Response Time']}ms")
                print(f"95th Percentile: {row['95%']}ms")
                print(f"Requests/sec: {row['Requests/s']}")
                break
except Exception as e:
    print(f"Could not read summary stats: {e}")
EOF
fi

echo ""
echo "🎉 Performance test suite completed!"
echo "Results saved to: $RESULTS_DIR"
echo "Timestamp: $TIMESTAMP"

# Final cleanup
echo ""
echo "🧹 Final cleanup..."
python scripts/test_db_teardown.py 2>/dev/null || true

echo "✅ All done!"