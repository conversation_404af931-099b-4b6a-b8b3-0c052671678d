#!/usr/bin/env python3
"""
Set up the test database with all required tables and initial data.
"""
import asyncio
import os
import sys
from pathlib import Path
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import logging

# Add backend to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models import Base

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Check virtual environment
def check_venv():
    """Verify we're in a virtual environment."""
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) or
        os.environ.get('VIRTUAL_ENV') is not None
    )
    
    if not in_venv:
        logger.error("Not running in a virtual environment!")
        logger.error("Please activate the virtual environment first: source venv/bin/activate")
        sys.exit(1)

check_venv()

TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
ADMIN_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/postgres"
TEST_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"

# Path to database migrations
DB_DIR = Path(__file__).parent.parent.parent / "database"
MIGRATIONS_DIR = DB_DIR / "migrations"


async def create_test_database():
    """Create the test database if it doesn't exist."""
    engine = create_async_engine(ADMIN_DATABASE_URL, isolation_level="AUTOCOMMIT")
    
    try:
        async with engine.connect() as conn:
            # Check if database exists
            result = await conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = 'simile_test'")
            )
            exists = result.scalar() is not None
            
            if not exists:
                logger.info("Creating simile_test database...")
                await conn.execute(text("CREATE DATABASE simile_test"))
                logger.info("simile_test database created successfully")
            else:
                logger.info("simile_test database already exists")
                
    except Exception as e:
        logger.error(f"Error creating test database: {e}")
        raise
    finally:
        await engine.dispose()


async def setup_database_schema():
    """Set up database schema using SQLAlchemy models."""
    engine = create_async_engine(TEST_DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            # Drop all existing tables
            logger.info("Dropping existing tables...")
            await conn.run_sync(Base.metadata.drop_all)
            
            # Create all tables
            logger.info("Creating database tables...")
            await conn.run_sync(Base.metadata.create_all)
            
            # Insert initial units
            logger.info("Inserting initial units...")
            await conn.execute(text("""
                INSERT INTO units (name, symbol, created_at, updated_at) VALUES
                ('Length', 'm', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                ('Mass', 'kg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                ('Time', 's', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                ('Count', '#', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                ('Volume', 'L', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT (name) DO NOTHING
            """))
            
            logger.info("Database schema setup completed successfully")
            
    except Exception as e:
        logger.error(f"Error setting up database schema: {e}")
        raise
    finally:
        await engine.dispose()


async def verify_database():
    """Verify the database is ready for tests."""
    engine = create_async_engine(TEST_DATABASE_URL)
    
    try:
        async with engine.connect() as conn:
            # Check tables exist
            result = await conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """))
            tables = [row[0] for row in result]
            
            expected_tables = {'entities', 'units', 'connections'}
            found_tables = set(tables)
            
            if not expected_tables.issubset(found_tables):
                missing = expected_tables - found_tables
                raise Exception(f"Missing tables: {missing}")
            
            # Check units exist
            result = await conn.execute(text("SELECT COUNT(*) FROM units"))
            unit_count = result.scalar()
            
            if unit_count < 5:
                raise Exception(f"Expected at least 5 units, found {unit_count}")
            
            logger.info(f"✅ Database verification passed. Found tables: {', '.join(sorted(found_tables))}")
            logger.info(f"✅ Found {unit_count} units")
            
    except Exception as e:
        logger.error(f"Database verification failed: {e}")
        raise
    finally:
        await engine.dispose()


async def main():
    """Main setup function."""
    try:
        # Create database if needed
        await create_test_database()
        
        # Set up schema
        await setup_database_schema()
        
        # Verify everything is ready
        await verify_database()
        
        logger.info("✅ Test database setup completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"Test database setup failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))