#!/usr/bin/env python3
"""
Resource monitoring script for SIMILE performance tests.
Monitors CPU, memory, disk, and network usage during tests.
"""
import asyncio
import json
import time
import psutil
import argparse
import signal
import sys
from datetime import datetime
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResourceMonitor:
    def __init__(self, output_file=None, interval=1):
        self.output_file = output_file
        self.interval = interval
        self.metrics = []
        self.running = False
        self.start_time = None
        
    def collect_system_metrics(self):
        """Collect system-wide metrics."""
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=None)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # Memory metrics
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # Disk metrics
        disk_usage = psutil.disk_usage('/')
        disk_io = psutil.disk_io_counters()
        
        # Network metrics
        network_io = psutil.net_io_counters()
        
        # Process metrics (find SIMILE-related processes)
        simile_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'cmdline']):
            try:
                if any(keyword in ' '.join(proc.info['cmdline'] or []).lower() 
                      for keyword in ['simile', 'uvicorn', 'fastapi', 'locust']):
                    simile_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cpu_percent': proc.info['cpu_percent'],
                        'memory_percent': proc.info['memory_percent'],
                        'cmdline': ' '.join(proc.info['cmdline'] or [])
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'elapsed_time': time.time() - self.start_time if self.start_time else 0,
            'cpu': {
                'percent': cpu_percent,
                'count': cpu_count,
                'frequency': cpu_freq._asdict() if cpu_freq else None
            },
            'memory': {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used,
                'free': memory.free,
                'buffers': memory.buffers,
                'cached': memory.cached
            },
            'swap': {
                'total': swap.total,
                'used': swap.used,
                'free': swap.free,
                'percent': swap.percent
            },
            'disk': {
                'usage': {
                    'total': disk_usage.total,
                    'used': disk_usage.used,
                    'free': disk_usage.free,
                    'percent': disk_usage.percent
                },
                'io': disk_io._asdict() if disk_io else None
            },
            'network': {
                'bytes_sent': network_io.bytes_sent,
                'bytes_recv': network_io.bytes_recv,
                'packets_sent': network_io.packets_sent,
                'packets_recv': network_io.packets_recv,
                'errin': network_io.errin,
                'errout': network_io.errout,
                'dropin': network_io.dropin,
                'dropout': network_io.dropout
            },
            'processes': simile_processes
        }
    
    def collect_docker_metrics(self):
        """Collect Docker container metrics if available."""
        try:
            import docker
            client = docker.from_env()
            containers = client.containers.list()
            
            container_metrics = []
            for container in containers:
                if any(keyword in container.name.lower() for keyword in ['simile', 'postgres']):
                    try:
                        stats = container.stats(stream=False)
                        
                        # Calculate CPU percentage
                        cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                                   stats['precpu_stats']['cpu_usage']['total_usage']
                        system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                                      stats['precpu_stats']['system_cpu_usage']
                        
                        if system_delta > 0:
                            cpu_percent = (cpu_delta / system_delta) * 100.0
                        else:
                            cpu_percent = 0.0
                        
                        # Memory usage
                        memory_usage = stats['memory_stats']['usage']
                        memory_limit = stats['memory_stats']['limit']
                        memory_percent = (memory_usage / memory_limit) * 100.0
                        
                        # Network stats
                        networks = stats.get('networks', {})
                        total_rx = sum(net['rx_bytes'] for net in networks.values())
                        total_tx = sum(net['tx_bytes'] for net in networks.values())
                        
                        container_metrics.append({
                            'name': container.name,
                            'id': container.id[:12],
                            'cpu_percent': cpu_percent,
                            'memory_usage': memory_usage,
                            'memory_limit': memory_limit,
                            'memory_percent': memory_percent,
                            'network_rx': total_rx,
                            'network_tx': total_tx,
                            'status': container.status
                        })
                    except Exception as e:
                        logger.warning(f"Could not get stats for container {container.name}: {e}")
                        
            return container_metrics
            
        except ImportError:
            logger.debug("Docker library not available, skipping container metrics")
            return []
        except Exception as e:
            logger.warning(f"Could not collect Docker metrics: {e}")
            return []
    
    async def monitor_loop(self):
        """Main monitoring loop."""
        self.start_time = time.time()
        self.running = True
        
        logger.info(f"Starting resource monitoring (interval: {self.interval}s)")
        
        while self.running:
            try:
                # Collect system metrics
                metrics = self.collect_system_metrics()
                
                # Collect Docker metrics if available
                docker_metrics = self.collect_docker_metrics()
                if docker_metrics:
                    metrics['containers'] = docker_metrics
                
                self.metrics.append(metrics)
                
                # Log periodic summary
                if len(self.metrics) % 30 == 0:  # Every 30 seconds
                    logger.info(f"Collected {len(self.metrics)} metric samples. "
                              f"CPU: {metrics['cpu']['percent']:.1f}%, "
                              f"Memory: {metrics['memory']['percent']:.1f}%")
                
                await asyncio.sleep(self.interval)
                
            except Exception as e:
                logger.error(f"Error collecting metrics: {e}")
                await asyncio.sleep(self.interval)
    
    def stop(self):
        """Stop monitoring."""
        self.running = False
        logger.info("Stopping resource monitoring")
    
    def save_metrics(self, output_file=None):
        """Save collected metrics to file."""
        if not output_file:
            output_file = self.output_file
        
        if not output_file:
            output_file = f"resource_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Calculate summary statistics
        if self.metrics:
            summary = self.calculate_summary()
        else:
            summary = {}
        
        output_data = {
            'monitoring_info': {
                'start_time': self.start_time,
                'end_time': time.time(),
                'duration': time.time() - self.start_time if self.start_time else 0,
                'interval': self.interval,
                'sample_count': len(self.metrics)
            },
            'summary': summary,
            'metrics': self.metrics
        }
        
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        logger.info(f"Resource metrics saved to {output_path}")
        return output_path
    
    def calculate_summary(self):
        """Calculate summary statistics from collected metrics."""
        if not self.metrics:
            return {}
        
        # Extract time series data
        cpu_values = [m['cpu']['percent'] for m in self.metrics]
        memory_values = [m['memory']['percent'] for m in self.metrics]
        
        # Calculate statistics
        import statistics
        
        summary = {
            'cpu': {
                'mean': statistics.mean(cpu_values),
                'median': statistics.median(cpu_values),
                'min': min(cpu_values),
                'max': max(cpu_values),
                'stdev': statistics.stdev(cpu_values) if len(cpu_values) > 1 else 0
            },
            'memory': {
                'mean': statistics.mean(memory_values),
                'median': statistics.median(memory_values),
                'min': min(memory_values),
                'max': max(memory_values),
                'stdev': statistics.stdev(memory_values) if len(memory_values) > 1 else 0
            },
            'peak_usage': {
                'cpu_percent': max(cpu_values),
                'memory_percent': max(memory_values),
                'memory_bytes': max(m['memory']['used'] for m in self.metrics)
            }
        }
        
        # Add container summaries if available
        if self.metrics and 'containers' in self.metrics[0]:
            container_names = set()
            for metric in self.metrics:
                if 'containers' in metric:
                    for container in metric['containers']:
                        container_names.add(container['name'])
            
            summary['containers'] = {}
            for container_name in container_names:
                container_cpu = []
                container_memory = []
                
                for metric in self.metrics:
                    if 'containers' in metric:
                        for container in metric['containers']:
                            if container['name'] == container_name:
                                container_cpu.append(container['cpu_percent'])
                                container_memory.append(container['memory_percent'])
                
                if container_cpu:
                    summary['containers'][container_name] = {
                        'cpu': {
                            'mean': statistics.mean(container_cpu),
                            'max': max(container_cpu)
                        },
                        'memory': {
                            'mean': statistics.mean(container_memory),
                            'max': max(container_memory)
                        }
                    }
        
        return summary

# Global monitor instance for signal handling
monitor = None

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    global monitor
    if monitor:
        monitor.stop()
    sys.exit(0)

async def main():
    global monitor
    
    parser = argparse.ArgumentParser(description="Resource monitoring for SIMILE performance tests")
    parser.add_argument("--output", type=str, help="Output file for metrics")
    parser.add_argument("--interval", type=float, default=1.0, 
                       help="Monitoring interval in seconds (default: 1.0)")
    parser.add_argument("--duration", type=float, help="Maximum monitoring duration in seconds")
    
    args = parser.parse_args()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create monitor
    monitor = ResourceMonitor(output_file=args.output, interval=args.interval)
    
    try:
        # Start monitoring
        if args.duration:
            # Run for specified duration
            monitor_task = asyncio.create_task(monitor.monitor_loop())
            await asyncio.sleep(args.duration)
            monitor.stop()
            await monitor_task
        else:
            # Run until interrupted
            await monitor.monitor_loop()
    
    except KeyboardInterrupt:
        logger.info("Monitoring interrupted by user")
    except Exception as e:
        logger.error(f"Error during monitoring: {e}")
    finally:
        # Save metrics
        if monitor.metrics:
            output_path = monitor.save_metrics()
            print(f"Resource metrics saved to: {output_path}")
        else:
            print("No metrics collected")

if __name__ == "__main__":
    asyncio.run(main())