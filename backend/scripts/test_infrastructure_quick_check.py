#!/usr/bin/env python3
"""
Quick infrastructure check script - runs a few critical tests to validate the setup.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

async def main():
    """Run quick infrastructure checks."""
    print("🔍 Running quick infrastructure checks...")
    
    # Check 1: Simple unit test
    print("\n1. Testing simple unit functionality...")
    result = os.system("source venv/bin/activate && PYTHONPATH=. TEST_DATABASE_HOST=localhost python -m pytest tests/test_entities_comprehensive_coverage.py::TestEntitiesComprehensiveCoverage::test_create_entity_basic_success -v --tb=short")
    if result == 0:
        print("✅ Basic entity tests passed")
    else:
        print("❌ Basic entity tests failed")
        return False
    
    # Check 2: Test database connection
    print("\n2. Testing database validation...")
    result = os.system("source venv/bin/activate && PYTHONPATH=. TEST_DATABASE_HOST=localhost python scripts/validate_test_infrastructure.py")
    if result == 0:
        print("✅ Infrastructure validation passed")
    else:
        print("❌ Infrastructure validation failed")
        return False
    
    # Check 3: Test one performance test
    print("\n3. Testing performance test infrastructure...")
    result = os.system("source venv/bin/activate && PYTHONPATH=. TEST_DATABASE_HOST=localhost python -c \"import psutil; import pytest_benchmark; print('Performance deps OK')\"")
    if result == 0:
        print("✅ Performance dependencies available")
    else:
        print("❌ Performance dependencies missing")
        return False
    
    print("\n🎉 All infrastructure checks passed!")
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)