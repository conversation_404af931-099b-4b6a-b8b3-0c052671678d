"""
Factory pattern for creating FastAPI app instances.
This allows tests to create isolated app instances to avoid shared state.
"""
from typing import Any, Dict, Union
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from .routes import entities, units, connections, compare


def create_app() -> FastAPI:
    """
    Create a new FastAPI app instance with all routes and middleware
    configured.

    Returns:
        FastAPI: A configured FastAPI application instance
    """
    app = FastAPI(
        title="SIMILE API",
        description="API for calculating transitive relationships between "
                    "entities",
        version="1.0.0",
        docs_url="/api/v1/docs",
        redoc_url="/api/v1/redoc",
        openapi_url="/api/v1/openapi.json"
    )

    # Custom exception handler for validation errors
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(
            request: Request, exc: RequestValidationError):
        """Handle validation errors with proper 422 status code and standard
        format"""
        # Return errors in standard FastAPI format (list of error objects)
        errors = []
        for error in exc.errors():
            # Create a JSON-serializable error object
            input_value = error.get('input')
            # Convert bytes to string for JSON serialization
            if isinstance(input_value, bytes):
                try:
                    input_value = input_value.decode('utf-8')
                except UnicodeDecodeError:
                    input_value = str(input_value)

            enhanced_error = {
                'type': error['type'],
                'loc': error['loc'],
                'msg': error['msg'],
                'input': input_value
            }

            # Handle context that might contain non-serializable objects
            if 'ctx' in error and error['ctx']:
                ctx: Dict[str, Union[str, int, float, bool, None]] = {}
                for key, value in error['ctx'].items():
                    if key == 'error' and hasattr(value, '__str__'):
                        # Convert error objects to strings
                        ctx[key] = str(value)
                    elif isinstance(value, (str, int, float, bool,
                                            type(None))):
                        # Keep JSON-serializable types
                        ctx[key] = value
                    else:
                        # Convert other types to strings
                        ctx[key] = str(value)
                enhanced_error['ctx'] = ctx

            # Add URL if present
            if 'url' in error:
                enhanced_error['url'] = error['url']

            # Enhance error messages for better user experience
            if error['type'] == 'value_error':
                enhanced_error['msg'] = error['msg']
            elif error['type'] == 'assertion_error':
                enhanced_error['msg'] = error['msg']
            elif (error['type'] == 'greater_than' and
                  'multiplier' in str(error['loc'])):
                enhanced_error['msg'] = "Multiplier must be positive"
            elif error['type'] == 'decimal_parsing':
                enhanced_error['msg'] = "Invalid decimal format"
            elif (error.get('msg') == "Input should be greater than 0" and
                  'multiplier' in str(error['loc'])):
                enhanced_error['msg'] = "Multiplier must be positive"
            elif (error.get('msg') ==
                  "Cannot create connection from entity to itself"):
                enhanced_error['msg'] = ("Cannot create connection from "
                                         "entity to itself")

            errors.append(enhanced_error)

        return JSONResponse(
            status_code=422,
            content={"detail": errors}
        )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, replace with specific origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers (matching main.py exactly)
    app.include_router(entities.router, prefix="/api/v1/entities",
                       tags=["entities"])
    app.include_router(units.router, prefix="/api/v1/units", tags=["units"])
    app.include_router(connections.router, prefix="/api/v1/connections",
                       tags=["connections"])
    app.include_router(compare.router, prefix="/api/v1/compare",
                       tags=["compare"])

    # Health check endpoint (matching main.py)
    @app.get("/api/v1/health")
    async def health_check():
        return {"status": "healthy", "service": "simile-api"}

    return app
