from decimal import Decimal
import logging

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ..database import get_db
from ..models import Entity, Unit
from ..schemas import CompareResponse
from ..services import find_shortest_path, get_entity_names_for_path

router = APIRouter()
logger = logging.getLogger(__name__)


async def _compare_entities_impl(
    from_entity_id: int,
    to_entity_id: int,
    unit_id: int,
    db: AsyncSession
) -> CompareResponse:
    """Internal implementation for entity comparison."""
    # Validate from_entity exists
    from_entity_result = await db.execute(
        select(Entity).where(Entity.id == from_entity_id)
    )
    from_entity = from_entity_result.scalar_one_or_none()
    if not from_entity:
        raise HTTPException(status_code=404, detail="From entity not found")

    # Validate to_entity exists
    to_entity_result = await db.execute(
        select(Entity).where(Entity.id == to_entity_id)
    )
    to_entity = to_entity_result.scalar_one_or_none()
    if not to_entity:
        raise HTTPException(status_code=404, detail="To entity not found")

    # Validate unit exists
    unit_result = await db.execute(select(Unit).where(Unit.id == unit_id))
    unit = unit_result.scalar_one_or_none()
    if not unit:
        raise HTTPException(status_code=404, detail="Unit not found")

    # Same entity comparison
    if from_entity_id == to_entity_id:
        return CompareResponse(
            from_entity=from_entity,
            to_entity=to_entity,
            unit=unit,
            multiplier=Decimal("1.0"),
            path=[],
            error=None
        )

    # Find path between entities
    logger.info(
        f"Finding path between entity {from_entity_id} and "
        f"{to_entity_id} for unit {unit_id}"
    )
    multiplier, path = await find_shortest_path(
        db, from_entity_id, to_entity_id, unit_id
    )

    if multiplier is None:
        logger.warning(
            f"No path found between '{from_entity.name}' "
            f"(id={from_entity_id}) and '{to_entity.name}' "
            f"(id={to_entity_id}) for unit '{unit.name}' (id={unit_id})"
        )
        raise HTTPException(
            status_code=404,
            detail=(f"No path found between '{from_entity.name}' and "
                    f"'{to_entity.name}' for unit '{unit.name}'. "
                    "Consider creating a direct connection.")
        )

    logger.info(
        f"Found path with multiplier {multiplier} and "
        f"{len(path) if path else 0} steps"
    )

    # Enrich path with entity names
    enriched_path = await get_entity_names_for_path(db, path) if path else []

    return CompareResponse(
        from_entity=from_entity,
        to_entity=to_entity,
        unit=unit,
        multiplier=multiplier,
        path=enriched_path,
        error=None
    )


@router.get("/", response_model=CompareResponse)
async def compare_entities_query(
    from_entity_id: int = Query(..., alias="from"),
    to_entity_id: int = Query(..., alias="to"),
    unit_id: int = Query(..., alias="unit"),
    db: AsyncSession = Depends(get_db)
):
    """Compare two entities and find their relationship.

    Uses query parameters for all inputs.
    """
    return await _compare_entities_impl(
        from_entity_id, to_entity_id, unit_id, db
    )


@router.get("/{from_entity_id}/{to_entity_id}", response_model=CompareResponse)
async def compare_entities_path(
    from_entity_id: int,
    to_entity_id: int,
    unit_id: int = Query(...),
    db: AsyncSession = Depends(get_db)
):
    """Compare two entities and find their relationship.

    Uses path parameters for entity IDs, query parameter for unit.
    """
    return await _compare_entities_impl(
        from_entity_id, to_entity_id, unit_id, db
    )
