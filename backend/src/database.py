import os
from sqlalchemy import MetaData
from sqlalchemy.ext.asyncio import (
    AsyncSession, async_sessionmaker, create_async_engine
)
from sqlalchemy.orm import declarative_base
from sqlalchemy.pool import NullPool

from .config import settings

# Determine if we're in test mode
IS_TESTING = (
    "pytest" in os.environ.get("_", "") or
    "TEST_DATABASE_HOST" in os.environ
)

# Configure engine differently for tests vs production
if IS_TESTING:
    # Use NullPool for tests to avoid connection sharing between tests
    # This ensures complete isolation between test processes
    engine = create_async_engine(
        settings.database_url,
        echo=settings.debug,
        future=True,
        # Use NullPool - creates new connections for each request
        poolclass=NullPool,
        # Async-specific settings for better pytest compatibility
        connect_args={
            "server_settings": {
                "application_name": "simile_test",
                "jit": "off",  # Disable JIT for more predictable test behavior
            }
        }
    )
else:
    # Production settings with connection pooling
    engine = create_async_engine(
        settings.database_url,
        echo=settings.debug,
        future=True,
        # Production pool configuration
        pool_size=20,          # Larger pool for production
        max_overflow=30,       # More overflow capacity
        pool_timeout=30,       # Wait up to 30 seconds for a connection
        pool_pre_ping=False,   # Disabled due to greenlet issues with asyncpg
        pool_recycle=3600,     # Recycle connections after 1 hour
    )

async_session = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,  # Disabled to prevent lazy loading issues
    # Session configuration for better test isolation
    autoflush=True,  # Always autoflush for consistency
    autocommit=False
)

Base = declarative_base()
metadata = MetaData()


async def get_db():
    async with async_session() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
