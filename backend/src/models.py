from datetime import datetime

from sqlalchemy import (
    CheckConstraint,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    Numeric,
    String,
    UniqueConstraint,
    func,
)

from .database import Base


class Unit(Base):
    __tablename__ = "units"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False)
    symbol = Column(String(10), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)


class Entity(Base):
    __tablename__ = "entities"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)

    __table_args__ = (
        Index("entities_name_unique", func.lower(name), unique=True),
    )


class Connection(Base):
    __tablename__ = "connections"

    id = Column(Integer, primary_key=True, index=True)
    from_entity_id = Column(Integer,
                            ForeignKey("entities.id", ondelete="CASCADE"),
                            nullable=False)
    to_entity_id = Column(Integer,
                          ForeignKey("entities.id", ondelete="CASCADE"),
                          nullable=False)
    unit_id = Column(Integer,
                     ForeignKey("units.id", ondelete="RESTRICT"),
                     nullable=False)
    multiplier = Column(Numeric(10, 1), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)

    __table_args__ = (
        UniqueConstraint("from_entity_id", "to_entity_id", "unit_id",
                         name="connections_unique"),
        CheckConstraint("from_entity_id != to_entity_id",
                        name="connections_no_self_reference"),
        CheckConstraint("multiplier > 0",
                        name="connections_positive_multiplier"),
    )
