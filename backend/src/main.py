from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .config import settings
from .routes import entities, units, connections, compare

# For backward compatibility, create a default app instance
# In production, this is what uvicorn will use
app = FastAPI(
    title=settings.app_name,
    version="1.0.0",
    docs_url="/api/v1/docs",
    redoc_url="/api/v1/redoc",
    openapi_url="/api/v1/openapi.json"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(entities.router, prefix="/api/v1/entities",
                   tags=["entities"])
app.include_router(units.router, prefix="/api/v1/units", tags=["units"])
app.include_router(connections.router, prefix="/api/v1/connections",
                   tags=["connections"])
app.include_router(compare.router, prefix="/api/v1/compare",
                   tags=["compare"])


@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy", "service": "simile-api"}
