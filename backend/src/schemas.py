from datetime import datetime
from decimal import Decimal, ROUND_HALF_EVEN
from typing import Optional

from pydantic import (BaseModel, Field, field_validator, model_validator,
                      ConfigDict)


class UnitBase(BaseModel):
    name: str = Field(..., max_length=50, min_length=1)
    symbol: str = Field(..., max_length=10, min_length=1)

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Name cannot be empty or only whitespace')
        return v.strip()

    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v):
        if not v or not v.strip():
            raise ValueError('Symbol cannot be empty or only whitespace')
        return v.strip()


class UnitCreate(UnitBase):
    pass


class Unit(UnitBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class EntityBase(BaseModel):
    name: str = Field(..., max_length=100, pattern=r"^[a-zA-Z\s]+$")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('Name cannot be empty or only whitespace')
        return v.strip()


class EntityCreate(EntityBase):
    pass


class EntityUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100, pattern=r"^[a-zA-Z\s]+$")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('Name cannot be empty or only whitespace')
            return v.strip()
        return v


class Entity(EntityBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ConnectionBase(BaseModel):
    model_config = ConfigDict(extra='forbid')

    from_entity_id: int
    to_entity_id: int
    unit_id: int
    multiplier: Decimal = Field(
        ...,
        gt=0,
        le=float(*********.9),
        description="Multiplier must be between 0 and 999,999,999.9 "
                    "(inclusive)"
    )

    @field_validator('multiplier')
    @classmethod
    def validate_multiplier(cls, v):
        if v <= 0:
            raise ValueError('Multiplier must be positive')
        # Check upper bound before rounding
        if v >= Decimal('*********.9'):
            raise ValueError('Multiplier too large - must be less than or '
                             'equal to 999,999,999.9')
        # Round to 1 decimal place using ROUND_HALF_EVEN (banker's rounding)
        rounded = v.quantize(Decimal('0.1'), rounding=ROUND_HALF_EVEN)
        # Reject values that round to less than 0.1 (too small)
        if rounded < Decimal('0.1'):
            raise ValueError('Multiplier too small - must be at least 0.1 '
                             'after rounding')
        return rounded

    @model_validator(mode='after')
    def validate_no_self_connection(self):
        """Validate that from_entity_id and to_entity_id are different"""
        if self.from_entity_id == self.to_entity_id:
            raise ValueError('Cannot create connection from entity to itself')
        return self


class ConnectionCreate(ConnectionBase):
    pass


class ConnectionUpdate(BaseModel):
    model_config = ConfigDict(extra='forbid')

    multiplier: Optional[Decimal] = Field(
        None,
        gt=0,
        le=float(*********.9),
        description="Multiplier must be between 0 and 999,999,999.9 "
                    "(inclusive)"
    )

    @field_validator('multiplier')
    @classmethod
    def validate_multiplier(cls, v):
        if v is not None:
            if v <= 0:
                raise ValueError('Multiplier must be positive')
            # Check upper bound before rounding
            if v >= Decimal('1000000.0'):
                raise ValueError('Multiplier too large - must be less than '
                                 'or equal to 1,000,000.0')
            # Round to 1 decimal place using ROUND_HALF_EVEN (banker's)
            rounded = v.quantize(Decimal('0.1'), rounding=ROUND_HALF_EVEN)
            # Reject values that round to too small (less than 0.1)
            if rounded < Decimal('0.1'):
                raise ValueError('Multiplier too small - must be at least '
                                 '0.1 after rounding')
            return rounded
        return v


class Connection(ConnectionBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class CompareRequest(BaseModel):
    from_entity_id: int
    to_entity_id: int
    unit_id: int


class CompareResponse(BaseModel):
    from_entity: Entity
    to_entity: Entity
    unit: Unit
    multiplier: Optional[Decimal]
    path: Optional[list[dict]]
    error: Optional[str]
