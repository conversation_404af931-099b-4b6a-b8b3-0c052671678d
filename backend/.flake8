[flake8]
# Ignore common issues that don't affect functionality
# E501: line too long
# W503: line break before binary operator
# W504: line break after binary operator  
# F841: local variable assigned but never used (common in tests)
# W391: blank line at end of file
# W291: trailing whitespace
ignore = E501,W503,W504,F841,W391,W291
max-line-length = 88
max-complexity = 10
exclude = 
    .git,
    __pycache__,
    venv,
    .venv,
    .pytest_cache,
    htmlcov,
    .coverage,
    build,
    dist,
    *.egg-info