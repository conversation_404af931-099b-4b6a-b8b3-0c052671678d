# Test Database Isolation Fix Documentation

## Issue Summary

**Date:** July 6, 2025  
**Severity:** HIGH - Test contamination in production database  
**Impact:** Test artifacts appearing in main `simile` database instead of isolated `simile_test` database  

## Root Cause Analysis

### 1. Configuration Issue in `enhanced_fixtures.py`
- The file imported `get_settings` function that didn't exist
- This caused it to fall back to the global `settings` instance
- The global `settings` was instantiated at import time before `TEST_DATABASE_HOST` was set
- Result: Tests used the production database URL instead of test database URL

### 2. Missing `get_settings()` Function
- No function to create fresh settings instances with current environment variables
- Tests relied on module-level `settings` which was frozen at import time

### 3. Test Database Didn't Exist
- The `simile_test` database was never created
- Tests fell back to the main `simile` database

## Evidence of Contamination

### Database Logs (July 6, 2025 01:20-01:27)
```
2025-07-06 01:20:02.457 UTC [212] ERROR:  deadlock detected
2025-07-06 01:20:02.471 UTC [209] ERROR:  duplicate key value violates unique constraint "entities_name_unique"
2025-07-06 01:20:02.471 UTC [209] DETAIL:  Key (lower(name::text))=(concurrent test entity) already exists.
```

### Contaminated Entities Found
```sql
SELECT name FROM entities WHERE name LIKE '%Test%' OR name LIKE '%Cleanu%';
```
Results: 14 test entities with names like:
- `Cleanu W D AAB V`
- `Cleanu W D AAC F` 
- `Cleanu W D AAD Z`
- etc.

## Fixes Implemented

### 1. Fixed `src/config.py`
**Added the missing `get_settings()` function:**
```python
def get_settings() -> Settings:
    """
    Get settings instance with proper test database configuration.
    
    This function ensures that the TEST_DATABASE_HOST environment variable
    is checked at the time the settings are requested, not at import time.
    """
    return Settings()
```

### 2. Fixed `tests/enhanced_fixtures.py`
**Removed invalid import:**
```python
# OLD (broken):
from src.database import get_async_session, Base

# NEW (fixed):
from src.database import Base
```

### 3. Created Test Database
```bash
docker exec simile-db-dev psql -U postgres -c "CREATE DATABASE simile_test;"
```

### 4. Cleaned Main Database
**Removed 14 contaminated test entities** from main database:
- Entities before cleanup: 17
- Entities after cleanup: 3 (A, B, C - legitimate entities)
- Test entities removed: 14

## Tools Created

### 1. `scripts/validate_test_isolation.py`
**Comprehensive validation script that checks:**
- Environment variables
- Settings configuration
- Database connectivity  
- Contamination detection
- Provides actionable recommendations

### 2. `scripts/cleanup_main_database.py`
**Safe cleanup script that:**
- Identifies test entity patterns
- Shows preview of what will be removed
- Requires double confirmation
- Provides backup instructions
- Removes test entities and their connections

## Prevention Measures

### 1. Always Use `get_settings()`
**Instead of global `settings`:**
```python
# GOOD:
from src.config import get_settings
settings = get_settings()

# BAD:
from src.config import settings  # Frozen at import time
```

### 2. Ensure Test Database Exists
**Run validation before tests:**
```bash
python scripts/validate_test_isolation.py
```

### 3. Set Environment Variables Correctly
**For tests:**
```bash
export TEST_DATABASE_HOST=localhost
export DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test"
```

### 4. Monitor for Contamination
**Regular checks:**
```bash
# Run after test sessions
python scripts/validate_test_isolation.py

# Check for test entities in main DB
docker exec simile-db-dev psql -U postgres -d simile -c "SELECT COUNT(*) FROM entities WHERE name LIKE '%Test%';"
```

## Testing the Fix

### 1. Validation Results (After Fix)
```
✅ Main Database (simile): Connected successfully - 3 entities
✅ Test Database (simile_test): Connected successfully - 0 entities
✅ Main database is clean (no test entities found)
✅ Test isolation appears to be working correctly
```

### 2. Configuration Test
```python
# Test environment variable handling
os.environ["TEST_DATABASE_HOST"] = "localhost"
settings = get_settings()
assert "simile_test" in settings.database_url
```

## Critical Lessons Learned

### 1. Import-Time vs Runtime Configuration
- **Problem:** Global settings instantiated at import time
- **Solution:** Use factory functions that check environment at runtime

### 2. Test Infrastructure Dependencies  
- **Problem:** Missing test database caused fallback to production
- **Solution:** Validate infrastructure before running tests

### 3. Environment Variable Timing
- **Problem:** Environment variables set after imports
- **Solution:** Defer configuration until explicitly requested

## Future Recommendations

### 1. Automated Validation
Add to CI/CD pipeline:
```bash
# Before tests
python scripts/validate_test_isolation.py
if [ $? -ne 0 ]; then exit 1; fi
```

### 2. Database Naming Convention
Use distinct prefixes:
- Production: `simile`
- Test: `simile_test`
- Development: `simile_dev`

### 3. Configuration Validation Tests
Add unit tests for configuration logic:
```python
def test_settings_with_test_host():
    os.environ["TEST_DATABASE_HOST"] = "localhost"
    settings = get_settings()
    assert "simile_test" in settings.database_url
```

### 4. Test Database Initialization
Add to test setup scripts:
```bash
# Ensure test database exists
docker exec simile-db-dev psql -U postgres -c "CREATE DATABASE IF NOT EXISTS simile_test;"
```

## Summary

The test database contamination was caused by configuration timing issues where environment variables were not available when the global settings were instantiated. This was fixed by:

1. **Adding `get_settings()` function** for runtime configuration
2. **Fixing broken imports** in test fixtures  
3. **Creating missing test database**
4. **Cleaning contaminated main database**
5. **Creating validation and cleanup tools**

The main database now contains only legitimate entities (A, B, C) and tests should be properly isolated to the `simile_test` database.

**Status: ✅ RESOLVED**