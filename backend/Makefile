# Backend Makefile for SIMILE project

.PHONY: help check-venv check-services test-setup test-teardown test test-parallel lint typecheck coverage clean
.PHONY: perf-deps perf-test perf-quick perf-stress perf-baseline perf-clean test-data perf-benchmark perf-benchmark-ci

help:
	@echo "Available commands:"
	@echo "  make check-venv      - Check virtual environment is active"
	@echo "  make check-services  - Check required services are running"
	@echo "  make test-setup      - Set up test database"
	@echo "  make test-teardown   - Clean up test database"
	@echo "  make test            - Run all tests with parallel execution"
	@echo "  make test-parallel   - Run tests in parallel processes (same as test)"
	@echo "  make lint            - Run linting"
	@echo "  make typecheck       - Run type checking"
	@echo "  make coverage        - Run tests with coverage"
	@echo "  make clean           - Clean up cache files"
	@echo ""
	@echo "Performance Testing:"
	@echo "  make perf-deps       - Install performance testing dependencies"
	@echo "  make perf-test       - Run full performance test suite"
	@echo "  make perf-quick      - Run quick performance test (2 min)"
	@echo "  make perf-stress     - Run stress test with high load"
	@echo "  make perf-baseline   - Run baseline performance measurement"
	@echo "  make perf-benchmark  - Run pytest-based performance benchmarks"
	@echo "  make perf-benchmark-ci - Run performance benchmarks for CI/CD"
	@echo "  make perf-validate   - Validate performance test suite setup"
	@echo "  make perf-clean      - Clean up performance test data and results"
	@echo "  make test-data       - Generate test data for performance testing"

# Check virtual environment is active
check-venv:
	@python scripts/check_venv.py

check-services: check-venv
	@python scripts/check_test_services.py

test-setup: check-services
	python scripts/test_db_setup.py

test-teardown: check-venv
	python scripts/test_db_teardown.py

test-parallel: check-venv test-setup
	DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test" \
	TEST_DATABASE_HOST=localhost \
	PYTHONPATH=. \
	pytest -n auto --dist loadfile -v || (make test-teardown && exit 1)
	make test-teardown

# Make parallel test the default
test: test-parallel

lint:
	flake8 src tests

typecheck:
	mypy src

coverage: check-venv test-setup
	DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test" \
	TEST_DATABASE_HOST=localhost \
	PYTHONPATH=. \
	pytest -n auto --dist loadfile --cov=src --cov-report=term-missing --cov-report=html || (make test-teardown && exit 1)
	make test-teardown

clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf .pytest_cache
	rm -rf .coverage
	rm -rf htmlcov

# Performance Testing Commands
# ===========================

perf-deps: check-venv
	@echo "Installing performance testing dependencies..."
	pip install -r requirements-perf.txt

test-data: check-venv perf-deps test-setup
	@echo "Generating test data for performance testing..."
	python scripts/test_data_generator.py --entities 1000 --connections 5000 --complexity moderate

perf-baseline: check-venv perf-deps check-services
	@echo "Running baseline performance measurement..."
	USERS=10 SPAWN_RATE=2 RUN_TIME=3m ./scripts/run_performance_tests.sh

perf-quick: check-venv perf-deps check-services
	@echo "Running quick performance test..."
	USERS=25 SPAWN_RATE=5 RUN_TIME=2m ./scripts/run_performance_tests.sh

perf-test: check-venv perf-deps check-services
	@echo "Running full performance test suite..."
	USERS=50 SPAWN_RATE=5 RUN_TIME=5m ./scripts/run_performance_tests.sh

perf-stress: check-venv perf-deps check-services
	@echo "Running stress test with high load..."
	USERS=200 SPAWN_RATE=20 RUN_TIME=10m ./scripts/run_performance_tests.sh

perf-clean: check-venv
	@echo "Cleaning up performance test data and results..."
	python scripts/test_data_generator.py --cleanup || true
	rm -rf performance_results/
	rm -f performance_baseline.json
	make test-teardown

# Database state validation
db-state-capture: check-venv check-services
	@echo "Capturing current database state..."
	python scripts/validate_db_state.py --capture db_state_$(shell date +%Y%m%d_%H%M%S).json

db-integrity-check: check-venv check-services
	@echo "Validating database integrity..."
	python scripts/validate_db_state.py --integrity

# Resource monitoring (run in background)
monitor-resources: check-venv perf-deps
	@echo "Starting resource monitoring (run in background)..."
	python scripts/resource_monitor.py --output resource_monitor_$(shell date +%Y%m%d_%H%M%S).json &

monitor-db: check-venv perf-deps check-services
	@echo "Starting database performance monitoring (run in background)..."
	python scripts/db_performance_monitor.py --output db_monitor_$(shell date +%Y%m%d_%H%M%S).json &

# Performance Benchmark Commands (pytest-based)
# ===============================================

perf-benchmark: check-venv perf-deps check-services
	@echo "Running pytest-based performance benchmarks..."
	DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test" \
	TEST_DATABASE_HOST=localhost \
	PYTHONPATH=. \
	python scripts/run_performance_benchmark.py --test-pattern "test_performance_benchmarks.py" --output-dir performance_reports

perf-benchmark-ci: check-venv perf-deps check-services
	@echo "Running performance benchmarks for CI/CD (JSON output only)..."
	DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test" \
	TEST_DATABASE_HOST=localhost \
	PYTHONPATH=. \
	python scripts/run_performance_benchmark.py --test-pattern "test_performance_benchmarks.py" --output-dir performance_reports --json-only

perf-validate: check-venv
	@echo "Validating performance test suite setup..."
	PYTHONPATH=. python scripts/validate_performance_tests.py