#!/usr/bin/env python3
"""
Comprehensive script to fix all flake8 issues in test files.
"""

import re
import sys

def fix_comprehensive_issues(file_path):
    """Fix all flake8 issues in a file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix E303 - too many blank lines (max 2)
    content = re.sub(r'\n\n\n\n+', '\n\n\n', content)
    
    # Fix E302 - expected 2 blank lines before class
    content = re.sub(r'\n\nclass ', r'\n\n\nclass ', content)
    
    # Fix E122 - continuation line missing indentation
    # Fix common patterns where function calls are not properly indented
    content = re.sub(
        r'(\s+)(create_response = await test_client\.post\(\n)(\s+)("/api/v1/[^"]+", json=)',
        r'\1\2\1    \4',
        content
    )
    
    # Fix E501 - line too long by breaking long lines
    # Pattern 1: Long f-strings with API calls
    content = re.sub(
        r'f"/api/v1/compare/\?from=\{([^}]+)\}&to=\{([^}]+)\}&unit=\{([^}]+)\}"',
        r'f"/api/v1/compare/?from={\1}&to={\2}&"\n            f"unit={\3}"',
        content
    )
    
    # Pattern 2: Long entity creation calls
    content = re.sub(
        r'create_test_entity_name\("([^"]{30,})"\)',
        r'create_test_entity_name(\n            "\1")',
        content
    )
    
    # Pattern 3: Long test client calls
    content = re.sub(
        r'(\s+)(create_response\d* = await test_client\.post\()("[^"]{50,}", json=)',
        r'\1\2\n\1    \3',
        content
    )
    
    # Pattern 4: Long assertion error messages
    content = re.sub(
        r'assert "([^"]{40,})" in ([^"]+\["detail"\])',
        r'assert (\n            "\1" in \2)',
        content
    )
    
    # Fix specific E122 issues - continuation lines need proper indentation
    content = re.sub(
        r'(\s+)(response = await test_client\.get\(\n)(\s+)(f"/api/v1/)',
        r'\1\2\1    \4',
        content
    )
    
    # Fix continuation line for json parameter
    content = re.sub(
        r'(\s+)(await test_client\.post\(\n)(\s+)("/api/v1/[^"]+", json=)',
        r'\1\2\1    \3',
        content
    )
    
    # Fix very long create_test_entity_name calls
    patterns_to_fix = [
        (r'create_test_entity_name\("([^"]{25,})"\)', r'create_test_entity_name(\n            "\1")'),
        (r'f"/api/v1/compare/\?from=\{entity1\[\'id\'\]\}&to=\{entity2\[\'id\'\]\}&unit=9999"', 
         r'f"/api/v1/compare/?from={entity1[\'id\']}&to={entity2[\'id\']}&"\n            f"unit=9999"'),
        (r'f"/api/v1/compare/\?from=\{entity_id\}&to=\{entity_id\}&unit=\{unit_id\}"',
         r'f"/api/v1/compare/?from={entity_id}&to={entity_id}&"\n            f"unit={unit_id}"'),
        (r'f"/api/v1/compare/\?from=\{([^}]+)\}&to=\{([^}]+)\}&unit=\{([^}]+)\}"',
         r'f"/api/v1/compare/?from={\1}&to={\2}&"\n            f"unit={\3}"'),
        (r'f"/api/v1/compare/\?from=\{from_entity_id\}&to=9999&unit=\{unit_id\}"',
         r'f"/api/v1/compare/?from={from_entity_id}&to=9999&"\n            f"unit={unit_id}"'),
    ]
    
    for pattern, replacement in patterns_to_fix:
        content = re.sub(pattern, replacement, content)
    
    # Fix specific line length issues in entity creation
    content = re.sub(
        r'create_response1 = await test_client\.post\("/api/v1/entities/", json=entity1_data\)',
        r'create_response1 = await test_client.post(\n            "/api/v1/entities/", json=entity1_data)',
        content
    )
    
    content = re.sub(
        r'create_response2 = await test_client\.post\("/api/v1/entities/", json=entity2_data\)',
        r'create_response2 = await test_client.post(\n            "/api/v1/entities/", json=entity2_data)',
        content
    )
    
    # Fix long lines in for loops
    content = re.sub(
        r'(\s+)(create_response = await test_client\.post\(\n)(\s+)("/api/v1/entities/", json=entity_data\))',
        r'\1\2\1    \4',
        content
    )
    
    # Fix long lines in connections
    content = re.sub(
        r'(\s+)(conn_response = await test_client\.post\(\n)(\s+)("/api/v1/connections/", json=conn_data\))',
        r'\1\2\1    \4',
        content
    )
    
    # Fix long lines in specific connection creation
    content = re.sub(
        r'(\s+)(conn_ab_response = await test_client\.post\(\n)(\s+)("/api/v1/connections/", json=connection_ab_data\))',
        r'\1\2\1    \4',
        content
    )
    
    content = re.sub(
        r'(\s+)(conn_bc_response = await test_client\.post\(\n)(\s+)("/api/v1/connections/", json=connection_bc_data\))',
        r'\1\2\1    \4',
        content
    )
    
    # Fix long lines in entity name creation
    content = re.sub(
        r'entity_name = create_test_entity_name\("([^"]{25,})"\)',
        r'entity_name = create_test_entity_name(\n            "\1")',
        content
    )
    
    # Fix the specific patterns that are still failing
    content = re.sub(
        r'(\s+)(entity_data = \{"name": entity_name\}\n)(\s+)(create_response = await test_client\.post\(\n)(\s+)("/api/v1/entities/", json=entity_data\))',
        r'\1\2\1\4\1    \6',
        content
    )
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed comprehensive flake8 issues in {file_path}")

if __name__ == "__main__":
    files_to_fix = [
        "tests/test_compare_comprehensive.py",
        "tests/test_compare_error_scenarios.py", 
        "tests/test_comprehensive_connections.py",
        "tests/test_comprehensive_entities.py",
        "tests/test_comprehensive_pathfinding.py"
    ]
    
    for file_path in files_to_fix:
        try:
            fix_comprehensive_issues(file_path)
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")