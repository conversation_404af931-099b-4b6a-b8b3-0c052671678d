#!/usr/bin/env python3
"""
Quick script to fix common linting issues in the specified files.
"""
import re
import sys

def fix_file(filename):
    """Fix common linting issues in a file."""
    with open(filename, 'r') as f:
        content = f.read()
    
    # Fix whitespace on blank lines (W293)
    content = re.sub(r'^ +$', '', content, flags=re.MULTILINE)
    
    # Ensure file ends with newline (W292)
    if not content.endswith('\n'):
        content += '\n'
    
    with open(filename, 'w') as f:
        f.write(content)
    
    print(f"Fixed whitespace issues in {filename}")

if __name__ == "__main__":
    files_to_fix = [
        "tests/fixtures/complex_graphs.py",
        "tests/test_services_comprehensive.py"
    ]
    
    for filename in files_to_fix:
        fix_file(filename)