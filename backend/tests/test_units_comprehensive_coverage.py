"""
Comprehensive test suite for Units route to achieve 85% coverage.
Targets all missing code paths including edge cases, error handling,
and validation.
"""
import pytest
from httpx import AsyncClient


class TestUnitsComprehensiveCoverage:
    """Comprehensive test suite for units route coverage improvement."""

    @pytest.mark.asyncio
    async def test_get_units_success(self, test_client: AsyncClient):
        """Test getting all units successfully."""
        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200
        units = response.json()
        assert isinstance(units, list)
        assert len(units) > 0  # Should have predefined units

        # Verify unit structure
        for unit in units:
            assert "id" in unit
            assert "name" in unit
            assert "symbol" in unit
            assert "created_at" in unit
            assert "updated_at" in unit
            assert isinstance(unit["id"], int)
            assert isinstance(unit["name"], str)
            assert isinstance(unit["symbol"], str)

    @pytest.mark.asyncio
    async def test_get_units_ordering(self, test_client: AsyncClient):
        """Test that units are returned ordered by name."""
        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200
        units = response.json()

        # Verify ordering by name
        unit_names = [unit["name"] for unit in units]
        sorted_names = sorted(unit_names)
        assert unit_names == sorted_names

    @pytest.mark.asyncio
    async def test_create_unit_success(self, test_client: AsyncClient):
        """Test successful unit creation."""
        unit_data = {
            "name": "testunit",
            "symbol": "tu"
        }

        response = await test_client.post("/api/v1/units/", json=unit_data)
        assert response.status_code == 200
        unit = response.json()
        assert unit["name"] == unit_data["name"]
        assert unit["symbol"] == unit_data["symbol"]
        assert "id" in unit
        assert "created_at" in unit
        assert "updated_at" in unit

    @pytest.mark.asyncio
    async def test_create_unit_with_various_valid_names(
            self, test_client: AsyncClient):
        """Test creating units with various valid name patterns."""
        valid_units = [
            {"name": "distance", "symbol": "d"},
            {"name": "velocity", "symbol": "v"},
            {"name": "acceleration", "symbol": "a"},
            {"name": "force", "symbol": "F"},
            {"name": "energy", "symbol": "E"},
            {"name": "power", "symbol": "P"},
            {"name": "temperature", "symbol": "T"},
            {"name": "electric_current", "symbol": "I"},
            {"name": "luminous_intensity", "symbol": "Iv"}
        ]

        for unit_data in valid_units:
            response = await test_client.post("/api/v1/units/", json=unit_data)
            assert response.status_code == 200
            unit = response.json()
            assert unit["name"] == unit_data["name"]
            assert unit["symbol"] == unit_data["symbol"]

    @pytest.mark.asyncio
    async def test_create_unit_duplicate_name(self, test_client: AsyncClient):
        """Test creating unit with duplicate name."""
        unit_data = {
            "name": "duplicate_unit",
            "symbol": "dup"
        }

        # Create first unit
        response1 = await test_client.post("/api/v1/units/", json=unit_data)
        assert response1.status_code == 200

        # Try to create duplicate
        response2 = await test_client.post("/api/v1/units/", json=unit_data)
        assert response2.status_code == 400
        assert "already exists" in response2.json()["detail"]

    @pytest.mark.asyncio
    async def test_create_unit_duplicate_symbol(
            self, test_client: AsyncClient):
        """Test creating unit with duplicate symbol (if enforced)."""
        unit_data1 = {
            "name": "unit_one",
            "symbol": "same"
        }
        unit_data2 = {
            "name": "unit_two",
            "symbol": "same"
        }

        # Create first unit
        response1 = await test_client.post("/api/v1/units/", json=unit_data1)
        assert response1.status_code == 200

        # Try to create with same symbol
        response2 = await test_client.post("/api/v1/units/", json=unit_data2)
        # This might succeed or fail depending on DB constraints
        assert response2.status_code in [200, 400]

    @pytest.mark.asyncio
    async def test_create_unit_validation_errors(
            self, test_client: AsyncClient):
        """Test unit creation validation errors."""
        # Test missing name
        response = await test_client.post(
            "/api/v1/units/", json={"symbol": "test"})
        assert response.status_code == 422

        # Test missing symbol
        response = await test_client.post(
            "/api/v1/units/", json={"name": "test"})
        assert response.status_code == 422

        # Test empty name
        response = await test_client.post(
            "/api/v1/units/", json={"name": "", "symbol": "test"})
        assert response.status_code == 422

        # Test empty symbol
        response = await test_client.post(
            "/api/v1/units/", json={"name": "test", "symbol": ""})
        assert response.status_code == 422

        # Test null values
        response = await test_client.post(
            "/api/v1/units/", json={"name": None, "symbol": "test"})
        assert response.status_code == 422

        response = await test_client.post(
            "/api/v1/units/", json={"name": "test", "symbol": None})
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_unit_malformed_request(
            self, test_client: AsyncClient):
        """Test unit creation with malformed requests."""
        # Test with empty JSON
        response = await test_client.post("/api/v1/units/", json={})
        assert response.status_code == 422

        # Test with invalid JSON structure
        response = await test_client.post(
            "/api/v1/units/",
            content="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422

        # Test with wrong content type
        response = await test_client.post(
            "/api/v1/units/",
            data="name=test&symbol=t",
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_unit_extra_fields(self, test_client: AsyncClient):
        """Test unit creation with extra fields (should be ignored)."""
        unit_data = {
            "name": "extra_fields_unit",
            "symbol": "efu",
            "extra_field": "should_be_ignored",
            "another_extra": 123
        }

        response = await test_client.post("/api/v1/units/", json=unit_data)
        assert response.status_code == 200
        unit = response.json()
        assert unit["name"] == unit_data["name"]
        assert unit["symbol"] == unit_data["symbol"]
        assert "extra_field" not in unit
        assert "another_extra" not in unit

    @pytest.mark.asyncio
    async def test_get_unit_by_id_success(self, test_client: AsyncClient):
        """Test getting unit by ID successfully."""
        # First create a unit
        unit_data = {
            "name": "get_by_id_unit",
            "symbol": "gbiu"
        }

        create_response = await test_client.post(
            "/api/v1/units/", json=unit_data)
        assert create_response.status_code == 200
        created_unit = create_response.json()

        # Get unit by ID
        response = await test_client.get(f"/api/v1/units/{created_unit['id']}")
        assert response.status_code == 200
        retrieved_unit = response.json()
        assert retrieved_unit["id"] == created_unit["id"]
        assert retrieved_unit["name"] == unit_data["name"]
        assert retrieved_unit["symbol"] == unit_data["symbol"]

    @pytest.mark.asyncio
    async def test_get_unit_by_id_with_predefined_units(
            self, test_client: AsyncClient):
        """Test getting predefined units by ID."""
        # Get all units first
        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200
        units = response.json()
        assert len(units) > 0

        # Test getting each unit by ID
        for unit in units:
            response = await test_client.get(f"/api/v1/units/{unit['id']}")
            assert response.status_code == 200
            retrieved_unit = response.json()
            assert retrieved_unit["id"] == unit["id"]
            assert retrieved_unit["name"] == unit["name"]
            assert retrieved_unit["symbol"] == unit["symbol"]

    @pytest.mark.asyncio
    async def test_get_unit_by_id_not_found(self, test_client: AsyncClient):
        """Test getting unit by non-existent ID."""
        response = await test_client.get("/api/v1/units/99999")
        assert response.status_code == 404
        assert "Unit not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_get_unit_by_invalid_id(self, test_client: AsyncClient):
        """Test getting unit with invalid ID formats."""
        invalid_ids = ["invalid", "0.5", "-1", "abc123", "null",
                       "undefined"]

        for invalid_id in invalid_ids:
            response = await test_client.get(f"/api/v1/units/{invalid_id}")
            assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_unit_by_zero_id(self, test_client: AsyncClient):
        """Test getting unit with ID 0."""
        response = await test_client.get("/api/v1/units/0")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_get_unit_by_negative_id(self, test_client: AsyncClient):
        """Test getting unit with negative ID."""
        response = await test_client.get("/api/v1/units/-1")
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_unit_integrity_error_handling(
            self, test_client: AsyncClient):
        """Test integrity error handling during unit creation."""
        unit_data = {
            "name": "integrity_test_unit",
            "symbol": "itu"
        }

        # Create first unit
        response1 = await test_client.post("/api/v1/units/", json=unit_data)
        assert response1.status_code == 200

        # Try to create duplicate (should trigger integrity error)
        response2 = await test_client.post("/api/v1/units/", json=unit_data)
        assert response2.status_code == 400
        error_detail = response2.json()["detail"]
        assert "already exists" in error_detail
        assert unit_data["name"] in error_detail

    @pytest.mark.asyncio
    async def test_create_unit_long_name_and_symbol(
            self, test_client: AsyncClient):
        """Test creating unit with very long name and symbol."""
        long_unit_data = {
            "name": "a" * 100,  # Very long name
            "symbol": "s" * 50  # Very long symbol
        }

        response = await test_client.post(
            "/api/v1/units/", json=long_unit_data)
        # Should either succeed or fail with validation error
        assert response.status_code in [200, 422]

    @pytest.mark.asyncio
    async def test_create_unit_special_characters(
            self, test_client: AsyncClient):
        """Test creating unit with special characters."""
        special_units = [
            {"name": "unit_with_underscore", "symbol": "u_w_u"},
            {"name": "unit-with-dash", "symbol": "u-w-d"},
            {"name": "unit.with.dots", "symbol": "u.w.d"},
            {"name": "unit with spaces", "symbol": "u w s"},
            {"name": "unit123numbers", "symbol": "u123"},
            {"name": "UPPERCASE_UNIT", "symbol": "UU"},
        ]

        for unit_data in special_units:
            response = await test_client.post("/api/v1/units/", json=unit_data)
            # Should either succeed or fail with validation error
            assert response.status_code in [200, 422]
            if response.status_code == 200:
                unit = response.json()
                assert unit["name"] == unit_data["name"]
                assert unit["symbol"] == unit_data["symbol"]

    @pytest.mark.asyncio
    async def test_create_unit_unicode_characters(
            self, test_client: AsyncClient):
        """Test creating unit with unicode characters."""
        unicode_units = [
            {"name": "température", "symbol": "°C"},
            {"name": "ångström", "symbol": "Å"},
            {"name": "unit_with_emoji", "symbol": "🔥"},
            {"name": "数学单位", "symbol": "数"},
            {"name": "единица", "symbol": "е"},
        ]

        for unit_data in unicode_units:
            response = await test_client.post("/api/v1/units/", json=unit_data)
            # Should either succeed or fail with validation error
            assert response.status_code in [200, 422]
            if response.status_code == 200:
                unit = response.json()
                assert unit["name"] == unit_data["name"]
                assert unit["symbol"] == unit_data["symbol"]

    @pytest.mark.asyncio
    async def test_units_crud_integration(self, test_client: AsyncClient):
        """Test complete CRUD integration for units."""
        unit_data = {
            "name": "integration_test_unit",
            "symbol": "itu"
        }

        # Create unit
        create_response = await test_client.post(
            "/api/v1/units/", json=unit_data)
        assert create_response.status_code == 200
        created_unit = create_response.json()

        # Verify in list
        list_response = await test_client.get("/api/v1/units/")
        assert list_response.status_code == 200
        units = list_response.json()
        created_unit_in_list = None
        for unit in units:
            if unit["id"] == created_unit["id"]:
                created_unit_in_list = unit
                break
        assert created_unit_in_list is not None
        assert created_unit_in_list["name"] == unit_data["name"]
        assert created_unit_in_list["symbol"] == unit_data["symbol"]

        # Verify individual get
        get_response = await test_client.get(
            f"/api/v1/units/{created_unit['id']}")
        assert get_response.status_code == 200
        retrieved_unit = get_response.json()
        assert retrieved_unit["id"] == created_unit["id"]
        assert retrieved_unit["name"] == unit_data["name"]
        assert retrieved_unit["symbol"] == unit_data["symbol"]

    @pytest.mark.asyncio
    async def test_units_edge_case_boundary_values(
            self, test_client: AsyncClient):
        """Test units with edge case boundary values."""
        edge_cases = [
            {"name": "a", "symbol": "a"},  # Single character
            {"name": "A", "symbol": "A"},  # Single uppercase
            {"name": "1", "symbol": "1"},  # Single digit
            {"name": " ", "symbol": " "},  # Single space
        ]

        for unit_data in edge_cases:
            response = await test_client.post("/api/v1/units/", json=unit_data)
            # Should either succeed or fail with validation error
            assert response.status_code in [200, 422]

    @pytest.mark.asyncio
    async def test_get_units_consistency(self, test_client: AsyncClient):
        """Test consistency of get_units endpoint."""
        # Call get_units multiple times and verify consistent results
        responses = []
        for _ in range(3):
            response = await test_client.get("/api/v1/units/")
            assert response.status_code == 200
            responses.append(response.json())

        # All responses should be identical
        first_response = responses[0]
        for response in responses[1:]:
            assert len(response) == len(first_response)
            # Check that all units are present in same order
            for i, unit in enumerate(response):
                assert unit["id"] == first_response[i]["id"]
                assert unit["name"] == first_response[i]["name"]
                assert unit["symbol"] == first_response[i]["symbol"]

    @pytest.mark.asyncio
    async def test_unit_data_types_and_structure(
            self, test_client: AsyncClient):
        """Test unit data types and structure validation."""
        # Create a unit to test structure
        unit_data = {
            "name": "structure_test_unit",
            "symbol": "stu"
        }

        response = await test_client.post("/api/v1/units/", json=unit_data)
        assert response.status_code == 200
        unit = response.json()

        # Verify data types
        assert isinstance(unit["id"], int)
        assert isinstance(unit["name"], str)
        assert isinstance(unit["symbol"], str)
        assert isinstance(unit["created_at"], str)
        assert isinstance(unit["updated_at"], str)

        # Verify required fields exist
        required_fields = ["id", "name", "symbol", "created_at", "updated_at"]
        for field in required_fields:
            assert field in unit
            assert unit[field] is not None

    @pytest.mark.asyncio
    async def test_units_response_format(self, test_client: AsyncClient):
        """Test units response format compliance."""
        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"

        units = response.json()
        assert isinstance(units, list)

        if len(units) > 0:
            unit = units[0]
            # Check response structure matches schema
            expected_keys = {"id", "name", "symbol", "created_at",
                             "updated_at"}
            actual_keys = set(unit.keys())
            assert expected_keys.issubset(actual_keys)

    @pytest.mark.asyncio
    async def test_create_unit_rollback_on_error(
            self, test_client: AsyncClient):
        """Test that database rollback works correctly on errors."""
        unit_data = {
            "name": "rollback_test_unit",
            "symbol": "rtu"
        }

        # Create unit successfully first
        response1 = await test_client.post("/api/v1/units/", json=unit_data)
        assert response1.status_code == 200

        # Get count before attempting duplicate
        list_response_before = await test_client.get("/api/v1/units/")
        count_before = len(list_response_before.json())

        # Try to create duplicate (should fail and rollback)
        response2 = await test_client.post("/api/v1/units/", json=unit_data)
        assert response2.status_code == 400

        # Verify count is the same (rollback worked)
        list_response_after = await test_client.get("/api/v1/units/")
        count_after = len(list_response_after.json())
        assert count_after == count_before
