"""
Integration test for performance benchmark suite.
Verifies that the performance testing infrastructure works correctly.
"""
import pytest
import time
import os
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from tests.test_performance_benchmarks import (
    PerformanceTestMixin,
    PerformanceMetrics
)


class TestPerformanceBenchmarkIntegration(PerformanceTestMixin):
    """Integration tests for performance benchmark infrastructure."""
    @pytest.mark.asyncio
    async def test_performance_mixin_functionality(self, test_client):
        """Test that PerformanceTestMixin provides expected functionality."""
        # Test performance monitoring context manager
        with self.performance_monitor():
            # Simulate some work
            await test_client.get("/api/v1/health")
            time.sleep(0.1)  # Small delay to ensure measurable time

        # Verify metrics were captured
        assert hasattr(self, 'last_response_time')
        assert hasattr(self, 'last_memory_usage')
        assert hasattr(self, 'last_cpu_usage')
        assert self.last_response_time > 0
    @pytest.mark.asyncio
    async def test_measure_endpoint_performance(self, test_client):
        """Test endpoint performance measurement functionality."""
        # Test health endpoint (should be fast and reliable)
        metrics = await self.measure_endpoint_performance(
            test_client,
            "GET",
            "/api/v1/health",
            iterations=5
        )

        # Verify metrics structure
        assert isinstance(metrics, PerformanceMetrics)
        assert len(metrics.response_times) == 5
        assert metrics.success_rate == 1.0
        assert metrics.avg_response_time > 0
        assert metrics.p95_response_time > 0

    @pytest.mark.asyncio
    async def test_create_performance_test_data(self, test_client):
        """Test performance test data generation."""
        # Create small test dataset
        test_data = await self.create_performance_test_data(
            test_client,
            entity_count=5,
            connection_density=0.2
        )

        # Verify test data structure
        assert "entities" in test_data
        assert "connections" in test_data
        assert "units" in test_data

        # Verify entities were created
        entities = test_data["entities"]
        assert len(entities) == 5
        assert all("id" in entity for entity in entities)
        assert all("name" in entity for entity in entities)

        # Verify connections were created (approximately)
        connections = test_data["connections"]
        # With 5 entities and 0.2 density, expect some connections
        # (5 * 4 * 0.2 / 2 = 2 connections approximately)
        assert len(connections) >= 0  # At least no errors

        # Verify units exist
        units = test_data["units"]
        assert len(units) > 0
        assert all("id" in unit for unit in units)

    @pytest.mark.asyncio
    async def test_performance_test_class_instantiation(self, test_client):
        """Test that performance test classes can be instantiated and used."""
        # Create instance of a performance test mixin
        entity_perf_test = PerformanceTestMixin()

        # Verify it has the mixin functionality
        assert hasattr(entity_perf_test, 'performance_monitor')
        assert hasattr(entity_perf_test, 'measure_endpoint_performance')
        assert hasattr(entity_perf_test, 'create_performance_test_data')

        # Test that it can create test data
        test_data = await entity_perf_test.create_performance_test_data(
            test_client,
            entity_count=3,
            connection_density=0.1
        )

        # May be less than 3 if some fail
        assert len(test_data["entities"]) >= 0

    @pytest.mark.asyncio
    async def test_performance_metrics_properties(self, test_client):
        """Test PerformanceMetrics property calculations."""
        # Create metrics with known values
        metrics = PerformanceMetrics(
            response_times=[10.0, 20.0, 30.0, 40.0, 50.0],
            memory_usage=[1.0, 2.0, 3.0, 4.0, 5.0],
            cpu_usage=[0.1, 0.2, 0.3, 0.4, 0.5],
            database_queries=10,
            success_rate=0.8
        )

        # Test property calculations
        assert metrics.avg_response_time == 30.0
        assert metrics.median_response_time == 30.0
        # 95th percentile of 5 items is the 5th item
        assert metrics.p95_response_time == 50.0
        assert metrics.avg_memory_usage == 3.0
        assert metrics.avg_cpu_usage == 0.3

        # Test empty metrics
        empty_metrics = PerformanceMetrics(
            response_times=[],
            memory_usage=[],
            cpu_usage=[],
            database_queries=0,
            success_rate=1.0
        )

        assert empty_metrics.avg_response_time == 0.0
        assert empty_metrics.median_response_time == 0.0
        assert empty_metrics.p95_response_time == 0.0

    @pytest.mark.asyncio
    async def test_environment_variables(self, test_client):
        """Test that required environment variables are available."""
        # Check for test database configuration
        test_db_host = os.getenv("TEST_DATABASE_HOST")
        if test_db_host:
            assert test_db_host in ["localhost", "127.0.0.1", "postgres"]

        # Check for database URL
        database_url = os.getenv("DATABASE_URL")
        if database_url:
            assert "postgresql" in database_url

    @pytest.mark.asyncio
    async def test_basic_api_availability(self, test_client):
        """Test that basic API endpoints are available."""
        # Test health endpoint
        response = await test_client.get("/api/v1/health")
        assert response.status_code == 200

        # Test entities endpoint
        response = await test_client.get("/api/v1/entities/")
        assert response.status_code == 200

        # Test units endpoint
        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200

        # Test connections endpoint
        response = await test_client.get("/api/v1/connections/")
        assert response.status_code == 200


class TestPerformanceBenchmarkConfiguration:
    """Test performance benchmark configuration and setup."""

    def test_performance_test_markers(self):
        """Test that performance test markers are properly configured."""
        # This test ensures that pytest markers are available
        # The actual marker functionality is tested by running pytest
        # Placeholder - actual functionality tested via pytest execution
        assert True

    def test_performance_dependencies(self):
        """Test that performance testing dependencies are available."""
        try:
            import psutil
            from statistics import mean, median, stdev

            # Test that psutil can get process info
            process = psutil.Process()
            memory_info = process.memory_info()
            assert memory_info.rss > 0

            # Test that statistics functions work
            test_data = [1, 2, 3, 4, 5]
            assert mean(test_data) == 3.0
            assert median(test_data) == 3.0
            assert stdev(test_data) > 0

        except ImportError as e:
            msg = f"Required performance testing dependency not available: {e}"
            pytest.fail(msg)

    def test_performance_test_file_structure(self):
        """Test that performance test files are properly structured."""
        # Check that the main performance test file exists
        parent_dir = Path(__file__).parent
        perf_test_file = parent_dir / "test_performance_benchmarks.py"
        assert perf_test_file.exists(), "Performance benchmark test file not found"

        # Check that the benchmark runner script exists
        base_dir = Path(__file__).parent.parent
        runner_script = base_dir / "scripts" / "run_performance_benchmark.py"
        assert runner_script.exists(), "Performance benchmark runner script not found"

        # Check that the script is executable
        msg = "Performance benchmark runner script not executable"
        assert os.access(runner_script, os.X_OK), msg


# Mark all tests in this file as integration tests
pytestmark = [
    pytest.mark.asyncio,
    pytest.mark.integration,
    pytest.mark.performance
]

