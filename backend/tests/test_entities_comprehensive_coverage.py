"""
Comprehensive test suite for Entities route to achieve 85% coverage.
Targets all missing code paths including edge cases, error handling, and validation.
"""
import pytest
from httpx import AsyncClient
from .conftest import create_test_entity_name


class TestEntitiesComprehensiveCoverage:
    """Comprehensive test suite for entities route coverage improvement."""

    @pytest.mark.asyncio
    async def test_create_entity_basic_success(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test basic entity creation."""
        entity_name = create_test_entity_name("BasicTestEntity")

        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )

        assert response.status_code == 201
        entity = response.json()
        assert entity["name"] == entity_name
        assert "id" in entity
        assert "created_at" in entity
        assert "updated_at" in entity
        test_data_isolation.track_entity(entity["id"])

    @pytest.mark.asyncio
    async def test_create_entity_with_spaces_and_mixed_case(self, test_client: AsyncClient, test_data_isolation):
        """Test entity creation with various valid name patterns."""
        test_cases = [
            "Entity With Multiple Spaces",
            "SingleWordEntity",
            "MixedCaseEntityName",
            "lower case entity",
            "UPPER CASE ENTITY",
            "A B C D E",  # Single characters with spaces
            "Entity" + " " * 10 + "WithLotsOfSpaces"  # Multiple consecutive spaces
        ]

        for entity_name in test_cases:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity_name}
            )

            assert response.status_code == 201
            entity = response.json()
            assert entity["name"] == entity_name.strip()  # Should be trimmed
            test_data_isolation.track_entity(entity["id"])

    @pytest.mark.asyncio
    async def test_create_entity_duplicate_handling(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test duplicate entity handling and case-insensitive uniqueness."""
        entity_name = create_test_entity_name("DuplicateTestEntity")

        # Create first entity
        response1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response1.status_code == 201
        entity1 = response1.json()
        test_data_isolation.track_entity(entity1["id"])

        # Try to create exact duplicate
        response2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response2.status_code == 400
        assert "already exists" in response2.json()["detail"]

        # Try case variations - should also fail due to case-insensitive uniqueness
        case_variations = [
            entity_name.upper(),
            entity_name.lower(),
            entity_name.title(),
            entity_name.swapcase()
        ]

        for variation in case_variations:
            if variation != entity_name:  # Skip if it's the same
                response = await test_client.post(
                    "/api/v1/entities/",
                    json={"name": variation}
                )
                assert response.status_code == 400
                assert "already exists" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_create_entity_validation_errors(
        self,
        test_client: AsyncClient
    ):
        """Test entity creation validation error scenarios."""
        validation_test_cases = [
            ("", 422, "empty string"),
            ("   ", 422, "whitespace only"),
            ("Entity123", 422, "contains numbers"),
            ("Entity@Symbol", 422, "contains special characters"),
            ("Entity-Dash", 422, "contains dash"),
            ("Entity_Underscore", 422, "contains underscore"),
            ("Entity.Period", 422, "contains period"),
            ("Entity,Comma", 422, "contains comma"),
            ("Entity!Exclamation", 422, "contains exclamation"),
            ("Entity?Question", 422, "contains question mark"),
            ("A" * 101, 422, "too long (>100 chars)"),
        ]

        for name, expected_status, description in validation_test_cases:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            assert response.status_code == expected_status, f"Failed test case: {description}"

    @pytest.mark.asyncio
    async def test_create_entity_missing_name_field(
        self,
        test_client: AsyncClient
    ):
        """Test entity creation with missing name field."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={}
        )
        assert response.status_code == 422
        assert "field required" in response.json()["detail"][0]["msg"].lower()

    @pytest.mark.asyncio
    async def test_create_entity_null_name(self, test_client: AsyncClient):
        """Test entity creation with null name."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": None}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_entities_empty_list(self, test_client: AsyncClient):
        """Test getting entities when none exist."""
        response = await test_client.get("/api/v1/entities/")
        assert response.status_code == 200
        entities = response.json()
        assert isinstance(entities, list)

    @pytest.mark.asyncio
    async def test_get_entities_with_pagination(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test entity pagination functionality."""
        # Create multiple entities for pagination testing
        entity_ids = []
        for i in range(5):
            entity_name = create_test_entity_name(f"PaginationTestEntity{chr(65+i)}")
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity_name}
            )
            assert response.status_code == 201
            entity_ids.append(response.json()["id"])
            test_data_isolation.track_entity(entity_ids[-1])

        # Test pagination with limit
        response = await test_client.get("/api/v1/entities/?limit=3")
        assert response.status_code == 200
        entities = response.json()
        assert len(entities) <= 3

        # Test pagination with skip and limit
        response = await test_client.get("/api/v1/entities/?skip=2&limit=2")
        assert response.status_code == 200
        entities = response.json()
        assert len(entities) <= 2

        # Test with very high skip value
        response = await test_client.get("/api/v1/entities/?skip=1000")
        assert response.status_code == 200
        entities = response.json()
        assert isinstance(entities, list)

    @pytest.mark.asyncio
    async def test_get_entities_invalid_pagination(
        self,
        test_client: AsyncClient
    ):
        """Test entity pagination with invalid parameters."""
        # Test negative limit
        response = await test_client.get("/api/v1/entities/?limit=-1")
        assert response.status_code == 422

        # Test invalid parameter types
        response = await test_client.get("/api/v1/entities/?skip=invalid")
        assert response.status_code == 422

        response = await test_client.get("/api/v1/entities/?limit=invalid")
        assert response.status_code == 422

        # Test zero limit (might be valid or invalid depending on implementation)
        response = await test_client.get("/api/v1/entities/?limit=0")
        assert response.status_code in [200, 422]

    @pytest.mark.asyncio
    async def test_get_entity_by_id_success(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test getting entity by ID successfully."""
        entity_name = create_test_entity_name("GetByIdTestEntity")

        # Create entity
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response.status_code == 201
        created_entity = response.json()
        test_data_isolation.track_entity(created_entity["id"])

        # Get entity by ID
        response = await test_client.get(f"/api/v1/entities/{created_entity['id']}")
        assert response.status_code == 200
        retrieved_entity = response.json()
        assert retrieved_entity["id"] == created_entity["id"]
        assert retrieved_entity["name"] == entity_name

    @pytest.mark.asyncio
    async def test_get_entity_by_id_not_found(self, test_client: AsyncClient):
        """Test getting entity by non-existent ID."""
        response = await test_client.get("/api/v1/entities/99999")
        assert response.status_code == 404
        assert "Entity not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_get_entity_by_invalid_id(self, test_client: AsyncClient):
        """Test getting entity with invalid ID formats."""
        invalid_ids = ["invalid", "0.5", "-1", "abc123"]

        for invalid_id in invalid_ids:
            response = await test_client.get(f"/api/v1/entities/{invalid_id}")
            # Some invalid IDs might return 404 instead of 422 depending on FastAPI path parsing
            assert response.status_code in [404, 422]

    @pytest.mark.asyncio
    async def test_update_entity_success(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test successful entity update."""
        entity_name = create_test_entity_name("UpdateTestEntity")
        new_entity_name = create_test_entity_name("UpdatedTestEntity")

        # Create entity
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response.status_code == 201
        created_entity = response.json()
        test_data_isolation.track_entity(created_entity["id"])
        original_updated_at = created_entity["updated_at"]

        # Update entity
        response = await test_client.put(
            f"/api/v1/entities/{created_entity['id']}",
            json={"name": new_entity_name}
        )
        assert response.status_code == 200
        updated_entity = response.json()
        assert updated_entity["name"] == new_entity_name
        assert updated_entity["id"] == created_entity["id"]
        assert updated_entity["updated_at"] != original_updated_at

    @pytest.mark.asyncio
    async def test_update_entity_not_found(self, test_client: AsyncClient):
        """Test updating non-existent entity."""
        response = await test_client.put(
            "/api/v1/entities/99999",
            json={"name": create_test_entity_name("NonExistentUpdate")}
        )
        assert response.status_code == 404
        assert "Entity not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_update_entity_duplicate_name(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test updating entity with name that already exists."""
        entity1_name = create_test_entity_name("UpdateDuplicateTestEntityOne")
        entity2_name = create_test_entity_name("UpdateDuplicateTestEntityTwo")

        # Create two entities
        response1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        assert response1.status_code == 201
        entity1 = response1.json()
        test_data_isolation.track_entity(entity1["id"])

        response2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )
        assert response2.status_code == 201
        entity2 = response2.json()
        test_data_isolation.track_entity(entity2["id"])

        # Try to update entity2 with entity1's name
        response = await test_client.put(
            f"/api/v1/entities/{entity2['id']}",
            json={"name": entity1_name}
        )
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_update_entity_validation_errors(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test update entity validation errors."""
        entity_name = create_test_entity_name("ValidationUpdateTestEntity")

        # Create entity
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response.status_code == 201
        created_entity = response.json()
        test_data_isolation.track_entity(created_entity["id"])

        # Test invalid update data
        invalid_updates = [
            ("", 422, "empty string"),
            ("   ", 422, "whitespace only"),
            ("Entity123", 422, "contains numbers"),
            ("Entity@Symbol", 422, "contains special characters"),
            ("A" * 101, 422, "too long"),
        ]

        for name, expected_status, description in invalid_updates:
            response = await test_client.put(
                f"/api/v1/entities/{created_entity['id']}",
                json={"name": name}
            )
            assert response.status_code == expected_status, f"Failed test case: {description}"

    @pytest.mark.asyncio
    async def test_update_entity_null_name(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test updating entity with null name (should be allowed for optional field)."""
        entity_name = create_test_entity_name("NullUpdateTestEntity")

        # Create entity
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response.status_code == 201
        created_entity = response.json()
        test_data_isolation.track_entity(created_entity["id"])

        # Update with null name (should not change the entity)
        response = await test_client.put(
            f"/api/v1/entities/{created_entity['id']}",
            json={"name": None}
        )
        # This should either succeed with no change or return validation error
        assert response.status_code in [200, 422]

    @pytest.mark.asyncio
    async def test_update_entity_empty_json(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test updating entity with empty JSON (should be allowed)."""
        entity_name = create_test_entity_name("EmptyUpdateTestEntity")

        # Create entity
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response.status_code == 201
        created_entity = response.json()
        test_data_isolation.track_entity(created_entity["id"])
        original_name = created_entity["name"]

        # Update with empty JSON (should not change anything)
        response = await test_client.put(
            f"/api/v1/entities/{created_entity['id']}",
            json={}
        )
        assert response.status_code == 200
        updated_entity = response.json()
        assert updated_entity["name"] == original_name

    @pytest.mark.asyncio
    async def test_delete_entity_success(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test successful entity deletion."""
        entity_name = create_test_entity_name("DeleteTestEntity")

        # Create entity
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response.status_code == 201
        created_entity = response.json()
        test_data_isolation.track_entity(created_entity["id"])

        # Delete entity
        response = await test_client.delete(f"/api/v1/entities/{created_entity['id']}")
        assert response.status_code == 200
        assert "deleted successfully" in response.json()["message"]

        # Verify entity is deleted
        response = await test_client.get(f"/api/v1/entities/{created_entity['id']}")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_entity_not_found(self, test_client: AsyncClient):
        """Test deleting non-existent entity."""
        response = await test_client.delete("/api/v1/entities/99999")
        assert response.status_code == 404
        assert "Entity not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_delete_entity_invalid_id(self, test_client: AsyncClient):
        """Test deleting entity with invalid ID."""
        response = await test_client.delete("/api/v1/entities/invalid")
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_entity_with_existing_connections_deletion(self, test_client: AsyncClient, test_data_isolation):
        """Test deletion of entity that has connections (should cascade delete)."""
        # Create two entities
        entity1_name = create_test_entity_name("ConnectionEntitySource")
        entity2_name = create_test_entity_name("ConnectionEntityTarget")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        assert entity1_response.status_code == 201
        entity1 = entity1_response.json()
        test_data_isolation.track_entity(entity1["id"])

        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )
        assert entity2_response.status_code == 201
        entity2 = entity2_response.json()
        test_data_isolation.track_entity(entity2["id"])

        # Get a unit for the connection
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        assert len(units) > 0
        unit_id = units[0]["id"]

        # Create a connection between the entities
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 2.0
        }

        connection_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert connection_response.status_code == 201
        connection = connection_response.json()
        test_data_isolation.track_connection(connection["id"])

        # Delete entity1 (should cascade delete the connection)
        delete_response = await test_client.delete(f"/api/v1/entities/{entity1['id']}")
        assert delete_response.status_code == 200

        # Verify entity is deleted
        entity_response = await test_client.get(f"/api/v1/entities/{entity1['id']}")
        assert entity_response.status_code == 404

        # Verify connection is also deleted (cascade)
        connection_response = await test_client.get(f"/api/v1/connections/{connection['id']}")
        assert connection_response.status_code == 404

    @pytest.mark.asyncio
    async def test_entity_ordering_by_name(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test that entities are returned ordered by name."""
        # Create entities with names that will test alphabetical ordering
        entity_names = [
            create_test_entity_name("ZebraEntity"),
            create_test_entity_name("AlphaEntity"),
            create_test_entity_name("BetaEntity"),
            create_test_entity_name("CharlieEntity")
        ]

        entity_ids = []
        # Create entities in non-alphabetical order
        for name in entity_names:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            assert response.status_code == 201
            entity_ids.append(response.json()["id"])
            test_data_isolation.track_entity(entity_ids[-1])

        # Get all entities and verify ordering
        response = await test_client.get("/api/v1/entities/")
        assert response.status_code == 200
        entities = response.json()

        # Filter to just our test entities and verify ordering
        test_entities = [e for e in entities if any(name in e["name"] for name in entity_names)]
        assert len(test_entities) >= 4

        # Verify they are ordered by name
        entity_names_retrieved = [e["name"] for e in test_entities]
        sorted_names = sorted(entity_names_retrieved)
        assert entity_names_retrieved == sorted_names

    @pytest.mark.asyncio
    async def test_create_entity_malformed_request(
        self,
        test_client: AsyncClient
    ):
        """Test entity creation with malformed JSON requests."""
        # Test with invalid JSON structure
        response = await test_client.post(
            "/api/v1/entities/",
            content="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422

        # Test with wrong content type
        response = await test_client.post(
            "/api/v1/entities/",
            data="name=TestEntity",
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_entity_name_trimming(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test that entity names are properly trimmed of whitespace."""
        entity_name_with_spaces = "  EntityWithSpaces  "
        expected_name = "EntityWithSpaces"

        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name_with_spaces}
        )

        assert response.status_code == 201
        entity = response.json()
        assert entity["name"] == expected_name
        test_data_isolation.track_entity(entity["id"])

    @pytest.mark.asyncio
    async def test_entity_case_sensitivity_in_operations(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test case sensitivity in various entity operations."""
        entity_name = create_test_entity_name("CaseSensitiveTestEntity")

        # Create entity
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response.status_code == 201
        entity = response.json()
        test_data_isolation.track_entity(entity["id"])

        # Test that the exact name is preserved
        assert entity["name"] == entity_name

        # Test retrieval preserves case
        response = await test_client.get(f"/api/v1/entities/{entity['id']}")
        assert response.status_code == 200
        retrieved_entity = response.json()
        assert retrieved_entity["name"] == entity_name
