"""
Integration tests for the SIMILE API.
Tests full workflows and API endpoint interactions.
"""
import pytest
from .conftest import generate_valid_entity_suffix


@pytest.mark.asyncio
class TestEntityIntegration:
    """Test entity management workflows."""

    async def test_entity_crud_workflow(self, test_client):
        """Test complete entity CRUD workflow."""
        # Create entity
        create_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Test Entity Integration CRUD {generate_valid_entity_suffix()}"}
        )
        print(f"Create response status: {create_response.status_code}")
        print(f"Create response text: {create_response.text}")
        assert create_response.status_code == 201
        entity_data = create_response.json()
        entity_id = entity_data["id"]
        original_name = entity_data["name"]

        # Read entity
        get_response = await test_client.get(f"/api/v1/entities/{entity_id}")
        assert get_response.status_code == 200
        assert get_response.json()["name"] == original_name

        # Update entity
        update_response = await test_client.put(
            f"/api/v1/entities/{entity_id}",
            json={"name": f"Updated Entity {generate_valid_entity_suffix()}"}
        )
        print(f"Update response status: {update_response.status_code}")
        print(f"Update response text: {update_response.text}")
        assert update_response.status_code == 200
        updated_name = update_response.json()["name"]

        # List entities
        list_response = await test_client.get("/api/v1/entities/")
        assert list_response.status_code == 200
        entities = list_response.json()
        print(f"Entities returned: {entities}")
        print(f"Looking for entity_id: {entity_id}")
        assert any(e["id"] == entity_id for e in entities)

        # Delete entity
        delete_response = await test_client.delete(f"/api/v1/entities/{entity_id}")
        assert delete_response.status_code == 200

        # Verify deletion
        get_deleted_response = await test_client.get(f"/api/v1/entities/{entity_id}")
        assert get_deleted_response.status_code == 404


@pytest.mark.asyncio  
class TestConnectionIntegration:
    """Test connection management workflows."""

    async def test_connection_with_inverse_creation(self, test_client):
        """Test connection creation with automatic inverse."""
        # Create test entities with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity One {suffix}"}
        )
        assert entity1_response.status_code == 201
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity Two {suffix}"}
        )
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit (assuming units are pre-loaded)
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        assert len(units) > 0
        unit_id = units[0]["id"]

        # Create connection
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.5
            }
        )
        assert connection_response.status_code == 201
        connection_data = connection_response.json()

        # Verify connection details
        assert connection_data["from_entity_id"] == entity1_id
        assert connection_data["to_entity_id"] == entity2_id
        assert float(connection_data["multiplier"]) == 2.5

        # Verify inverse connection was created
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()

        # Should have both forward and inverse connections
        forward_connection = next(
            (c for c in connections 
             if c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id),
            None
        )
        inverse_connection = next(
            (c for c in connections 
             if c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id),
            None
        )

        assert forward_connection is not None
        assert inverse_connection is not None
        assert float(forward_connection["multiplier"]) == 2.5
        assert float(inverse_connection["multiplier"]) == 0.4  # 1/2.5


@pytest.mark.asyncio
class TestComparisonIntegration:
    """Test entity comparison workflows."""

    async def test_direct_comparison(self, test_client):
        """Test direct entity comparison."""
        # Create test entities with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Small {suffix}"}
        )
        assert entity1_response.status_code == 201
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Large {suffix}"}
        )
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create connection
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 0.5
            }
        )

        # Compare entities
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity2_id}&unit={unit_id}"
        )
        assert compare_response.status_code == 200
        result = compare_response.json()

        assert result["from_entity"]["id"] == entity1_id
        assert result["to_entity"]["id"] == entity2_id
        assert float(result["multiplier"]) == 0.5
        assert result["error"] is None
        assert len(result["path"]) == 1

    async def test_transitive_comparison(self, test_client):
        """Test transitive entity comparison through intermediate entity."""
        # Create test entities: A -> B -> C with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()

        entity_a_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity A {suffix}"}
        )
        assert entity_a_response.status_code == 201
        entity_b_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity B {suffix}"}
        )
        assert entity_b_response.status_code == 201
        entity_c_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity C {suffix}"}
        )
        assert entity_c_response.status_code == 201

        entity_a_id = entity_a_response.json()["id"]
        entity_b_id = entity_b_response.json()["id"]
        entity_c_id = entity_c_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create connections: A -> B (2x) and B -> C (3x)
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity_a_id,
                "to_entity_id": entity_b_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity_b_id,
                "to_entity_id": entity_c_id,
                "unit_id": unit_id,
                "multiplier": 3.0
            }
        )

        # Compare A to C (should be 6x through B)
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity_a_id}&to={entity_c_id}&unit={unit_id}"
        )
        assert compare_response.status_code == 200
        result = compare_response.json()

        assert float(result["multiplier"]) == 6.0  # 2.0 * 3.0
        assert result["error"] is None
        assert len(result["path"]) == 2  # A->B->C

    async def test_no_path_comparison(self, test_client):
        """Test comparison when no path exists between entities."""
        # Create disconnected entities with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Isolated One {suffix}"}
        )
        assert entity1_response.status_code == 201
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Isolated Two {suffix}"}
        )
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Try to compare without any connections
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity2_id}&unit={unit_id}"
        )
        assert compare_response.status_code == 404
        result = compare_response.json()

        assert "detail" in result
        assert "no path found" in result["detail"].lower()


@pytest.mark.asyncio
class TestErrorHandling:
    """Test error handling and edge cases."""

    async def test_invalid_entity_comparison(self, test_client):
        """Test comparison with invalid entity IDs."""
        compare_response = await test_client.get(
            "/api/v1/compare/?from=999&to=998&unit=1"
        )
        assert compare_response.status_code == 404

    async def test_duplicate_entity_creation(self, test_client):
        """Test creating entities with duplicate names."""
        # Create entity with unique name
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
        entity_name = f"Duplicate Entity {suffix}"

        first_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert first_response.status_code == 201

        # Try to create duplicate (should fail based on unique constraint)
        duplicate_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        # Based on test failures, duplicates are NOT allowed
        assert duplicate_response.status_code == 400

    async def test_self_referencing_connection(self, test_client):
        """Test preventing self-referencing connections."""
        # Create entity with unique name
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()

        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Self Entity {suffix}"}
        )
        assert entity_response.status_code == 201
        entity_id = entity_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Try to create self-referencing connection
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity_id,
                "to_entity_id": entity_id,
                "unit_id": unit_id,
                "multiplier": 1.0
            }
        )
        assert connection_response.status_code == 422  # Validation error


@pytest.mark.asyncio
class TestDataConsistency:
    """Test data consistency and integrity."""

    async def test_connection_deletion_cascade(self, test_client):
        """Test that deleting entity removes associated connections."""
        # Create entities and connection with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity To Delete {suffix}"}
        )
        assert entity1_response.status_code == 201
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity To Keep {suffix}"}
        )
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get unit and create connection
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )
        connection_id = connection_response.json()["id"]

        # Verify connection exists
        get_connection_response = await test_client.get(f"/api/v1/connections/{connection_id}")
        assert get_connection_response.status_code == 200

        # Delete entity
        await test_client.delete(f"/api/v1/entities/{entity1_id}")

        # Verify connection is also deleted
        get_connection_after_response = await test_client.get(f"/api/v1/connections/{connection_id}")
        assert get_connection_after_response.status_code == 404
