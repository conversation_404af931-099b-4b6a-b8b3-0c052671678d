"""
Comprehensive error scenario tests for the compare route.
Tests all error paths and edge cases to improve code coverage.
"""
import pytest
from httpx import AsyncClient

from tests.conftest import create_test_entity_name


class TestCompareRouteErrorScenarios:
    """Test compare route error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_compare_nonexistent_from_entity_404(
        self,
        test_client: AsyncClient
    ):
        """Test compare with non-existent from entity returns 404."""
        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Try to compare with non-existent from entity
        response = await test_client.get(

            f"/api/v1/compare/?from=99999&to=99998&unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "From entity not found" in error_detail["detail"]

    @pytest.mark.asyncio
    async def test_compare_nonexistent_to_entity_404(
        self,
        test_client: AsyncClient
    ):
        """Test compare with non-existent to entity returns 404."""
        # Create a test entity for from_entity
        entity_name = \
            create_test_entity_name("FromEntityTest")
        entity_data = {"name": entity_name}
        create_response = await test_client.post(

            "/api/v1/entities/", json=entity_data)
        assert create_response.status_code == 201
        from_entity = create_response.json()

        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Try to compare with non-existent to entity
        response = await test_client.get(

            f"/api/v1/compare/?from={from_entity['id']}&to=99999&unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "To entity not found" in error_detail["detail"]

    @pytest.mark.asyncio
    async def test_compare_nonexistent_unit_404(
        self,
        test_client: AsyncClient
    ):
        """Test compare with non-existent unit returns 404."""
        # Create test entities
        entity1_name = \
            create_test_entity_name("UnitTestA")
        entity2_name = \
            create_test_entity_name("UnitTestB")

        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}

        create_response1 = await test_client.post(
            "/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()

        create_response2 = await test_client.post(
            "/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()

        # Try to compare with non-existent unit
        response = await test_client.get(

            f"/api/v1/compare/?from={entity1['id']}&to={entity2['id']}&unit=99999")
        assert response.status_code == 404
        error_detail = response.json()
        assert "Unit not found" in error_detail["detail"]

    @pytest.mark.asyncio
    async def test_compare_same_entity_returns_one(
            self, test_client: AsyncClient):
        """Test comparing entity to itself returns multiplier 1.0."""
        # Create a test entity
        entity_name = \
            create_test_entity_name("SameEntityTest")
        entity_data = {"name": entity_name}
        create_response = await test_client.post(

            "/api/v1/entities/", json=entity_data)
        assert create_response.status_code == 201
        entity = create_response.json()

        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Compare entity to itself
        response = await test_client.get(

            f"/api/v1/compare/?from={entity['id']}&to={entity['id']}&"
            f"unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        assert float(result["multiplier"]) == 1.0
        assert result["path"] == []
        assert result["error"] is None

    @pytest.mark.asyncio
    async def test_compare_no_path_returns_404(self, test_client: AsyncClient):
        """Test compare when no path exists returns 404 with helpful message."""
        # Create two isolated entities
        entity1_name = \
            create_test_entity_name("IsolatedA")
        entity2_name = \
            create_test_entity_name("IsolatedB")

        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}

        create_response1 = await test_client.post(
            "/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()

        create_response2 = await test_client.post(
            "/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()

        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Try to compare with no connection
        response = await test_client.get(

            f"/api/v1/compare/?from={entity1['id']}&to={entity2['id']}&"
            f"unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "No path found between" in error_detail["detail"]
        assert entity1_name in error_detail["detail"]
        assert entity2_name in error_detail["detail"]
        assert "Consider creating a direct connection" in \
            error_detail["detail"]


class TestCompareRouteQueryParameterValidation:
    """Test query parameter validation and edge cases."""

    @pytest.mark.asyncio
    async def test_compare_missing_required_parameters(
            self, test_client: AsyncClient):
        """Test compare with missing required query parameters."""
        # Missing 'from' parameter
        response = await test_client.get(
            "/api/v1/compare/?to=1&unit=1")
        assert response.status_code == 422

        # Missing 'to' parameter
        response = await test_client.get(
            "/api/v1/compare/?from=1&unit=1")
        assert response.status_code == 422

        # Missing 'unit' parameter
        response = await test_client.get(
            "/api/v1/compare/?from=1&to=2")
        assert response.status_code == 422

        # Missing all parameters
        response = await test_client.get(
            "/api/v1/compare/")
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_compare_invalid_parameter_types(
            self, test_client: AsyncClient):
        """Test compare with invalid parameter types."""
        # Non-numeric entity IDs
        response = await test_client.get(
            "/api/v1/compare/?from=abc&to=def&unit=xyz")
        assert response.status_code == 422

        # Float values (should be integers)
        response = await test_client.get(
            "/api/v1/compare/?from=1.5&to=2.7&unit=3.14")
        assert response.status_code == 422

        # Empty string values
        response = await test_client.get(
            "/api/v1/compare/?from=&to=&unit=")
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_compare_edge_case_zero_ids(
            self, test_client: AsyncClient):
        """Test compare with zero as entity/unit IDs."""
        # Zero is technically a valid integer but should not exist as an ID
        response = await test_client.get(
            "/api/v1/compare/?from=0&to=0&unit=0")
        assert response.status_code == 404

        # Mix of zero and valid-looking IDs
        response = await test_client.get(
            "/api/v1/compare/?from=1&to=0&unit=1")
        assert response.status_code == 404

        response = await test_client.get(
            "/api/v1/compare/?from=0&to=1&unit=1")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_compare_with_negative_entity_ids(
            self, test_client: AsyncClient):
        """Test compare with negative entity IDs."""
        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Test with negative IDs
        response = await test_client.get(

            f"/api/v1/compare/?from=-1&to=-2&unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "From entity not found" in error_detail["detail"]

        # Test with one negative and one positive
        response = await test_client.get(

            f"/api/v1/compare/?from=-1&to=1&unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "From entity not found" in error_detail["detail"]

    @pytest.mark.asyncio
    async def test_compare_with_very_large_entity_ids(
            self, test_client: AsyncClient):
        """Test compare with very large entity IDs that don't exist."""
        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Test with very large IDs
        large_id = 2147483647  # Max 32-bit integer
        response = await test_client.get(

            f"/api/v1/compare/?from={large_id}&to={large_id-1}&"
            f"unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "From entity not found" in error_detail["detail"]

    @pytest.mark.asyncio
    async def test_compare_with_unicode_and_special_characters(
            self, test_client: AsyncClient):
        """Test compare with unicode and special characters in parameters."""
        # Test with unicode characters
        response = await test_client.get(
            "/api/v1/compare/?from=🚀&to=💥&unit=🌟")
        assert response.status_code == 422

        # Test with boolean values
        response = await test_client.get(
            "/api/v1/compare/?from=true&to=false&unit=true")
        assert response.status_code == 422

        # Test with null-like strings
        response = await test_client.get(
            "/api/v1/compare/?from=null&to=undefined&unit=NaN")
        assert response.status_code == 422


class TestCompareRouteDataIntegrityScenarios:
    """Test data integrity and concurrent operation scenarios."""

    @pytest.mark.asyncio
    async def test_compare_entity_deleted_during_operation(
            self, test_client: AsyncClient):
        """Test compare when entity is deleted between validation and pathfinding."""
        # Create test entities
        entity1_name = \
            create_test_entity_name("DeleteTestA")
        entity2_name = \
            create_test_entity_name("DeleteTestB")

        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}

        create_response1 = await test_client.post(
            "/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()

        create_response2 = await test_client.post(
            "/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()

        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Create a connection first
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 3.0
        }
        conn_response = await test_client.post(

            "/api/v1/connections/", json=connection_data)
        assert conn_response.status_code == 201

        # Delete one entity - this will cascade delete the connection
        delete_response = await test_client.delete(f"/api/v1/entities/{entity2['id']}")
        assert delete_response.status_code == 200

        # Try to compare - should get 404 for missing entity
        response = await test_client.get(

            f"/api/v1/compare/?from={entity1['id']}&to={entity2['id']}&"
            f"unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "To entity not found" in error_detail["detail"]

    @pytest.mark.asyncio
    async def test_compare_with_path_using_different_units(
            self, test_client: AsyncClient):
        """Test that paths requiring different units return 404."""
        # Create test entities A->B->C
        entity_names = [
            create_test_entity_name("DiffUnitA"),
            create_test_entity_name("DiffUnitB"),
            create_test_entity_name("DiffUnitC")
        ]

        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post(

                "/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())

        entity_a, entity_b, entity_c = entities

        # Get valid unit IDs
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit1_id = units[0]["id"]
        unit2_id = units[1]["id"]

        # Create connections in different units:
        # A->B in unit1, B->C in unit2
        connection_ab_data = {
            "from_entity_id": entity_a["id"],
            "to_entity_id": entity_b["id"],
            "unit_id": unit1_id,
            "multiplier": 2.0
        }
        connection_bc_data = {
            "from_entity_id": entity_b["id"],
            "to_entity_id": entity_c["id"],
            "unit_id": unit2_id,
            "multiplier": 3.0
        }

        conn_ab_response = await test_client.post(
            "/api/v1/connections/", json=connection_ab_data)
        assert conn_ab_response.status_code == 201

        conn_bc_response = await test_client.post(
            "/api/v1/connections/", json=connection_bc_data)
        assert conn_bc_response.status_code == 201

        # Test comparison A->C in unit1 (should return 404 because path is incomplete)
        response = await test_client.get(

            f"/api/v1/compare/?from={entity_a['id']}&to={entity_c['id']}&"
            f"unit={unit1_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "No path found between" in error_detail["detail"]

    @pytest.mark.asyncio
    async def test_compare_direct_connection_success(
            self, test_client: AsyncClient):
        """Test successful comparison with direct connection."""
        # Create test entities
        entity1_name = \
            create_test_entity_name("DirectA")
        entity2_name = \
            create_test_entity_name("DirectB")

        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}

        create_response1 = await test_client.post(
            "/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()

        create_response2 = await test_client.post(
            "/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()

        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Create a direct connection
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 2.5
        }
        conn_response = await test_client.post(

            "/api/v1/connections/", json=connection_data)
        assert conn_response.status_code == 201

        # Test comparison
        response = await test_client.get(

            f"/api/v1/compare/?from={entity1['id']}&to={entity2['id']}&"
            f"unit={unit_id}")
        assert response.status_code == 200
        result = response.json()

        # Verify direct connection results
        assert result["from_entity"]["id"] == entity1["id"]
        assert result["to_entity"]["id"] == entity2["id"]
        assert result["unit"]["id"] == unit_id
        assert float(result["multiplier"]) == 2.5
        assert len(result["path"]) == 1
        assert result["error"] is None

        # Verify path structure
        path_step = result["path"][0]
        assert path_step["from_entity_id"] == entity1["id"]
        assert path_step["to_entity_id"] == entity2["id"]
        assert path_step["from_entity_name"] == entity1_name
        assert path_step["to_entity_name"] == entity2_name
        assert path_step["multiplier"] == 2.5
        assert path_step["hop"] == 1

    @pytest.mark.asyncio
    async def test_compare_multi_hop_path_success(
            self, test_client: AsyncClient):
        """Test successful comparison with multi-hop path."""
        # Create test entities A->B->C
        entity_names = [
            create_test_entity_name("MultiA"),
            create_test_entity_name("MultiB"),
            create_test_entity_name("MultiC")
        ]

        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post(

                "/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())

        entity_a, entity_b, entity_c = entities

        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Create connections: A->B (2.0) and B->C (3.0)
        connection_ab_data = {
            "from_entity_id": entity_a["id"],
            "to_entity_id": entity_b["id"],
            "unit_id": unit_id,
            "multiplier": 2.0
        }
        connection_bc_data = {
            "from_entity_id": entity_b["id"],
            "to_entity_id": entity_c["id"],
            "unit_id": unit_id,
            "multiplier": 3.0
        }

        conn_ab_response = await test_client.post(
            "/api/v1/connections/", json=connection_ab_data)
        assert conn_ab_response.status_code == 201

        conn_bc_response = await test_client.post(
            "/api/v1/connections/", json=connection_bc_data)
        assert conn_bc_response.status_code == 201

        # Test comparison A->C (should be 2.0 * 3.0 = 6.0)
        response = await test_client.get(

            f"/api/v1/compare/?from={entity_a['id']}&to={entity_c['id']}&"
            f"unit={unit_id}")
        assert response.status_code == 200
        result = response.json()

        # Verify two-hop path results
        assert result["from_entity"]["id"] == entity_a["id"]
        assert result["to_entity"]["id"] == entity_c["id"]
        assert result["unit"]["id"] == unit_id
        assert float(result["multiplier"]) == 6.0
        assert len(result["path"]) == 2
        assert result["error"] is None

        # Verify path structure
        path_step_1 = result["path"][0]
        assert path_step_1["from_entity_id"] == entity_a["id"]
        assert path_step_1["to_entity_id"] == entity_b["id"]
        assert path_step_1["multiplier"] == 2.0
        assert path_step_1["hop"] == 1

        path_step_2 = result["path"][1]
        assert path_step_2["from_entity_id"] == entity_b["id"]
        assert path_step_2["to_entity_id"] == entity_c["id"]
        assert path_step_2["multiplier"] == 3.0
        assert path_step_2["hop"] == 2


class TestCompareRoutePerformanceEdgeCases:
    """Test performance-related edge cases and resource limits."""

    @pytest.mark.asyncio
    async def test_compare_with_max_path_length(
            self, test_client: AsyncClient):
        """Test compare with path at maximum allowed length."""
        # Create a chain of entities up to max path length
        entities = []
        entity_names = []

        for i in range(7):  # Create 7 entities for 6-hop path
            entity_name = create_test_entity_name(
            f"MaxPath{chr(65+i)}")  # Use A, B, C, etc.
            entity_names.append(entity_name)
            entity_data = {"name": entity_name}
            create_response = await test_client.post(

                "/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())

        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Create chain of connections
        for i in range(len(entities) - 1):
            connection_data = {
                "from_entity_id": entities[i]["id"],
                "to_entity_id": entities[i + 1]["id"],
                "unit_id": unit_id,
                "multiplier": 2.0
            }
            conn_response = await test_client.post(

                "/api/v1/connections/", json=connection_data)
            assert conn_response.status_code == 201

        # Test comparison from first to last (6 hops)
        response = await test_client.get(

            f"/api/v1/compare/?from={entities[0]['id']}&to={entities[6]['id']}&"
            f"unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 6  # Maximum allowed hops
        assert float(result["multiplier"]) == 64.0  # 2^6

    @pytest.mark.asyncio
    async def test_compare_with_path_exceeding_max_length(
            self, test_client: AsyncClient):
        """Test compare when shortest path would exceed maximum allowed length."""
        # Create a chain of 8 entities for 7-hop path (exceeds default max of 6)
        entities = []

        for i in range(8):
            entity_name = create_test_entity_name(
            f"ExceedPath{chr(65+i)}")  # Use A, B, C, etc.
            entity_data = {"name": entity_name}
            create_response = await test_client.post(

                "/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())

        # Get a valid unit ID
        units_response = await test_client.get(
            "/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Create chain of connections
        for i in range(len(entities) - 1):
            connection_data = {
                "from_entity_id": entities[i]["id"],
                "to_entity_id": entities[i + 1]["id"],
                "unit_id": unit_id,
                "multiplier": 1.5
            }
            conn_response = await test_client.post(

                "/api/v1/connections/", json=connection_data)
            assert conn_response.status_code == 201

        # Test comparison from first to last (would need 7 hops)
        # This should return 404 as no path within max hops exists
        response = await test_client.get(

            f"/api/v1/compare/?from={entities[0]['id']}&to={entities[7]['id']}&"
            f"unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "No path found between" in error_detail["detail"]
