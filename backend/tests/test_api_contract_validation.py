"""
Comprehensive API contract and validation tests for the SIMILE API.
Tests response schema validation, request parameter validation, HTTP status
codes, content-type headers, and API versioning behavior.
"""
import pytest
from decimal import Decimal
from typing import Dict, Any
from pydantic import ValidationError

from src.schemas import (
    Entity, EntityCreate, EntityUpdate,
    Connection, ConnectionCreate, ConnectionUpdate,
    Unit, UnitCreate,
    CompareRequest, CompareResponse
)


class TestAPIResponseSchemas:
    """Test response schema validation for all endpoints."""

    @pytest.mark.asyncio
    async def test_entity_response_schema(self, test_client):
        """Test entity response conforms to schema."""
        # Create entity
        entity_data = {"name": "Test Entity"}
        response = await test_client.post(
            "/api/v1/entities/", json=entity_data)
        assert response.status_code == 201

        # Validate response schema
        entity_json = response.json()
        entity = Entity(**entity_json)

        # Verify all required fields are present
        assert entity.id > 0
        assert entity.name == "Test Entity"
        assert entity.created_at is not None
        assert entity.updated_at is not None

        # Test list response
        response = await test_client.get("/api/v1/entities/")
        assert response.status_code == 200
        entities = response.json()
        assert isinstance(entities, list)
        for entity_data in entities:
            Entity(**entity_data)  # Should not raise validation error

    @pytest.mark.asyncio
    async def test_connection_response_schema(self, test_client):
        """Test connection response conforms to schema."""
        # Create entities first
        entity1 = await test_client.post(
            "/api/v1/entities/", json={"name": "Entity One"})
        entity2 = await test_client.post(
            "/api/v1/entities/", json={"name": "Entity Two"})

        # Create connection
        connection_data = {
            "from_entity_id": entity1.json()["id"],
            "to_entity_id": entity2.json()["id"],
            "unit_id": 1,  # Length
            "multiplier": 2.5
        }
        response = await test_client.post(
            "/api/v1/connections/", json=connection_data)
        assert response.status_code == 201

        # Validate response schema
        connection_json = response.json()
        connection = Connection(**connection_json)

        # Verify all required fields
        assert connection.id > 0
        assert connection.from_entity_id == connection_data["from_entity_id"]
        assert connection.to_entity_id == connection_data["to_entity_id"]
        assert connection.unit_id == 1
        assert float(connection.multiplier) == 2.5
        assert connection.created_at is not None
        assert connection.updated_at is not None

    @pytest.mark.asyncio
    async def test_unit_response_schema(self, test_client):
        """Test unit response conforms to schema."""
        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200

        units = response.json()
        assert isinstance(units, list)
        assert len(units) >= 5  # Predefined units

        for unit_data in units:
            unit = Unit(**unit_data)
            assert unit.id > 0
            assert unit.name
            assert unit.symbol
            assert unit.created_at is not None
            assert unit.updated_at is not None

    @pytest.mark.asyncio
    async def test_compare_response_schema(self, test_client):
        """Test compare endpoint response schema."""
        # Create test data
        entity1 = await test_client.post(
            "/api/v1/entities/", json={"name": "Meter"})
        entity2 = await test_client.post(
            "/api/v1/entities/", json={"name": "Centimeter"})
        e1_id = entity1.json()["id"]
        e2_id = entity2.json()["id"]

        # Create connection
        await test_client.post("/api/v1/connections/", json={
            "from_entity_id": e1_id,
            "to_entity_id": e2_id,
            "unit_id": 1,
            "multiplier": 100
        })

        # Test compare
        response = await test_client.get(
            f"/api/v1/compare/?from={e1_id}&to={e2_id}&unit=1"
        )
        assert response.status_code == 200

        # Validate response schema
        compare_data = response.json()
        compare_response = CompareResponse(**compare_data)

        assert compare_response.from_entity.id == e1_id
        assert compare_response.to_entity.id == e2_id
        assert compare_response.unit.id == 1
        assert compare_response.multiplier is not None
        assert compare_response.path is not None
        assert compare_response.error is None


class TestRequestParameterValidation:
    """Test request parameter validation and error handling."""

    @pytest.mark.asyncio
    async def test_entity_name_validation(self, test_client):
        """Test entity name validation rules."""
        # Valid names
        valid_names = [
            "Simple Name",
            "Name With Spaces",
            "UPPERCASE",
            "lowercase",
            "MixedCase",
            "A",  # Single character
            "A" * 100  # Max length (100 chars)
        ]

        for name in valid_names:
            response = await test_client.post(
                "/api/v1/entities/", json={"name": name})
            assert response.status_code == 201, f"Failed for valid name: {name}"

        # Invalid names
        invalid_names = [
            "",  # Empty
            "   ",  # Whitespace only
            "Name123",  # Contains numbers (invalid)
            "Name@Special",  # Special characters
            "Name-With-Dash",  # Dash
            "Name_With_Underscore",  # Underscore
            "A" * 101,  # Too long (101 chars)
            "123",  # Numbers only
            "Name!",  # Exclamation
            "Name#1",  # Hash with number
        ]

        for name in invalid_names:
            response = await test_client.post(
                "/api/v1/entities/", json={"name": name})
            assert response.status_code == 422, f"Should fail for invalid name: {name}"
            error = response.json()
            assert "detail" in error

    @pytest.mark.asyncio
    async def test_connection_multiplier_validation(self, test_client):
        """Test connection multiplier validation rules."""
        # Create entities
        entity1 = await test_client.post(
            "/api/v1/entities/", json={"name": "Entity One"})
        entity2 = await test_client.post(
            "/api/v1/entities/", json={"name": "Entity Two"})
        e1_id = entity1.json()["id"]
        e2_id = entity2.json()["id"]

        # Valid multipliers
        valid_multipliers = [
            0.1,  # Min value
            1.0,
            10.5,
            999999999.8,  # Just under max value (999999999.9 might be at
                          # limit)
            100,
            0.05,  # Should round to 0.1
            0.14,  # Should round to 0.1
            0.15,  # Should round to 0.2
        ]

        for multiplier in valid_multipliers:
            response = await test_client.post("/api/v1/connections/", json={
                "from_entity_id": e1_id,
                "to_entity_id": e2_id,
                "unit_id": 1,
                "multiplier": multiplier
            })
            if multiplier < 0.1:
                assert response.status_code == 422, \
                    f"Should fail for multiplier: {multiplier}"
            else:
                assert response.status_code == 201, \
                    f"Failed for valid multiplier: {multiplier}"
                # Clean up
                await test_client.delete(
                    f"/api/v1/connections/{response.json()['id']}")

        # Invalid multipliers
        invalid_multipliers = [
            0,  # Zero
            -1,  # Negative
            -0.1,
            1000000000,  # Too large
            0.01,  # Too small (rounds to 0.0)
            0.04,  # Too small (rounds to 0.0)
        ]

        for multiplier in invalid_multipliers:
            response = await test_client.post("/api/v1/connections/", json={
                "from_entity_id": e1_id,
                "to_entity_id": e2_id,
                "unit_id": 1,
                "multiplier": multiplier
            })
            assert response.status_code == 422, \
                f"Should fail for invalid multiplier: {multiplier}"

    @pytest.mark.asyncio
    async def test_self_connection_validation(self, test_client):
        """Test that self-connections are rejected."""
        entity = await test_client.post(
            "/api/v1/entities/", json={"name": "Test Entity"})
        entity_id = entity.json()["id"]

        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity_id,
            "to_entity_id": entity_id,
            "unit_id": 1,
            "multiplier": 1.0
        })
        assert response.status_code == 422
        error = response.json()
        assert "Cannot create connection from entity to itself" in \
            str(error["detail"])

    @pytest.mark.asyncio
    async def test_missing_required_fields(self, test_client):
        """Test validation of missing required fields."""
        # Entity missing name
        response = await test_client.post(
            "/api/v1/entities/", json={})
        assert response.status_code == 422

        # Connection missing fields
        entity = await test_client.post(
            "/api/v1/entities/", json={"name": "Test"})
        entity_id = entity.json()["id"]

        missing_field_requests = [
            {"from_entity_id": entity_id, "to_entity_id": entity_id + 1,
             "unit_id": 1},  # Missing multiplier
            {"to_entity_id": entity_id + 1, "unit_id": 1,
             "multiplier": 1.0},  # Missing from_entity_id
            {"from_entity_id": entity_id, "unit_id": 1,
             "multiplier": 1.0},  # Missing to_entity_id
            {"from_entity_id": entity_id, "to_entity_id": entity_id + 1,
             "multiplier": 1.0},  # Missing unit_id
        ]

        for request_data in missing_field_requests:
            response = await test_client.post(
                "/api/v1/connections/", json=request_data)
            assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_extra_fields_rejected(self, test_client):
        """Test that extra fields are rejected (strict validation)."""
        # Connection with extra fields
        entity1 = await test_client.post(
            "/api/v1/entities/", json={"name": "Entity One"})
        entity2 = await test_client.post(
            "/api/v1/entities/", json={"name": "Entity Two"})

        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity1.json()["id"],
            "to_entity_id": entity2.json()["id"],
            "unit_id": 1,
            "multiplier": 2.0,
            "extra_field": "should_be_rejected"
        })
        assert response.status_code == 422


class TestHTTPStatusCodes:
    """Test HTTP status code consistency."""

    @pytest.mark.asyncio
    async def test_successful_status_codes(self, test_client):
        """Test successful operation status codes."""
        # GET returns 200
        response = await test_client.get("/api/v1/entities/")
        assert response.status_code == 200

        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200

        # POST returns 201
        response = await test_client.post(
            "/api/v1/entities/", json={"name": "Test Entity"})
        assert response.status_code == 201
        entity_id = response.json()["id"]

        # GET by ID returns 200
        response = await test_client.get(f"/api/v1/entities/{entity_id}")
        assert response.status_code == 200

        # PUT returns 200
        response = await test_client.put(
            f"/api/v1/entities/{entity_id}", json={"name": "Updated Entity"})
        assert response.status_code == 200

        # DELETE returns 200 (not 204 in this implementation)
        response = await test_client.delete(f"/api/v1/entities/{entity_id}")
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_not_found_status_codes(self, test_client):
        """Test 404 status codes for non-existent resources."""
        # Non-existent entity
        response = await test_client.get("/api/v1/entities/99999")
        assert response.status_code == 404

        response = await test_client.put(
            "/api/v1/entities/99999", json={"name": "Update"})
        assert response.status_code == 404

        response = await test_client.delete("/api/v1/entities/99999")
        assert response.status_code == 404

        # Non-existent connection
        response = await test_client.get("/api/v1/connections/99999")
        assert response.status_code == 404

        response = await test_client.delete("/api/v1/connections/99999")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_validation_error_status_codes(self, test_client):
        """Test 422 status codes for validation errors."""
        # Invalid entity name
        response = await test_client.post(
            "/api/v1/entities/", json={"name": "123"})
        assert response.status_code == 422

        # Invalid connection
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": 1,
            "to_entity_id": 1,  # Self-connection
            "unit_id": 1,
            "multiplier": 1.0
        })
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_method_not_allowed(self, test_client):
        """Test 405 status codes for unsupported methods."""
        # POST to entity detail endpoint
        response = await test_client.post(
            "/api/v1/entities/1",
            json={"name": "Test"}
        )
        assert response.status_code == 405

        # PUT to entity list endpoint
        response = await test_client.put(
            "/api/v1/entities/",
            json={"name": "Test"}
        )
        assert response.status_code == 405

        # DELETE to entity list endpoint
        response = await test_client.delete("/api/v1/entities/")
        assert response.status_code == 405


class TestContentTypeAndHeaders:
    """Test content-type and header validation."""

    @pytest.mark.asyncio
    async def test_response_content_type(self, test_client):
        """Test that all responses have correct content-type."""
        endpoints = [
            ("/api/v1/entities/", "GET"),
            ("/api/v1/units/", "GET"),
            ("/api/v1/connections/", "GET"),
            ("/api/v1/health", "GET"),
        ]

        for endpoint, method in endpoints:
            if method == "GET":
                response = await test_client.get(endpoint)

            assert response.headers["content-type"] == "application/json"

    @pytest.mark.asyncio
    async def test_request_content_type_validation(self, test_client):
        """Test that API validates request content-type."""
        # Send request with wrong content-type
        response = await test_client.post(
            "/api/v1/entities/",
            data="name=Test",  # Form data instead of JSON
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_cors_headers(self, test_client):
        """Test CORS headers are present in production (not in test client)."""
        # Note: CORS headers may not be present in test client
        # This test is more relevant for integration/E2E tests
        response = await test_client.get("/api/v1/entities/")
        # In test environment, CORS middleware might not be active
        # so we just check for successful response
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_options_request(self, test_client):
        """Test OPTIONS request for CORS preflight."""
        # OPTIONS might not be implemented or might return 405
        response = await test_client.options("/api/v1/entities/")
        # Accept either 200 (CORS preflight) or 405 (method not allowed)
        assert response.status_code in [200, 405]


class TestAPIVersioning:
    """Test API versioning behavior."""

    @pytest.mark.asyncio
    async def test_v1_prefix_required(self, test_client):
        """Test that /api/v1 prefix is required."""
        # Without version prefix
        response = await test_client.get("/api/entities/")
        assert response.status_code == 404

        response = await test_client.get("/entities/")
        assert response.status_code == 404

        # With version prefix
        response = await test_client.get("/api/v1/entities/")
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_openapi_version_info(self, test_client):
        """Test OpenAPI spec contains version information."""
        response = await test_client.get("/api/v1/openapi.json")
        assert response.status_code == 200

        spec = response.json()
        assert spec["info"]["version"] == "1.0.0"
        assert spec["info"]["title"] == "SIMILE API"

        # Check that all paths have v1 prefix
        for path in spec["paths"]:
            assert path.startswith("/api/v1/")


class TestPaginationAndQueryParameters:
    """Test pagination and query parameter handling."""

    @pytest.mark.asyncio
    async def test_entity_list_query_params(self, test_client):
        """Test entity list accepts and ignores unknown query params gracefully."""
        # Create some entities
        for i in range(5):
            await test_client.post("/api/v1/entities/", json={"name": f"Entity {chr(65 + i)}"})

        # Test with various query params (should be ignored for now)
        response = await test_client.get("/api/v1/entities/?page=1&limit=10&sort=name")
        assert response.status_code == 200
        entities = response.json()
        assert len(entities) >= 5

    @pytest.mark.asyncio
    async def test_connection_filtering(self, test_client):
        """Test connection list filtering parameters."""
        # Create test data
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Source"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Target"}
        )
        e1_id = entity1.json()["id"]
        e2_id = entity2.json()["id"]

        await test_client.post("/api/v1/connections/", json={
            "from_entity_id": e1_id,
            "to_entity_id": e2_id,
            "unit_id": 1,
            "multiplier": 2.0
        })

        # Test filtering (if implemented)
        response = await test_client.get(f"/api/v1/connections/?entity_id={e1_id}")
        assert response.status_code == 200


class TestDataTypeValidation:
    """Test data type validation for all fields."""

    @pytest.mark.asyncio
    async def test_integer_field_validation(self, test_client):
        """Test that integer fields reject non-integer values."""
        # String instead of integer for IDs
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": "not_an_int",
            "to_entity_id": 2,
            "unit_id": 1,
            "multiplier": 1.0
        })
        assert response.status_code == 422

        # Float instead of integer
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": 1.5,
            "to_entity_id": 2,
            "unit_id": 1,
            "multiplier": 1.0
        })
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_decimal_field_validation(self, test_client):
        """Test decimal field validation and precision."""
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Entity One"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Entity Two"}
        )

        # Test string representation of decimal
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity1.json()["id"],
            "to_entity_id": entity2.json()["id"],
            "unit_id": 1,
            "multiplier": "2.5"
        })
        assert response.status_code == 201
        assert float(response.json()["multiplier"]) == 2.5

        # Test precision rounding
        await test_client.delete(f"/api/v1/connections/{response.json()['id']}")
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity1.json()["id"],
            "to_entity_id": entity2.json()["id"],
            "unit_id": 1,
            "multiplier": 2.55  # Should round to 2.6
        })
        assert response.status_code == 201
        assert float(response.json()["multiplier"]) == 2.6

    @pytest.mark.asyncio
    async def test_string_field_trimming(self, test_client):
        """Test that string fields are properly trimmed."""
        # Entity name with extra spaces
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "  Test Entity  "}
        )
        assert response.status_code == 201
        assert response.json()["name"] == "Test Entity"

        # Update with spaces
        entity_id = response.json()["id"]
        response = await test_client.put(
            f"/api/v1/entities/{entity_id}",
            json={"name": "  Updated  "}
        )
        assert response.status_code == 200
        assert response.json()["name"] == "Updated"
