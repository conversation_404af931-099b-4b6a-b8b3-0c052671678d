"""
Phase 3 test: Automatic inverse connection creation validation.
"""
import pytest
from .conftest import generate_valid_entity_suffix


class TestInverseConnectionCreation:
    """Test automatic inverse connection creation functionality."""

    @pytest.mark.asyncio
    async def test_automatic_inverse_creation(self, test_client):
        """Test that creating A->B also creates B->A with 1/multiplier."""
        # Create test entities
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"InverseTestOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"InverseTestTwo {suffix}"}
        )

        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create connection A -> B with multiplier 4.0
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 4.0
            }
        )

        assert connection_response.status_code == 201
        original_connection = connection_response.json()
        assert float(original_connection["multiplier"]) == 4.0

        # Get all connections to find the inverse
        all_connections_response = await test_client.get(
            "/api/v1/connections/")
        assert all_connections_response.status_code == 200
        all_connections = all_connections_response.json()

        # Find the inverse connection (B -> A)
        inverse_connection = None
        for conn in all_connections:
            if (conn["from_entity_id"] == entity2_id and
                    conn["to_entity_id"] == entity1_id and
                    conn["unit_id"] == unit_id):
                inverse_connection = conn
                break

        assert inverse_connection is not None, "Inverse connection not found"

        # Verify inverse multiplier is 1/4 = 0.25, rounded to 0.3
        # (1 decimal place)
        expected_inverse = round(1.0 / 4.0, 1)  # Should be 0.3 with rounding
        actual_inverse = float(inverse_connection["multiplier"])

        print("Original multiplier: 4.0")
        print(f"Expected inverse: {expected_inverse}")
        print(f"Actual inverse: {actual_inverse}")

        # Allow for slight decimal precision differences
        assert abs(actual_inverse - expected_inverse) < 0.01

    @pytest.mark.asyncio
    async def test_decimal_rounding_in_inverse(self, test_client):
        """Test decimal rounding behavior in inverse connections."""
        # Create test entities
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"RoundTestOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"RoundTestTwo {suffix}"}
        )

        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Test with multiplier 3.0 -> inverse should be 0.3
        # (1/3 = 0.333... rounded to 0.3)
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 3.0
            }
        )

        assert connection_response.status_code == 201

        # Get all connections to find the inverse
        all_connections_response = await test_client.get(
            "/api/v1/connections/")
        all_connections = all_connections_response.json()

        # Find the inverse connection
        inverse_connection = None
        for conn in all_connections:
            if (conn["from_entity_id"] == entity2_id and
                    conn["to_entity_id"] == entity1_id and
                    conn["unit_id"] == unit_id):
                inverse_connection = conn
                break

        assert inverse_connection is not None

        # 1/3 = 0.333... should round to 0.3 with 1 decimal place
        expected_inverse = round(1.0 / 3.0, 1)  # 0.3
        actual_inverse = float(inverse_connection["multiplier"])

        print(f"1/3 rounded to 1 decimal: {expected_inverse}")
        print(f"Actual inverse: {actual_inverse}")

        assert abs(actual_inverse - expected_inverse) < 0.01

    @pytest.mark.asyncio
    async def test_connection_deletion_removes_inverse(self, test_client):
        """Test that deleting a connection also removes its inverse."""
        # Create test entities
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DeleteTestOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DeleteTestTwo {suffix}"}
        )

        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create connection
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )

        assert connection_response.status_code == 201
        original_connection_id = connection_response.json()["id"]

        # Verify both connections exist
        all_connections_before = await test_client.get("/api/v1/connections/")
        connections_before = all_connections_before.json()

        forward_exists = any(
            c for c in connections_before
            if c["from_entity_id"] == entity1_id and
            c["to_entity_id"] == entity2_id
        )
        inverse_exists = any(
            c for c in connections_before
            if c["from_entity_id"] == entity2_id and
            c["to_entity_id"] == entity1_id
        )

        assert forward_exists, "Forward connection should exist"
        assert inverse_exists, "Inverse connection should exist"

        # Delete the original connection
        delete_response = await test_client.delete(
            f"/api/v1/connections/{original_connection_id}")
        assert delete_response.status_code == 200

        # Verify both connections are gone
        all_connections_after = await test_client.get("/api/v1/connections/")
        connections_after = all_connections_after.json()

        forward_exists_after = any(
            c for c in connections_after
            if c["from_entity_id"] == entity1_id and
            c["to_entity_id"] == entity2_id
        )
        inverse_exists_after = any(
            c for c in connections_after
            if c["from_entity_id"] == entity2_id and
            c["to_entity_id"] == entity1_id
        )

        assert not forward_exists_after, "Forward connection should be deleted"
        assert not inverse_exists_after, "Inverse connection should be deleted"
