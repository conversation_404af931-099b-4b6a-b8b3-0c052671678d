# SIMILE Backend Comprehensive Test Suite

This directory contains a comprehensive test suite for the SIMILE backend API, designed to run in the containerized environment.

## Test Organization

The test suite is organized into several files, each focusing on specific functionality:

### 1. `test_endpoints.py`
- Basic health check endpoints
- API documentation endpoints
- OpenAPI spec validation

### 2. `test_comprehensive_entities.py`
- **Create Operations**: Success cases, validation, special characters, length limits
- **Read Operations**: Get all, get by ID, pagination, sorting
- **Update Operations**: Success cases, duplicate names, validation
- **Delete Operations**: Success cases, cascade behavior
- **Edge Cases**: Concurrent creation, case sensitivity, whitespace handling

### 3. `test_comprehensive_connections.py`
- **Create Operations**: Success cases, auto-inverse creation, decimal precision
- **Validation**: Same entity connections, negative/zero multipliers
- **Business Logic**: Duplicate prevention, unit validation
- **Delete Operations**: Cascade deletion of inverse connections
- **Edge Cases**: Very large/small multipliers, multiple units

### 4. `test_comprehensive_pathfinding.py`
- **Direct Comparisons**: Single-hop paths
- **Transitive Relationships**: Multi-hop paths (2-6 hops)
- **Path Selection**: Shortest path algorithm
- **Decimal Calculations**: Precision through multiple hops
- **Graph Structures**: Cycles, multiple paths, isolated nodes
- **Limits**: Maximum path length (6 hops)

### 5. `test_comprehensive_edge_cases.py`
- **Concurrency**: Race conditions, concurrent updates
- **Extreme Values**: Very large/small numbers, boundary conditions
- **Security**: SQL injection attempts, input sanitization
- **Unicode**: International characters, emojis
- **Database**: Transaction rollback, empty database
- **API Design**: Versioning, error handling

## Running the Tests

### Prerequisites
1. Ensure containers are running:
   ```bash
   podman-compose up -d
   ```

2. Verify container status:
   ```bash
   podman ps
   ```

### Run All Tests
Use the provided script to run all tests in the container:
```bash
./run_comprehensive_tests.sh
```

### Run Individual Test Files
To run a specific test file in the container:
```bash
# Run entity tests
podman exec simile-api pytest -v /app/tests/test_comprehensive_entities.py

# Run connection tests
podman exec simile-api pytest -v /app/tests/test_comprehensive_connections.py

# Run pathfinding tests
podman exec simile-api pytest -v /app/tests/test_comprehensive_pathfinding.py

# Run edge case tests
podman exec simile-api pytest -v /app/tests/test_comprehensive_edge_cases.py
```

### Run Specific Test Methods
To run a specific test:
```bash
podman exec simile-api pytest -v /app/tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success
```

### Run with Coverage
To see test coverage:
```bash
podman exec simile-api pytest --cov=src --cov-report=term-missing /app/tests/
```

## Test Database

The tests use a separate test database (`simile_test`) configured in `conftest.py`. This ensures:
- Tests don't affect production data
- Each test starts with a clean state
- Parallel test execution is possible

## Current Test Status (June 13, 2025 - UPDATED)

### ✅ Database Connectivity FIXED
The database connectivity and async event loop issues have been resolved:
- **Fixed**: All 79 async event loop errors
- **Fixed**: Database connection configuration
- **Solution**: Updated to pytest-asyncio strict mode and proper test configuration

### Test Results Summary
| Test Suite | Tests | Passing | Failing | Notes |
|------------|-------|---------|---------|-------|
| Basic Endpoints | 3 | 3 ✅ | 0 | All passing |
| Entity CRUD | 25 | 17 ✅ | 8 ❌ | Business logic issues |
| Connection CRUD | 18 | 1 ✅ | 17 ❌ | Validation logic needed |
| Pathfinding | 13 | 0 | 13 ❌ | Algorithm implementation needed |
| Edge Cases | 14 | 3 ✅ | 11 ❌ | Edge case handling needed |
| Integration Simple | 16 | 6 ✅ | 10 ❌ | Integration issues |
| Integration | 9 | 1 ✅ | 8 ❌ | Complex workflow issues |

**Total**: 98 tests, 31 passing (32%), 67 failing (68%)

## Running Tests After Infrastructure Fix

### Local Testing (Recommended)
```bash
cd backend
source venv/bin/activate

# Ensure database container is running
podman ps | grep simile-db

# Run tests with proper environment
TEST_DATABASE_HOST=localhost pytest -v

# Run specific test file
TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py -v

# Run with coverage
TEST_DATABASE_HOST=localhost pytest --cov=src --cov-report=term-missing
```

### Known Remaining Issues

1. **Business Logic Failures**:
   - Connection validation rules not fully implemented
   - Pathfinding algorithm edge cases
   - Entity name validation (case sensitivity, special chars)

2. **Test Implementation Gaps**:
   - Some fixtures need updating
   - Integration tests need TestClient configuration fixes

3. **Minor Type Errors**:
   - A few mypy errors in config.py and routes
   - These don't affect functionality

## Test Patterns

### Fixtures
- `test_client`: Async HTTP client for API calls
- `db_session`: Database session for direct database access
- `test_entities_and_unit`: Creates test data for connection tests
- `linear_chain_setup`: Creates a chain of connected entities
- `complex_graph_setup`: Creates a complex graph structure

### Assertions
Tests verify:
- HTTP status codes
- Response body content
- Database state changes
- Error messages
- Business rule enforcement

### Edge Case Coverage
- Boundary values (min/max lengths, decimals)
- Invalid inputs (special characters, SQL injection)
- Concurrent operations
- Empty/null values
- Very large/small numbers

## Adding New Tests

When adding new tests:
1. Follow the existing pattern of test organization
2. Use descriptive test names that explain what is being tested
3. Include both positive and negative test cases
4. Test edge cases and boundary conditions
5. Use fixtures to avoid code duplication
6. Add docstrings explaining complex test scenarios

## Debugging Failed Tests

To debug failing tests:
1. Run with verbose output: `-v` or `-vv`
2. Show full traceback: `--tb=long`
3. Drop into debugger on failure: `--pdb`
4. Print stdout: `-s`

Example:
```bash
podman exec simile-api pytest -vv --tb=long -s /app/tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success
```