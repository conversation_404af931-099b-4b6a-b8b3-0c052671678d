"""
Advanced test scenarios for connections route with focus on error handling,
integrity constraints, and complex business logic scenarios.

This test suite complements the comprehensive CRUD tests with:
1. Database integrity constraint testing
2. Complex error scenarios and recovery
3. Concurrent operation simulation
4. Performance edge cases
5. Advanced data cleanup scenarios
"""
import pytest
from httpx import AsyncClient

from tests.conftest import create_test_entity_name


class TestConnectionsAdvancedScenarios:
    """Advanced test scenarios for connections with error handling focus."""

    async def create_minimal_setup(self, test_client: AsyncClient):
        """Create minimal test setup for advanced scenarios."""
        # Create just two entities and get a unit
        entity1_name = create_test_entity_name("AdvancedTestEntityA")
        entity2_name = create_test_entity_name("AdvancedTestEntityB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        units_response = await test_client.get("/api/v1/units/")
        units = units_response.json()

        return {
            "entity1_id": entity1_response.json()["id"],
            "entity2_id": entity2_response.json()["id"],
            "unit_id": units[0]["id"]
        }

    @pytest.mark.asyncio
    async def test_connection_integrity_constraints(
        self,
        test_client,
        test_data_isolation
    ):
        """Test database integrity constraints and error handling."""
        setup = await self.create_minimal_setup(test_client)

        # Track entities
        test_data_isolation.track_entity(setup["entity1_id"])
        test_data_isolation.track_entity(setup["entity2_id"])

        # Test 1: Foreign key constraint (non-existent entity after deletion)
        # Create a third entity to delete
        entity3_name = create_test_entity_name("ToBeDeletedEntity")
        entity3_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity3_name}
        )
        entity3_id = entity3_response.json()["id"]
        test_data_isolation.track_entity(entity3_id)

        # Create connection with this entity
        connection_data = {
            "from_entity_id": setup["entity1_id"],
            "to_entity_id": entity3_id,
            "unit_id": setup["unit_id"],
            "multiplier": 2.0
        }

        response = await test_client.post(
            "/api/v1/connections/", json=connection_data
        )
        assert response.status_code == 201
        connection = response.json()
        test_data_isolation.track_connection(connection["id"])

        # Now delete the entity (this should cascade delete the connection)
        delete_response = await test_client.delete(
            f"/api/v1/entities/{entity3_id}"
        )
        assert delete_response.status_code == 200

        # Verify connection was also deleted
        get_response = await test_client.get(
            f"/api/v1/connections/{connection['id']}"
        )
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_connection_multiplier_boundary_values(
        self,
        test_client,
        test_data_isolation
    ):
        """Test boundary values for multiplier field."""
        setup = await self.create_minimal_setup(test_client)

        # Track entities
        test_data_isolation.track_entity(setup["entity1_id"])
        test_data_isolation.track_entity(setup["entity2_id"])

        # Test very small positive values
        boundary_tests = [
            (0.001, 422),  # Too small, should be rejected
            (0.01, 422),  # Still too small
            (0.1, 201),  # Minimum acceptable
            (0.05, 422),  # Rounds to 0.0, should be rejected
            (999999.9, 201),  # Large but acceptable
            # Too large (exactly at limit but >= fails validation)
            (999999999.9, 422),
        ]

        for i, (multiplier, expected_status) in enumerate(boundary_tests):
            # Create unique entities for each test
            entity1_name = create_test_entity_name(
                f"BoundaryTestEntity{chr(65+i)}Source"
            )
            entity2_name = create_test_entity_name(
                f"BoundaryTestEntity{chr(65+i)}Target"
            )

            entity1_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity1_name}
            )
            entity2_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity2_name}
            )

            entity1_id = entity1_response.json()["id"]
            entity2_id = entity2_response.json()["id"]

            test_data_isolation.track_entity(entity1_id)
            test_data_isolation.track_entity(entity2_id)

            connection_data = {
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": setup["unit_id"],
                "multiplier": multiplier
            }

            response = await test_client.post("/api/v1/connections/", json=connection_data)

            if expected_status == 201:
                assert response.status_code == 201, \
                    f"Failed for multiplier {multiplier}: {response.text}"
                connection = response.json()
                test_data_isolation.track_connection(connection["id"])
            else:
                assert response.status_code == expected_status, \
                    f"Expected {expected_status} for multiplier {multiplier}, " \
                    f"got {response.status_code}"

    @pytest.mark.asyncio
    async def test_connection_decimal_rounding_edge_cases(self, test_client, test_data_isolation):
        """Test edge cases in decimal rounding that could cause issues."""
        setup = await self.create_minimal_setup(test_client)

        # Test rounding edge cases that could cause precision issues (using banker's rounding)
        rounding_tests = [
            # (input, expected_main, expected_inverse_approx)
            (1.05, 1.0, 1.0),     # Banker's rounding: 1.05 rounds to 1.0 (even)
            (2.15, 2.2, 0.5),     # Banker's rounding: 2.15 rounds to 2.2 (even)
            (3.25, 3.2, 0.3),     # Banker's rounding: 3.25 rounds to 3.2 (even)
            (0.95, 1.0, 1.0),     # 0.95 rounds to 1.0
            (1.01, 1.0, 1.0),     # 1.01 rounds to 1.0
            (0.99, 1.0, 1.0),     # 0.99 rounds to 1.0
            (10.05, 10.0, 0.1),   # Banker's rounding: 10.05 rounds to 10.0 (even)
        ]

        for i, (input_val, expected_main, expected_inverse) in enumerate(rounding_tests):
            # Create unique entities for each test
            entity1_name = create_test_entity_name(f"RoundingTestEntity{chr(65+i)}Source")
            entity2_name = create_test_entity_name(f"RoundingTestEntity{chr(65+i)}Target")

            entity1_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity1_name}
            )
            entity2_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity2_name}
            )

            entity1_id = entity1_response.json()["id"]
            entity2_id = entity2_response.json()["id"]

            test_data_isolation.track_entity(entity1_id)
            test_data_isolation.track_entity(entity2_id)

            connection_data = {
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": setup["unit_id"],
                "multiplier": input_val
            }

            response = await test_client.post("/api/v1/connections/", json=connection_data)
            assert response.status_code == 201, f"Failed for input {input_val}: {response.text}"

            connection = response.json()
            test_data_isolation.track_connection(connection["id"])

            # Verify main connection multiplier (allow for floating point precision)
            actual_main = float(connection["multiplier"])
            assert abs(actual_main - expected_main) < 0.01, \
                f"Main connection: expected ~{expected_main}, got {actual_main} for input {input_val}"

            # Find and verify inverse connection
            all_connections_response = await test_client.get("/api/v1/connections/?limit=1000")
            all_connections = all_connections_response.json()

            inverse_connection = None
            for conn in all_connections:
                if (conn["from_entity_id"] == entity2_id and
                    conn["to_entity_id"] == entity1_id and
                    conn["unit_id"] == setup["unit_id"]):
                    inverse_connection = conn
                    break

            assert inverse_connection is not None, f"Inverse connection not found for input {input_val}"
            test_data_isolation.track_connection(inverse_connection["id"])

            actual_inverse = float(inverse_connection["multiplier"])
            assert abs(actual_inverse - expected_inverse) < 0.1, \
                f"Inverse connection: expected ~{expected_inverse}, got {actual_inverse} for input {input_val}"

    @pytest.mark.asyncio
    async def test_connection_update_edge_cases(
        self,
        test_client,
        test_data_isolation
    ):
        """Test edge cases in connection updates."""
        setup = await self.create_minimal_setup(test_client)

        # Track entities
        test_data_isolation.track_entity(setup["entity1_id"])
        test_data_isolation.track_entity(setup["entity2_id"])

        # Create initial connection
        connection_data = {
            "from_entity_id": setup["entity1_id"],
            "to_entity_id": setup["entity2_id"],
            "unit_id": setup["unit_id"],
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        connection = response.json()
        test_data_isolation.track_connection(connection["id"])

        # Test 1: Update with same value (should be idempotent)
        update_response = await test_client.put(
            f"/api/v1/connections/{connection['id']}",
            json={"multiplier": 2.0}
        )
        assert update_response.status_code == 200
        updated_connection = update_response.json()
        assert float(updated_connection["multiplier"]) == 2.0

        # Test 2: Update with very small value
        update_response = await test_client.put(
            f"/api/v1/connections/{connection['id']}",
            json={"multiplier": 0.1}
        )
        assert update_response.status_code == 200
        updated_connection = update_response.json()
        assert float(updated_connection["multiplier"]) == 0.1

        # Verify inverse was updated to 10.0
        all_connections_response = await test_client.get("/api/v1/connections/?limit=1000")
        all_connections = all_connections_response.json()

        inverse_connection = None
        for conn in all_connections:
            if (conn["from_entity_id"] == setup["entity2_id"] and
                conn["to_entity_id"] == setup["entity1_id"] and
                conn["unit_id"] == setup["unit_id"]):
                inverse_connection = conn
                break

        assert inverse_connection is not None
        test_data_isolation.track_connection(inverse_connection["id"])
        assert float(inverse_connection["multiplier"]) == 10.0

        # Test 3: Update with large value
        update_response = await test_client.put(
            f"/api/v1/connections/{connection['id']}",
            json={"multiplier": 1000.0}
        )
        assert update_response.status_code == 200

        # Test 4: Update with invalid values
        invalid_updates = [
            {"multiplier": 0.0},      # Zero
            {"multiplier": -1.0},     # Negative
        ]

        for invalid_data in invalid_updates:
            update_response = await test_client.put(
                f"/api/v1/connections/{connection['id']}",
                json=invalid_data
            )
            assert update_response.status_code == 422, \
                f"Should reject invalid update: {invalid_data}"

    @pytest.mark.asyncio
    async def test_connection_deletion_scenarios(
        self,
        test_client,
        test_data_isolation
    ):
        """Test various connection deletion scenarios."""
        setup = await self.create_minimal_setup(test_client)

        # Track entities
        test_data_isolation.track_entity(setup["entity1_id"])
        test_data_isolation.track_entity(setup["entity2_id"])

        # Test 1: Normal deletion
        connection_data = {
            "from_entity_id": setup["entity1_id"],
            "to_entity_id": setup["entity2_id"],
            "unit_id": setup["unit_id"],
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        connection = response.json()

        # Delete connection
        delete_response = await test_client.delete(f"/api/v1/connections/{connection['id']}")
        assert delete_response.status_code == 200

        # Verify both main and inverse are deleted
        get_response = await test_client.get(f"/api/v1/connections/{connection['id']}")
        assert get_response.status_code == 404

        # Test 2: Double deletion (idempotent)
        delete_response2 = await test_client.delete(f"/api/v1/connections/{connection['id']}")
        assert delete_response2.status_code == 404

        # Test 3: Delete non-existent connection
        delete_response3 = await test_client.delete("/api/v1/connections/99999")
        assert delete_response3.status_code == 404

    @pytest.mark.asyncio
    async def test_connection_cascade_deletion_with_entities(self, test_client, test_data_isolation):
        """Test that connections are properly deleted when entities are deleted."""
        # Create entities
        entity1_name = create_test_entity_name("CascadeTestEntitySource")
        entity2_name = create_test_entity_name("CascadeTestEntityTarget")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        test_data_isolation.track_entity(entity1_id)
        test_data_isolation.track_entity(entity2_id)

        # Get unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create connection
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": unit_id,
            "multiplier": 3.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        connection = response.json()

        # Get initial connection count
        connections_response = await test_client.get("/api/v1/connections/?limit=1000")
        initial_count = len(connections_response.json())

        # Delete one of the entities
        delete_response = await test_client.delete(f"/api/v1/entities/{entity1_id}")
        assert delete_response.status_code == 200

        # Verify connections involving this entity are deleted
        connections_response = await test_client.get("/api/v1/connections/?limit=1000")
        final_connections = connections_response.json()

        # Should have fewer connections now
        assert len(final_connections) < initial_count

        # Verify no connections reference the deleted entity
        for conn in final_connections:
            assert conn["from_entity_id"] != entity1_id
            assert conn["to_entity_id"] != entity1_id

    @pytest.mark.asyncio
    async def test_connection_error_recovery(
        self,
        test_client,
        test_data_isolation
    ):
        """Test error recovery scenarios."""
        setup = await self.create_minimal_setup(test_client)

        # Track entities
        test_data_isolation.track_entity(setup["entity1_id"])
        test_data_isolation.track_entity(setup["entity2_id"])

        # Test 1: Attempt invalid operation, then valid operation
        # Try invalid connection first
        invalid_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": setup["entity1_id"],
                "to_entity_id": setup["entity1_id"],  # Self-connection
                "unit_id": setup["unit_id"],
                "multiplier": 2.0
            }
        )
        assert invalid_response.status_code == 422

        # Now try valid connection - should work fine
        valid_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": setup["entity1_id"],
                "to_entity_id": setup["entity2_id"],
                "unit_id": setup["unit_id"],
                "multiplier": 2.0
            }
        )
        assert valid_response.status_code == 201
        connection = valid_response.json()
        test_data_isolation.track_connection(connection["id"])

        # Test 2: Attempt invalid update, then valid update
        invalid_update = await test_client.put(
            f"/api/v1/connections/{connection['id']}",
            json={"multiplier": -5.0}  # Invalid negative
        )
        assert invalid_update.status_code == 422

        # Valid update should still work
        valid_update = await test_client.put(
            f"/api/v1/connections/{connection['id']}",
            json={"multiplier": 3.0}
        )
        assert valid_update.status_code == 200

    @pytest.mark.asyncio
    async def test_connection_data_consistency(
        self,
        test_client,
        test_data_isolation
    ):
        """Test data consistency across connection operations."""
        setup = await self.create_minimal_setup(test_client)

        # Track entities
        test_data_isolation.track_entity(setup["entity1_id"])
        test_data_isolation.track_entity(setup["entity2_id"])

        # Create connection
        connection_data = {
            "from_entity_id": setup["entity1_id"],
            "to_entity_id": setup["entity2_id"],
            "unit_id": setup["unit_id"],
            "multiplier": 4.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        main_connection = response.json()
        test_data_isolation.track_connection(main_connection["id"])

        # Find inverse connection
        all_connections_response = await test_client.get("/api/v1/connections/?limit=1000")
        all_connections = all_connections_response.json()

        inverse_connection = None
        for conn in all_connections:
            if (conn["from_entity_id"] == setup["entity2_id"] and
                conn["to_entity_id"] == setup["entity1_id"] and
                conn["unit_id"] == setup["unit_id"]):
                inverse_connection = conn
                break

        assert inverse_connection is not None
        test_data_isolation.track_connection(inverse_connection["id"])

        # Verify mathematical relationship
        main_multiplier = float(main_connection["multiplier"])
        inverse_multiplier = float(inverse_connection["multiplier"])

        # Should be inverse relationship (with rounding tolerance)
        expected_inverse = 1.0 / main_multiplier
        assert abs(inverse_multiplier - expected_inverse) < 0.1, \
            f"Multipliers should be inverse: {main_multiplier} and {inverse_multiplier}"

        # Update main connection and verify inverse is updated consistently
        update_response = await test_client.put(
            f"/api/v1/connections/{main_connection['id']}",
            json={"multiplier": 8.0}
        )
        assert update_response.status_code == 200

        # Get updated inverse connection
        inverse_response = await test_client.get(f"/api/v1/connections/{inverse_connection['id']}")
        assert inverse_response.status_code == 200
        updated_inverse = inverse_response.json()

        # Verify inverse was updated consistently
        new_inverse_multiplier = float(updated_inverse["multiplier"])
        expected_new_inverse = 1.0 / 8.0  # 0.125, rounds to 0.1
        assert abs(new_inverse_multiplier - 0.1) < 0.01, \
            f"Updated inverse should be ~0.1, got {new_inverse_multiplier}"

    @pytest.mark.asyncio
    async def test_connection_with_malformed_data(
        self,
        test_client,
        test_data_isolation
    ):
        """Test handling of malformed request data."""
        setup = await self.create_minimal_setup(test_client)

        # Test various malformed request bodies
        malformed_requests = [
            {},  # Empty body
            {"from_entity_id": setup["entity1_id"]},  # Missing fields
            {"multiplier": "not_a_number"},  # Wrong data type
            {"from_entity_id": "not_an_int"},  # Wrong data type
            {
                "from_entity_id": setup["entity1_id"],
                "to_entity_id": setup["entity2_id"],
                "unit_id": setup["unit_id"],
                "multiplier": "2.0",  # String instead of number
                "extra_field": "should_be_ignored"  # Extra field
            },
        ]

        for i, malformed_data in enumerate(malformed_requests):
            response = await test_client.post("/api/v1/connections/", json=malformed_data)
            assert response.status_code == 422, \
                f"Should reject malformed request {i}: {malformed_data}"

            # Verify error response has detail
            error_response = response.json()
            assert "detail" in error_response

    @pytest.mark.asyncio
    async def test_connection_list_pagination_edge_cases(
        self,
        test_client,
        test_data_isolation
    ):
        """Test edge cases in connection list pagination."""
        # Test pagination with no connections
        response = await test_client.get("/api/v1/connections/")
        assert response.status_code == 200
        assert response.json() == []

        # Test pagination with invalid parameters
        invalid_params = [
            "?skip=-1",           # Negative skip
            "?limit=-1",          # Negative limit
            "?skip=abc",          # Non-numeric skip
            "?limit=xyz",         # Non-numeric limit
            "?limit=0",           # Zero limit
        ]

        for params in invalid_params:
            response = await test_client.get(f"/api/v1/connections/{params}")
            # Should either work with defaults or return 422
            assert response.status_code in [200, 422]

        # Test very large pagination values
        response = await test_client.get("/api/v1/connections/?skip=999999&limit=999999")
        assert response.status_code == 200
        assert response.json() == []  # Should return empty list
