"""
Comprehensive test suite for Entity CRUD operations.
Tests all edge cases, validation, and business logic.
"""
import pytest


class TestEntityCRUD:
    """Test all Entity CRUD operations comprehensively."""

    @pytest.mark.asyncio
    async def test_create_entity_success(self, test_client):
        """Test successful entity creation."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Test Entity Create Success"}
        )
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Entity Create Success"
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

    @pytest.mark.asyncio
    async def test_create_entity_with_spaces(self, test_client):
        """Test entity creation with spaces in name."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "New York City"}
        )
        assert response.status_code == 201
        assert response.json()["name"] == "New York City"

    @pytest.mark.asyncio
    async def test_create_entity_case_insensitive(self, test_client):
        """Test case-insensitive entity name uniqueness."""
        # Create first entity
        response1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "TestCase"}
        )
        assert response1.status_code == 201

        # Try to create with different case
        response2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "testcase"}
        )
        assert response2.status_code == 400
        assert "already exists" in response2.json()["detail"]

    @pytest.mark.asyncio
    async def test_create_entity_empty_name(self, test_client):
        """Test entity creation with empty name."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": ""}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_entity_whitespace_only(self, test_client):
        """Test entity creation with whitespace-only name."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "   "}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_entity_special_characters(self, test_client):
        """Test entity creation with special characters."""
        # Should fail - only alphanumeric and spaces allowed
        special_chars = ["Test@Entity", "Test#1", "Test$", "Test&Co", "Test!"]
        for name in special_chars:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            assert response.status_code == 422, f"Failed for name: {name}"

    @pytest.mark.asyncio
    async def test_create_entity_max_length(self, test_client):
        """Test entity creation with maximum length name."""
        # Max length is 100 characters
        long_name = "A" * 100
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": long_name}
        )
        assert response.status_code == 201
        assert response.json()["name"] == long_name

    @pytest.mark.asyncio
    async def test_create_entity_exceeds_max_length(self, test_client):
        """Test entity creation exceeding max length."""
        long_name = "A" * 101
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": long_name}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_entity_numeric_name(self, test_client):
        """Test entity creation with numeric name (should fail)."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "12345"}
        )
        assert response.status_code == 422  # Should fail validation

    @pytest.mark.asyncio
    async def test_create_entity_alphanumeric(self, test_client):
        """Test entity creation with alphanumeric name."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Boeing Seven Four Seven"}
        )
        assert response.status_code == 201
        assert response.json()["name"] == "Boeing Seven Four Seven"

    @pytest.mark.asyncio
    async def test_get_all_entities_empty(self, test_client):
        """Test getting entities when none exist."""
        response = await test_client.get(
            "/api/v1/entities/")
        assert response.status_code == 200
        assert isinstance(response.json(), list)

    @pytest.mark.asyncio
    async def test_get_all_entities_with_data(self, test_client):
        """Test getting all entities."""
        # Create multiple entities
        entities = ["EntityOne", "EntityTwo", "EntityThree"]
        created_ids = []

        for name in entities:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            created_ids.append(response.json()["id"])

        # Get all entities
        response = await test_client.get(
            "/api/v1/entities/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 3

        # Check if our entities are in the response
        response_names = [e["name"] for e in data]
        for name in entities:
            assert name in response_names

    @pytest.mark.asyncio
    async def test_get_entity_by_id(self, test_client):
        """Test getting a specific entity by ID."""
        # Create entity
        create_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Specific Entity"}
        )
        entity_id = create_response.json()["id"]

        # Get by ID
        response = await test_client.get(

            f"/api/v1/entities/{entity_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == entity_id
        assert data["name"] == "Specific Entity"

    @pytest.mark.asyncio
    async def test_get_entity_not_found(self, test_client):
        """Test getting non-existent entity."""
        response = await test_client.get(
            "/api/v1/entities/99999")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_get_entity_invalid_id(self, test_client):
        """Test getting entity with invalid ID format."""
        response = await test_client.get(
            "/api/v1/entities/invalid")
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_update_entity_success(self, test_client):
        """Test successful entity update."""
        # Create entity
        create_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Original Name"}
        )
        entity_id = create_response.json()["id"]

        # Update entity
        response = await test_client.put(
            f"/api/v1/entities/{entity_id}",
            json={"name": "Updated Name"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == entity_id
        assert data["name"] == "Updated Name"
        assert data["updated_at"] > data["created_at"]

    @pytest.mark.asyncio
    async def test_update_entity_not_found(self, test_client):
        """Test updating non-existent entity."""
        response = await test_client.put(
            "/api/v1/entities/99999",
            json={"name": "Updated Name"}
        )
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_entity_duplicate_name(self, test_client):
        """Test updating entity to duplicate name."""
        # Create two entities with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()

        response1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity A {suffix}"}
        )
        assert response1.status_code == 201
        response2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity B {suffix}"}
        )
        assert response2.status_code == 201
        entity_b_id = response2.json()["id"]

        # Try to update Entity B to have same name as Entity A
        response = await test_client.put(
            f"/api/v1/entities/{entity_b_id}",
            json={"name": f"Entity A {suffix}"}
        )
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_update_entity_validation(self, test_client):
        """Test entity update with invalid data."""
        # Create entity
        create_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Valid Entity"}
        )
        entity_id = create_response.json()["id"]

        # Try invalid updates
        invalid_names = ["", "   ", "@Invalid", "A" * 101]
        for name in invalid_names:
            response = await test_client.put(
                f"/api/v1/entities/{entity_id}",
                json={"name": name}
            )
            assert response.status_code in [400, 422], f"Failed for name: {name}"

    @pytest.mark.asyncio
    async def test_delete_entity_success(self, test_client):
        """Test successful entity deletion."""
        # Create entity
        create_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "To Delete"}
        )
        entity_id = create_response.json()["id"]

        # Delete entity
        response = await test_client.delete(f"/api/v1/entities/{entity_id}")
        assert response.status_code == 200

        # Verify it's deleted
        get_response = await test_client.get(
            f"/api/v1/entities/{entity_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_entity_not_found(self, test_client):
        """Test deleting non-existent entity."""
        response = await test_client.delete("/api/v1/entities/99999")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_entity_with_connections(self, test_client):
        """Test deleting entity that has connections."""
        # Create entities with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()

        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity With Connection One {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity With Connection Two {suffix}"}
        )
        assert entity1.status_code == 201
        assert entity2.status_code == 201
        entity1_id = entity1.json()["id"]
        entity2_id = entity2.json()["id"]

        # Get a unit
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]

        # Create connection
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.5
            }
        )

        # Try to delete entity with connection
        response = await test_client.delete(f"/api/v1/entities/{entity1_id}")
        # Should either succeed (cascade delete) or fail (protect)
        # Based on your business logic
        assert response.status_code in [200, 400]

    @pytest.mark.asyncio
    async def test_entity_concurrent_creation(self, test_client):
        """Test concurrent entity creation with same name."""
        import asyncio

        async def create_entity(name: str):
            return await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )

        # Try to create same entity concurrently
        results = await asyncio.gather(
            create_entity("Concurrent Entity"),
            create_entity("Concurrent Entity"),
            return_exceptions=True
        )

        # One should succeed, one should fail
        status_codes = [r.status_code for r in results if hasattr(r, 'status_code')]
        assert 201 in status_codes
        assert 400 in status_codes or len([s for s in status_codes if s == 201]) == 1

    @pytest.mark.asyncio
    async def test_entity_pagination(self, test_client):
        """Test entity list pagination (if implemented)."""
        # Create many entities with valid names (letters and spaces only)
        entity_names = [
            "Paginated Entity Alpha", "Paginated Entity Beta", "Paginated Entity Gamma",
            "Paginated Entity Delta", "Paginated Entity Epsilon", "Paginated Entity Zeta",
            "Paginated Entity Eta", "Paginated Entity Theta", "Paginated Entity Iota",
            "Paginated Entity Kappa", "Paginated Entity Lambda", "Paginated Entity Mu",
            "Paginated Entity Nu", "Paginated Entity Xi", "Paginated Entity Omicron"
        ]

        for name in entity_names:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            assert response.status_code == 201, f"Failed to create entity '{name}'"

        # Test pagination parameters (if your API supports them)
        response = await test_client.get(
            "/api/v1/entities/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 15

    @pytest.mark.asyncio
    async def test_entity_sorting(self, test_client):
        """Test entity list sorting."""
        # Create entities with different names
        names = ["Zebra", "Alpha", "Mike", "Charlie"]
        for name in names:
            await test_client.post("/api/v1/entities/", json={"name": name})

        # Get all entities with higher limit to ensure we see newly created ones
        response = await test_client.get(
            "/api/v1/entities/?limit=1000")
        data = response.json()

        # Check if naturally sorted (your API might sort by ID or name)
        entity_names = [e["name"] for e in data]
        # Verify entities exist
        for name in names:
            assert name in entity_names
