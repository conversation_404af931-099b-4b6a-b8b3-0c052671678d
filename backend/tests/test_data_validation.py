"""
Test data validation framework for ensuring test data management works.

Validates cleanup procedures, data isolation, and naming conventions.
"""
import pytest
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
import os

from .conftest import (
    create_test_entity_name,
    test_data_generator,
    clean_test_database,
    ensure_test_database_ready,
)


class TestDataValidationFramework:
    """Test framework for validating test data management."""

    @pytest.mark.asyncio
    async def test_database_setup_verification(self):
        """Verify that test database is properly set up."""
        await ensure_test_database_ready()
        TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        TEST_DATABASE_URL = (
            f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:"
            f"5432/simile_test"
        )

        engine = create_async_engine(TEST_DATABASE_URL)
        try:
            async with engine.connect() as conn:
                # Verify tables exist
                tables_result = await conn.execute(
                    text(
                        """
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                """
                    )
                )
                tables = [row[0] for row in tables_result.fetchall()]

                expected_tables = ["entities", "connections", "units"]
                for table in expected_tables:
                    assert table in tables, f"Table {table} not found"

                # Verify units are populated
                units_result = await conn.execute(
                    text("SELECT COUNT(*) FROM units")
                )
                units_count = units_result.scalar()
                assert (
                    units_count >= 5
                ), f"Expected at least 5 units, found {units_count}"

                # Verify sequences exist
                sequences_result = await conn.execute(
                    text(
                        """
                    SELECT sequence_name
                    FROM information_schema.sequences
                    WHERE sequence_schema = 'public'
                """
                    )
                )
                sequences = [row[0] for row in sequences_result.fetchall()]

                expected_sequences = ["entities_id_seq", "connections_id_seq"]
                for seq in expected_sequences:
                    assert seq in sequences, f"Sequence {seq} not found"

        finally:
            await engine.dispose()

    @pytest.mark.asyncio
    async def test_cleanup_verification(self):
        """Test that cleanup procedures work correctly."""
        TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        TEST_DATABASE_URL = (
            f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:"
            f"5432/simile_test"
        )

        engine = create_async_engine(TEST_DATABASE_URL)
        try:
            # Create test data
            async with engine.begin() as conn:
                await conn.execute(
                    text(
                        """
                    INSERT INTO entities (name, created_at, updated_at)
                    VALUES ('TestCleanupEntity', CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP)
                """
                    )
                )

                # Get entity ID
                entity_result = await conn.execute(
                    text(
                        """
                    SELECT id FROM entities WHERE name = 'TestCleanupEntity'
                """
                    )
                )
                entity_id = entity_result.scalar()

                # Get unit ID
                unit_result = await conn.execute(
                    text("SELECT id FROM units LIMIT 1")
                )
                unit_id = unit_result.scalar()

                # Create a second entity for connection
                await conn.execute(
                    text(
                        """
                    INSERT INTO entities (name, created_at, updated_at)
                    VALUES ('TestCleanupEntity2', CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP)
                """
                    )
                )

                # Get second entity ID
                entity2_result = await conn.execute(
                    text(
                        """
                    SELECT id FROM entities WHERE name = 'TestCleanupEntity2'
                """
                    )
                )
                entity2_id = entity2_result.scalar()

                # Create connection between two different entities
                await conn.execute(
                    text(
                        """
                    INSERT INTO connections (
                        from_entity_id, to_entity_id, unit_id, multiplier,
                        created_at, updated_at
                    )
                    VALUES (
                        :entity1_id, :entity2_id, :unit_id, 1.0,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    )
                """
                    ),
                    {
                        "entity1_id": entity_id,
                        "entity2_id": entity2_id,
                        "unit_id": unit_id,
                    },
                )

                # Verify data was created
                entities_result = await conn.execute(
                    text("SELECT COUNT(*) FROM entities")
                )
                connections_result = await conn.execute(
                    text("SELECT COUNT(*) FROM connections")
                )

                entities_count = entities_result.scalar()
                connections_count = connections_result.scalar()

                assert entities_count > 0, "Test entities not created"
                assert connections_count > 0, "Test connections not created"

            # Test cleanup
            await clean_test_database()

            # Verify cleanup
            async with engine.connect() as conn:
                entities_result = await conn.execute(
                    text("SELECT COUNT(*) FROM entities")
                )
                connections_result = await conn.execute(
                    text("SELECT COUNT(*) FROM connections")
                )
                units_result = await conn.execute(
                    text("SELECT COUNT(*) FROM units")
                )

                entities_count = entities_result.scalar()
                connections_count = connections_result.scalar()
                units_count = units_result.scalar()

                assert (
                    entities_count == 0
                ), f"Cleanup failed: {entities_count} entities remaining"
                assert (
                    connections_count == 0
                ), f"Cleanup failed: {connections_count} connections remaining"
                assert (
                    units_count >= 5
                ), f"Cleanup removed units: {units_count} units remaining"

        finally:
            await engine.dispose()

    @pytest.mark.asyncio
    async def test_entity_name_uniqueness(self):
        """Test that entity name generation produces unique names."""
        # Generate multiple entity names
        names = [create_test_entity_name("UniquenessTest") for _ in range(100)]

        # Verify all names are unique
        unique_names = set(names)
        assert len(unique_names) == len(
            names
        ), "Entity name generation not unique"

        # Verify names follow validation rules
        for name in names:
            assert isinstance(name, str), "Entity name must be string"
            assert len(name) <= 100, f"Entity name too long: {len(name)} chars"
            assert (
                name.strip() == name
            ), "Entity name has leading/trailing whitespace"
            assert all(
                c.isalpha() or c.isspace() for c in name
            ), f"Entity name contains invalid characters: {name}"

    @pytest.mark.asyncio
    async def test_test_data_generator_edge_cases(self):
        """Test edge case data generation."""
        edge_cases = test_data_generator.edge_case_entity_names()

        # Verify max length is exactly 100 characters
        assert (
            len(edge_cases["max_length"]) == 100
        ), "Max length edge case incorrect"

        # Verify single character
        assert (
            len(edge_cases["single_char"]) == 1
        ), "Single char edge case incorrect"

        # Verify spaces are handled correctly
        assert " " in edge_cases["with_spaces"], "Spaces edge case incorrect"

        # Verify mixed case
        assert (
            edge_cases["mixed_case"].lower() != edge_cases["mixed_case"]
        ), "Mixed case edge case incorrect"

    @pytest.mark.asyncio
    async def test_invalid_entity_names(self):
        """Test invalid entity name generation for validation testing."""
        invalid_names = test_data_generator.invalid_entity_names()

        # Verify empty string
        assert invalid_names["empty"] == "", "Empty string case incorrect"

        # Verify whitespace only
        assert (
            invalid_names["whitespace_only"].strip() == ""
        ), "Whitespace only case incorrect"

        # Verify too long
        assert len(invalid_names["too_long"]) > 100, "Too long case incorrect"

        # Verify numbers only
        assert invalid_names[
            "numbers_only"
        ].isdigit(), "Numbers only case incorrect"

        # Verify special characters
        special_chars = invalid_names["special_chars"]
        assert isinstance(special_chars, list), "Special chars should be list"
        assert len(special_chars) > 0, "Special chars list should not be empty"

        for name in special_chars:
            assert any(
                not (c.isalnum() or c.isspace()) for c in name
            ), f"Name should contain special chars: {name}"

    @pytest.mark.asyncio
    async def test_sequence_reset_verification(self):
        """Test that database sequences are properly reset."""
        TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        TEST_DATABASE_URL = (
            f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:"
            f"5432/simile_test"
        )

        engine = create_async_engine(TEST_DATABASE_URL)
        try:
            # Clean database to ensure sequences are reset
            await clean_test_database()

            # Create entity and verify ID starts at 1
            async with engine.begin() as conn:
                await conn.execute(
                    text(
                        """
                    INSERT INTO entities (name, created_at, updated_at)
                    VALUES ('SequenceTestEntity', CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP)
                """
                    )
                )

                entity_result = await conn.execute(
                    text(
                        """
                    SELECT id FROM entities WHERE name = 'SequenceTestEntity'
                """
                    )
                )
                entity_id = entity_result.scalar()

                assert (
                    entity_id == 1
                ), f"Entity ID should be 1 after reset, got {entity_id}"

                # Clean again for next test
                await conn.execute(text("DELETE FROM entities"))
                await conn.execute(
                    text("ALTER SEQUENCE entities_id_seq RESTART WITH 1")
                )

        finally:
            await engine.dispose()

    @pytest.mark.asyncio
    async def test_foreign_key_constraint_cleanup(self):
        """Test that cleanup handles foreign key constraints correctly."""
        TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        TEST_DATABASE_URL = (
            f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:"
            f"5432/simile_test"
        )

        engine = create_async_engine(TEST_DATABASE_URL)
        try:
            # Create entities and connections
            async with engine.begin() as conn:
                # Create entities
                await conn.execute(
                    text(
                        """
                    INSERT INTO entities (name, created_at, updated_at)
                    VALUES
                    ('FKTestEntity1', CURRENT_TIMESTAMP,
                     CURRENT_TIMESTAMP),
                    ('FKTestEntity2', CURRENT_TIMESTAMP,
                     CURRENT_TIMESTAMP)
                """
                    )
                )

                # Get entity IDs
                entity_result = await conn.execute(
                    text(
                        """
                    SELECT id FROM entities WHERE name LIKE 'FKTestEntity%'
                    ORDER BY id
                """
                    )
                )
                entity_ids = [row[0] for row in entity_result.fetchall()]

                # Get unit ID
                unit_result = await conn.execute(
                    text("SELECT id FROM units LIMIT 1")
                )
                unit_id = unit_result.scalar()

                # Create connection
                await conn.execute(
                    text(
                        """
                    INSERT INTO connections (
                        from_entity_id, to_entity_id, unit_id, multiplier,
                        created_at, updated_at
                    )
                    VALUES (
                        :from_id, :to_id, :unit_id, 2.5,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    )
                """
                    ),
                    {
                        "from_id": entity_ids[0],
                        "to_id": entity_ids[1],
                        "unit_id": unit_id,
                    },
                )

                # Verify data was created
                entities_result = await conn.execute(
                    text("SELECT COUNT(*) FROM entities")
                )
                connections_result = await conn.execute(
                    text("SELECT COUNT(*) FROM connections")
                )

                assert (
                    entities_result.scalar() >= 2
                ), "Test entities not created"
                assert (
                    connections_result.scalar() >= 1
                ), "Test connections not created"

            # Test cleanup - should handle foreign key constraints
            await clean_test_database()

            # Verify cleanup succeeded
            async with engine.connect() as conn:
                entities_result = await conn.execute(
                    text("SELECT COUNT(*) FROM entities")
                )
                connections_result = await conn.execute(
                    text("SELECT COUNT(*) FROM connections")
                )

                assert entities_result.scalar() == 0, "Entities not cleaned up"
                assert (
                    connections_result.scalar() == 0
                ), "Connections not cleaned up"

        finally:
            await engine.dispose()

    @pytest.mark.asyncio
    async def test_cleanup_verification_concept(self, test_client):
        """Test the cleanup verification concept."""
        # Create some test data
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": create_test_entity_name("CleanupVerificationTest")},
        )
        assert entity_response.status_code == 201

        # Verify entity exists
        entities_response = await test_client.get("/api/v1/entities/")
        entities_before = entities_response.json()
        assert len(entities_before) > 0, "Should have entities before cleanup"

        # Clean database
        await clean_test_database()

        # Verify cleanup worked
        entities_response = await test_client.get("/api/v1/entities/")
        entities_after = entities_response.json()
        assert (
            len(entities_after) == 0
        ), "Should have no entities after cleanup"

    @pytest.mark.asyncio
    async def test_data_isolation_concept(self, test_client):
        """Test the data isolation concept without fixtures."""
        # Test that demonstrates data isolation tracking concept

        # Create entities
        entity1_name = create_test_entity_name("IsolationTestA")

        entity1_response = await test_client.post(
            "/api/v1/entities/", json={"name": entity1_name}
        )
        assert entity1_response.status_code == 201
        entity1_id = entity1_response.json()["id"]

        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": create_test_entity_name("IsolationTestB")},
        )
        assert entity2_response.status_code == 201
        entity2_id = entity2_response.json()["id"]

        # Verify entities were created
        all_entities_response = await test_client.get("/api/v1/entities/")
        all_entities = all_entities_response.json()

        created_ids = [entity1_id, entity2_id]
        found_ids = [entity["id"] for entity in all_entities]

        for entity_id in created_ids:
            assert (
                entity_id in found_ids
            ), f"Entity {entity_id} not found in database"


class TestDataIntegration:
    """Integration tests for test data management with actual API calls."""

    @pytest.mark.asyncio
    async def test_entity_creation_with_unique_names(self, test_client):
        """Test that entity creation works with unique names."""
        # Create multiple entities with unique names
        entity_names = [
            create_test_entity_name("IntegrationTest") for _ in range(5)
        ]

        created_entities = []
        for name in entity_names:
            response = await test_client.post(
                "/api/v1/entities/", json={"name": name}
            )
            assert (
                response.status_code == 201
            ), f"Failed to create entity {name}"
            created_entities.append(response.json())

        # Verify all entities were created successfully
        assert len(created_entities) == 5, "Not all entities were created"

        # Verify names are unique
        created_names = [entity["name"] for entity in created_entities]
        assert len(set(created_names)) == len(
            created_names
        ), "Entity names not unique"

        # Verify entities exist in database
        all_entities_response = await test_client.get("/api/v1/entities/")
        assert all_entities_response.status_code == 200

        all_entities = all_entities_response.json()
        all_entity_names = [entity["name"] for entity in all_entities]

        for name in entity_names:
            assert (
                name in all_entity_names
            ), f"Entity {name} not found in database"

    @pytest.mark.asyncio
    async def test_connection_creation_with_test_data(self, test_client):
        """Test connection creation using test data management."""
        # Create entities
        entity1_name = create_test_entity_name("ConnectionSource")
        entity2_name = create_test_entity_name("ConnectionTarget")

        entity1_response = await test_client.post(
            "/api/v1/entities/", json={"name": entity1_name}
        )
        assert entity1_response.status_code == 201
        entity1_id = entity1_response.json()["id"]

        entity2_response = await test_client.post(
            "/api/v1/entities/", json={"name": entity2_name}
        )
        assert entity2_response.status_code == 201
        entity2_id = entity2_response.json()["id"]

        # Get unit
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]

        # Create connection
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.5,
            },
        )
        assert connection_response.status_code == 201
        connection = connection_response.json()

        # Verify connection
        assert connection["from_entity_id"] == entity1_id
        assert connection["to_entity_id"] == entity2_id
        assert connection["unit_id"] == unit_id
        assert float(connection["multiplier"]) == 2.5

    @pytest.mark.asyncio
    async def test_test_isolation_between_methods(self, test_client):
        """Test that test isolation works between test methods."""
        # This test should start with a clean database
        entities_response = await test_client.get("/api/v1/entities/")
        assert entities_response.status_code == 200

        entities = entities_response.json()
        # Should have no entities from previous tests
        assert (
            len(entities) == 0
        ), f"Database not clean: found {len(entities)} entities"

        # Create test data
        entity_name = create_test_entity_name("IsolationTestMethod")
        response = await test_client.post(
            "/api/v1/entities/", json={"name": entity_name}
        )
        assert response.status_code == 201

        # Verify entity was created
        entities_response = await test_client.get("/api/v1/entities/")
        entities = entities_response.json()
        assert len(entities) == 1, "Entity not created"
        assert entities[0]["name"] == entity_name

        # This data should be cleaned up before the next test
