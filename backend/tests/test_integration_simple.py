"""
Simple integration tests for the SIMILE API using AsyncClient.
Tests basic workflows and API endpoint interactions.
"""
import pytest


class TestBasicIntegration:
    """Test basic API integration workflows."""

    @pytest.mark.asyncio
    async def test_api_health_and_docs(self, test_client):
        """Test that basic API endpoints are working."""
        # Health check
        health_response = await test_client.get("/api/v1/health")
        assert health_response.status_code == 200
        assert health_response.json()["status"] == "healthy"

        # API docs
        docs_response = await test_client.get("/api/v1/docs")
        assert docs_response.status_code == 200

        # OpenAPI spec
        openapi_response = await test_client.get("/api/v1/openapi.json")
        assert openapi_response.status_code == 200
        spec = openapi_response.json()
        assert "info" in spec
        assert spec["info"]["title"] == "SIMILE API"

    @pytest.mark.asyncio
    async def test_units_endpoint(self, test_client):
        """Test that units endpoint returns expected data."""
        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200
        units = response.json()
        assert isinstance(units, list)
        assert len(units) > 0

        # Check unit structure
        unit = units[0]
        assert "id" in unit
        assert "name" in unit
        assert "symbol" in unit

    @pytest.mark.asyncio
    async def test_entities_endpoint(self, test_client):
        """Test basic entities endpoint functionality."""
        # List entities
        response = await test_client.get("/api/v1/entities/")
        assert response.status_code == 200
        entities = response.json()
        assert isinstance(entities, list)

    @pytest.mark.asyncio
    async def test_connections_endpoint(self, test_client):
        """Test basic connections endpoint functionality."""
        # List connections
        response = await test_client.get("/api/v1/connections/")
        assert response.status_code == 200
        connections = response.json()
        assert isinstance(connections, list)

    @pytest.mark.asyncio
    async def test_compare_endpoint_invalid_ids(self, test_client):
        """Test compare endpoint with invalid entity IDs."""
        response = await test_client.get(
            "/api/v1/compare/?from=9999&to=9998&unit=1"
        )
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_invalid_endpoints(self, test_client):
        """Test that invalid endpoints return 404."""
        response = await test_client.get("/api/v1/invalid/")
        assert response.status_code == 404

        response = await test_client.get("/invalid/")
        assert response.status_code == 404


class TestEntityBasicWorkflow:
    """Test basic entity management workflow."""

    @pytest.mark.asyncio
    async def test_entity_creation_and_retrieval(self, test_client):
        """Test creating and retrieving an entity."""
        # Create entity
        create_data = {"name": "Test Entity Basic Workflow"}
        create_response = await test_client.post(
            "/api/v1/entities/", json=create_data
        )

        if create_response.status_code == 201:
            # Creation successful
            entity = create_response.json()
            assert "id" in entity
            assert entity["name"] == "Test Entity Basic Workflow"
            entity_id = entity["id"]

            # Try to retrieve it
            get_response = await test_client.get(
                f"/api/v1/entities/{entity_id}"
            )
            assert get_response.status_code == 200
            retrieved_entity = get_response.json()
            assert retrieved_entity["name"] == "Test Entity Basic Workflow"
        else:
            # If creation fails, just verify we get a proper error
            assert create_response.status_code in [400, 422, 500]

    @pytest.mark.asyncio
    async def test_entity_validation(self, test_client):
        """Test entity validation rules."""
        # Test empty name
        response = await test_client.post(
            "/api/v1/entities/", json={"name": ""}
        )
        assert response.status_code == 422

        # Test invalid characters (numbers)
        response = await test_client.post(
            "/api/v1/entities/", json={"name": "Test123"}
        )
        assert response.status_code == 422

        # Test too long name (101 characters, should exceed max_length=100)
        long_name = "a" * 101
        response = await test_client.post(
            "/api/v1/entities/", json={"name": long_name}
        )
        assert response.status_code == 422


class TestConnectionBasicWorkflow:
    """Test basic connection management workflow."""

    @pytest.mark.asyncio
    async def test_connection_validation(self, test_client):
        """Test connection validation rules."""
        # Test invalid entity IDs
        connection_data = {
            "from_entity_id": 9999,
            "to_entity_id": 9998,
            "unit_id": 1,
            "multiplier": 2.5,
        }
        response = await test_client.post(
            "/api/v1/connections/", json=connection_data
        )
        assert response.status_code in [404, 422]

        # Test self-referencing connection
        connection_data = {
            "from_entity_id": 1,
            "to_entity_id": 1,
            "unit_id": 1,
            "multiplier": 2.5,
        }
        response = await test_client.post(
            "/api/v1/connections/", json=connection_data
        )
        assert response.status_code == 422

        # Test negative multiplier
        connection_data = {
            "from_entity_id": 1,
            "to_entity_id": 2,
            "unit_id": 1,
            "multiplier": -1.0,
        }
        response = await test_client.post(
            "/api/v1/connections/", json=connection_data
        )
        assert response.status_code == 422


class TestComparisonBasicWorkflow:
    """Test basic comparison functionality."""

    @pytest.mark.asyncio
    async def test_same_entity_comparison(self, test_client):
        """Test comparing an entity to itself."""
        # Get available entities and units
        entities_response = await test_client.get("/api/v1/entities/")
        units_response = await test_client.get("/api/v1/units/")

        if (
            entities_response.status_code == 200
            and units_response.status_code == 200
        ):
            entities = entities_response.json()
            units = units_response.json()

            if len(entities) > 0 and len(units) > 0:
                entity_id = entities[0]["id"]
                unit_id = units[0]["id"]

                response = await test_client.get(
                    f"/api/v1/compare/?from={entity_id}&to={entity_id}&unit={unit_id}"  # noqa: E501
                )
                assert response.status_code == 200
                result = response.json()
                assert float(result["multiplier"]) == 1.0
                assert result["error"] is None

    @pytest.mark.asyncio
    async def test_comparison_with_existing_data(self, test_client):
        """Test comparison using existing entities in the database."""
        # Get available entities and units
        entities_response = await test_client.get("/api/v1/entities/")
        units_response = await test_client.get("/api/v1/units/")

        if (
            entities_response.status_code == 200
            and units_response.status_code == 200
        ):
            entities = entities_response.json()
            units = units_response.json()

            if len(entities) >= 2 and len(units) > 0:
                entity1_id = entities[0]["id"]
                entity2_id = entities[1]["id"]
                unit_id = units[0]["id"]

                response = await test_client.get(
                    f"/api/v1/compare/?from={entity1_id}&to={entity2_id}&unit={unit_id}"  # noqa: E501
                )
                # With our fix, this should return 404 when no path exists
                assert response.status_code == 404
                result = response.json()

                # 404 response should have error detail
                assert "detail" in result


class TestErrorHandling:
    """Test error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_invalid_http_methods(self, test_client):
        """Test invalid HTTP methods on endpoints."""
        # POST to read-only endpoint
        response = await test_client.post("/api/v1/units/")
        # Should either succeed (if creation is allowed) or return method not
        # allowed
        assert response.status_code in [200, 201, 405, 422]

        # PUT to list endpoint
        response = await test_client.put("/api/v1/entities/")
        assert response.status_code == 405

    @pytest.mark.asyncio
    async def test_malformed_json(self, test_client):
        """Test endpoints with malformed JSON."""
        response = await test_client.post(
            "/api/v1/entities/",
            content="invalid json",
            headers={"Content-Type": "application/json"},
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_missing_required_fields(self, test_client):
        """Test endpoints with missing required fields."""
        response = await test_client.post("/api/v1/entities/", json={})
        assert response.status_code == 422

        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": 1,
                # Missing other required fields
            },
        )
        assert response.status_code == 422


class TestDataConsistency:
    """Test basic data consistency."""

    @pytest.mark.asyncio
    async def test_response_formats(self, test_client):
        """Test that API responses have consistent formats."""
        # Test entities response format
        response = await test_client.get("/api/v1/entities/")
        if response.status_code == 200:
            entities = response.json()
            if len(entities) > 0:
                entity = entities[0]
                required_fields = ["id", "name", "created_at", "updated_at"]
                for field in required_fields:
                    assert field in entity

        # Test units response format
        response = await test_client.get("/api/v1/units/")
        if response.status_code == 200:
            units = response.json()
            if len(units) > 0:
                unit = units[0]
                required_fields = [
                    "id",
                    "name",
                    "symbol",
                    "created_at",
                    "updated_at",
                ]
                for field in required_fields:
                    assert field in unit

    @pytest.mark.asyncio
    async def test_api_version_consistency(self, test_client):
        """Test that all endpoints use the same API version prefix."""
        endpoints = [
            "/api/v1/health",
            "/api/v1/entities/",
            "/api/v1/units/",
            "/api/v1/connections/",
            "/api/v1/docs",
            "/api/v1/openapi.json",
        ]

        for endpoint in endpoints:
            response = await test_client.get(endpoint)
            # All endpoints should be accessible (200) or properly reject
            # (4xx/5xx)
            assert 200 <= response.status_code < 600
