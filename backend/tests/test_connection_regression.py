"""
Regression tests for connection creation to prevent async/greenlet errors.
Tests added after fixing the SQLAlchemy async configuration issues.
"""
import pytest
import uuid
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from decimal import Decimal

from src.models import Connection, Entity, Unit


class TestConnectionRegression:
    """Test connection creation doesn't regress with async/greenlet issues."""

    @pytest.mark.asyncio
    async def test_no_greenlet_error_on_connection_creation(self, test_client):
        """Test that connection creation doesn't trigger greenlet errors."""
        # Create test entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Greenlet Test Entity A {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Greenlet Test Entity B {suffix}"}
        )

        # Get a unit
        units = await test_client.get("/api/v1/units/")
        unit = units.json()[0]

        # Create connection - this should not trigger greenlet errors
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1.json()["id"],
                "to_entity_id": entity2.json()["id"],
                "unit_id": unit["id"],
                "multiplier": 5.5
            }
        )

        # Should succeed with 201
        assert response.status_code == 201, f"Expected 201, got {response.status_code}: {response.text}"

        # Verify the response has all expected fields
        result = response.json()
        assert "id" in result
        assert "from_entity_id" in result
        assert "to_entity_id" in result
        assert "unit_id" in result
        assert "multiplier" in result
        assert "created_at" in result
        assert "updated_at" in result

        # Verify data correctness
        assert result["from_entity_id"] == entity1.json()["id"]
        assert result["to_entity_id"] == entity2.json()["id"]
        assert result["unit_id"] == unit["id"]
        assert float(result["multiplier"]) == 5.5

    @pytest.mark.asyncio
    async def test_cors_headers_on_success(self, test_client):
        """Test CORS headers are present on successful requests."""
        # Create test entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"CORS Test Entity A {suffix}"}
        )
        assert entity1.status_code == 201, f"Entity creation failed: {entity1.text}"

        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"CORS Test Entity B {suffix}"}
        )
        assert entity2.status_code == 201, f"Entity creation failed: {entity2.text}"

        # Get a unit
        units = await test_client.get("/api/v1/units/")
        unit = units.json()[0]

        # Create connection with Origin header
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1.json()["id"],
                "to_entity_id": entity2.json()["id"],
                "unit_id": unit["id"],
                "multiplier": 3.3
            },
            headers={"Origin": "http://localhost:3000"}
        )

        # Check CORS headers are present
        assert response.status_code == 201
        assert "access-control-allow-origin" in response.headers
        # Test environment uses * for CORS
        assert response.headers["access-control-allow-origin"] == "*"
        assert "access-control-allow-credentials" in response.headers
        assert response.headers["access-control-allow-credentials"] == "true"

    @pytest.mark.asyncio
    async def test_cors_headers_on_error(self, test_client):
        """Test CORS headers are present even on error responses."""
        # Try to create connection with invalid entity IDs
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": 99999,  # Non-existent
                "to_entity_id": 99998,     # Non-existent
                "unit_id": 1,
                "multiplier": 2.0
            },
            headers={"Origin": "http://localhost:3000"}
        )

        # Should get 404 error
        assert response.status_code == 404

        # But CORS headers should still be present
        assert "access-control-allow-origin" in response.headers
        # Test environment uses * for CORS
        assert response.headers["access-control-allow-origin"] == "*"

    @pytest.mark.asyncio
    async def test_no_lazy_loading_after_commit(self, test_client):
        """Test that objects don't trigger lazy loading after commit."""
        # Create test entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Lazy Load Test Entity A {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Lazy Load Test Entity B {suffix}"}
        )

        # Get a unit
        units = await test_client.get("/api/v1/units/")
        unit = units.json()[0]

        # Create connection
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1.json()["id"],
                "to_entity_id": entity2.json()["id"],
                "unit_id": unit["id"],
                "multiplier": 7.7
            }
        )

        assert response.status_code == 201
        connection_id = response.json()["id"]

        # Immediately get the connection again - should not trigger lazy loading
        get_response = await test_client.get(f"/api/v1/connections/{connection_id}")
        assert get_response.status_code == 200

        # Verify all fields are accessible
        data = get_response.json()
        assert data["id"] == connection_id
        assert "from_entity_id" in data
        assert "to_entity_id" in data
        assert "unit_id" in data
        assert "multiplier" in data

    @pytest.mark.asyncio
    async def test_update_connection_no_greenlet_error(self, test_client):
        """Test that updating connections doesn't trigger greenlet errors."""
        # Create test entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Update Test Entity A {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Update Test Entity B {suffix}"}
        )

        # Get a unit
        units = await test_client.get("/api/v1/units/")
        unit = units.json()[0]

        # Create connection
        create_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1.json()["id"],
                "to_entity_id": entity2.json()["id"],
                "unit_id": unit["id"],
                "multiplier": 2.0
            }
        )

        assert create_response.status_code == 201
        connection_id = create_response.json()["id"]

        # Update the connection - should not trigger greenlet errors
        update_response = await test_client.put(
            f"/api/v1/connections/{connection_id}",
            json={"multiplier": 4.5}
        )

        assert update_response.status_code == 200
        result = update_response.json()
        assert float(result["multiplier"]) == 4.5

        # Verify inverse was also updated
        connections = await test_client.get("/api/v1/connections/")
        conn_list = connections.json()

        # Find the inverse connection
        inverse = next(
            (c for c in conn_list 
             if c["from_entity_id"] == entity2.json()["id"] 
             and c["to_entity_id"] == entity1.json()["id"]
             and c["unit_id"] == unit["id"]),
            None
        )

        assert inverse is not None
        # Inverse of 4.5 should be 0.2 (rounded to 1 decimal)
        assert float(inverse["multiplier"]) == 0.2

    @pytest.mark.asyncio
    async def test_multiple_rapid_connections_no_pool_issues(self, test_client):
        """Test creating multiple connections rapidly doesn't cause pool issues."""
        # Create base entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Pool Test Entity A {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Pool Test Entity B {suffix}"}
        )
        entity3 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Pool Test Entity C {suffix}"}
        )

        entities = [entity1.json()["id"], entity2.json()["id"], entity3.json()["id"]]

        # Get units
        units = await test_client.get("/api/v1/units/")
        unit_ids = [u["id"] for u in units.json()[:3]]  # Use first 3 units

        # Create multiple connections rapidly
        created_connections = []

        for i in range(5):
            for j in range(len(entities)):
                if j < len(entities) - 1:
                    response = await test_client.post(
                        "/api/v1/connections/",
                        json={
                            "from_entity_id": entities[j],
                            "to_entity_id": entities[j + 1],
                            "unit_id": unit_ids[i % len(unit_ids)],
                            "multiplier": float(i + 1) + (j + 1) * 0.1
                        }
                    )

                    # All should succeed without pool/greenlet errors
                    assert response.status_code in [201, 200], \
                        f"Connection creation failed: {response.status_code} - {response.text}"

                    if response.status_code == 201:
                        created_connections.append(response.json()["id"])

        # Verify we can still query connections
        all_connections = await test_client.get("/api/v1/connections/")
        assert all_connections.status_code == 200
        assert len(all_connections.json()) > 0
