"""
Comprehensive database transaction and rollback test suite for SIMILE backend.
Tests transaction integrity, rollback scenarios, isolation levels, and error
recovery.
"""
import pytest
import asyncio
import logging
from sqlalchemy import select

from src.database import async_session
from src.models import Entity, Connection
from .conftest import create_test_entity_name

# Configure logging for transaction tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestTransactionIntegrity:
    """Test transaction integrity scenarios."""

    @pytest.mark.asyncio
    async def test_entity_creation_transaction_success(self, test_client):
        """Test ID: TXN-001 - Single entity creation with successful commit."""
        entity_name = create_test_entity_name("TransactionTest")

        # Create entity
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )

        assert response.status_code == 201
        created_entity = response.json()

        # Verify entity is immediately queryable
        get_response = await test_client.get(f"/api/v1/entities/{created_entity['id']}")
        assert get_response.status_code == 200
        assert get_response.json()["name"] == entity_name

        # Verify entity exists in database
        async with async_session() as db:
            result = await db.execute(select(Entity).where(Entity.id == created_entity["id"]))
            db_entity = result.scalar_one_or_none()
            assert db_entity is not None
            assert db_entity.name == entity_name

    @pytest.mark.asyncio
    async def test_entity_creation_duplicate_name_rollback(self, test_client):
        """Test ID: TXN-002 - Entity creation with duplicate name constraint violation."""
        entity_name = create_test_entity_name("DuplicateTest")

        # Create first entity
        response1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response1.status_code == 201

        # Attempt to create duplicate
        response2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert response2.status_code == 400
        assert "already exists" in response2.json()["detail"]

        # Verify only one entity exists
        async with async_session() as db:
            result = await db.execute(
                select(Entity).where(Entity.name.ilike(entity_name))
            )
            entities = result.scalars().all()
            assert len(entities) == 1

    @pytest.mark.asyncio
    async def test_connection_creation_with_inverse_atomic(self, test_client):
        """Test ID: TXN-004 - Connection creation with automatic inverse creation."""
        # Create test entities
        entity1_name = create_test_entity_name("ConnTestA")
        entity2_name = create_test_entity_name("ConnTestB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create connection
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.5
            }
        )

        assert connection_response.status_code == 201

        # Verify both forward and inverse connections exist
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()

        forward_conn = next((c for c in connections 
                           if c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id), None)
        inverse_conn = next((c for c in connections 
                           if c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id), None)

        assert forward_conn is not None
        assert inverse_conn is not None
        assert float(forward_conn["multiplier"]) == 2.5
        assert float(inverse_conn["multiplier"]) == 0.4  # 1/2.5

    @pytest.mark.asyncio
    async def test_connection_self_reference_constraint_violation(self, test_client):
        """Test ID: TXN-005 - Connection creation with constraint violation (self-reference)."""
        # Create test entity
        entity_name = create_test_entity_name("SelfRefTest")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        entity_id = entity_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Attempt to create self-referencing connection
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity_id,
                "to_entity_id": entity_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )

        assert connection_response.status_code == 422
        response_data = connection_response.json()
        # FastAPI validation errors return a list of error objects
        assert "detail" in response_data
        error_details = response_data["detail"]
        assert any("Cannot create connection from entity to itself" in str(error) for error in error_details)

        # Verify no connections were created
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        self_connections = [c for c in connections if c["from_entity_id"] == entity_id]
        assert len(self_connections) == 0

    @pytest.mark.asyncio
    async def test_multiple_entity_creation_atomic(self, test_client):
        """Test ID: TXN-007 - Multiple entity creation in single transaction."""
        entity_names = [
            create_test_entity_name("MultiTestA"),
            create_test_entity_name("MultiTestB"),
            create_test_entity_name("MultiTestC")
        ]

        # Create multiple entities
        created_entities = []
        for name in entity_names:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            assert response.status_code == 201
            created_entities.append(response.json())

        # Verify all entities exist
        for entity in created_entities:
            get_response = await test_client.get(f"/api/v1/entities/{entity['id']}")
            assert get_response.status_code == 200
            assert get_response.json()["name"] in entity_names


class TestRollbackScenarios:
    """Test rollback scenarios for various failure conditions."""

    @pytest.mark.asyncio
    async def test_connection_negative_multiplier_rollback(self, test_client):
        """Test ID: RBK-002 - Connection multiplier check constraint violation."""
        # Create test entities
        entity1_name = create_test_entity_name("RollbackTestA")
        entity2_name = create_test_entity_name("RollbackTestB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Attempt to create connection with negative multiplier
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": -1.5
            }
        )

        assert connection_response.status_code == 422
        response_data = connection_response.json()
        assert "detail" in response_data
        error_details = response_data["detail"]
        assert any("Multiplier must be positive" in str(error) for error in error_details)

        # Verify no connections were created
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        entity_connections = [c for c in connections 
                            if c["from_entity_id"] == entity1_id or c["to_entity_id"] == entity1_id]
        assert len(entity_connections) == 0

    @pytest.mark.asyncio
    async def test_connection_invalid_entity_rollback(self, test_client):
        """Test ID: TXN-006 - Connection creation with invalid entity reference."""
        # Create one valid entity
        entity_name = create_test_entity_name("InvalidRefTest")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        entity_id = entity_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Attempt to create connection with non-existent entity
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity_id,
                "to_entity_id": 99999,  # Non-existent entity
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )

        assert connection_response.status_code == 404
        assert "entity not found" in connection_response.json()["detail"].lower()

        # Verify no connections were created
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        entity_connections = [c for c in connections if c["from_entity_id"] == entity_id]
        assert len(entity_connections) == 0

    @pytest.mark.asyncio
    async def test_transaction_rollback_preserves_state(self, test_client):
        """Test ID: RBK-001 - Transaction rollback preserves original data."""
        # Create initial entity
        initial_entity_name = create_test_entity_name("InitialState")
        initial_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": initial_entity_name}
        )
        assert initial_response.status_code == 201
        initial_entity_id = initial_response.json()["id"]

        # Get initial entity count
        entities_response = await test_client.get("/api/v1/entities/")
        initial_count = len(entities_response.json())

        # Attempt to create duplicate entity (should fail)
        duplicate_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": initial_entity_name}
        )
        assert duplicate_response.status_code == 400

        # Verify state is preserved
        entities_response = await test_client.get("/api/v1/entities/")
        final_count = len(entities_response.json())
        assert final_count == initial_count

        # Verify original entity still exists
        get_response = await test_client.get(f"/api/v1/entities/{initial_entity_id}")
        assert get_response.status_code == 200
        assert get_response.json()["name"] == initial_entity_name


class TestConcurrentTransactions:
    """Test concurrent transaction scenarios and isolation."""

    @pytest.mark.asyncio
    async def test_concurrent_entity_creation_same_name(self, test_client):
        """Test ID: ISO-004 - Concurrent entity creation with same name."""
        entity_name = create_test_entity_name("ConcurrentTest")

        async def create_entity():
            """Helper function to create entity."""
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity_name}
            )
            return response

        # Run concurrent creation attempts
        tasks = [create_entity() for _ in range(3)]
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # Count successful creations
        successful_creations = 0
        for response in responses:
            if hasattr(response, 'status_code') and response.status_code == 201:
                successful_creations += 1

        # Only one should succeed
        assert successful_creations == 1

        # Verify only one entity exists with this name
        async with async_session() as db:
            result = await db.execute(
                select(Entity).where(Entity.name.ilike(entity_name))
            )
            entities = result.scalars().all()
            assert len(entities) == 1

    @pytest.mark.asyncio
    async def test_concurrent_connection_creation(self, test_client):
        """Test ID: ISO-005 - Concurrent connection creation between same entities."""
        # Create test entities
        entity1_name = create_test_entity_name("ConcConnA")
        entity2_name = create_test_entity_name("ConcConnB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        async def create_connection(multiplier):
            """Helper function to create connection."""
            response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity1_id,
                    "to_entity_id": entity2_id,
                    "unit_id": unit_id,
                    "multiplier": multiplier
                }
            )
            return response

        # Run concurrent connection creation attempts
        tasks = [create_connection(2.0), create_connection(3.0), create_connection(4.0)]
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # All should succeed (updates existing or creates new)
        for response in responses:
            if hasattr(response, 'status_code'):
                assert response.status_code == 201

        # Verify only one connection exists between entities
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()

        forward_connections = [c for c in connections 
                             if c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id]
        assert len(forward_connections) == 1

    @pytest.mark.asyncio
    async def test_concurrent_entity_deletion_and_connection_creation(self, test_client):
        """Test ID: ISO-006 - Concurrent entity deletion and connection creation."""
        # Create test entities
        entity1_name = create_test_entity_name("ConcDelA")
        entity2_name = create_test_entity_name("ConcDelB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        async def delete_entity():
            """Helper function to delete entity."""
            response = await test_client.delete(f"/api/v1/entities/{entity1_id}")
            return response

        async def create_connection():
            """Helper function to create connection."""
            response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity1_id,
                    "to_entity_id": entity2_id,
                    "unit_id": unit_id,
                    "multiplier": 2.0
                }
            )
            return response

        # Run concurrent operations
        delete_task = delete_entity()
        create_task = create_connection()

        delete_response, create_response = await asyncio.gather(
            delete_task, create_task, return_exceptions=True
        )

        # Either the entity is deleted and connection creation fails,
        # or connection is created and entity deletion fails
        if hasattr(delete_response, 'status_code') and delete_response.status_code == 204:
            # Entity deleted successfully
            if hasattr(create_response, 'status_code'):
                assert create_response.status_code == 404  # Entity not found
        elif hasattr(create_response, 'status_code') and create_response.status_code == 201:
            # Connection created successfully
            if hasattr(delete_response, 'status_code'):
                assert delete_response.status_code == 400  # Cannot delete entity with connections

        # Verify database consistency
        async with async_session() as db:
            # Check entity existence
            entity_result = await db.execute(select(Entity).where(Entity.id == entity1_id))
            entity = entity_result.scalar_one_or_none()

            # Check connection existence
            connection_result = await db.execute(
                select(Connection).where(Connection.from_entity_id == entity1_id)
            )
            connections = connection_result.scalars().all()

            # Either entity exists with connections or entity doesn't exist with no connections
            if entity is not None:
                # Entity exists, connections should exist
                assert len(connections) >= 0  # Connections may or may not exist
            else:
                # Entity doesn't exist, no connections should exist
                assert len(connections) == 0


class TestDatabaseConstraints:
    """Test database constraint violation handling."""

    @pytest.mark.asyncio
    async def test_foreign_key_constraint_violation(self, test_client):
        """Test ID: CNS-002 - Connection creation with non-existent entity."""
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Attempt to create connection with non-existent entities
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": 99998,
                "to_entity_id": 99999,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )

        assert connection_response.status_code == 404
        assert "entity not found" in connection_response.json()["detail"].lower()

        # Verify no orphaned connections
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        orphaned_connections = [c for c in connections 
                              if c["from_entity_id"] == 99998 or c["to_entity_id"] == 99999]
        assert len(orphaned_connections) == 0

    @pytest.mark.asyncio
    async def test_check_constraint_zero_multiplier(self, test_client):
        """Test ID: CNS-004 - Connection with zero multiplier."""
        # Create test entities
        entity1_name = create_test_entity_name("ZeroMultA")
        entity2_name = create_test_entity_name("ZeroMultB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Attempt to create connection with zero multiplier
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 0.0
            }
        )

        assert connection_response.status_code == 422
        response_data = connection_response.json()
        assert "detail" in response_data
        error_details = response_data["detail"]
        assert any("Multiplier must be positive" in str(error) for error in error_details)

        # Verify no invalid connections created
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        zero_multiplier_connections = [c for c in connections 
                                     if float(c["multiplier"]) == 0.0]
        assert len(zero_multiplier_connections) == 0


class TestConnectionCleanup:
    """Test connection cleanup after failures."""

    @pytest.mark.asyncio
    async def test_session_cleanup_after_failure(self, test_client):
        """Test ID: CLN-001 - Database session cleanup after transaction failure."""
        # Create test entity
        entity_name = create_test_entity_name("CleanupTest")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        entity_id = entity_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Attempt multiple failed operations
        failed_operations = 0
        for i in range(5):
            # Attempt self-referencing connection (should fail)
            connection_response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity_id,
                    "to_entity_id": entity_id,
                    "unit_id": unit_id,
                    "multiplier": 2.0
                }
            )
            if connection_response.status_code == 422:
                failed_operations += 1

        assert failed_operations == 5

        # Verify system still functions after failures
        test_entity_name = create_test_entity_name("PostFailureTest")
        success_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": test_entity_name}
        )
        assert success_response.status_code == 201

    @pytest.mark.asyncio
    async def test_connection_pool_recovery(self, test_client):
        """Test ID: CLN-003 - Connection pool management after failures."""
        # Create test entities
        entity1_name = create_test_entity_name("PoolTestA")
        entity2_name = create_test_entity_name("PoolTestB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Generate many concurrent failures
        async def create_failing_connection():
            """Create connection that will fail."""
            response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity1_id,
                    "to_entity_id": entity2_id,
                    "unit_id": unit_id,
                    "multiplier": -1.0  # Will fail check constraint
                }
            )
            return response

        # Create many concurrent failing operations
        tasks = [create_failing_connection() for _ in range(10)]
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # All should fail
        failed_count = 0
        for response in responses:
            if hasattr(response, 'status_code') and response.status_code == 422:
                failed_count += 1

        assert failed_count == 10

        # Verify system still functions after many failures
        success_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )
        assert success_response.status_code == 201


class TestErrorRecovery:
    """Test error recovery scenarios."""

    @pytest.mark.asyncio
    async def test_database_state_consistency_after_errors(self, test_client):
        """Test ID: ERR-003 - Database maintains consistent state after errors."""
        # Create initial state
        entity1_name = create_test_entity_name("ConsistencyA")
        entity2_name = create_test_entity_name("ConsistencyB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create successful connection
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )
        assert connection_response.status_code == 201

        # Record initial state
        initial_entities = await test_client.get("/api/v1/entities/")
        initial_connections = await test_client.get("/api/v1/connections/")

        initial_entity_count = len(initial_entities.json())
        initial_connection_count = len(initial_connections.json())

        # Cause multiple errors
        error_operations = [
            # Duplicate entity
            test_client.post("/api/v1/entities/", json={"name": entity1_name}),
            # Self-referencing connection
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": entity1_id, "to_entity_id": entity1_id,
                "unit_id": unit_id, "multiplier": 2.0
            }),
            # Negative multiplier
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": entity1_id, "to_entity_id": entity2_id,
                "unit_id": unit_id, "multiplier": -1.0
            }),
            # Non-existent entity
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": 99999, "to_entity_id": entity2_id,
                "unit_id": unit_id, "multiplier": 2.0
            })
        ]

        # Execute all error operations
        await asyncio.gather(*error_operations, return_exceptions=True)

        # Verify state consistency
        final_entities = await test_client.get("/api/v1/entities/")
        final_connections = await test_client.get("/api/v1/connections/")

        final_entity_count = len(final_entities.json())
        final_connection_count = len(final_connections.json())

        # State should be unchanged by failed operations
        assert final_entity_count == initial_entity_count
        assert final_connection_count == initial_connection_count

        # Verify original entities still exist
        entity1_get = await test_client.get(f"/api/v1/entities/{entity1_id}")
        entity2_get = await test_client.get(f"/api/v1/entities/{entity2_id}")

        assert entity1_get.status_code == 200
        assert entity2_get.status_code == 200

        # Verify original connection still exists
        connections = final_connections.json()
        original_connection = next(
            (c for c in connections 
             if c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id),
            None
        )
        assert original_connection is not None
        assert float(original_connection["multiplier"]) == 2.0


class TestAdvancedTransactionScenarios:
    """Test advanced transaction scenarios with complex business logic."""

    @pytest.mark.asyncio
    async def test_complex_connection_graph_transaction(self, test_client):
        """Test complex transaction involving multiple entities and connections."""
        # Create a network of entities
        entity_names = [
            create_test_entity_name("GraphA"),
            create_test_entity_name("GraphB"),
            create_test_entity_name("GraphC"),
            create_test_entity_name("GraphD")
        ]

        # Create entities
        entities = []
        for name in entity_names:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            assert response.status_code == 201
            entities.append(response.json())

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create connections: A->B, B->C, C->D, A->D
        connections_to_create = [
            (entities[0]["id"], entities[1]["id"], 2.0),
            (entities[1]["id"], entities[2]["id"], 3.0),
            (entities[2]["id"], entities[3]["id"], 4.0),
            (entities[0]["id"], entities[3]["id"], 24.0)  # 2 * 3 * 4
        ]

        # Create all connections
        created_connections = []
        for from_id, to_id, multiplier in connections_to_create:
            response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": from_id,
                    "to_entity_id": to_id,
                    "unit_id": unit_id,
                    "multiplier": multiplier
                }
            )
            assert response.status_code == 201
            created_connections.append(response.json())

        # Verify all connections exist (forward and inverse)
        all_connections = await test_client.get("/api/v1/connections/")
        connections = all_connections.json()

        # Should have 8 connections (4 forward, 4 inverse)
        assert len(connections) == 8

        # Verify each forward connection has its inverse
        for from_id, to_id, multiplier in connections_to_create:
            forward = next(
                (c for c in connections 
                 if c["from_entity_id"] == from_id and c["to_entity_id"] == to_id),
                None
            )
            inverse = next(
                (c for c in connections 
                 if c["from_entity_id"] == to_id and c["to_entity_id"] == from_id),
                None
            )

            assert forward is not None
            assert inverse is not None
            assert float(forward["multiplier"]) == multiplier
            assert abs(float(inverse["multiplier"]) - (1.0 / multiplier)) < 0.1

    @pytest.mark.asyncio
    async def test_transaction_with_pathfinding_consistency(self, test_client):
        """Test transaction consistency with pathfinding operations."""
        # Create entities for pathfinding
        entity_a_name = create_test_entity_name("PathA")
        entity_b_name = create_test_entity_name("PathB")
        entity_c_name = create_test_entity_name("PathC")

        entity_a = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_a_name}
        )
        entity_b = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_b_name}
        )
        entity_c = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_c_name}
        )

        entity_a_id = entity_a.json()["id"]
        entity_b_id = entity_b.json()["id"]
        entity_c_id = entity_c.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create connection A->B
        connection_ab = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity_a_id,
                "to_entity_id": entity_b_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )
        assert connection_ab.status_code == 201

        # Create connection B->C
        connection_bc = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity_b_id,
                "to_entity_id": entity_c_id,
                "unit_id": unit_id,
                "multiplier": 3.0
            }
        )
        assert connection_bc.status_code == 201

        # Test pathfinding A->C (should be 2.0 * 3.0 = 6.0)
        path_response = await test_client.get(
            f"/api/v1/compare/{entity_a_id}/{entity_c_id}?unit_id={unit_id}"
        )
        assert path_response.status_code == 200
        path_data = path_response.json()
        assert abs(float(path_data["multiplier"]) - 6.0) < 0.1

        # Now attempt to create a conflicting direct connection A->C
        # This should update the existing path or create a new direct connection
        direct_connection = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity_a_id,
                "to_entity_id": entity_c_id,
                "unit_id": unit_id,
                "multiplier": 10.0
            }
        )
        assert direct_connection.status_code == 201

        # Verify pathfinding now uses direct connection
        path_response_after = await test_client.get(
            f"/api/v1/compare/{entity_a_id}/{entity_c_id}?unit_id={unit_id}"
        )
        assert path_response_after.status_code == 200
        path_data_after = path_response_after.json()
        assert abs(float(path_data_after["multiplier"]) - 10.0) < 0.1

        # Verify all connections are still consistent
        all_connections = await test_client.get("/api/v1/connections/")
        connections = all_connections.json()

        # Should have 6 connections (3 forward, 3 inverse)
        assert len(connections) == 6

        # Verify each connection has its proper inverse
        for conn in connections:
            inverse = next(
                (c for c in connections 
                 if c["from_entity_id"] == conn["to_entity_id"] 
                 and c["to_entity_id"] == conn["from_entity_id"]
                 and c["unit_id"] == conn["unit_id"]),
                None
            )
            assert inverse is not None
            expected_inverse = 1.0 / float(conn["multiplier"])
            actual_inverse = float(inverse["multiplier"])
            # Allow for rounding differences due to 1 decimal place precision
            # For example, 1/3.0 = 0.3333... but stored as 0.3, so tolerance should account for this
            tolerance = max(0.1, abs(expected_inverse) * 0.5)  # At least 0.1 or 50% of expected value
            assert abs(actual_inverse - expected_inverse) < tolerance

