"""
Basic endpoint tests for the SIMILE API.
These test fundamental API functionality using the new fixture pattern.
"""
import pytest


class TestBasicEndpoints:
    """Test basic API endpoints and connectivity."""

    @pytest.mark.asyncio
    async def test_health_endpoint(self, test_client):
        """Test the health check endpoint."""
        response = await test_client.get("/api/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "simile-api"

    @pytest.mark.asyncio
    async def test_api_docs(self, test_client):
        """Test that API documentation is accessible."""
        response = await test_client.get("/api/v1/docs")
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_openapi_spec(self, test_client):
        """Test that OpenAPI spec is generated."""
        response = await test_client.get("/api/v1/openapi.json")
        assert response.status_code == 200
        spec = response.json()
        assert "openapi" in spec
        assert "info" in spec
        assert spec["info"]["title"] == "SIMILE API"

    @pytest.mark.asyncio
    async def test_get_units(self, test_client):
        """Test getting all units (requires database)."""
        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200
        units = response.json()
        assert isinstance(units, list)
        assert len(units) >= 5  # We insert 5 units in conftest.py
        unit_names = [u["name"] for u in units]
        assert "Length" in unit_names
        assert "Mass" in unit_names
