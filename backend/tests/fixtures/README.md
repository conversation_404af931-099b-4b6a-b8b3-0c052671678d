# Complex Graph Test Fixtures

This directory contains comprehensive test fixtures for testing complex graph scenarios and edge cases in the SIMILE backend.

## Overview

The fixtures provide various graph topologies and edge cases for thorough testing of:
- Path finding with 6+ hop connections
- Multiple valid paths between entities
- Disconnected graph components
- Complex unit relationships
- Large-scale graph operations
- Edge cases (circular references, disconnected components, deep paths)

## Available Fixtures

### 1. Star Topology (`StarTopology`)
- **Structure**: Central hub connected to all spokes
- **Use Case**: Testing hub-and-spoke patterns, centralized architectures
- **Properties**: Spokes are only connected through the hub
- **Example**: `ComplexGraphFixtures.create_star_graph(test_client, num_spokes=5)`

### 2. Chain Topology (`ChainTopology`)
- **Structure**: Linear chain of entities (A → B → C → D...)
- **Use Case**: Testing long paths, max path length limits
- **Properties**: Single path between any two entities
- **Example**: `ComplexGraphFixtures.create_chain_graph(test_client, length=8)`

### 3. Grid Topology (`GridTopology`)
- **Structure**: 2D grid with horizontal and vertical connections
- **Use Case**: Testing multiple paths, pathfinding algorithms
- **Properties**: Multiple paths between distant points
- **Example**: `ComplexGraphFixtures.create_grid_graph(test_client, rows=3, cols=3)`

### 4. Complete Graph (`CompleteGraphTopology`)
- **Structure**: Every node connected to every other node
- **Use Case**: Testing optimal path selection, direct connections
- **Properties**: All paths are direct (single hop)
- **Example**: `ComplexGraphFixtures.create_complete_graph(test_client, num_nodes=5)`

### 5. Disconnected Graph (`DisconnectedGraphTopology`)
- **Structure**: Multiple separate components with no cross-connections
- **Use Case**: Testing "no path" scenarios, component isolation
- **Properties**: No paths between different components
- **Example**: `ComplexGraphFixtures.create_disconnected_graph(test_client, num_components=3, nodes_per_component=3)`

### 6. Cyclic Graph (`CyclicGraphTopology`)
- **Structure**: Circular connections forming cycles
- **Use Case**: Testing cycle detection, infinite loop prevention
- **Properties**: Multiple paths around the cycle
- **Example**: `ComplexGraphFixtures.create_cyclic_graph(test_client, cycle_length=4)`

### 7. Deep Tree (`DeepTreeTopology`)
- **Structure**: Tree with specified depth and branching factor
- **Use Case**: Testing max path length limits, deep hierarchies
- **Properties**: Paths from root to leaves can be very long
- **Example**: `ComplexGraphFixtures.create_deep_tree(test_client, depth=7, branching_factor=2)`

### 8. Multi-Unit Graph (`MultiUnitGraphTopology`)
- **Structure**: Graph with connections using different units
- **Use Case**: Testing unit isolation, cross-unit path blocking
- **Properties**: Paths only exist within same unit type
- **Example**: `ComplexGraphFixtures.create_multi_unit_graph(test_client)`

### 9. Large Scale Graph (`LargeScaleGraphTopology`)
- **Structure**: Large graph with configurable density
- **Use Case**: Performance testing, stress testing
- **Properties**: Configurable size and connection density
- **Example**: `ComplexGraphFixtures.create_large_scale_graph(test_client, num_entities=50, connection_density=0.1)`

### 10. Edge Cases (`EdgeCaseGraphTopology`)
- **Structure**: Various edge case scenarios
- **Use Case**: Testing boundary conditions, special cases
- **Properties**: Isolated entities, extreme multipliers, decimal precision
- **Example**: `ComplexGraphFixtures.create_edge_case_graph(test_client)`

## Usage Examples

### Basic Usage
```python
# Create a star topology
data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=5)
entities = data["entities"]
unit_id = data["unit_id"]

# Test path from hub to spoke
response = await test_client.get(
    "/api/v1/compare/",
    params={
        "from": entities["Hub"],
        "to": entities["Spoke0"],
        "unit": unit_id
    }
)
```

### Advanced Usage with Custom Parameters
```python
# Create a large grid for performance testing
data = await ComplexGraphFixtures.create_grid_graph(
    test_client, 
    rows=5, 
    cols=5, 
    unit_name="mass"
)

# Test corner-to-corner path
response = await test_client.get(
    "/api/v1/compare/",
    params={
        "from": data["entities"]["Grid00"],
        "to": data["entities"]["Grid44"],
        "unit": data["unit_id"]
    }
)
```

### Testing Edge Cases
```python
# Create disconnected components
data = await ComplexGraphFixtures.create_disconnected_graph(
    test_client, 
    num_components=3, 
    nodes_per_component=4
)

# Test that cross-component paths fail
response = await test_client.get(
    "/api/v1/compare/",
    params={
        "from": data["entities"]["Comp0Node0"],
        "to": data["entities"]["Comp1Node0"],
        "unit": data["unit_id"]
    }
)
assert response.status_code == 404  # No path should exist
```

## Test Structure

The corresponding test file `test_complex_graph_scenarios.py` contains comprehensive test classes:

- `TestStarTopology`: Tests for star topology
- `TestChainTopology`: Tests for chain topology
- `TestGridTopology`: Tests for grid topology
- `TestCompleteGraphTopology`: Tests for complete graph
- `TestDisconnectedGraphTopology`: Tests for disconnected components
- `TestCyclicGraphTopology`: Tests for cyclic graphs
- `TestDeepTreeTopology`: Tests for deep tree structures
- `TestMultiUnitGraphTopology`: Tests for multi-unit scenarios
- `TestLargeScaleGraphTopology`: Tests for large-scale performance
- `TestEdgeCaseGraphTopology`: Tests for edge cases
- `TestComplexGraphInteractions`: Tests for complex interactions

## Running the Tests

```bash
# Run all complex graph tests
pytest tests/test_complex_graph_scenarios.py -v

# Run specific topology tests
pytest tests/test_complex_graph_scenarios.py::TestStarTopology -v

# Run with coverage
pytest tests/test_complex_graph_scenarios.py --cov=src --cov-report=term-missing
```

## Key Features

### Automatic Test Isolation
- Each fixture creates entities with unique suffixes to prevent naming conflicts
- Tests are isolated through the existing database cleanup mechanisms

### Comprehensive Coverage
- All major graph topologies represented
- Edge cases and boundary conditions tested
- Performance scenarios included

### Realistic Data Patterns
- Varying multiplier values to simulate real-world scenarios
- Different connection densities and patterns
- Mixed precision decimal calculations

### Error Scenarios
- No path scenarios (disconnected components)
- Max path length violations (deep trees)
- Unit isolation testing
- Invalid parameter handling

## Performance Considerations

The fixtures are designed to be:
- **Fast**: Simple topologies for basic testing
- **Scalable**: Configurable sizes for performance testing
- **Realistic**: Representative of actual use cases
- **Maintainable**: Clear structure and documentation

## Dependencies

The fixtures depend on:
- `conftest.py`: For test utilities and database setup
- `models.py`: For entity and connection models
- `FastAPI test client`: For API interaction
- `pytest-asyncio`: For async test support

## Contributing

When adding new fixtures:
1. Follow the existing pattern of inheriting from `GraphTopology`
2. Implement the `setup` method to create entities and connections
3. Add corresponding tests in `test_complex_graph_scenarios.py`
4. Update this documentation with usage examples
5. Ensure fixtures are added to `ComplexGraphFixtures` class for easy access