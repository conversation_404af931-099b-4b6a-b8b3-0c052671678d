"""
Validation script for complex graph fixtures.
This script validates that all fixtures can be instantiated correctly.
"""

import asyncio
from complex_graphs import (
    StarTopology, ChainTopology, GridTopology, CompleteGraphTopology,
    DisconnectedGraphTopology, CyclicGraphTopology, DeepTreeTopology,
    MultiUnitGraphTopology, LargeScaleGraphTopology, EdgeCaseGraphTopology,
    ComplexGraphFixtures
)


async def validate_topology_creation():
    """Validate that all topology classes can be instantiated."""
    print("Validating topology class instantiation...")

    # Test all topology classes
    topologies = [
        StarTopology(num_spokes=3),
        ChainTopology(length=5),
        GridTopology(rows=2, cols=2),
        CompleteGraphTopology(num_nodes=4),
        DisconnectedGraphTopology(num_components=2, nodes_per_component=3),
        CyclicGraphTopology(cycle_length=4),
        DeepTreeTopology(depth=3, branching_factor=2),
        MultiUnitGraphTopology(),
        LargeScaleGraphTopology(num_entities=10, connection_density=0.2),
        EdgeCaseGraphTopology(),
    ]

    for topology in topologies:
        print(f"✓ {topology.__class__.__name__} instantiated successfully")

    print("All topology classes validated successfully!")


def validate_fixture_methods():
    """Validate that all fixture methods exist and are callable."""
    print("\nValidating fixture methods...")

    methods = [
        'create_star_graph',
        'create_chain_graph',
        'create_grid_graph',
        'create_complete_graph',
        'create_disconnected_graph',
        'create_cyclic_graph',
        'create_deep_tree',
        'create_multi_unit_graph',
        'create_large_scale_graph',
        'create_edge_case_graph',
    ]

    for method_name in methods:
        method = getattr(ComplexGraphFixtures, method_name)
        if callable(method):
            print(f"✓ {method_name} is callable")
        else:
            print(f"✗ {method_name} is NOT callable")

    print("All fixture methods validated successfully!")


def validate_topology_properties():
    """Validate that topology properties are set correctly."""
    print("\nValidating topology properties...")

    # Test specific properties
    star = StarTopology(num_spokes=5)
    assert star.num_spokes == 5, "Star topology num_spokes not set correctly"
    print("✓ StarTopology properties validated")

    chain = ChainTopology(length=8)
    assert chain.length == 8, "Chain topology length not set correctly"
    print("✓ ChainTopology properties validated")

    grid = GridTopology(rows=3, cols=4)
    assert (grid.rows == 3 and grid.cols == 4), \
           "Grid topology dimensions not set correctly"
    print("✓ GridTopology properties validated")

    complete = CompleteGraphTopology(num_nodes=6)
    assert complete.num_nodes == 6, \
           "Complete graph num_nodes not set correctly"
    print("✓ CompleteGraphTopology properties validated")

    disconnected = DisconnectedGraphTopology(
        num_components=3, nodes_per_component=4
    )
    assert (disconnected.num_components == 3 and
            disconnected.nodes_per_component == 4), \
           "Disconnected graph properties not set correctly"
    print("✓ DisconnectedGraphTopology properties validated")

    cyclic = CyclicGraphTopology(cycle_length=5)
    assert cyclic.cycle_length == 5, \
           "Cyclic graph cycle_length not set correctly"
    print("✓ CyclicGraphTopology properties validated")

    deep_tree = DeepTreeTopology(depth=4, branching_factor=3)
    assert (deep_tree.depth == 4 and deep_tree.branching_factor == 3), \
           "Deep tree properties not set correctly"
    print("✓ DeepTreeTopology properties validated")

    large_scale = LargeScaleGraphTopology(
        num_entities=50, connection_density=0.15
    )
    assert (large_scale.num_entities == 50 and
            large_scale.connection_density == 0.15), \
           "Large scale properties not set correctly"
    print("✓ LargeScaleGraphTopology properties validated")

    print("All topology properties validated successfully!")


async def main():
    """Run all validation tests."""
    print("🔍 Validating Complex Graph Fixtures")
    print("=" * 50)

    await validate_topology_creation()
    validate_fixture_methods()
    validate_topology_properties()

    print("\n" + "=" * 50)
    print("🎉 All validations passed! Complex graph fixtures are ready "
          "for use.")


if __name__ == "__main__":
    asyncio.run(main())
