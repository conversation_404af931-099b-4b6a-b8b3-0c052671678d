"""
Critical Coverage Tests for Connections Module

These tests specifically target the missing coverage lines in connections.py
to achieve the highest impact on overall test coverage.

Current Coverage: 21% (29/135 statements)
Target Coverage: 85% (115/135 statements)
Missing Lines: 35-50, 52-188, 210-218, 230-233, 247-300, 312-334

Priority: CRITICAL - This single module can improve overall coverage by ~15%
"""

import pytest
from httpx import AsyncClient
from unittest.mock import patch
from sqlalchemy.exc import IntegrityError
import asyncio

from tests.conftest import create_test_entity_name


class TestConnectionsErrorPaths:
    """Test critical error paths in connections module."""

    @pytest.mark.asyncio
    async def test_create_connection_database_failure(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test connection creation with invalid unit ID (simulates database constraint error)."""
        # Create test entities first
        entity1_name = create_test_entity_name("DbFailTest")
        entity2_name = create_test_entity_name("DbFailTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Use invalid unit ID to trigger foreign key constraint error
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 99999,  # Non-existent unit ID
            "multiplier": 2.5
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        # Should get 404 error due to unit not found
        assert response.status_code == 404
        assert "Unit not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_create_connection_from_entity_not_found(self, test_client: AsyncClient, test_data_isolation):
        """Test creating connection with non-existent from_entity - covers lines 35-41."""
        # Create one valid entity
        entity_name = create_test_entity_name("ValidEntity")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        valid_entity_id = entity_response.json()["id"]

        connection_data = {
            "from_entity_id": 99999,  # Non-existent entity
            "to_entity_id": valid_entity_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "From entity not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_create_connection_to_entity_not_found(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test creating connection with non-existent to_entity - covers lines 47-52."""
        # Create one valid entity
        entity_name = create_test_entity_name("ValidEntity")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        valid_entity_id = entity_response.json()["id"]

        connection_data = {
            "from_entity_id": valid_entity_id,
            "to_entity_id": 99999,  # Non-existent entity
            "unit_id": 1,
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "To entity not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_create_connection_unit_not_found(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test creating connection with non-existent unit - covers lines 61-62."""
        # Create test entities
        entity1_name = create_test_entity_name("UnitTest")
        entity2_name = create_test_entity_name("UnitTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 99999,  # Non-existent unit
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "Unit not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_create_connection_duplicate_update_path(self, test_client: AsyncClient, test_data_isolation):
        """Test creating duplicate connection triggers update path - covers lines 75-114."""
        # Create test entities
        entity1_name = create_test_entity_name("DuplicateTest")
        entity2_name = create_test_entity_name("DuplicateTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        # Create initial connection
        response1 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response1.status_code == 201

        # Create duplicate connection with different multiplier
        connection_data["multiplier"] = 3.0
        response2 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response2.status_code == 201

        # Verify the connection was updated, not duplicated
        updated_connection = response2.json()
        assert float(updated_connection["multiplier"]) == 3.0

    @pytest.mark.asyncio
    async def test_create_connection_integrity_error_foreign_key(self, test_client: AsyncClient, test_data_isolation):
        """Test IntegrityError handling for foreign key violations - covers lines 204-207."""
        # Create one valid entity
        entity1_name = create_test_entity_name("FKFailTest")
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity1_id = entity1_response.json()["id"]

        # Use non-existent entity ID to trigger foreign key constraint
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": 99999,  # Non-existent entity
            "unit_id": 1,
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "entity not found" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_create_connection_integrity_error_self_reference(self, test_client: AsyncClient, test_data_isolation):
        """Test IntegrityError handling for self-reference constraint - covers lines 176-180."""
        # Create test entity first
        entity_name = create_test_entity_name("SelfRefTest")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        entity_id = entity_response.json()["id"]

        # Try to create self-referencing connection
        connection_data = {
            "from_entity_id": entity_id,
            "to_entity_id": entity_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 422
        # The error is returned as a validation error list
        error_detail = response.json()["detail"]
        assert any("Cannot create connection from entity to itself" in str(error) for error in error_detail)

    @pytest.mark.asyncio
    async def test_create_connection_integrity_error_positive_multiplier(self, test_client: AsyncClient, test_data_isolation):
        """Test IntegrityError handling for positive multiplier constraint - covers lines 181-185."""
        # Create test entities first
        entity1_name = create_test_entity_name("PosMultTest")
        entity2_name = create_test_entity_name("PosMultTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Try to create connection with negative multiplier (caught by Pydantic validation)
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": -1.0  # Negative value
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 422
        assert "multiplier must be positive" in response.json()["detail"][0]["msg"].lower()

    @pytest.mark.asyncio
    async def test_create_connection_integrity_error_generic(self, test_client: AsyncClient, test_data_isolation):
        """Test IntegrityError handling for generic constraint violations - covers lines 187-191."""
        # Create test entities first
        entity1_name = create_test_entity_name("GenericError")
        entity2_name = create_test_entity_name("GenericError")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Create a valid connection first
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        # First connection should succeed
        response1 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response1.status_code == 201

        # Second identical connection should succeed but update the existing one
        response2 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response2.status_code == 201


class TestConnectionsPaginationAndValidation:
    """Test pagination and validation paths in connections module."""

    @pytest.mark.asyncio
    async def test_get_connections_negative_skip(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test get_connections with negative skip parameter - covers lines 202-203."""
        response = await test_client.get("/api/v1/connections/?skip=-1")
        assert response.status_code == 422
        assert "Skip parameter must be non-negative" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_get_connections_negative_limit(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test get_connections with negative limit parameter - covers lines 204-205."""
        response = await test_client.get("/api/v1/connections/?limit=-1")
        assert response.status_code == 422
        assert "Limit parameter must be non-negative" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_get_connection_not_found(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test get_connection with non-existent ID - covers lines 230-233."""
        response = await test_client.get("/api/v1/connections/99999")
        assert response.status_code == 404
        assert "Connection not found" in response.json()["detail"]


class TestConnectionsUpdateAndDelete:
    """Test update and delete operations in connections module."""

    @pytest.mark.asyncio
    async def test_update_connection_not_found(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test update_connection with non-existent ID - covers lines 247-249."""
        update_data = {"multiplier": 3.0}
        response = await test_client.put("/api/v1/connections/99999", json=update_data)
        assert response.status_code == 404
        assert "Connection not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_update_connection_no_inverse_found(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test update_connection when inverse connection doesn't exist - covers lines 266-289."""
        # Create test entities
        entity1_name = create_test_entity_name("UpdateTest")
        entity2_name = create_test_entity_name("UpdateTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Create connection
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        connection_id = create_response.json()["id"]

        # Test normal connection update
        update_data = {"multiplier": 3.0}
        response = await test_client.put(f"/api/v1/connections/{connection_id}", json=update_data)
        assert response.status_code == 200

        # Verify the update worked
        updated_connection = response.json()
        assert float(updated_connection["multiplier"]) == 3.0

    @pytest.mark.asyncio
    async def test_update_connection_integrity_error(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test update_connection with IntegrityError - covers lines 292-297."""
        # Create test entities and connection
        entity1_name = create_test_entity_name("UpdateIntegrityTest")
        entity2_name = create_test_entity_name("UpdateIntegrityTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        connection_id = create_response.json()["id"]

        # Mock IntegrityError on update
        with patch('sqlalchemy.ext.asyncio.AsyncSession.commit') as mock_commit:
            mock_commit.side_effect = IntegrityError("Update constraint violation", None, None)

            update_data = {"multiplier": 3.0}
            response = await test_client.put(f"/api/v1/connections/{connection_id}", json=update_data)
            assert response.status_code == 400
            assert "Update failed due to constraint violations" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_update_connection_no_fields_to_update(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test update_connection with no fields to update - covers lines 299-300."""
        # Create test entities and connection
        entity1_name = create_test_entity_name("NoUpdateTest")
        entity2_name = create_test_entity_name("NoUpdateTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        connection_id = create_response.json()["id"]

        # Update with None multiplier (no change)
        update_data = {"multiplier": None}
        response = await test_client.put(f"/api/v1/connections/{connection_id}", json=update_data)
        assert response.status_code == 200
        # Should return the existing connection unchanged
        assert float(response.json()["multiplier"]) == 2.0

    @pytest.mark.asyncio
    async def test_delete_connection_not_found(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test delete_connection with non-existent ID - covers lines 312-314."""
        response = await test_client.delete("/api/v1/connections/99999")
        assert response.status_code == 404
        assert "Connection not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_delete_connection_no_inverse(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test delete_connection when inverse doesn't exist - covers lines 326-334."""
        # Create test entities and connection
        entity1_name = create_test_entity_name("DeleteTest")
        entity2_name = create_test_entity_name("DeleteTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        connection_id = create_response.json()["id"]

        # Test normal connection deletion
        response = await test_client.delete(f"/api/v1/connections/{connection_id}")
        assert response.status_code == 200
        assert "deleted successfully" in response.json()["message"]


class TestConnectionsDecimalPrecision:
    """Test decimal precision handling in connections module."""

    @pytest.mark.asyncio
    async def test_create_connection_extreme_small_multiplier(self, test_client: AsyncClient, test_data_isolation):
        """Test connection creation with multiplier that rounds to minimum 0.1."""
        # Create test entities
        entity1_name = create_test_entity_name("DecimalTest")
        entity2_name = create_test_entity_name("DecimalTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a valid unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Test with very small multiplier that should be rejected
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": unit_id,
            "multiplier": 0.05  # Should be rejected as too small
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 422

        # Test with minimum valid multiplier (0.1)
        valid_connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": unit_id,
            "multiplier": 0.1  # Minimum valid multiplier
        }

        valid_response = await test_client.post("/api/v1/connections/", json=valid_connection_data)
        assert valid_response.status_code == 201

        # Verify the inverse connection is created with proper inverse value
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()

        # Find the inverse connection
        inverse_connection = next(
            (c for c in connections if c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id),
            None
        )

        assert inverse_connection is not None, "Inverse connection should exist"
        assert inverse_connection["multiplier"] == "10.0", f"Inverse multiplier should be 10.0 (1/0.1), got {inverse_connection['multiplier']}"

    @pytest.mark.asyncio
    async def test_create_connection_bankers_rounding(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test banker's rounding (ROUND_HALF_EVEN) for connection multipliers."""
        # Create test entities
        entity1_name = create_test_entity_name("BankersTest")
        entity2_name = create_test_entity_name("BankersTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Test cases for banker's rounding
        test_cases = [
            (1.15, "1.2"),  # 0.5 rounds to even (up to 2)
            (1.25, "1.2"),  # 0.5 rounds to even (down to 2)
            (1.35, "1.4"),  # 0.5 rounds to even (up to 4)
            (1.45, "1.4"),  # 0.5 rounds to even (down to 4)
        ]

        for i, (input_multiplier, expected_str) in enumerate(test_cases):
            # Create unique entities for each test case (using letters instead of numbers)
            letter_index = chr(65 + i)  # 0->A, 1->B, etc.
            entity1_test_name = create_test_entity_name(f"BankersRoundTest{letter_index}A")
            entity2_test_name = create_test_entity_name(f"BankersRoundTest{letter_index}B")

            entity1_test_response = await test_client.post("/api/v1/entities/", json={"name": entity1_test_name})
            entity2_test_response = await test_client.post("/api/v1/entities/", json={"name": entity2_test_name})

            entity1_test_id = entity1_test_response.json()["id"]
            entity2_test_id = entity2_test_response.json()["id"]

            connection_data = {
        "from_entity_id": entity1_test_id,
        "to_entity_id": entity2_test_id,
        "unit_id": 1,
        "multiplier": input_multiplier
            }

            response = await test_client.post("/api/v1/connections/", json=connection_data)
            assert response.status_code == 201

            # Verify the multiplier is properly rounded
            created_connection = response.json()
            assert created_connection["multiplier"] == expected_str


class TestConnectionsConcurrentOperations:
    """Test concurrent operations scenarios in connections module."""

    @pytest.mark.asyncio
    async def test_concurrent_connection_creation(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test concurrent creation of the same connection."""
        # Create test entities
        entity1_name = create_test_entity_name("ConcurrentTest")
        entity2_name = create_test_entity_name("ConcurrentTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        # Create first connection
        response1 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response1.status_code == 201

        # Create second connection with different multiplier (should update)
        connection_data["multiplier"] = 3.0
        response2 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response2.status_code == 201

        # Verify only one connection pair exists (not 4)
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        # Should have exactly 2 connections (forward and inverse)
        relevant_connections = [
            c for c in connections 
            if (c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id) or
               (c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id)
        ]
        assert len(relevant_connections) == 2

        # Verify the multiplier was updated to 3.0
        primary_connection = next((c for c in relevant_connections if c["from_entity_id"] == entity1_id), None)
        assert primary_connection is not None
        assert float(primary_connection["multiplier"]) == 3.0

    @pytest.mark.asyncio
    async def test_concurrent_connection_update(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test concurrent updates to the same connection."""
        # Create test entities and connection
        entity1_name = create_test_entity_name("ConcurrentUpdateTest")
        entity2_name = create_test_entity_name("ConcurrentUpdateTest")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        connection_id = create_response.json()["id"]

        # Create concurrent update requests
        async def update_connection(multiplier):
            return await test_client.put(f"/api/v1/connections/{connection_id}", 
                               json={"multiplier": multiplier})

        tasks = [update_connection(3.0 + i) for i in range(3)]
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # All updates should succeed
        success_count = sum(1 for r in responses if not isinstance(r, Exception) and r.status_code == 200)
        assert success_count == 3

        # Verify final state is consistent
        final_response = await test_client.get(f"/api/v1/connections/{connection_id}")
        assert final_response.status_code == 200
        final_multiplier = float(final_response.json()["multiplier"])
        assert final_multiplier >= 3.0  # Should be one of the updated values


class TestConnectionsIntegrationScenarios:
    """Test integration scenarios and complex connection networks."""

    @pytest.mark.asyncio
    async def test_cascade_entity_deletion_impact(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test how entity deletion affects related connections - integration test."""
        # Create test entities
        entity1_name = create_test_entity_name("CascadeA")
        entity2_name = create_test_entity_name("CascadeB")
        entity3_name = create_test_entity_name("CascadeC")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )
        entity3_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity3_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        entity3_id = entity3_response.json()["id"]

        # Create connections: A->B and B->C
        connection1_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        connection2_data = {
            "from_entity_id": entity2_id,
            "to_entity_id": entity3_id,
            "unit_id": 1,
            "multiplier": 3.0
        }

        await test_client.post("/api/v1/connections/", json=connection1_data)
        await test_client.post("/api/v1/connections/", json=connection2_data)

        # Verify connections exist
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        assert len(connections) == 4  # 2 primary + 2 inverse

        # Delete entity B (middle entity)
        delete_response = await test_client.delete(f"/api/v1/entities/{entity2_id}")
        assert delete_response.status_code == 200

        # Verify connections involving entity B are removed
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()

        # Should have no connections involving entity2_id
        remaining_connections = [
            c for c in connections 
            if c["from_entity_id"] == entity2_id or c["to_entity_id"] == entity2_id
        ]
        assert len(remaining_connections) == 0

    @pytest.mark.asyncio
    async def test_complex_connection_network_creation(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test creating a complex network of connections."""
        # Create 5 entities (using letters instead of numbers)
        entity_names = [create_test_entity_name(f"Network{chr(65 + i)}") for i in range(5)]
        entity_ids = []

        for name in entity_names:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            entity_ids.append(response.json()["id"])

        # Create connections in a star pattern: 0->1, 0->2, 0->3, 0->4
        for i in range(1, 5):
            connection_data = {
                "from_entity_id": entity_ids[0],
                "to_entity_id": entity_ids[i],
                "unit_id": 1,
                "multiplier": float(i + 1)
            }

            response = await test_client.post("/api/v1/connections/", json=connection_data)
            assert response.status_code == 201

        # Verify all connections exist
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()

        # Should have 8 connections (4 primary + 4 inverse)
        assert len(connections) == 8

        # Verify each connection has correct multiplier
        for i in range(1, 5):
            primary_conn = next((
        c for c in connections 
        if c["from_entity_id"] == entity_ids[0] and c["to_entity_id"] == entity_ids[i]
            ), None)

            assert primary_conn is not None
            assert float(primary_conn["multiplier"]) == float(i + 1)

            # Verify inverse connection
            inverse_conn = next((
        c for c in connections 
        if c["from_entity_id"] == entity_ids[i] and c["to_entity_id"] == entity_ids[0]
            ), None)

            assert inverse_conn is not None
            expected_inverse = 1.0 / float(i + 1)
            assert abs(float(inverse_conn["multiplier"]) - expected_inverse) < 0.1

    @pytest.mark.asyncio
    async def test_transaction_rollback_on_error(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test that transactions are properly rolled back on errors."""
        # Create test entities
        entity1_name = create_test_entity_name("TxRollbackA")
        entity2_name = create_test_entity_name("TxRollbackB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get initial connection count
        connections_response = await test_client.get("/api/v1/connections/")
        initial_count = len(connections_response.json())

        # Try to create connection with invalid unit_id (should fail)
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 99999,  # Invalid unit
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "Unit not found" in response.json()["detail"]

        # Verify no connections were created (transaction rolled back)
        connections_response = await test_client.get("/api/v1/connections/")
        final_count = len(connections_response.json())
        assert final_count == initial_count

    @pytest.mark.asyncio
    async def test_connection_validation_error_paths(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test various validation error paths in connection creation."""
        # Create test entities
        entity1_name = create_test_entity_name("ValidationA")
        entity2_name = create_test_entity_name("ValidationB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Test non-existent from_entity (covers lines 35-41)
        connection_data = {
            "from_entity_id": 99999,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "From entity not found" in response.json()["detail"]

        # Test non-existent to_entity (covers lines 47-52)
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": 99999,
            "unit_id": 1,
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "To entity not found" in response.json()["detail"]

        # Test non-existent unit (covers lines 61-62)
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 99999,
            "multiplier": 2.0
        }

        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "Unit not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_connection_pagination_edge_cases(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test pagination with edge cases to cover more lines."""
        # Test with negative skip (covers lines 202-203)
        response = await test_client.get("/api/v1/connections/?skip=-1")
        assert response.status_code == 422
        assert "Skip parameter must be non-negative" in response.json()["detail"]

        # Test with negative limit (covers lines 204-205)
        response = await test_client.get("/api/v1/connections/?limit=-1")
        assert response.status_code == 422
        assert "Limit parameter must be non-negative" in response.json()["detail"]

        # Test with zero limit (should work)
        response = await test_client.get("/api/v1/connections/?limit=0")
        assert response.status_code == 200
        assert len(response.json()) == 0

    @pytest.mark.asyncio
    async def test_connection_crud_complete_cycle(
        self,
        test_client: AsyncClient,
        test_data_isolation
    ):
        """Test complete CRUD cycle for connections to ensure all paths are covered."""
        # Create test entities
        entity1_name = create_test_entity_name("CRUDTestA")
        entity2_name = create_test_entity_name("CRUDTestB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # CREATE - Create connection
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.5
        }

        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert create_response.status_code == 201
        connection_id = create_response.json()["id"]

        # READ - Get specific connection
        get_response = await test_client.get(f"/api/v1/connections/{connection_id}")
        assert get_response.status_code == 200
        assert float(get_response.json()["multiplier"]) == 2.5

        # READ - Get all connections
        list_response = await test_client.get("/api/v1/connections/")
        assert list_response.status_code == 200
        connections = list_response.json()
        assert len(connections) == 2  # Primary + inverse

        # UPDATE - Update connection
        update_data = {"multiplier": 3.5}
        update_response = await test_client.put(f"/api/v1/connections/{connection_id}", json=update_data)
        assert update_response.status_code == 200
        assert float(update_response.json()["multiplier"]) == 3.5

        # DELETE - Delete connection
        delete_response = await test_client.delete(f"/api/v1/connections/{connection_id}")
        assert delete_response.status_code == 200
        assert "Connection and its inverse deleted successfully" in delete_response.json()["message"]

        # Verify deletion
        get_response = await test_client.get(f"/api/v1/connections/{connection_id}")
        assert get_response.status_code == 404

