"""
Locust performance test suite for SIMILE API.
Simulates realistic user behavior and API usage patterns.
"""
from locust import HttpUser, task, between, events
import random
import time
import logging
from datetime import datetime
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimileAPIUser(HttpUser):
    wait_time = between(1, 3)  # Wait 1-3 seconds between tasks

    def on_start(self):
        """Initialize test data on user creation."""
        self.entity_ids = []
        self.connection_ids = []
        self.units = ["length", "mass", "time", "volume", "area", "count"]
        self.user_id = str(uuid.uuid4())[:8]

        # Create some initial entities for this user
        for i in range(5):
            self.create_entity_internal()

    def create_entity_internal(self):
        """Internal method to create entity without task overhead."""
        unit = random.choice(self.units)
        name = f"PerfTest_{self.user_id}_{len(self.entity_ids)}"

        with self.client.post("/api/entities", json={
            "name": name,
            "unit_id": unit
        }, catch_response=True) as response:
            if response.status_code == 201:
                data = response.json()
                self.entity_ids.append(data["id"])
                return data["id"]
            else:
                logger.error(
                    f"Failed to create entity: {response.status_code} - "
                    f"{response.text}"
                )
                return None

    @task(10)
    def list_entities(self):
        """Test entity listing with various parameters."""
        # Test different pagination scenarios
        scenarios = [
            {"limit": 10, "offset": 0},
            {"limit": 20, "offset": 10},
            {"limit": 50, "offset": 0},
            {},  # Default parameters
        ]

        params = random.choice(scenarios)

        with self.client.get("/api/entities", params=params,
                             catch_response=True) as response:
            if response.status_code != 200:
                response.failure(
                    f"Failed to list entities: {response.status_code}"
                )

    @task(6)
    def create_entity(self):
        """Test entity creation endpoint."""
        unit = random.choice(self.units)
        name = f"PerfTest_{self.user_id}_{int(time.time() * 1000)}"

        with self.client.post("/api/entities", json={
            "name": name,
            "unit_id": unit
        }, catch_response=True) as response:
            if response.status_code == 201:
                data = response.json()
                self.entity_ids.append(data["id"])
            elif response.status_code == 409:
                # Name conflict is acceptable
                pass
            else:
                response.failure(
                    f"Failed to create entity: {response.status_code}"
                )

    @task(4)
    def get_entity(self):
        """Test individual entity retrieval."""
        if not self.entity_ids:
            return

        entity_id = random.choice(self.entity_ids)

        with self.client.get(f"/api/entities/{entity_id}",
                             catch_response=True) as response:
            if response.status_code != 200:
                response.failure(
                    f"Failed to get entity: {response.status_code}"
                )

    @task(3)
    def update_entity(self):
        """Test entity updates."""
        if not self.entity_ids:
            return

        entity_id = random.choice(self.entity_ids)
        new_name = f"UpdatedPerfTest_{self.user_id}_{int(time.time() * 1000)}"

        with self.client.put(f"/api/entities/{entity_id}", json={
            "name": new_name
        }, catch_response=True) as response:
            # 404 is acceptable if entity was deleted
            if response.status_code not in [200, 404]:
                response.failure(
                    f"Failed to update entity: {response.status_code}"
                )

    @task(8)
    def create_connection(self):
        """Test connection creation."""
        if len(self.entity_ids) < 2:
            # Create more entities if needed
            for _ in range(3):
                self.create_entity_internal()
            return

        # Select two entities with same unit
        from_entity_id = random.choice(self.entity_ids)
        to_entity_id = random.choice(
            [eid for eid in self.entity_ids if eid != from_entity_id]
        )

        # Generate realistic connection values
        connection_value = round(random.uniform(0.1, 100.0), 1)

        with self.client.post("/api/connections", json={
            "from_entity_id": from_entity_id,
            "to_entity_id": to_entity_id,
            "connection_value": connection_value
        }, catch_response=True) as response:
            if response.status_code == 201:
                data = response.json()
                self.connection_ids.append(data["id"])
            elif response.status_code == 409:
                # Connection already exists
                pass
            elif response.status_code == 400:
                # Unit mismatch is acceptable
                pass
            else:
                response.failure(
                    f"Failed to create connection: {response.status_code}"
                )

    @task(12)
    def list_connections(self):
        """Test connection listing."""
        scenarios = [
            {"limit": 10, "offset": 0},
            {"limit": 25, "offset": 5},
            {},  # Default parameters
        ]

        params = random.choice(scenarios)

        with self.client.get("/api/connections", params=params,
                             catch_response=True) as response:
            if response.status_code != 200:
                response.failure(
                    f"Failed to list connections: {response.status_code}"
                )

    @task(15)
    def compare_entities(self):
        """Test entity comparison (pathfinding)."""
        if len(self.entity_ids) < 2:
            return

        from_entity_id = random.choice(self.entity_ids)
        to_entity_id = random.choice(
            [eid for eid in self.entity_ids if eid != from_entity_id]
        )

        # Test different path limits
        path_limit = random.choice([3, 6, 10])

        with self.client.get(
            f"/api/compare/{from_entity_id}/{to_entity_id}",
            params={"max_path_length": path_limit},
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                # Validate response structure
                if "comparison_result" not in data:
                    response.failure("Missing comparison_result in response")
            elif response.status_code == 404:
                # No path found is acceptable
                pass
            else:
                response.failure(
                    f"Failed to compare entities: {response.status_code}"
                )

    @task(2)
    def get_connection(self):
        """Test individual connection retrieval."""
        if not self.connection_ids:
            return

        connection_id = random.choice(self.connection_ids)

        with self.client.get(f"/api/connections/{connection_id}",
                             catch_response=True) as response:
            # 404 is acceptable if connection was deleted
            if response.status_code not in [200, 404]:
                response.failure(
                    f"Failed to get connection: {response.status_code}"
                )

    @task(1)
    def delete_connection(self):
        """Test connection deletion."""
        if not self.connection_ids:
            return

        connection_id = self.connection_ids.pop(
            random.randint(0, len(self.connection_ids) - 1)
        )

        with self.client.delete(f"/api/connections/{connection_id}",
                                catch_response=True) as response:
            # 404 is acceptable if already deleted
            if response.status_code not in [200, 404]:
                response.failure(
                    f"Failed to delete connection: {response.status_code}"
                )

    @task(5)
    def list_units(self):
        """Test units listing."""
        with self.client.get("/api/units", catch_response=True) as response:
            if response.status_code != 200:
                response.failure(
                    f"Failed to list units: {response.status_code}"
                )

    @task(1)
    def stress_test_rapid_requests(self):
        """Stress test with rapid sequential requests."""
        # Make 5 rapid requests
        for i in range(5):
            self.client.get("/api/units")
            time.sleep(0.1)  # Small delay between requests


class HeavyUser(HttpUser):
    """Heavy user that creates more load."""
    wait_time = between(0.5, 1.5)  # Shorter wait times
    weight = 1  # Lower weight means fewer users of this type

    def on_start(self):
        """Initialize heavy user with more entities."""
        self.entity_ids = []
        self.connection_ids = []
        self.units = ["length", "mass", "time", "volume", "area", "count"]
        self.user_id = str(uuid.uuid4())[:8]

        # Create more initial entities
        for i in range(20):
            self.create_entity_internal()

    def create_entity_internal(self):
        """Internal method to create entity without task overhead."""
        unit = random.choice(self.units)
        name = f"HeavyTest_{self.user_id}_{len(self.entity_ids)}"

        with self.client.post("/api/entities", json={
            "name": name,
            "unit_id": unit
        }, catch_response=True) as response:
            if response.status_code == 201:
                data = response.json()
                self.entity_ids.append(data["id"])
                return data["id"]
            return None

    @task(20)
    def heavy_comparison_load(self):
        """Heavy comparison operations."""
        if len(self.entity_ids) < 2:
            return

        # Make multiple comparisons
        for _ in range(3):
            from_entity_id = random.choice(self.entity_ids)
            to_entity_id = random.choice(
                [eid for eid in self.entity_ids if eid != from_entity_id]
            )

            self.client.get(f"/api/compare/{from_entity_id}/{to_entity_id}")
            time.sleep(0.1)

    @task(10)
    def bulk_connection_creation(self):
        """Create multiple connections in sequence."""
        if len(self.entity_ids) < 5:
            return

        # Create 5 connections rapidly
        for _ in range(5):
            if len(self.entity_ids) >= 2:
                from_entity_id = random.choice(self.entity_ids)
                to_entity_id = random.choice(
                    [eid for eid in self.entity_ids if eid != from_entity_id]
                )

                self.client.post("/api/connections", json={
                    "from_entity_id": from_entity_id,
                    "to_entity_id": to_entity_id,
                    "connection_value": round(random.uniform(0.1, 100.0), 1)
                })
            time.sleep(0.05)

# Performance monitoring events


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Log test start."""
    logger.info(f"Performance test started at {datetime.utcnow()}")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Log test completion and stats."""
    logger.info(f"Performance test completed at {datetime.utcnow()}")

    # Log final statistics
    stats = environment.stats
    logger.info(f"Total requests: {stats.total.num_requests}")
    logger.info(f"Total failures: {stats.total.num_failures}")
    logger.info(
        f"Average response time: {stats.total.avg_response_time:.2f}ms"
    )
    logger.info(
        f"95th percentile response time: "
        f"{stats.total.get_response_time_percentile(0.95):.2f}ms"
    )
    logger.info(f"Requests per second: {stats.total.current_rps:.2f}")


@events.request_failure.add_listener
def on_request_failure(
    request_type,
    name,
    response_time,
    response_length,
    exception,
    **kwargs
):
    """Log request failures."""
    logger.warning(f"Request failed: {request_type} {name} - {exception}")


# Custom shape classes for different load patterns
class StepLoadShape:
    """Gradually increase load in steps."""

    def tick(self):
        run_time = self.get_run_time()

        if run_time < 60:
            return (10, 2)  # 10 users, spawn 2 per second
        elif run_time < 120:
            return (20, 4)  # 20 users, spawn 4 per second
        elif run_time < 180:
            return (50, 5)  # 50 users, spawn 5 per second
        elif run_time < 240:
            return (100, 10)  # 100 users, spawn 10 per second
        else:
            return None  # Stop test
