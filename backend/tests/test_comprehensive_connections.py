"""
Comprehensive test suite for Connection CRUD operations.
Tests all edge cases, validation, auto-inverse creation, and business logic.
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.models import Connection


class TestConnectionCRUD:
    """Test all Connection CRUD operations comprehensively."""

    async def create_test_entities_and_unit(self, test_client):
        """Helper function to create test entities and get a unit for connection tests."""
        # Create two test entities with unique names to avoid conflicts
        import string
        import random

        # Generate random suffix with only letters (no numbers)
        suffix = ''.join(random.choices(string.ascii_letters, k=8))

        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Test Source Entity {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Test Target Entity {suffix}"}
        )

        # Get first available unit
        units = await test_client.get("/api/v1/units/")
        unit = units.json()[0]

        return {
            "entity1_id": entity1.json()["id"],
            "entity2_id": entity2.json()["id"],
            "unit_id": unit["id"],
            "unit": unit
        }

    @pytest.mark.asyncio
    async def test_create_connection_success(self, test_client):
        """Test successful connection creation."""
        data = await self.create_test_entities_and_unit(test_client)

        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": data["entity1_id"],
                "to_entity_id": data["entity2_id"],
                "unit_id": data["unit_id"],
                "multiplier": 2.5
            }
        )

        assert response.status_code == 201
        result = response.json()
        assert result["from_entity_id"] == data["entity1_id"]
        assert result["to_entity_id"] == data["entity2_id"]
        assert result["unit_id"] == data["unit_id"]
        assert float(result["multiplier"]) == 2.5
        assert "id" in result
        assert "created_at" in result

    @pytest.mark.asyncio
    async def test_create_connection_auto_inverse(self, test_client):
        """Test automatic inverse connection creation."""
        data = await self.create_test_entities_and_unit(test_client)

        # Create connection A -> B with multiplier 4
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": data["entity1_id"],
                "to_entity_id": data["entity2_id"],
                "unit_id": data["unit_id"],
                "multiplier": 4.0
            }
        )
        print(f"Connection creation response: {response.status_code}")
        print(f"Response text: {response.text}")
        assert response.status_code == 201

        # Get all connections
        connections = await test_client.get("/api/v1/connections/")
        conn_list = connections.json()

        print(f"All connections: {conn_list}")
        print(f"Looking for inverse: from_entity_id={data['entity2_id']}, to_entity_id={data['entity1_id']}")

        # Find the inverse connection
        inverse = next(
            (c for c in conn_list
             if c["from_entity_id"] == data["entity2_id"]
             and c["to_entity_id"] == data["entity1_id"]),
            None
        )

        assert inverse is not None
        expected_inverse = round(1 / 4, 1)  # 1/4 = 0.25, let's see what Python rounds to
        assert float(inverse["multiplier"]) == expected_inverse
        assert inverse["unit_id"] == data["unit_id"]

    @pytest.mark.asyncio
    async def test_create_connection_decimal_precision(self, test_client):
        """Test connection creation with decimal precision (1 decimal place)."""
        data = await self.create_test_entities_and_unit(test_client)

        # Test various decimal values - each with unique entity pairs to avoid duplicates
        test_values = [
            (1.1, 1.1),
            (1.15, 1.2),  # Should round to 1 decimal (away from even)
            (1.14, 1.1),  # Should round down
            (1.99, 2.0),
            (0.15, 0.2),  # Use 0.15 instead of 0.05 (banker's rounding issue)
            (0.04, "REJECT"),  # Should be rejected as rounds to 0
        ]

        # First create additional entities for testing (names must be letters only)
        entity_names = ["Alpha", "Beta", "Gamma", "Delta", "Epsilon", "Zeta"]
        entity_ids = []
        for i in range(len(test_values)):
            entity_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity_names[i]}
            )
            assert entity_response.status_code == 201
            entity_ids.append(entity_response.json()["id"])

        for i, (input_val, expected) in enumerate(test_values):
            # Use different entity pairs for each test to avoid duplicate connection errors
            from_entity_id = data["entity1_id"] if i % 2 == 0 else data["entity2_id"]
            to_entity_id = entity_ids[i]  # Use the newly created entities

            response = await test_client.post(
            "/api/v1/connections/",
            json={
                    "from_entity_id": from_entity_id,
                    "to_entity_id": to_entity_id,
                    "unit_id": data["unit_id"],
                    "multiplier": input_val
            }
        )

            if expected == "REJECT":
                # These values should be rejected
                assert response.status_code == 422, f"Expected rejection for input {input_val}: {response.text}"
            else:
                assert response.status_code == 201, f"Failed for input {input_val}: {response.text}"
                assert float(response.json()["multiplier"]) == expected

    @pytest.mark.asyncio
    async def test_create_connection_same_entity(self, test_client):
        """Test creating connection from entity to itself."""
        data = await self.create_test_entities_and_unit(test_client)

        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": data["entity1_id"],
                "to_entity_id": data["entity1_id"],
                "unit_id": data["unit_id"],
                "multiplier": 1.0
            }
        )

        assert response.status_code == 422
        # Check that the error message mentions self-connection prevention
        error_details = response.json()["detail"]
        error_msg = str(error_details).lower()
        assert "cannot create connection from entity to itself" in \
            error_msg

    @pytest.mark.asyncio
    async def test_create_connection_duplicate(self, test_client):
        """Test creating duplicate connection updates existing one."""
        data = await self.create_test_entities_and_unit(test_client)

        # Create first connection
        response1 = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": data["entity1_id"],
                "to_entity_id": data["entity2_id"],
                "unit_id": data["unit_id"],
                "multiplier": 2.0
            }
        )
        assert response1.status_code == 201
        connection1 = response1.json()

        # Try to create duplicate - should update instead
        response2 = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": data["entity1_id"],
                "to_entity_id": data["entity2_id"],
                "unit_id": data["unit_id"],
                "multiplier": 3.0
            }
        )
        assert response2.status_code == 201  # Now returns 201 for updates
        connection2 = response2.json()

        # Verify it's the same connection ID but with updated multiplier
        assert connection2["id"] == connection1["id"]
        assert connection2["multiplier"] == "3.0"
        assert connection2["updated_at"] != connection1["created_at"]

    @pytest.mark.asyncio
    async def test_create_connection_negative_multiplier(self, test_client):
        """Test creating connection with negative multiplier."""
        data = await self.create_test_entities_and_unit(test_client)

        response = await test_client.post(
            "/api/v1/connections/",
            json={
                    "from_entity_id": data["entity1_id"],
                    "to_entity_id": data["entity2_id"],
                    "unit_id": data["unit_id"],
                    "multiplier": -2.5
                }
        )

        assert response.status_code == 422
        # Check that the error message mentions positive values
        error_details = response.json()["detail"]
        error_msg = str(error_details).lower()
        assert "multiplier must be positive" in error_msg or "greater_than" in error_msg

    @pytest.mark.asyncio
    async def test_create_connection_zero_multiplier(self, test_client):
        """Test creating connection with zero multiplier."""
        data = await self.create_test_entities_and_unit(test_client)

        response = await test_client.post(
            "/api/v1/connections/",
            json={
                    "from_entity_id": data["entity1_id"],
                    "to_entity_id": data["entity2_id"],
                    "unit_id": data["unit_id"],
                    "multiplier": 0
                }
        )

        assert response.status_code == 422
        # Check that the error message mentions positive values
        error_details = response.json()["detail"]
        error_msg = str(error_details).lower()
        assert "multiplier must be positive" in error_msg or "greater_than" in error_msg

    @pytest.mark.asyncio
    async def test_create_connection_very_large_multiplier(self, test_client):
        """Test creating connection with very large multiplier should fail validation."""
        data = await self.create_test_entities_and_unit(test_client)

        response = await test_client.post(
            "/api/v1/connections/",
            json={
                    "from_entity_id": data["entity1_id"],
                    "to_entity_id": data["entity2_id"],
                    "unit_id": data["unit_id"],
                    "multiplier": 1e10  # 10 billion - exceeds database constraint
                }
        )

        assert response.status_code == 422
        error_details = response.json()["detail"]
        assert any("less than or equal" in str(error).lower() for error in error_details)

    @pytest.mark.asyncio
    async def test_create_connection_very_small_multiplier(self, test_client):
        """Test creating connection with very small multiplier."""
        data = await self.create_test_entities_and_unit(test_client)

        response = await test_client.post(
            "/api/v1/connections/",
            json={
                    "from_entity_id": data["entity1_id"],
                    "to_entity_id": data["entity2_id"],
                    "unit_id": data["unit_id"],
                    "multiplier": 0.0001
            }
        )

        # Should round to 0.0 and possibly fail
        assert response.status_code in [201, 422]

    @pytest.mark.asyncio
    async def test_create_connection_invalid_entity(self, test_client):
        """Test creating connection with non-existent entity."""
        data = await self.create_test_entities_and_unit(test_client)

        response = await test_client.post(
            "/api/v1/connections/",
            json={
                    "from_entity_id": 99999,
                    "to_entity_id": data["entity2_id"],
                    "unit_id": data["unit_id"],
                    "multiplier": 2.0
                }
        )

        assert response.status_code == 404
        assert "entity" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_create_connection_invalid_unit(self, test_client):
        """Test creating connection with non-existent unit."""
        data = await self.create_test_entities_and_unit(test_client)

        response = await test_client.post(
            "/api/v1/connections/",
            json={
                    "from_entity_id": data["entity1_id"],
                    "to_entity_id": data["entity2_id"],
                    "unit_id": 99999,
                    "multiplier": 2.0
                }
        )

        assert response.status_code == 404
        assert "unit" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_get_all_connections(self, test_client):
        """Test getting all connections."""
        data = await self.create_test_entities_and_unit(test_client)

        # Create multiple connections
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
        connection_test_names = ["Alpha", "Beta", "Gamma"]
        entities = []
        for name in connection_test_names:
            entity = await test_client.post(
                    "/api/v1/entities/",
                    json={"name": f"Connection Test Entity {name} {suffix}"}
            )
            entities.append(entity.json()["id"])

        # Create connections between them
        for i in range(len(entities) - 1):
            await test_client.post(
                        "/api/v1/connections/",
                    json={
                        "from_entity_id": entities[i],
                        "to_entity_id": entities[i + 1],
                        "unit_id": data["unit_id"],
                        "multiplier": i + 1
                    }
            )

        # Get all connections - use high limit to ensure we get the newly created ones
        response = await test_client.get(
            "/api/v1/connections/?limit=1000")
        assert response.status_code == 200
        connections = response.json()

        # Filter to only the connections involving our test entities
        test_entity_ids = set(entities)
        relevant_connections = [
            c for c in connections
            if c["from_entity_id"] in test_entity_ids or c["to_entity_id"] in test_entity_ids
        ]
        assert len(relevant_connections) >= 4  # At least 2 connections + their inverses

    @pytest.mark.asyncio
    async def test_get_connection_by_id(self, test_client):
        """Test getting specific connection by ID."""
        data = await self.create_test_entities_and_unit(test_client)

        # Create connection
        create_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": data["entity1_id"],
                "to_entity_id": data["entity2_id"],
                "unit_id": data["unit_id"],
                "multiplier": 3.5
            }
        )
        connection_id = create_response.json()["id"]

        # Get by ID
        response = await test_client.get(

            f"/api/v1/connections/{connection_id}")
        assert response.status_code == 200
        conn = response.json()
        assert conn["id"] == connection_id
        assert float(conn["multiplier"]) == 3.5

    @pytest.mark.asyncio
    async def test_get_connection_not_found(self, test_client):
        """Test getting non-existent connection."""
        response = await test_client.get(
            "/api/v1/connections/99999")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_connection_success(self, test_client):
        """Test successful connection deletion."""
        data = await self.create_test_entities_and_unit(test_client)

        # Create connection
        create_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": data["entity1_id"],
                "to_entity_id": data["entity2_id"],
                "unit_id": data["unit_id"],
                "multiplier": 2.0
            }
        )
        connection_id = create_response.json()["id"]

        # Delete connection
        response = await test_client.delete(f"/api/v1/connections/{connection_id}")
        assert response.status_code == 200

        # Verify it's deleted
        get_response = await test_client.get(
            f"/api/v1/connections/{connection_id}")
        assert get_response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_connection_deletes_inverse(self, test_client):
        """Test that deleting a connection also deletes its inverse."""
        data = await self.create_test_entities_and_unit(test_client)

        # Create connection
        await test_client.post(
                "/api/v1/connections/",
            json={
                    "from_entity_id": data["entity1_id"],
                    "to_entity_id": data["entity2_id"],
                    "unit_id": data["unit_id"],
                    "multiplier": 5.0
            }
        )

        # Get all connections before delete - use high limit to see all connections
        all_before = await test_client.get("/api/v1/connections/?limit=1000")
        connections_before = all_before.json()

        # Find the main connection
        main_conn = next(
            c for c in connections_before
            if c["from_entity_id"] == data["entity1_id"]
            and c["to_entity_id"] == data["entity2_id"]
        )

        # Delete the main connection
        await test_client.delete(f"/api/v1/connections/{main_conn['id']}")

        # Get all connections after delete - use high limit to see all connections
        all_after = await test_client.get("/api/v1/connections/?limit=1000")
        connections_after = all_after.json()

        # Verify both connections are gone
        for conn in connections_after:
            assert not (conn["from_entity_id"] == data["entity1_id"] and
                       conn["to_entity_id"] == data["entity2_id"])
            assert not (conn["from_entity_id"] == data["entity2_id"] and
                       conn["to_entity_id"] == data["entity1_id"])

    @pytest.mark.asyncio
    async def test_connection_with_different_units(self, test_client):
        """Test creating multiple connections between same entities with different units."""
        # Create entities
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()

        entity1 = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"Multi Unit Entity One {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Multi Unit Entity Two {suffix}"}
        )
        entity1_id = entity1.json()["id"]
        entity2_id = entity2.json()["id"]

        # Get multiple units
        units = await test_client.get("/api/v1/units/")
        unit_list = units.json()

        # Create connections with different units
        for i, unit in enumerate(unit_list[:3]):  # Use first 3 units
            response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity1_id,
                    "to_entity_id": entity2_id,
                    "unit_id": unit["id"],
                    "multiplier": (i + 1) * 1.5
                }
            )
            assert response.status_code == 201

        # Verify all connections exist - use high limit to ensure we get the newly created ones
        all_connections = await test_client.get("/api/v1/connections/?limit=1000")
        connections = [
            c for c in all_connections.json()
            if c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id
        ]
        assert len(connections) >= 3

    @pytest.mark.asyncio
    async def test_connection_edge_case_rounding(self, test_client):
        """Test edge cases in decimal rounding for multipliers."""
        data = await self.create_test_entities_and_unit(test_client)

        # Test banker's rounding edge cases (ROUND_HALF_EVEN)
        test_cases = [
                (1.05, 1.0),   # 0.05 rounds to even (down)
                (1.15, 1.2),   # 0.15 rounds to even (up)
                (1.25, 1.2),   # 0.25 rounds to even (down)
                (1.35, 1.4),   # 0.35 rounds to even (up)
                (1.45, 1.4),   # 0.45 rounds to even (down)
                (2.05, 2.0),   # Different whole number
                (10.05, 10.0), # Larger number
        ]

        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
        test_names = ["Alpha", "Beta", "Gamma", "Delta", "Epsilon", "Zeta", "Eta"]

        for i, (input_val, expected) in enumerate(test_cases):
            # Create new entities for each test to avoid conflicts
            e1 = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"Rounding Test From {test_names[i]} {suffix}"}
            )
            e2 = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"Rounding Test To {test_names[i]} {suffix}"}
            )

            response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": e1.json()["id"],
                    "to_entity_id": e2.json()["id"],
                    "unit_id": data["unit_id"],
                    "multiplier": input_val
                }
            )

            assert response.status_code == 201
            # Check if rounding is as expected (may vary by implementation)
            response_data = response.json()
            actual = float(response_data["multiplier"])
            assert abs(actual - expected) < 0.01, f"Expected {expected}, got {actual} for input {input_val}"
