"""
Phase 3 test: Pathfinding algorithm with recursive CTE validation.
"""
import pytest
from .conftest import generate_valid_entity_suffix


class TestPathfindingAlgorithm:
    """Test pathfinding and comparison functionality."""

    @pytest.mark.asyncio
    async def test_direct_path_comparison(self, test_client):
        """Test direct connection pathfinding."""
        # Create test entities
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DirectPathOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DirectPathTwo {suffix}"}
        )

        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create direct connection A -> B with multiplier 2.5
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.5
            }
        )
        assert connection_response.status_code == 201

        # Test direct comparison A -> B
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity2_id}"
            f"&unit={unit_id}"
        )

        assert compare_response.status_code == 200
        result = compare_response.json()

        print(f"Direct comparison result: {result}")

        assert float(result["multiplier"]) == 2.5
        assert result["path"] is not None
        assert len(result["path"]) == 1  # Single hop
        assert result["error"] is None

        # Test reverse comparison B -> A (should use inverse connection)
        reverse_compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity2_id}&to={entity1_id}"
            f"&unit={unit_id}"
        )

        assert reverse_compare_response.status_code == 200
        reverse_result = reverse_compare_response.json()

        # Should be 1/2.5 = 0.4
        expected_reverse = round(1.0 / 2.5, 1)  # 0.4
        actual_reverse = float(reverse_result["multiplier"])

        print(f"Reverse comparison: expected {expected_reverse}, "
              f"got {actual_reverse}")

        assert abs(actual_reverse - expected_reverse) < 0.01

    @pytest.mark.asyncio
    async def test_two_hop_pathfinding(self, test_client):
        """Test pathfinding through two hops: A -> B -> C."""
        # Create test entities
        suffix = generate_valid_entity_suffix()

        entities = []
        for i in range(3):
            entity_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"TwoHop {chr(65+i)} {suffix}"}
            )
            assert entity_response.status_code == 201
            entities.append(entity_response.json()["id"])

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create connections: A -> B (x2) and B -> C (x3)
        # This should give A -> C (x6)

        # Connection A -> B with multiplier 2.0
        conn1_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entities[0],
                "to_entity_id": entities[1],
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )
        assert conn1_response.status_code == 201

        # Connection B -> C with multiplier 3.0
        conn2_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entities[1],
                "to_entity_id": entities[2],
                "unit_id": unit_id,
                "multiplier": 3.0
            }
        )
        assert conn2_response.status_code == 201

        # Test pathfinding A -> C (should be 2.0 * 3.0 = 6.0)
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entities[0]}&to={entities[2]}"
            f"&unit={unit_id}"
        )

        assert compare_response.status_code == 200
        result = compare_response.json()

        print(f"Two-hop pathfinding result: {result}")

        assert result["multiplier"] is not None
        assert float(result["multiplier"]) == 6.0
        assert result["path"] is not None
        assert len(result["path"]) == 2  # Two hops
        assert result["error"] is None

        # Verify path details
        path = result["path"]
        assert path[0]["from_entity_id"] == entities[0]
        assert path[0]["to_entity_id"] == entities[1]
        assert float(path[0]["multiplier"]) == 2.0

        assert path[1]["from_entity_id"] == entities[1]
        assert path[1]["to_entity_id"] == entities[2]
        assert float(path[1]["multiplier"]) == 3.0

    @pytest.mark.asyncio
    async def test_no_path_exists(self, test_client):
        """Test pathfinding when no path exists between entities."""
        # Create isolated entities with no connections
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"IsolatedOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"IsolatedTwo {suffix}"}
        )

        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Test comparison with no connections
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity2_id}"
            f"&unit={unit_id}"
        )

        assert compare_response.status_code == 404
        result = compare_response.json()

        print(f"No path result: {result}")

        assert "detail" in result
        assert "no path found" in result["detail"].lower()

    @pytest.mark.asyncio
    async def test_same_entity_comparison(self, test_client):
        """Test comparison of entity to itself."""
        # Create test entity
        suffix = generate_valid_entity_suffix()

        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"SameEntity{suffix}"}
        )

        assert entity_response.status_code == 201
        entity_id = entity_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Test self-comparison
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity_id}&to={entity_id}"
            f"&unit={unit_id}"
        )

        assert compare_response.status_code == 200
        result = compare_response.json()

        print(f"Same entity comparison: {result}")

        assert float(result["multiplier"]) == 1.0
        assert result["path"] == []  # Empty path for same entity
        assert result["error"] is None

    @pytest.mark.asyncio
    async def test_different_unit_no_path(self, test_client):
        """Test that connections with different units don't create paths."""
        # Create test entities
        suffix = generate_valid_entity_suffix()

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DiffUnitOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DiffUnitTwo {suffix}"}
        )

        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get two different units
        units_response = await test_client.get("/api/v1/units/")
        units = units_response.json()
        unit1_id = units[0]["id"]
        unit2_id = units[1]["id"]

        # Create connection with unit1
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit1_id,
                "multiplier": 2.0
            }
        )
        assert connection_response.status_code == 201

        # Try to find path using unit2 (should fail)
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity2_id}"
            f"&unit={unit2_id}"
        )

        assert compare_response.status_code == 404
        result = compare_response.json()

        print(f"Different unit comparison: {result}")

        assert "detail" in result
        assert "no path found" in result["detail"].lower()
