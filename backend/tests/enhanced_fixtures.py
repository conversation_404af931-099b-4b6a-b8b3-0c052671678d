"""
Enhanced test fixtures for improved test data management.
Provides transaction-based isolation, comprehensive cleanup, and data
verification.
"""
import pytest_asyncio
import uuid
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text, select, func
from contextlib import asynccontextmanager

from src.models import Entity, Connection, Unit
from src.config import get_settings

logger = logging.getLogger(__name__)

# Test data naming utilities


def create_test_entity_name(prefix: str = "TestEntity") -> str:
    """Create a unique entity name with UUID suffix (letters only)."""
    unique_id = str(uuid.uuid4()).hex
    letters_only = ''.join(c for c in unique_id if c.isalpha())[:8]
    return f"{prefix} {letters_only}"


def create_test_connection_name(prefix: str = "TestConnection") -> str:
    """Create a unique connection identifier."""
    return f"{prefix}_{uuid.uuid4().hex[:8]}"


class TestDataTracker:
    """Track test data creation and cleanup for verification."""

    def __init__(self):
        self.created_entities: List[int] = []
        self.created_connections: List[int] = []
        self.cleanup_verified: bool = False
        self.cleanup_errors: List[str] = []

    def track_entity(self, entity_id: int):
        """Track a created entity."""
        self.created_entities.append(entity_id)

    def track_connection(self, connection_id: int):
        """Track a created connection."""
        self.created_connections.append(connection_id)

    def reset(self):
        """Reset tracking state."""
        self.created_entities.clear()
        self.created_connections.clear()
        self.cleanup_verified = False
        self.cleanup_errors.clear()


# Global test data tracker
_test_data_tracker = TestDataTracker()


@asynccontextmanager
async def get_test_db_session():
    """Get a database session for testing with proper cleanup."""
    settings = get_settings()
    engine = create_async_engine(settings.database_url)

    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()
            await engine.dispose()


@pytest_asyncio.fixture
async def db_transaction():
    """
    Provide a database transaction that can be rolled back.
    This provides better test isolation than database cleanup.
    """
    async with get_test_db_session() as session:
        transaction = await session.begin()
        try:
            yield session
        finally:
            await transaction.rollback()


@pytest_asyncio.fixture
async def test_data_tracker():
    """Provide test data tracker for verification."""
    _test_data_tracker.reset()
    yield _test_data_tracker

    # Verify cleanup after test
    await verify_test_data_cleanup(_test_data_tracker)


async def verify_test_data_cleanup(tracker: TestDataTracker):
    """Verify that test data was properly cleaned up."""
    try:
        async with get_test_db_session() as session:
            # Check if tracked entities still exist
            for entity_id in tracker.created_entities:
                result = await session.execute(
                    select(Entity).where(Entity.id == entity_id)
                )
                if result.scalar_one_or_none() is not None:
                    tracker.cleanup_errors.append(
                        f"Entity {entity_id} not cleaned up"
                    )

            # Check if tracked connections still exist
            for connection_id in tracker.created_connections:
                result = await session.execute(
                    select(Connection).where(Connection.id == connection_id)
                )
                if result.scalar_one_or_none() is not None:
                    tracker.cleanup_errors.append(
                        f"Connection {connection_id} not cleaned up"
                    )

            tracker.cleanup_verified = True

            if tracker.cleanup_errors:
                logger.error(
                    f"Cleanup verification failed: {tracker.cleanup_errors}"
                )
            else:
                logger.info("Test data cleanup verified successfully")

    except Exception as e:
        tracker.cleanup_errors.append(f"Cleanup verification failed: {str(e)}")
        logger.error(f"Cleanup verification error: {e}")


@pytest_asyncio.fixture
async def comprehensive_cleanup():
    """Enhanced cleanup that handles all tables and verifies success."""

    async def cleanup_func():
        """Perform comprehensive database cleanup."""
        settings = get_settings()
        engine = create_async_engine(settings.database_url)

        try:
            async with engine.begin() as conn:
                # Get counts before cleanup
                entities_before = await conn.execute(
                    text("SELECT COUNT(*) FROM entities")
                )
                connections_before = await conn.execute(
                    text("SELECT COUNT(*) FROM connections")
                )

                logger.info(
                    f"Before cleanup: {entities_before.scalar()} entities, "
                    f"{connections_before.scalar()} connections"
                )

                # Clean all tables in dependency order
                await conn.execute(text("DELETE FROM connections"))
                await conn.execute(text("DELETE FROM entities"))

                # Reset sequences
                await conn.execute(
                    text("ALTER SEQUENCE entities_id_seq RESTART WITH 1")
                )
                await conn.execute(
                    text("ALTER SEQUENCE connections_id_seq RESTART WITH 1")
                )

                # Verify cleanup
                entities_after = await conn.execute(
                    text("SELECT COUNT(*) FROM entities")
                )
                connections_after = await conn.execute(
                    text("SELECT COUNT(*) FROM connections")
                )

                if (entities_after.scalar() != 0 or
                        connections_after.scalar() != 0):
                    raise Exception(
                        f"Cleanup failed: {entities_after.scalar()} "
                        f"entities, {connections_after.scalar()} "
                        f"connections remaining"
                    )

                logger.info("Comprehensive cleanup completed successfully")

        except Exception as e:
            logger.error(f"Comprehensive cleanup failed: {e}")
            raise
        finally:
            await engine.dispose()

    return cleanup_func


@pytest_asyncio.fixture
async def test_entities():
    """Provide a set of test entities for standard tests."""
    return {
        'entity_1': create_test_entity_name("EntityOne"),
        'entity_2': create_test_entity_name("EntityTwo"),
        'entity_3': create_test_entity_name("EntityThree"),
    }


@pytest_asyncio.fixture
async def edge_case_entities():
    """Provide entities for edge case testing."""
    return {
        'max_length': 'A' * 100,  # Maximum allowed length
        'single_char': 'A',
        'with_spaces': 'Entity With Multiple Spaces',
        'mixed_case': 'MixedCaseEntity',
    }


@pytest_asyncio.fixture
async def test_units():
    """Provide standard test units."""
    return {
        'length': {'name': 'Length', 'symbol': 'm'},
        'mass': {'name': 'Mass', 'symbol': 'kg'},
        'time': {'name': 'Time', 'symbol': 's'},
        'count': {'name': 'Count', 'symbol': '#'},
        'volume': {'name': 'Volume', 'symbol': 'L'},
    }


@pytest_asyncio.fixture
async def test_connections():
    """Provide test connection configurations."""
    return {
        'basic': {'multiplier': Decimal('2.5')},
        'decimal': {'multiplier': Decimal('1.5')},
        'integer': {'multiplier': Decimal('3.0')},
        'small_decimal': {'multiplier': Decimal('0.1')},
        'large_number': {'multiplier': Decimal('999.9')},
    }


class TestDataFactory:
    """Factory for creating test data with proper tracking."""

    def __init__(self, client, tracker: TestDataTracker):
        self.client = client
        self.tracker = tracker

    async def create_entity(
            self, name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a test entity and track it."""
        entity_name = name or create_test_entity_name()

        response = await self.client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )

        if response.status_code == 201:
            entity_data = response.json()
            self.tracker.track_entity(entity_data["id"])
            return entity_data
        else:
            raise Exception(
                f"Failed to create entity: {response.status_code} - "
                f"{response.text}"
            )

    async def create_connection(
            self, from_entity_id: int, to_entity_id: int,
            unit_id: int, multiplier: Decimal
    ) -> Dict[str, Any]:
        """Create a test connection and track it."""
        response = await self.client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": from_entity_id,
                "to_entity_id": to_entity_id,
                "unit_id": unit_id,
                "multiplier": float(multiplier)
            }
        )

        if response.status_code == 201:
            connection_data = response.json()
            self.tracker.track_connection(connection_data["id"])
            return connection_data
        else:
            raise Exception(
                f"Failed to create connection: {response.status_code} - "
                f"{response.text}"
            )

    async def create_test_setup(self) -> Dict[str, Any]:
        """Create a standard test setup with entities and units."""
        # Create entities
        entity1 = await self.create_entity("TestEntityA")
        entity2 = await self.create_entity("TestEntityB")

        # Get units
        units_response = await self.client.get("/api/v1/units/")
        units = units_response.json()

        return {
            'entity1': entity1,
            'entity2': entity2,
            'units': units,
            'length_unit': next(u for u in units if u['name'] == 'Length'),
        }


@pytest_asyncio.fixture
async def test_factory(test_client, test_data_tracker):
    """Provide a test data factory."""
    return TestDataFactory(test_client, test_data_tracker)


# Enhanced cleanup validation
async def validate_database_state():
    """Validate that the database is in a clean state."""
    async with get_test_db_session() as session:
        # Check entities
        entities_count = await session.execute(select(func.count(Entity.id)))
        entities_total = entities_count.scalar()

        # Check connections
        connections_count = await session.execute(
            select(func.count(Connection.id))
        )
        connections_total = connections_count.scalar()

        # Check units (should always exist)
        units_count = await session.execute(select(func.count(Unit.id)))
        units_total = units_count.scalar()

        logger.info(
            f"Database state: {entities_total} entities, "
            f"{connections_total} connections, {units_total} units"
        )

        return {
            'entities': entities_total,
            'connections': connections_total,
            'units': units_total,
        }


@pytest_asyncio.fixture(autouse=True)
async def database_state_validation():
    """Validate database state before and after tests."""
    # Validate before test
    initial_state = await validate_database_state()

    yield

    # Validate after test (optional, since cleanup handles this)
    final_state = await validate_database_state()

    # Log any unexpected changes
    if initial_state['entities'] != final_state['entities']:
        logger.warning(
            f"Entity count changed: {initial_state['entities']} -> "
            f"{final_state['entities']}"
        )

    if initial_state['connections'] != final_state['connections']:
        logger.warning(
            f"Connection count changed: {initial_state['connections']} -> "
            f"{final_state['connections']}"
        )


# Utility functions for test data verification
async def assert_entity_exists(session: AsyncSession, entity_id: int):
    """Assert that an entity exists in the database."""
    result = await session.execute(
        select(Entity).where(Entity.id == entity_id)
    )
    entity = result.scalar_one_or_none()
    assert entity is not None, f"Entity {entity_id} does not exist"
    return entity


async def assert_connection_exists(session: AsyncSession, connection_id: int):
    """Assert that a connection exists in the database."""
    result = await session.execute(
        select(Connection).where(Connection.id == connection_id)
    )
    connection = result.scalar_one_or_none()
    assert connection is not None, f"Connection {connection_id} does not exist"
    return connection


async def assert_entity_not_exists(session: AsyncSession, entity_id: int):
    """Assert that an entity does not exist in the database."""
    result = await session.execute(
        select(Entity).where(Entity.id == entity_id)
    )
    entity = result.scalar_one_or_none()
    assert entity is None, f"Entity {entity_id} should not exist"


async def assert_connection_not_exists(
        session: AsyncSession, connection_id: int
):
    """Assert that a connection does not exist in the database."""
    result = await session.execute(
        select(Connection).where(Connection.id == connection_id)
    )
    connection = result.scalar_one_or_none()
    assert connection is None, f"Connection {connection_id} should not exist"
