#!/usr/bin/env python3
"""
Script to fix specific broken lines caused by the previous script.
"""

import re

def fix_broken_lines(file_path):
    """Fix specific broken lines."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix broken create_response lines
    content = re.sub(
        r'create_response = await test_client\.post\(\n\s+\n\s+entity_data\)',
        r'create_response = await test_client.post(\n            "/api/v1/entities/", json=entity_data)',
        content
    )
    
    # Fix broken response.get lines
    content = re.sub(
        r'response = await test_client\.get\(\n\s+\n\s+f"',
        r'response = await test_client.get(\n            f"',
        content
    )
    
    # Fix broken create_response lines with different patterns
    content = re.sub(
        r'create_response = await test_client\.post\(\n\s+\n\s+entity_data\)',
        r'create_response = await test_client.post(\n            "/api/v1/entities/", json=entity_data)',
        content
    )
    
    # Fix broken conn_response lines
    content = re.sub(
        r'conn_response = await test_client\.post\(\n\s+\n\s+connection_data\)',
        r'conn_response = await test_client.post(\n            "/api/v1/connections/", json=connection_data)',
        content
    )
    
    # Fix broken conn_ab_response lines
    content = re.sub(
        r'conn_ab_response = await test_client\.post\(\n\s+\n\s+connection_ab_data\)',
        r'conn_ab_response = await test_client.post(\n            "/api/v1/connections/", json=connection_ab_data)',
        content
    )
    
    # Fix broken conn_bc_response lines
    content = re.sub(
        r'conn_bc_response = await test_client\.post\(\n\s+\n\s+connection_bc_data\)',
        r'conn_bc_response = await test_client.post(\n            "/api/v1/connections/", json=connection_bc_data)',
        content
    )
    
    # Fix broken conn_response lines
    content = re.sub(
        r'conn_response = await test_client\.post\(\n\s+\n\s+conn_data\)',
        r'conn_response = await test_client.post(\n            "/api/v1/connections/", json=conn_data)',
        content
    )
    
    # Fix specific issues in the compare files
    content = re.sub(
        r'(\s+)create_response = await test_client\.post\(\n\s+\n\s+entity_data\)',
        r'\1create_response = await test_client.post(\n\1    "/api/v1/entities/", json=entity_data)',
        content
    )
    
    # Fix specific missing JSON parameter
    content = re.sub(
        r'await test_client\.post\(\n\s+entity_data\)',
        r'await test_client.post(\n            "/api/v1/entities/", json=entity_data)',
        content
    )
    
    # Fix specific missing JSON parameter for connections
    content = re.sub(
        r'await test_client\.post\(\n\s+connection_data\)',
        r'await test_client.post(\n            "/api/v1/connections/", json=connection_data)',
        content
    )
    
    # Fix specific missing JSON parameter for connections
    content = re.sub(
        r'await test_client\.post\(\n\s+conn_data\)',
        r'await test_client.post(\n            "/api/v1/connections/", json=conn_data)',
        content
    )
    
    # Fix specific missing JSON parameter for connections
    content = re.sub(
        r'await test_client\.post\(\n\s+connection_ab_data\)',
        r'await test_client.post(\n            "/api/v1/connections/", json=connection_ab_data)',
        content
    )
    
    # Fix specific missing JSON parameter for connections
    content = re.sub(
        r'await test_client\.post\(\n\s+connection_bc_data\)',
        r'await test_client.post(\n            "/api/v1/connections/", json=connection_bc_data)',
        content
    )
    
    # Fix entity names with proper line continuation
    content = re.sub(
        r'entity_name = create_test_entity_name\(\n\s+f"([^"]+)"\)',
        r'entity_name = create_test_entity_name(\n            f"\1")',
        content
    )
    
    # Fix broken response.get lines with proper indentation
    content = re.sub(
        r'response = await test_client\.get\(\n\s+\n\s+(f"/api/v1/[^"]+"\n\s+f"[^"]+"\))',
        r'response = await test_client.get(\n            \1',
        content
    )
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed broken lines in {file_path}")

if __name__ == "__main__":
    files_to_fix = [
        "tests/test_compare_comprehensive.py",
        "tests/test_compare_error_scenarios.py", 
        "tests/test_comprehensive_connections.py",
        "tests/test_comprehensive_entities.py",
        "tests/test_comprehensive_pathfinding.py"
    ]
    
    for file_path in files_to_fix:
        try:
            fix_broken_lines(file_path)
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")