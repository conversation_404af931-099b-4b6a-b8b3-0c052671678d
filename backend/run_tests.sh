#!/bin/bash
set -e  # Exit on any error

# Check if virtual environment is activated
if [[ -z "$VIRTUAL_ENV" ]]; then
    echo "❌ Virtual environment not activated!"
    echo "   Please run: source venv/bin/activate"
    exit 1
fi

echo "✅ Using virtual environment: $VIRTUAL_ENV"
echo

# Check services are running
echo "Checking required services..."
python scripts/check_test_services.py || exit 1
echo

# Set environment variables
export DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test"
export TEST_DATABASE_HOST="localhost"
export PYTHONPATH="."

# Setup test database
echo "Setting up test database..."
python scripts/test_db_setup.py || exit 1
echo

# Run tests with process isolation
echo "Running tests..."
pytest -v

# Capture test exit code
TEST_EXIT_CODE=$?

# Teardown test database
echo
echo "Cleaning up test database..."
python scripts/test_db_teardown.py

# Exit with test result
exit $TEST_EXIT_CODE
