#!/bin/bash
set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to show usage
show_usage() {
    echo "Usage: $0 --area=<functional-area>"
    echo ""
    echo "Available functional areas:"
    echo "  entities      - Entity management tests"
    echo "  connections   - Connection management tests"
    echo "  comparisons   - Entity comparison and pathfinding tests"
    echo "  navigation    - Navigation and routing tests"
    echo "  error-handling - Error handling and edge cases"
    echo "  integration   - Backend integration tests"
    echo "  all           - All functional area tests"
    echo ""
    echo "Examples:"
    echo "  $0 --area=entities"
    echo "  $0 --area=connections"
    echo "  $0 --area=all"
    exit 1
}

# Parse command line arguments
AREA=""
for arg in "$@"; do
    case $arg in
        --area=*)
            AREA="${arg#*=}"
            shift
            ;;
        -h|--help)
            show_usage
            ;;
        *)
            echo "Unknown argument: $arg"
            show_usage
            ;;
    esac
done

# Check if area is provided
if [ -z "$AREA" ]; then
    echo "Error: --area parameter is required"
    show_usage
fi

# Function to log and display messages
log_message() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Set log file based on area
LOG_FILE="functional-e2e-${AREA}.log"

# Initialize log file
echo "SIMILE E2E Functional Tests - $AREA - $(date)" > "$LOG_FILE"
echo "================================================" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

log_message "${BLUE}🧪 SIMILE E2E Functional Tests - $(echo $AREA | tr '[:lower:]' '[:upper:]')${NC}"
log_message "=============================================="
log_message ""

# Change to frontend directory
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    log_message "${RED}❌ Frontend dependencies not installed!${NC}"
    log_message "${YELLOW}   Please run: npm install${NC}"
    exit 1
fi

# Check if Playwright is installed
if ! npx playwright --version >/dev/null 2>&1; then
    log_message "${RED}❌ Playwright not installed!${NC}"
    log_message "${YELLOW}   Please run: npm run test:e2e:install${NC}"
    exit 1
fi

# Check if backend services are running
log_message "${BLUE}🔍 Checking backend services...${NC}"
if curl -s -f http://localhost:8000/api/v1/health >/dev/null 2>&1; then
    log_message "${GREEN}✅ Backend services are running${NC}"
else
    log_message "${RED}❌ Backend services not accessible${NC}"
    log_message "${YELLOW}   Please run: podman-compose up -d${NC}"
    log_message "${YELLOW}   Or: docker-compose -f docker-compose.dev.yml up -d${NC}"
    exit 1
fi

log_message ""

# Define test files for each functional area using case statement
case $AREA in
    entities)
        TESTS_TO_RUN="e2e/tests/functional-areas/entities/entities.spec.ts e2e/tests/functional-areas/entities/sequential-entity-creation.spec.ts"
        ;;
    connections)
        TESTS_TO_RUN="e2e/tests/functional-areas/connections/connections.spec.ts"
        ;;
    comparisons)
        TESTS_TO_RUN="e2e/tests/functional-areas/comparisons/comparisons.spec.ts"
        ;;
    navigation)
        TESTS_TO_RUN="e2e/tests/functional-areas/navigation/navigation.spec.ts e2e/tests/functional-areas/navigation/setup-verification.spec.ts"
        ;;
    error-handling)
        TESTS_TO_RUN="e2e/tests/functional-areas/error-handling/error-handling.spec.ts e2e/tests/functional-areas/integration/debug-validation.spec.ts"
        ;;
    integration)
        TESTS_TO_RUN="e2e/tests/functional-areas/integration/backend-connectivity-validation.spec.ts e2e/tests/functional-areas/integration/cleanup-system-validation.spec.ts"
        ;;
    all)
        TESTS_TO_RUN="e2e/tests/functional-areas/entities/entities.spec.ts e2e/tests/functional-areas/entities/sequential-entity-creation.spec.ts e2e/tests/functional-areas/connections/connections.spec.ts e2e/tests/functional-areas/comparisons/comparisons.spec.ts e2e/tests/functional-areas/navigation/navigation.spec.ts e2e/tests/functional-areas/navigation/setup-verification.spec.ts e2e/tests/functional-areas/error-handling/error-handling.spec.ts e2e/tests/functional-areas/integration/debug-validation.spec.ts e2e/tests/functional-areas/integration/backend-connectivity-validation.spec.ts e2e/tests/functional-areas/integration/cleanup-system-validation.spec.ts"
        ;;
    *)
        log_message "${RED}❌ Unknown functional area: $AREA${NC}"
        show_usage
        ;;
esac

# Convert tests to array for display
read -ra TEST_ARRAY <<< "$TESTS_TO_RUN"

# Run functional tests
log_message "${YELLOW}🧪 Running $AREA functional tests...${NC}"
log_message "Tests to run:"
for test in "${TEST_ARRAY[@]}"; do
    log_message "  - $test"
done
log_message ""

# Execute functional tests with area-specific settings
TEST_EXIT_CODE=0

# Set workers based on area complexity
case $AREA in
    entities|connections|comparisons)
        WORKERS=2  # More complex tests, use fewer workers for stability
        ;;
    navigation|error-handling|integration|all)
        WORKERS=3  # Simpler or mixed tests, can use more workers
        ;;
    *)
        WORKERS=2  # Default to conservative setting
        ;;
esac

# Run tests and capture exit code properly (tee loses exit codes)
npx playwright test \
    $TESTS_TO_RUN \
    --reporter=list \
    --workers=$WORKERS \
    --timeout=90000 \
    --retries=1 \
    2>&1 | tee -a "../$LOG_FILE"

# Capture the exit code from the pipeline (playwright, not tee)
TEST_EXIT_CODE=${PIPESTATUS[0]}

if [ $TEST_EXIT_CODE -eq 0 ]; then
    log_message "${GREEN}✅ All $AREA tests passed!${NC}"
else
    log_message "${RED}❌ Some $AREA tests failed!${NC}"
fi

# Return to project root
cd ..

log_message ""
log_message "$AREA functional test run completed at $(date)"
log_message "Test results saved to: $LOG_FILE"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    log_message "${GREEN}🎉 $AREA functional tests completed successfully!${NC}"
else
    log_message "${RED}💥 $AREA functional tests failed!${NC}"
    log_message "${YELLOW}Check $LOG_FILE for details${NC}"
fi

exit $TEST_EXIT_CODE
