{"config": {"configFile": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/playwright.config.ts", "rootDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 5400000, "grep": {}, "grepInvert": null, "maxFailures": 10, "metadata": {"optimizationVersion": "C.1", "workerCapacity": "3-worker-parallel", "isolationStrategy": "worker-database-isolation"}, "preserveOutput": "always", "reporter": [["/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/progress-reporter.ts", null], ["list", {"printSteps": false}], ["html", {"outputFolder": "playwright-report", "open": "never"}], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results", "repeatEach": 1, "retries": 1, "metadata": {"optimizationVersion": "C.1", "workerCapacity": "3-worker-parallel", "isolationStrategy": "worker-database-isolation"}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 90000}, {"outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results", "repeatEach": 1, "retries": 1, "metadata": {"optimizationVersion": "C.1", "workerCapacity": "3-worker-parallel", "isolationStrategy": "worker-database-isolation"}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 90000}, {"outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results", "repeatEach": 1, "retries": 1, "metadata": {"optimizationVersion": "C.1", "workerCapacity": "3-worker-parallel", "isolationStrategy": "worker-database-isolation"}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 90000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 3, "webServer": {"command": "npm start", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000, "ignoreHTTPSErrors": true, "stdout": "pipe", "stderr": "pipe", "cwd": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend", "env": {"TERM_PROGRAM": "WarpTerminal", "NODE": "/opt/homebrew/Cellar/node/24.3.0/bin/node", "INIT_CWD": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend", "WARP_HONOR_PS1": "0", "TERM": "xterm-256color", "SHELL": "/bin/zsh", "HOMEBREW_REPOSITORY": "/opt/homebrew", "TMPDIR": "/var/folders/t3/f0n2pz1s5wx_3zdnd2g2z8kh0000gn/T/", "npm_config_global_prefix": "/opt/homebrew", "TERM_PROGRAM_VERSION": "v0.2025.***********.stable_02", "COLOR": "1", "npm_config_noproxy": "", "npm_config_local_prefix": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend", "USER": "seth", "COMMAND_MODE": "unix2003", "npm_config_globalconfig": "/opt/homebrew/etc/npmrc", "SSH_AUTH_SOCK": "/private/tmp/com.apple.launchd.AIwg6ZEIHJ/Listeners", "__CF_USER_TEXT_ENCODING": "0x1F5:0x0:0x0", "WARP_IS_LOCAL_SHELL_SESSION": "1", "npm_execpath": "/opt/homebrew/lib/node_modules/npm/bin/npm-cli.js", "WARP_USE_SSH_WRAPPER": "1", "PATH": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/.bin:/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/.bin:/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/node_modules/.bin:/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/node_modules/.bin:/Users/<USER>/Documents/PROJECTS/HDS/node_modules/.bin:/Users/<USER>/Documents/PROJECTS/node_modules/.bin:/Users/<USER>/Documents/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/opt/homebrew/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/miniconda3/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin", "npm_package_json": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/package.json", "_": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/.bin/playwright", "LaunchInstanceID": "A0E20CAB-C2D2-45EF-AF80-460BA3AA06BD", "npm_config_userconfig": "/Users/<USER>/.npmrc", "npm_config_init_module": "/Users/<USER>/.npm-init.js", "__CFBundleIdentifier": "dev.warp.Warp-Stable", "npm_command": "exec", "PWD": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend", "npm_lifecycle_event": "npx", "EDITOR": "vi", "npm_package_name": "simile-frontend", "LANG": "en_US.UTF-8", "npm_config_npm_version": "11.4.2", "XPC_FLAGS": "0x0", "npm_config_node_gyp": "/opt/homebrew/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js", "npm_package_version": "0.1.0", "XPC_SERVICE_NAME": "0", "SHLVL": "2", "HOME": "/Users/<USER>", "HOMEBREW_PREFIX": "/opt/homebrew", "npm_config_cache": "/Users/<USER>/.npm", "LOGNAME": "seth", "npm_lifecycle_script": "\"playwright\"", "SSH_SOCKET_DIR": "~/.ssh", "npm_config_user_agent": "npm/11.4.2 node/v24.3.0 darwin arm64 workspaces/false", "INFOPATH": "/opt/homebrew/share/info:", "HOMEBREW_CELLAR": "/opt/homebrew/Cellar", "OSLogRateLimit": "64", "CONDA_CHANGEPS1": "false", "SECURITYSESSIONID": "186ad", "npm_node_execpath": "/opt/homebrew/Cellar/node/24.3.0/bin/node", "npm_config_prefix": "/opt/homebrew", "COLORTERM": "truecolor", "BROWSER": "none", "CI": "true"}}}, "suites": [], "errors": [{"message": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments.", "stack": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments."}], "stats": {"startTime": "2025-07-11T17:07:32.044Z", "duration": 13.479999999999961, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}