#!/bin/bash

# Test script to verify validation fixes
echo "🧪 Testing Form Validation Fixes"
echo "================================"

# Set working directory
cd "$(dirname "$0")"

# Run specific validation tests that were failing due to strict mode violations
echo "🔍 Running validation tests with specific error selectors..."

# Test 1: Decimal precision validation
echo "📋 Test 1: Decimal precision validation..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should validate decimal precision with real-time validation" \
  --reporter=line \
  --headed

echo ""

# Test 2: Zero multiplier validation
echo "📋 Test 2: Zero multiplier validation..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should prevent zero multiplier values" \
  --reporter=line \
  --headed

echo ""

# Test 3: Same unit validation
echo "📋 Test 3: Same unit validation..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should validate same-unit connections only" \
  --reporter=line \
  --headed

echo ""
echo "✅ Validation fix testing completed!"
echo "Check the output above to see if strict mode violations are resolved."
