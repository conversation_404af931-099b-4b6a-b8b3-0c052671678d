# Playwright E2E Tests for SIMILE

This directory contains end-to-end tests for the SIMILE frontend application using Playwright.

## Setup

1. Install dependencies:
   ```bash
   npm install
   npm run test:e2e:install
   ```

2. Ensure backend services are running:
   ```bash
   # From the root directory
   podman-compose up -d
   ```

3. Start the frontend development server (if not using webServer config):
   ```bash
   npm start
   ```

## Running Tests

### Basic Commands
```bash
# Run all tests
npm run test:e2e

# Run tests with browser UI (headed mode)
npm run test:e2e:headed

# Run tests in interactive UI mode
npm run test:e2e:ui

# Run tests in debug mode
npm run test:e2e:debug

# View test report
npm run test:e2e:report
```

### Advanced Commands
```bash
# Run specific test file
npx playwright test entities.spec.ts

# Run tests in specific browser
npx playwright test --project=chromium

# Run tests with specific tag
npx playwright test --grep="entity management"

# Run failed tests only
npx playwright test --last-failed
```

## Test Structure

### Test Files
- `navigation.spec.ts` - Navigation and routing tests
- `entities.spec.ts` - Entity management tests
- `connections.spec.ts` - Connection management tests
- `comparisons.spec.ts` - Entity comparison and pathfinding tests
- `error-handling.spec.ts` - Error handling and edge cases

### Support Files
- `fixtures/page-objects.ts` - Page object models for reusable components
- `fixtures/test-data.ts` - Test data and constants
- `utils/helpers.ts` - Utility functions and test helpers

## Page Object Models

The tests use the Page Object Model pattern for maintainability:

- `BasePage` - Base functionality for all pages
- `EntityManagerPage` - Entity management operations
- `ConnectionManagerPage` - Connection management operations
- `ComparisonManagerPage` - Entity comparison operations
- `NavigationComponent` - Navigation interactions

## Test Data Management

Tests use:
- Unique entity names with timestamps to avoid conflicts
- Cleanup procedures to remove test data after each test
- Isolated test environments where possible

## Debugging

### Screenshots
Screenshots are automatically captured on test failures and saved to `test-results/`.

### Debug Mode
```bash
npm run test:e2e:debug
```
This opens the Playwright inspector for step-by-step debugging.

### Browser DevTools
```bash
npx playwright test --headed --slowMo=1000
```
This runs tests in slow motion with visible browser.

### Trace Viewer
```bash
npx playwright show-trace test-results/trace.zip
```
View detailed traces of test execution.

## CI/CD Integration

Tests are configured to run in CI environments with:
- Retry mechanism for flaky tests
- Screenshot and video capture on failures
- Parallel execution where safe
- Service startup/shutdown management

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure frontend runs on 3000, backend on 8000
2. **Service startup**: Backend services must be running before tests
3. **Timeouts**: Increase timeouts for slow environments
4. **Data conflicts**: Tests clean up their own data

### Debug Commands
```bash
# Check if services are running
curl http://localhost:8000/api/v1/units
curl http://localhost:3000

# View browser console
npx playwright test --headed entities.spec.ts
```

## Contributing

When adding new tests:
1. Follow the existing Page Object Model pattern
2. Use descriptive test names
3. Add proper cleanup in `afterEach` hooks
4. Include both positive and negative test cases
5. Update this README if adding new test categories