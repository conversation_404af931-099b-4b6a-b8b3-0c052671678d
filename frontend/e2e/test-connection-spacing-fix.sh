#!/bin/bash

# Test script to verify connection spacing fixes
echo "🧪 Testing Connection Spacing Fixes"
echo "==================================="

# Set working directory
cd "$(dirname "$0")"

echo "Testing connection display with proper spacing..."
echo "Expected format: 'Human AYWRJ is 2.5x Basketball AWKNJ in Length'"
echo "Fixed spacing: [entity] + ' is ' + [multiplier] + 'x ' + [entity] + ' in ' + [unit]"
echo ""

# Test 1: Basic connection display
echo "📋 Test 1: Basic connection display with proper spacing..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should display connection management page correctly" \
  --reporter=line \
  --headed

echo ""

# Test 2: Connection creation and verification
echo "📋 Test 2: Connection creation and verification..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should create bidirectional connections" \
  --reporter=line \
  --headed

echo ""

# Test 3: Connection list after page refresh
echo "📋 Test 3: Connection list after page refresh..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should maintain connection list after page refresh" \
  --reporter=line \
  --headed

echo ""
echo "✅ Connection spacing fix testing completed!"
echo ""
echo "The React component now renders connections with proper spacing:"
echo "• Before: 'Human AYWRJis2.5xBasketball AWKNJin Length'"
echo "• After:  'Human AYWRJ is 2.5x Basketball AWKNJ in Length'"
echo ""
echo "If tests still fail, the issue may be with connection creation rather than display format."
