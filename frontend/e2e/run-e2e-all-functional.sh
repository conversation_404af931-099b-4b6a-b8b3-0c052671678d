#!/bin/bash

# SIMILE E2E All Functional Tests
# Run all functional area tests in sequence with proper error reporting
# This script runs ALL functional tests and reports accurate results

echo "🧪 SIMILE E2E Functional Tests - ALL"
echo "=============================================="
echo ""

# Track overall results
OVERALL_RESULT=0
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run tests for a specific area and track results
run_functional_area() {
  local area=$1
  local area_name=$2
  
  echo "📋 Running $area_name tests..."
  echo "================================"
  
  ./run-e2e-functional.sh --area=$area
  local result=$?
  
  if [ $result -eq 0 ]; then
    echo "✅ $area_name: PASSED"
    PASSED_TESTS=$((PASSED_TESTS + 1))
  else
    echo "❌ $area_name: FAILED (exit code: $result)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    OVERALL_RESULT=1
  fi
  
  TOTAL_TESTS=$((TOTAL_TESTS + 1))
  echo ""
}

# Run all functional areas
echo "Starting comprehensive functional test suite..."
echo ""

run_functional_area "navigation" "Navigation"
run_functional_area "entities" "Entity Management"
run_functional_area "connections" "Connection Management"
run_functional_area "comparisons" "Entity Comparisons"
run_functional_area "error-handling" "Error Handling"
run_functional_area "integration" "Integration"

# Final summary
echo "🏁 FUNCTIONAL TEST SUITE SUMMARY"
echo "================================="
echo "Total functional areas tested: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"
echo ""

if [ $OVERALL_RESULT -eq 0 ]; then
  echo "🎉 ALL FUNCTIONAL TESTS PASSED!"
  echo "All functional areas are working correctly."
else
  echo "❌ FUNCTIONAL TESTS FAILED"
  echo "One or more functional areas have issues that need attention."
  echo "Review the individual test results above for details."
fi

echo ""
echo "all functional test run completed at $(date)"
echo "Test results saved to: functional-e2e-all.log"

# Only show success message if tests actually passed
if [ $OVERALL_RESULT -eq 0 ]; then
  echo "🎉 all functional tests completed successfully!"
else
  echo "⚠️  functional tests completed with failures!"
fi

exit $OVERALL_RESULT
