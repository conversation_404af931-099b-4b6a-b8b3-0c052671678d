#!/bin/bash

# SIMILE E2E Performance Tests
# Run performance and stress tests
# These tests are run with single worker to avoid interference

echo "🚀 Running SIMILE E2E Performance Tests"
echo "======================================="
echo "Target: Performance benchmarks and stress tests"
echo "Expected duration: 5-10 minutes"
echo "Workers: 1 (to avoid interference)"
echo ""

# Set environment variables for performance testing
export TEST_TYPE="performance"
export TEST_TIMEOUT_MULTIPLIER="2.0"

# Run performance tests with single worker
npx playwright test \
  --grep="@performance" \
  performance/ \
  --reporter=line \
  --workers=1 \
  --timeout=120000 \
  --retries=0 \
  --max-failures=5

# Capture the exit code from the test run
TEST_RESULT=$?

echo ""
if [ $TEST_RESULT -eq 0 ]; then
  echo "✅ Performance tests PASSED"
  echo "All performance benchmarks met expectations."
else
  echo "❌ Performance tests FAILED"
  echo "Performance issues detected. Review the output above for details."
  echo "Exit code: $TEST_RESULT"
fi

echo "Check the output above for performance metrics and benchmarks."

# Exit with the actual test result
exit $TEST_RESULT
