#!/bin/bash

# Test script to verify connection format fixes
echo "🧪 Testing Connection Format Fixes"
echo "=================================="

# Set working directory
cd "$(dirname "$0")"

echo "Testing connection verification with correct UI format..."
echo "Expected format: 'EntityA is 2.5x EntityB in Length' (with space before second entity)"
echo ""

# Test 1: Basic connection creation and verification
echo "📋 Test 1: Basic connection creation and verification..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should display connection management page correctly" \
  --reporter=line \
  --headed

echo ""

# Test 2: Connection verification after page refresh
echo "📋 Test 2: Connection verification after page refresh..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should maintain connection list after page refresh" \
  --reporter=line \
  --headed

echo ""

# Test 3: Bidirectional connection verification
echo "📋 Test 3: Bidirectional connection verification..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should create bidirectional connections" \
  --reporter=line \
  --headed

echo ""
echo "✅ Connection format fix testing completed!"
echo ""
echo "If tests still fail, check the console output for debugging information about:"
echo "• Connection list element visibility"
echo "• Actual vs expected connection text format"
echo "• API vs UI synchronization issues"
