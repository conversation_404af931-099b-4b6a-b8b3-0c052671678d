#!/bin/bash

# Test script to verify parallel entity setup fix
echo "🧪 Testing Parallel Entity Setup Fix"
echo "===================================="

# Set working directory
cd "$(dirname "$0")"

# Run a simple connection test that uses parallel entity setup
echo "🔍 Running connection test with parallel entity setup..."

# Test the basic connection management which should use parallel setup
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should display connection management page correctly" \
  --reporter=line \
  --headed

echo ""
echo "✅ Parallel entity setup test completed!"
echo "Check the output above to see if parallel entity setup now works without UI verification failures."
