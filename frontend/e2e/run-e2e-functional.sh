#!/bin/bash

# SIMILE E2E Functional Area Tests
# Run tests for specific functional areas
# Usage: ./run-e2e-functional.sh --area=entities
# Options: entities, connections, comparisons, navigation, error-handling, integration

echo "🚀 Running SIMILE E2E Functional Area Tests"
echo "============================================"

# Parse command line arguments
AREA=""
for arg in "$@"; do
  case $arg in
    --area=*)
      AREA="${arg#*=}"
      shift
      ;;
    *)
      echo "Unknown argument: $arg"
      echo "Usage: $0 --area=[entities|connections|comparisons|navigation|error-handling|integration]"
      exit 1
      ;;
  esac
done

# Validate area parameter
if [ -z "$AREA" ]; then
  echo "Error: --area parameter is required"
  echo "Usage: $0 --area=[entities|connections|comparisons|navigation|error-handling|integration]"
  exit 1
fi

# Set environment variables
export TEST_TYPE="functional"
export TEST_AREA="$AREA"

echo "Target area: $AREA"
echo "Expected duration: 2-5 minutes per area"
echo ""

# Check if backend is running
echo "🔍 Checking backend connectivity..."
if ! curl -s http://localhost:8000/api/v1/health > /dev/null 2>&1; then
  echo "⚠️  Backend not detected at localhost:8000"
  echo "   Make sure the backend server is running before running E2E tests"
  echo ""
fi

case $AREA in
  entities)
    echo "Running Entity Management tests..."
    npx playwright test \
      --grep="@entities" \
      functional-areas/entities/ \
      --reporter=line \
      --workers=2 \
      --timeout=45000 \
      --retries=1
    ;;
  connections)
    echo "Running Connection Management tests..."
    npx playwright test \
      --grep="@connections" \
      functional-areas/connections/ \
      --reporter=line \
      --workers=2 \
      --timeout=45000 \
      --retries=1
    ;;
  comparisons)
    echo "Running Comparison tests..."
    npx playwright test \
      --grep="@comparisons" \
      functional-areas/comparisons/ \
      --reporter=line \
      --workers=2 \
      --timeout=45000 \
      --retries=1
    ;;
  navigation)
    echo "Running Navigation tests..."
    npx playwright test \
      --grep="@navigation" \
      functional-areas/navigation/ \
      --reporter=line \
      --workers=2 \
      --timeout=30000 \
      --retries=1
    ;;
  error-handling)
    echo "Running Error Handling tests..."
    npx playwright test \
      --grep="@error-handling" \
      functional-areas/error-handling/ \
      --reporter=line \
      --workers=2 \
      --timeout=30000 \
      --retries=1
    ;;
  integration)
    echo "Running Integration tests..."
    npx playwright test \
      --grep="@integration" \
      functional-areas/integration/ \
      --reporter=line \
      --workers=1 \
      --timeout=60000 \
      --retries=2
    ;;
  *)
    echo "Error: Invalid area '$AREA'"
    echo "Valid areas: entities, connections, comparisons, navigation, error-handling, integration"
    exit 1
    ;;
esac

# Capture the exit code from the test run
TEST_RESULT=$?

echo ""
if [ $TEST_RESULT -eq 0 ]; then
  echo "✅ Functional area tests PASSED for: $AREA"
else
  echo "❌ Functional area tests FAILED for: $AREA"
  echo "   Exit code: $TEST_RESULT"
fi

# Exit with the actual test result
exit $TEST_RESULT
