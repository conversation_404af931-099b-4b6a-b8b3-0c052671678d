#!/bin/bash

# Test script to verify timing fixes for connection tests
# Run a subset of connection tests to validate improvements

echo "🧪 Testing Timing Fixes for Connection Tests"
echo "============================================="

# Set working directory
cd "$(dirname "$0")"

# Run specific connection tests that were failing
echo "🔍 Running subset of connection tests to verify timing fixes..."

# Test 1: Basic connection management (should pass)
echo "📋 Test 1: Basic connection management..."
npx playwright test tests/functional-areas/connections/connections.spec.ts -g "should display connection management page correctly" --reporter=line

# Test 2: Decimal precision validation (was failing)
echo "📋 Test 2: Decimal precision validation..."
npx playwright test tests/functional-areas/connections/connections.spec.ts -g "should validate decimal precision with real-time validation" --reporter=line

# Test 3: Zero multiplier validation (was failing)
echo "📋 Test 3: Zero multiplier validation..."
npx playwright test tests/functional-areas/connections/connections.spec.ts -g "should prevent zero multiplier values" --reporter=line

# Test 4: Autocomplete functionality (was failing)
echo "📋 Test 4: Autocomplete functionality..."
npx playwright test tests/functional-areas/connections/connections.spec.ts -g "should handle autocomplete in entity selection" --reporter=line

echo ""
echo "✅ Timing fix validation complete!"
echo "Check the output above to see if the fixes resolved the timing issues."
