#!/bin/bash

# Test script to specifically verify autocomplete timing fixes
echo "🧪 Testing Autocomplete Timing Fixes"
echo "===================================="

# Set working directory
cd "$(dirname "$0")"

# Run the specific autocomplete test that was failing
echo "🔍 Running autocomplete test with enhanced logging..."

# Set environment variables for better debugging
export DEBUG=pw:api
export PLAYWRIGHT_SLOW_MO=500

# Run the specific test with increased timeout and detailed reporting
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should handle autocomplete in entity selection" \
  --timeout=60000 \
  --reporter=line \
  --headed

echo ""
echo "✅ Autocomplete test completed!"
echo "If the test still fails, check the trace and video files for detailed debugging."
