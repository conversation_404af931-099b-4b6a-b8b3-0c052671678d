#!/bin/bash

# SIMILE E2E Full Test Suite
# Complete test suite with proper sequencing
# This runs all test categories in the optimal order

echo "🚀 Running SIMILE E2E Full Test Suite"
echo "====================================="
echo "This will run all E2E tests in the following order:"
echo "1. Smoke tests (critical path)"
echo "2. Functional tests (by area)"
echo "3. Integration tests"
echo "4. Performance tests"
echo ""
echo "Expected total duration: 15-25 minutes"
echo ""

# Track overall results
SMOKE_RESULT=0
FUNCTIONAL_RESULT=0
INTEGRATION_RESULT=0
PERFORMANCE_RESULT=0

# Step 1: Run smoke tests
echo "📋 Step 1/4: Running smoke tests..."
echo "=================================="
./run-e2e-smoke.sh
SMOKE_RESULT=$?

if [ $SMOKE_RESULT -ne 0 ]; then
  echo "❌ Smoke tests failed! Critical issues detected."
  echo "   Recommendation: Fix critical issues before running full suite."
  echo ""
fi

# Step 2: Run functional tests by area
echo "📋 Step 2/4: Running functional tests..."
echo "========================================"

echo "2a. Navigation tests..."
./run-e2e-functional.sh --area=navigation
NAV_RESULT=$?

echo "2b. Entity tests..."
./run-e2e-functional.sh --area=entities
ENTITY_RESULT=$?

echo "2c. Connection tests..."
./run-e2e-functional.sh --area=connections
CONN_RESULT=$?

echo "2d. Comparison tests..."
./run-e2e-functional.sh --area=comparisons
COMP_RESULT=$?

echo "2e. Error handling tests..."
./run-e2e-functional.sh --area=error-handling
ERROR_RESULT=$?

# Calculate functional result
if [ $NAV_RESULT -ne 0 ] || [ $ENTITY_RESULT -ne 0 ] || [ $CONN_RESULT -ne 0 ] || [ $COMP_RESULT -ne 0 ] || [ $ERROR_RESULT -ne 0 ]; then
  FUNCTIONAL_RESULT=1
fi

# Step 3: Run integration tests
echo "📋 Step 3/4: Running integration tests..."
echo "========================================="
./run-e2e-functional.sh --area=integration
INTEGRATION_RESULT=$?

# Step 4: Run performance tests
echo "📋 Step 4/4: Running performance tests..."
echo "========================================="
./run-e2e-performance.sh
PERFORMANCE_RESULT=$?

# Final summary
echo ""
echo "🏁 FULL TEST SUITE SUMMARY"
echo "=========================="
echo "Smoke tests:        $([ $SMOKE_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED")"
echo "Functional tests:   $([ $FUNCTIONAL_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED")"
echo "Integration tests:  $([ $INTEGRATION_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED")"
echo "Performance tests:  $([ $PERFORMANCE_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED")"
echo ""

# Calculate overall result
OVERALL_RESULT=0
if [ $SMOKE_RESULT -ne 0 ] || [ $FUNCTIONAL_RESULT -ne 0 ] || [ $INTEGRATION_RESULT -ne 0 ] || [ $PERFORMANCE_RESULT -ne 0 ]; then
  OVERALL_RESULT=1
fi

if [ $OVERALL_RESULT -eq 0 ]; then
  echo "🎉 ALL TESTS PASSED! The application is ready for deployment."
else
  echo "⚠️  Some tests failed. Review the results above and fix issues before deployment."
fi

echo ""
echo "For detailed results, check the individual test reports."

exit $OVERALL_RESULT
