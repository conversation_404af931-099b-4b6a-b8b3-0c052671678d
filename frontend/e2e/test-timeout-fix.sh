#!/bin/bash

# Test script to verify timeout fix
echo "🧪 Testing Timeout Fix for Bidirectional Connections"
echo "===================================================="

# Set working directory
cd "$(dirname "$0")"

echo "Testing the bidirectional connections test that was timing out..."
echo "Fixed: Now uses pre-created entities from beforeEach hook instead of slow UI creation"
echo ""

# Test the specific test that was timing out
echo "📋 Running bidirectional connections test..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should create bidirectional connections" \
  --reporter=line \
  --headed \
  --timeout=60000

echo ""
echo "✅ Timeout fix testing completed!"
echo ""
echo "Fix applied:"
echo "• BEFORE: Created entities via slow UI (14+ seconds each)"
echo "• AFTER:  Uses pre-created entities from parallel API setup (17ms total)"
echo ""
echo "This should eliminate the 30-second timeout issue."
