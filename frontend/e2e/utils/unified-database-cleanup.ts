import { Page } from '@playwright/test';
import { performanceMonitor } from './performance-monitor';

/**
 * Unified Database Cleanup System
 * 
 * This system provides comprehensive database cleanup with:
 * - Proper foreign key constraint handling (connections before entities)
 * - Worker-specific isolation to prevent conflicts
 * - Bulk operations for performance
 * - Comprehensive error handling and recovery
 * - Real-time monitoring and validation
 * - Clean database state verification
 * 
 * Key Features:
 * - Handles entity deletion failures
 * - Prevents orphaned data
 * - Worker-specific cleanup isolation
 * - Batch processing for efficiency
 * - Comprehensive state verification
 */
export class UnifiedDatabaseCleanup {
  private static instances: Map<string, UnifiedDatabaseCleanup> = new Map();
  private workerId: string;
  private trackedEntityIds: Set<string> = new Set();
  private trackedConnectionIds: Set<string> = new Set();
  private cleanupMetrics: {
    entitiesDeleted: number;
    connectionsDeleted: number;
    totalTime: number;
    cleanupOperations: number;
  } = {
    entitiesDeleted: 0,
    connectionsDeleted: 0,
    totalTime: 0,
    cleanupOperations: 0
  };

  private constructor(private page: Page) {
    this.workerId = this.getWorkerId();
  }

  /**
   * Get singleton instance for current worker
   */
  public static getInstance(page: Page): UnifiedDatabaseCleanup {
    const workerId = UnifiedDatabaseCleanup.getWorkerIdStatic();
    if (!this.instances.has(workerId)) {
      this.instances.set(workerId, new UnifiedDatabaseCleanup(page));
    }
    return this.instances.get(workerId)!;
  }

  /**
   * Get current worker ID
   */
  private getWorkerId(): string {
    return process.env.TEST_WORKER_INDEX || '0';
  }

  private static getWorkerIdStatic(): string {
    return process.env.TEST_WORKER_INDEX || '0';
  }

  /**
   * CORE CLEANUP METHOD: Complete database cleanup with proper ordering
   * 
   * This method handles the complete cleanup process:
   * 1. Clean connections first (to avoid foreign key violations)
   * 2. Clean entities second
   * 3. Verify clean state
   * 4. Report metrics
   */
  public async performCompleteCleanup(): Promise<{
    success: boolean;
    entitiesDeleted: number;
    connectionsDeleted: number;
    duration: number;
    errors: string[];
  }> {
    const startTime = Date.now();
    const endTimer = performanceMonitor.startTimer('complete-database-cleanup');
    const errors: string[] = [];

    console.log(`🧹 Starting complete database cleanup (Worker ${this.workerId})`);

    try {
      // Step 1: Clean connections first (prevents foreign key violations)
      const connectionResult = await this.cleanupAllTestConnections();
      if (!connectionResult.success) {
        errors.push(...connectionResult.errors);
      }

      // Step 2: Clean entities after connections are removed
      const entityResult = await this.cleanupAllTestEntities();
      if (!entityResult.success) {
        errors.push(...entityResult.errors);
      }

      // Step 3: Verify clean state
      const verificationResult = await this.verifyDatabaseCleanState();
      if (!verificationResult.isClean) {
        errors.push(`Database verification failed: ${verificationResult.issues.join(', ')}`);
      }

      const duration = endTimer();
      const totalDeleted = connectionResult.connectionsDeleted + entityResult.entitiesDeleted;

      // Update metrics
      this.cleanupMetrics.entitiesDeleted += entityResult.entitiesDeleted;
      this.cleanupMetrics.connectionsDeleted += connectionResult.connectionsDeleted;
      this.cleanupMetrics.totalTime += duration;
      this.cleanupMetrics.cleanupOperations++;

      const success = errors.length === 0;
      
      console.log(`🧹 Complete cleanup ${success ? 'completed' : 'completed with errors'}: ${totalDeleted} items deleted in ${duration}ms`);
      if (errors.length > 0) {
        console.log(`  Errors: ${errors.join('; ')}`);
      }

      return {
        success,
        entitiesDeleted: entityResult.entitiesDeleted,
        connectionsDeleted: connectionResult.connectionsDeleted,
        duration,
        errors
      };

    } catch (error) {
      const duration = endTimer();
      const errorMessage = `Complete cleanup failed: ${error}`;
      console.error(`❌ ${errorMessage}`);
      
      return {
        success: false,
        entitiesDeleted: 0,
        connectionsDeleted: 0,
        duration,
        errors: [errorMessage]
      };
    }
  }

  /**
   * Clean all test connections with proper error handling
   * 
   * Connections must be cleaned before entities to avoid foreign key violations
   */
  public async cleanupAllTestConnections(): Promise<{
    success: boolean;
    connectionsDeleted: number;
    errors: string[];
  }> {
    const startTime = Date.now();
    console.log(`🔗 Starting connection cleanup (Worker ${this.workerId})`);

    try {
      // Get all connections
      const connections = await this.getAllConnections();
      const testConnections = this.filterTestConnections(connections);

      console.log(`  Found ${testConnections.length} test connections to clean`);

      if (testConnections.length === 0) {
        return { success: true, connectionsDeleted: 0, errors: [] };
      }

      // Delete connections in batches
      const batchSize = 10; // Smaller batches for connections due to complexity
      let deleted = 0;
      const errors: string[] = [];

      for (let i = 0; i < testConnections.length; i += batchSize) {
        const batch = testConnections.slice(i, i + batchSize);
        
        const batchResults = await Promise.allSettled(
          batch.map(conn => this.deleteConnectionById(conn.id))
        );

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            deleted++;
            console.log(`    ✓ Deleted connection: ${batch[index].id}`);
          } else {
            const error = `Failed to delete connection ${batch[index].id}: ${result.reason}`;
            errors.push(error);
            console.log(`    ❌ ${error}`);
          }
        });

        // Brief pause between batches
        await this.page.waitForTimeout(50);
      }

      const duration = Date.now() - startTime;
      console.log(`🔗 Connection cleanup complete: ${deleted} deleted, ${errors.length} errors in ${duration}ms`);

      return {
        success: errors.length === 0,
        connectionsDeleted: deleted,
        errors
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = `Connection cleanup failed: ${error}`;
      console.error(`❌ ${errorMessage} (${duration}ms)`);
      
      return {
        success: false,
        connectionsDeleted: 0,
        errors: [errorMessage]
      };
    }
  }

  /**
   * Clean all test entities with proper error handling
   * 
   * Entities are cleaned after connections to avoid foreign key violations
   */
  public async cleanupAllTestEntities(): Promise<{
    success: boolean;
    entitiesDeleted: number;
    errors: string[];
  }> {
    const startTime = Date.now();
    console.log(`👤 Starting entity cleanup (Worker ${this.workerId})`);

    try {
      // Get all entities
      const entities = await this.getAllEntities();
      const testEntities = this.filterTestEntities(entities);

      console.log(`  Found ${testEntities.length} test entities to clean`);

      if (testEntities.length === 0) {
        return { success: true, entitiesDeleted: 0, errors: [] };
      }

      // Delete entities in batches
      const batchSize = 20; // Larger batches for entities
      let deleted = 0;
      const errors: string[] = [];

      for (let i = 0; i < testEntities.length; i += batchSize) {
        const batch = testEntities.slice(i, i + batchSize);
        
        const batchResults = await Promise.allSettled(
          batch.map(entity => this.deleteEntityById(entity.id))
        );

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            deleted++;
            console.log(`    ✓ Deleted entity: ${batch[index].name} (${batch[index].id})`);
          } else {
            const error = `Failed to delete entity ${batch[index].name} (${batch[index].id}): ${result.reason}`;
            errors.push(error);
            console.log(`    ❌ ${error}`);
          }
        });

        // Brief pause between batches
        await this.page.waitForTimeout(50);
      }

      const duration = Date.now() - startTime;
      console.log(`👤 Entity cleanup complete: ${deleted} deleted, ${errors.length} errors in ${duration}ms`);

      return {
        success: errors.length === 0,
        entitiesDeleted: deleted,
        errors
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = `Entity cleanup failed: ${error}`;
      console.error(`❌ ${errorMessage} (${duration}ms)`);
      
      return {
        success: false,
        entitiesDeleted: 0,
        errors: [errorMessage]
      };
    }
  }

  /**
   * Get all entities from API
   */
  private async getAllEntities(): Promise<any[]> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/entities');
      if (response.ok()) {
        return await response.json();
      }
      throw new Error(`Failed to fetch entities: ${response.status()}`);
    } catch (error) {
      console.error('Error fetching entities:', error);
      return [];
    }
  }

  /**
   * Get all connections from API
   */
  private async getAllConnections(): Promise<any[]> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/connections');
      if (response.ok()) {
        return await response.json();
      }
      throw new Error(`Failed to fetch connections: ${response.status()}`);
    } catch (error) {
      console.error('Error fetching connections:', error);
      return [];
    }
  }

  /**
   * Filter entities to identify test entities
   * 
   * Uses comprehensive patterns to identify test entities including:
   * - Worker-specific patterns
   * - General test patterns
   * - Time-based patterns for recently created entities
   */
  private filterTestEntities(entities: any[]): any[] {
    // Worker-specific patterns (highest priority)
    const workerPatterns = [
      new RegExp(`\\bTEST W${this.workerId}\\b`, 'i'),
      new RegExp(`\\bWorker${this.workerId}\\b`, 'i'),
      new RegExp(`\\bW${this.workerId}_P\\d+\\b`, 'i'),
    ];

    // General test patterns (use cautiously)
    const generalTestPatterns = [
      /\bTest\s+[A-Z]{4,}/i,
      /\bTesting\s+Entity/i,
      /\bTemporary\s+Entity/i,
      /\bDebug\s+Entity/i,
      /\bBenchmark\s+Entity/i,
      /\bPerformance\s+Test/i,
      /\bParallel\s+Entity/i,
      /\bSequential\s+Entity/i,
      /\bConcurrent\s+Test/i,
      /\bValidation\s+Entity/i,
      /\bCleanup\s+Test/i,
      // Common test entity patterns from existing tests
      /\bHuman\s+[A-Z]+/i,
      /\bBall\s+[A-Z]+/i,
      /\bBuild\s+[A-Z]+/i,
      /\bCar\s+[A-Z]+/i,
      /\bEleph\s+[A-Z]+/i,
      /\bMouse\s+[A-Z]+/i,
      /\bBasketball Test/i,
      /\bConnection Test/i,
      /\bSample Entity/i,
      /\bRapid [A-Z]/i,
      /\bDelete/i,
      /\bDuplicate/i,
      /\bRefresh/i,
      /\bEdit Updated/i,
      /\bLarge Building/i,
      /\bSmall Object/i,
      /\bMulti From/i,
      /\bMulti To/i,
      /\bLarge Num/i,
      /\bSci Not/i,
    ];

    return entities.filter(entity => {
      // Always clean worker-specific entities
      if (workerPatterns.some(pattern => pattern.test(entity.name))) {
        return true;
      }

      // For general test patterns, add safety checks
      if (generalTestPatterns.some(pattern => pattern.test(entity.name))) {
        // Don't clean entities that clearly belong to other workers
        const otherWorkerIds = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
          .filter(id => id !== this.workerId);
        
        const belongsToOtherWorker = otherWorkerIds.some(otherId => 
          entity.name.includes(`Worker${otherId}`) || 
          entity.name.includes(`W${otherId}_P`) ||
          entity.name.includes(`TEST W${otherId}`)
        );

        return !belongsToOtherWorker;
      }

      return false;
    });
  }

  /**
   * Filter connections to identify test connections
   */
  private filterTestConnections(connections: any[]): any[] {
    return connections.filter(connection => {
      // Check if either from_entity or to_entity is a test entity name
      const fromEntityName = connection.from_entity_name || '';
      const toEntityName = connection.to_entity_name || '';
      
      // Use similar patterns as entity filtering
      const isFromTestEntity = this.isTestEntityName(fromEntityName);
      const isToTestEntity = this.isTestEntityName(toEntityName);
      
      return isFromTestEntity || isToTestEntity;
    });
  }

  /**
   * Check if entity name matches test patterns
   */
  private isTestEntityName(name: string): boolean {
    if (!name) return false;

    // Worker-specific patterns
    const workerPatterns = [
      new RegExp(`\\bTEST W${this.workerId}\\b`, 'i'),
      new RegExp(`\\bWorker${this.workerId}\\b`, 'i'),
      new RegExp(`\\bW${this.workerId}_P\\d+\\b`, 'i'),
    ];

    if (workerPatterns.some(pattern => pattern.test(name))) {
      return true;
    }

    // General test patterns (with safety checks)
    const generalTestPatterns = [
      /\bTest\s+[A-Z]{4,}/i,
      /\bTesting\s+Entity/i,
      /\bTemporary\s+Entity/i,
      /\bDebug\s+Entity/i,
      /\bBenchmark\s+Entity/i,
      /\bPerformance\s+Test/i,
      /\bHuman\s+[A-Z]+/i,
      /\bBall\s+[A-Z]+/i,
      /\bBuild\s+[A-Z]+/i,
    ];

    if (generalTestPatterns.some(pattern => pattern.test(name))) {
      // Safety check: don't clean if belongs to other worker
      const otherWorkerIds = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        .filter(id => id !== this.workerId);
      
      const belongsToOtherWorker = otherWorkerIds.some(otherId => 
        name.includes(`Worker${otherId}`) || 
        name.includes(`W${otherId}_P`) ||
        name.includes(`TEST W${otherId}`)
      );

      return !belongsToOtherWorker;
    }

    return false;
  }

  /**
   * Delete entity by ID with retry logic
   */
  private async deleteEntityById(entityId: string): Promise<void> {
    const maxRetries = 3;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await this.page.request.delete(`http://localhost:8000/api/v1/entities/${entityId}`);
        
        if (response.ok()) {
          return; // Success
        } else if (response.status() === 404) {
          // Entity already deleted, consider this success
          return;
        } else {
          throw new Error(`HTTP ${response.status()}: ${await response.text().catch(() => 'Unknown error')}`);
        }
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Exponential backoff
        const delay = 100 * Math.pow(2, attempt - 1);
        await this.page.waitForTimeout(delay);
      }
    }
  }

  /**
   * Delete connection by ID with retry logic
   */
  private async deleteConnectionById(connectionId: string): Promise<void> {
    const maxRetries = 3;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await this.page.request.delete(`http://localhost:8000/api/v1/connections/${connectionId}`);
        
        if (response.ok()) {
          return; // Success
        } else if (response.status() === 404) {
          // Connection already deleted, consider this success
          return;
        } else {
          throw new Error(`HTTP ${response.status()}: ${await response.text().catch(() => 'Unknown error')}`);
        }
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Exponential backoff
        const delay = 100 * Math.pow(2, attempt - 1);
        await this.page.waitForTimeout(delay);
      }
    }
  }

  /**
   * Verify database is in clean state
   */
  public async verifyDatabaseCleanState(): Promise<{
    isClean: boolean;
    issues: string[];
    testEntitiesRemaining: number;
    testConnectionsRemaining: number;
  }> {
    const issues: string[] = [];

    try {
      // Check for remaining test entities
      const entities = await this.getAllEntities();
      const remainingTestEntities = this.filterTestEntities(entities);
      
      // Check for remaining test connections
      const connections = await this.getAllConnections();
      const remainingTestConnections = this.filterTestConnections(connections);

      if (remainingTestEntities.length > 0) {
        issues.push(`${remainingTestEntities.length} test entities still exist`);
        remainingTestEntities.forEach(entity => {
          issues.push(`  Entity: ${entity.name} (${entity.id})`);
        });
      }

      if (remainingTestConnections.length > 0) {
        issues.push(`${remainingTestConnections.length} test connections still exist`);
        remainingTestConnections.forEach(conn => {
          issues.push(`  Connection: ${conn.id}`);
        });
      }

      const isClean = issues.length === 0;
      
      if (isClean) {
        console.log(`✅ Database clean state verified (Worker ${this.workerId})`);
      } else {
        console.log(`⚠️  Database clean state verification failed (Worker ${this.workerId}):`);
        issues.forEach(issue => console.log(`  ${issue}`));
      }

      return {
        isClean,
        issues,
        testEntitiesRemaining: remainingTestEntities.length,
        testConnectionsRemaining: remainingTestConnections.length
      };

    } catch (error) {
      const errorMessage = `Database verification failed: ${error}`;
      console.error(`❌ ${errorMessage}`);
      
      return {
        isClean: false,
        issues: [errorMessage],
        testEntitiesRemaining: -1,
        testConnectionsRemaining: -1
      };
    }
  }

  /**
   * Track entity for cleanup
   */
  public trackEntityForCleanup(entityId: string): void {
    this.trackedEntityIds.add(entityId);
  }

  /**
   * Track connection for cleanup
   */
  public trackConnectionForCleanup(connectionId: string): void {
    this.trackedConnectionIds.add(connectionId);
  }

  /**
   * Clean only tracked entities and connections
   */
  public async cleanupTrackedItems(): Promise<{
    success: boolean;
    entitiesDeleted: number;
    connectionsDeleted: number;
    duration: number;
    errors: string[];
  }> {
    const startTime = Date.now();
    const errors: string[] = [];
    let entitiesDeleted = 0;
    let connectionsDeleted = 0;

    console.log(`🎯 Cleaning tracked items (Worker ${this.workerId}): ${this.trackedConnectionIds.size} connections, ${this.trackedEntityIds.size} entities`);

    try {
      // Clean tracked connections first
      for (const connectionId of this.trackedConnectionIds) {
        try {
          await this.deleteConnectionById(connectionId);
          connectionsDeleted++;
        } catch (error) {
          errors.push(`Failed to delete tracked connection ${connectionId}: ${error}`);
        }
      }

      // Clean tracked entities second
      for (const entityId of this.trackedEntityIds) {
        try {
          await this.deleteEntityById(entityId);
          entitiesDeleted++;
        } catch (error) {
          errors.push(`Failed to delete tracked entity ${entityId}: ${error}`);
        }
      }

      // Clear tracking sets
      this.trackedConnectionIds.clear();
      this.trackedEntityIds.clear();

      const duration = Date.now() - startTime;
      const success = errors.length === 0;

      console.log(`🎯 Tracked cleanup ${success ? 'completed' : 'completed with errors'}: ${entitiesDeleted + connectionsDeleted} items in ${duration}ms`);

      return {
        success,
        entitiesDeleted,
        connectionsDeleted,
        duration,
        errors
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = `Tracked cleanup failed: ${error}`;
      console.error(`❌ ${errorMessage}`);
      
      return {
        success: false,
        entitiesDeleted,
        connectionsDeleted,
        duration,
        errors: [errorMessage]
      };
    }
  }

  /**
   * Get cleanup metrics
   */
  public getCleanupMetrics(): {
    entitiesDeleted: number;
    connectionsDeleted: number;
    totalTime: number;
    cleanupOperations: number;
    averageTimePerOperation: number;
  } {
    return {
      ...this.cleanupMetrics,
      averageTimePerOperation: this.cleanupMetrics.cleanupOperations > 0 
        ? this.cleanupMetrics.totalTime / this.cleanupMetrics.cleanupOperations 
        : 0
    };
  }

  /**
   * Reset cleanup metrics
   */
  public resetMetrics(): void {
    this.cleanupMetrics = {
      entitiesDeleted: 0,
      connectionsDeleted: 0,
      totalTime: 0,
      cleanupOperations: 0
    };
  }

  /**
   * Emergency cleanup - aggressive cleanup for critical situations
   */
  public async emergencyCleanup(): Promise<void> {
    console.log(`🚨 Emergency cleanup initiated (Worker ${this.workerId})`);
    
    try {
      // Perform complete cleanup without error checking
      await this.performCompleteCleanup();
      
      // Clear all tracking
      this.trackedEntityIds.clear();
      this.trackedConnectionIds.clear();
      
      console.log(`🚨 Emergency cleanup complete (Worker ${this.workerId})`);
      
    } catch (error) {
      console.error(`❌ Emergency cleanup failed (Worker ${this.workerId}):`, error);
      throw error;
    }
  }

  /**
   * Health check for cleanup system
   */
  public async healthCheck(): Promise<{
    healthy: boolean;
    response: string;
    latency: number;
    canAccessEntities: boolean;
    canAccessConnections: boolean;
  }> {
    const startTime = Date.now();
    
    try {
      // Test entity API access
      const entityResponse = await this.page.request.get('http://localhost:8000/api/v1/entities?limit=1');
      const canAccessEntities = entityResponse.ok();
      
      // Test connection API access
      const connectionResponse = await this.page.request.get('http://localhost:8000/api/v1/connections?limit=1');
      const canAccessConnections = connectionResponse.ok();
      
      const latency = Date.now() - startTime;
      const healthy = canAccessEntities && canAccessConnections;
      
      return {
        healthy,
        response: healthy ? 'All systems operational' : 'API access issues detected',
        latency,
        canAccessEntities,
        canAccessConnections
      };
      
    } catch (error) {
      const latency = Date.now() - startTime;
      return {
        healthy: false,
        response: `Health check failed: ${error}`,
        latency,
        canAccessEntities: false,
        canAccessConnections: false
      };
    }
  }

  /**
   * Generate comprehensive cleanup report
   */
  public generateCleanupReport(): string {
    const metrics = this.getCleanupMetrics();
    
    let report = `\n🧹 Unified Database Cleanup Report (Worker ${this.workerId}):\n`;
    report += `  • Total cleanup operations: ${metrics.cleanupOperations}\n`;
    report += `  • Entities deleted: ${metrics.entitiesDeleted}\n`;
    report += `  • Connections deleted: ${metrics.connectionsDeleted}\n`;
    report += `  • Total cleanup time: ${metrics.totalTime}ms\n`;
    
    if (metrics.cleanupOperations > 0) {
      report += `  • Average time per operation: ${Math.round(metrics.averageTimePerOperation)}ms\n`;
      report += `  • Performance efficiency: ${metrics.totalTime < 5000 ? 'Excellent' : metrics.totalTime < 15000 ? 'Good' : 'Needs improvement'}\n`;
    }
    
    report += `  • Currently tracked items: ${this.trackedEntityIds.size} entities, ${this.trackedConnectionIds.size} connections\n`;
    
    return report;
  }
}