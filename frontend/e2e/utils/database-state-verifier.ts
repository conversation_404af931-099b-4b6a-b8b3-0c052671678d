import { Page } from '@playwright/test';

/**
 * Database State Verification System
 * 
 * This system provides comprehensive database state verification with:
 * - Complete data integrity checks
 * - Foreign key constraint validation
 * - Orphaned data detection
 * - Worker isolation verification
 * - Performance impact assessment
 * - Detailed reporting and recommendations
 * 
 * Key Features:
 * - Multi-level verification (quick, standard, thorough)
 * - Automated issue detection and categorization
 * - Performance monitoring
 * - Detailed reporting with actionable recommendations
 */
export class DatabaseStateVerifier {
  private static instance: DatabaseStateVerifier;
  private verificationMetrics: {
    verificationsRun: number;
    issuesDetected: number;
    totalVerificationTime: number;
    lastVerificationTime: number;
  } = {
    verificationsRun: 0,
    issuesDetected: 0,
    totalVerificationTime: 0,
    lastVerificationTime: 0
  };

  private constructor(private page: Page) {}

  public static getInstance(page: Page): DatabaseStateVerifier {
    if (!DatabaseStateVerifier.instance) {
      DatabaseStateVerifier.instance = new DatabaseStateVerifier(page);
    }
    return DatabaseStateVerifier.instance;
  }

  /**
   * COMPREHENSIVE DATABASE STATE VERIFICATION
   * 
   * Performs complete verification of database state including:
   * - Entity integrity
   * - Connection integrity  
   * - Foreign key constraints
   * - Orphaned data detection
   * - Worker isolation verification
   */
  public async verifyDatabaseState(level: 'quick' | 'standard' | 'thorough' = 'standard'): Promise<{
    clean: boolean;
    issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }>;
    summary: {
      totalEntities: number;
      totalConnections: number;
      testEntities: number;
      testConnections: number;
      orphanedEntities: number;
      orphanedConnections: number;
      foreignKeyViolations: number;
      workerIsolationViolations: number;
    };
    performance: {
      verificationTime: number;
      efficiency: 'excellent' | 'good' | 'poor';
    };
    recommendations: string[];
  }> {
    const startTime = Date.now();
    const issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }> = [];
    const recommendations: string[] = [];

    console.log(`🔍 Starting ${level} database state verification`);

    try {
      // Get all data
      const entities = await this.getAllEntities();
      const connections = await this.getAllConnections();

      // Initialize summary
      const summary = {
        totalEntities: entities.length,
        totalConnections: connections.length,
        testEntities: 0,
        testConnections: 0,
        orphanedEntities: 0,
        orphanedConnections: 0,
        foreignKeyViolations: 0,
        workerIsolationViolations: 0
      };

      // Step 1: Basic data integrity checks
      const basicChecks = await this.performBasicIntegrityChecks(entities, connections);
      issues.push(...basicChecks.issues);
      summary.testEntities = basicChecks.testEntities;
      summary.testConnections = basicChecks.testConnections;

      // Step 2: Foreign key constraint verification
      if (level !== 'quick') {
        const foreignKeyChecks = await this.verifyForeignKeyConstraints(entities, connections);
        issues.push(...foreignKeyChecks.issues);
        summary.foreignKeyViolations = foreignKeyChecks.violations;
        summary.orphanedConnections = foreignKeyChecks.orphanedConnections;
      }

      // Step 3: Orphaned data detection
      if (level === 'thorough') {
        const orphanedDataChecks = await this.detectOrphanedData(entities, connections);
        issues.push(...orphanedDataChecks.issues);
        summary.orphanedEntities = orphanedDataChecks.orphanedEntities;
      }

      // Step 4: Worker isolation verification
      const isolationChecks = await this.verifyWorkerIsolation(entities, connections);
      issues.push(...isolationChecks.issues);
      summary.workerIsolationViolations = isolationChecks.violations;

      // Step 5: Performance impact assessment
      const performanceChecks = await this.assessPerformanceImpact(entities, connections);
      issues.push(...performanceChecks.issues);

      // Generate recommendations
      recommendations.push(...this.generateRecommendations(summary, issues));

      const verificationTime = Date.now() - startTime;
      this.updateMetrics(verificationTime, issues.length);

      const clean = !issues.some(issue => issue.type === 'error');
      const efficiency = this.assessVerificationEfficiency(verificationTime, summary.totalEntities + summary.totalConnections);

      console.log(`🔍 Database verification ${clean ? 'passed' : 'found issues'}: ${issues.length} issues in ${verificationTime}ms`);

      return {
        clean,
        issues,
        summary,
        performance: {
          verificationTime,
          efficiency
        },
        recommendations
      };

    } catch (error) {
      const verificationTime = Date.now() - startTime;
      const errorMessage = `Database verification failed: ${error}`;
      console.error(`❌ ${errorMessage}`);

      return {
        clean: false,
        issues: [{
          type: 'error',
          category: 'verification_failure',
          message: errorMessage
        }],
        summary: {
          totalEntities: 0,
          totalConnections: 0,
          testEntities: 0,
          testConnections: 0,
          orphanedEntities: 0,
          orphanedConnections: 0,
          foreignKeyViolations: 0,
          workerIsolationViolations: 0
        },
        performance: {
          verificationTime,
          efficiency: 'poor'
        },
        recommendations: ['Fix verification system connectivity issues']
      };
    }
  }

  /**
   * Basic integrity checks for entities and connections
   */
  private async performBasicIntegrityChecks(entities: any[], connections: any[]): Promise<{
    issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }>;
    testEntities: number;
    testConnections: number;
  }> {
    const issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }> = [];

    // Count test entities
    const testEntityPatterns = [
      /\btest\b/i,
      /\btesting\b/i,
      /\btemp\b/i,
      /\bdebug\b/i,
      /\bbenchmark\b/i,
      /\bperformance\b/i,
      /\bparallel\b/i,
      /\bsequential\b/i,
      /\bTEST W\d+/i,
      /\bWorker\d+/i,
    ];

    const testEntities = entities.filter(entity => 
      testEntityPatterns.some(pattern => pattern.test(entity.name))
    ).length;

    const testConnections = connections.filter(conn => 
      testEntityPatterns.some(pattern => 
        pattern.test(conn.from_entity_name || '') || pattern.test(conn.to_entity_name || '')
      )
    ).length;

    // Check for duplicate entity names
    const entityNameCounts = new Map<string, number>();
    entities.forEach(entity => {
      const count = entityNameCounts.get(entity.name) || 0;
      entityNameCounts.set(entity.name, count + 1);
    });

    const duplicateEntities = Array.from(entityNameCounts.entries()).filter(([name, count]) => count > 1);
    duplicateEntities.forEach(([name, count]) => {
      issues.push({
        type: 'error',
        category: 'data_integrity',
        message: `Duplicate entity name detected: "${name}" (${count} instances)`,
        details: { entityName: name, count }
      });
    });

    // Check for entities with invalid names
    const invalidNameEntities = entities.filter(entity => {
      if (!entity.name || entity.name.trim() === '') {
        return true;
      }
      if (entity.name.length > 20) {
        return true;
      }
      if (!/^[a-zA-Z0-9\s]+$/.test(entity.name)) {
        return true;
      }
      return false;
    });

    invalidNameEntities.forEach(entity => {
      issues.push({
        type: 'error',
        category: 'data_integrity',
        message: `Entity has invalid name: "${entity.name}" (ID: ${entity.id})`,
        details: { entityId: entity.id, entityName: entity.name }
      });
    });

    // Check for connections with invalid multipliers
    const invalidConnections = connections.filter(conn => {
      if (!conn.multiplier || isNaN(conn.multiplier) || conn.multiplier <= 0) {
        return true;
      }
      return false;
    });

    invalidConnections.forEach(conn => {
      issues.push({
        type: 'error',
        category: 'data_integrity',
        message: `Connection has invalid multiplier: ${conn.multiplier} (ID: ${conn.id})`,
        details: { connectionId: conn.id, multiplier: conn.multiplier }
      });
    });

    // Report test data presence
    if (testEntities > 0 || testConnections > 0) {
      issues.push({
        type: 'warning',
        category: 'test_data',
        message: `Test data detected: ${testEntities} entities, ${testConnections} connections`,
        details: { testEntities, testConnections }
      });
    }

    return {
      issues,
      testEntities,
      testConnections
    };
  }

  /**
   * Verify foreign key constraints between entities and connections
   */
  private async verifyForeignKeyConstraints(entities: any[], connections: any[]): Promise<{
    issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }>;
    violations: number;
    orphanedConnections: number;
  }> {
    const issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }> = [];

    // Create entity ID lookup
    const entityIds = new Set(entities.map(entity => entity.id));
    
    // Find connections with invalid entity references
    const orphanedConnections = connections.filter(conn => {
      const fromEntityExists = entityIds.has(conn.from_entity_id);
      const toEntityExists = entityIds.has(conn.to_entity_id);
      return !fromEntityExists || !toEntityExists;
    });

    orphanedConnections.forEach(conn => {
      const fromEntityExists = entityIds.has(conn.from_entity_id);
      const toEntityExists = entityIds.has(conn.to_entity_id);
      
      issues.push({
        type: 'error',
        category: 'foreign_key_violation',
        message: `Orphaned connection: ${conn.from_entity_name || 'Unknown'} → ${conn.to_entity_name || 'Unknown'} (ID: ${conn.id})`,
        details: {
          connectionId: conn.id,
          fromEntityId: conn.from_entity_id,
          toEntityId: conn.to_entity_id,
          fromEntityExists,
          toEntityExists
        }
      });
    });

    // Check for missing reverse connections (bidirectional constraint)
    const connectionPairs = new Map<string, any>();
    connections.forEach(conn => {
      const key = `${conn.from_entity_id}-${conn.to_entity_id}-${conn.unit_id}`;
      connectionPairs.set(key, conn);
    });

    const missingReverseConnections: any[] = [];
    connections.forEach(conn => {
      const reverseKey = `${conn.to_entity_id}-${conn.from_entity_id}-${conn.unit_id}`;
      if (!connectionPairs.has(reverseKey)) {
        missingReverseConnections.push(conn);
      }
    });

    missingReverseConnections.forEach(conn => {
      issues.push({
        type: 'warning',
        category: 'bidirectional_constraint',
        message: `Missing reverse connection for: ${conn.from_entity_name} → ${conn.to_entity_name}`,
        details: {
          connectionId: conn.id,
          fromEntity: conn.from_entity_name,
          toEntity: conn.to_entity_name,
          unit: conn.unit_name
        }
      });
    });

    return {
      issues,
      violations: orphanedConnections.length,
      orphanedConnections: orphanedConnections.length
    };
  }

  /**
   * Detect orphaned data that may cause issues
   */
  private async detectOrphanedData(entities: any[], connections: any[]): Promise<{
    issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }>;
    orphanedEntities: number;
  }> {
    const issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }> = [];

    // Find entities with no connections
    const entitiesInConnections = new Set();
    connections.forEach(conn => {
      entitiesInConnections.add(conn.from_entity_id);
      entitiesInConnections.add(conn.to_entity_id);
    });

    const orphanedEntities = entities.filter(entity => !entitiesInConnections.has(entity.id));

    // Only report orphaned entities if they look like test data (to avoid false positives)
    const testEntityPatterns = [
      /\btest\b/i,
      /\btesting\b/i,
      /\btemp\b/i,
      /\bdebug\b/i,
      /\bTEST W\d+/i,
    ];

    const orphanedTestEntities = orphanedEntities.filter(entity => 
      testEntityPatterns.some(pattern => pattern.test(entity.name))
    );

    orphanedTestEntities.forEach(entity => {
      issues.push({
        type: 'info',
        category: 'orphaned_data',
        message: `Orphaned test entity (no connections): ${entity.name} (ID: ${entity.id})`,
        details: { entityId: entity.id, entityName: entity.name }
      });
    });

    // Check for entities with same name but different IDs (potential duplicates)
    const nameToIds = new Map<string, string[]>();
    entities.forEach(entity => {
      const ids = nameToIds.get(entity.name) || [];
      ids.push(entity.id);
      nameToIds.set(entity.name, ids);
    });

    const duplicateNames = Array.from(nameToIds.entries()).filter(([name, ids]) => ids.length > 1);
    duplicateNames.forEach(([name, ids]) => {
      issues.push({
        type: 'warning',
        category: 'potential_duplicates',
        message: `Multiple entities with same name: "${name}" (IDs: ${ids.join(', ')})`,
        details: { entityName: name, entityIds: ids }
      });
    });

    return {
      issues,
      orphanedEntities: orphanedTestEntities.length
    };
  }

  /**
   * Verify worker isolation hasn't been compromised
   */
  private async verifyWorkerIsolation(entities: any[], connections: any[]): Promise<{
    issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }>;
    violations: number;
  }> {
    const issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }> = [];

    // Detect cross-worker connections
    const workerPatterns = new Map<string, RegExp>();
    workerPatterns.set('W0', /\bTEST W0\b|\bWorker0\b|\bW0P\d+\b/i);
    workerPatterns.set('W1', /\bTEST W1\b|\bWorker1\b|\bW1P\d+\b/i);
    workerPatterns.set('W2', /\bTEST W2\b|\bWorker2\b|\bW2P\d+\b/i);

    const getEntityWorker = (entityName: string): string | null => {
      for (const [workerId, pattern] of workerPatterns.entries()) {
        if (pattern.test(entityName)) {
          return workerId;
        }
      }
      return null;
    };

    const crossWorkerConnections = connections.filter(conn => {
      const fromWorker = getEntityWorker(conn.from_entity_name || '');
      const toWorker = getEntityWorker(conn.to_entity_name || '');
      
      // Violation: both entities belong to workers, but different workers
      return fromWorker && toWorker && fromWorker !== toWorker;
    });

    crossWorkerConnections.forEach(conn => {
      const fromWorker = getEntityWorker(conn.from_entity_name || '');
      const toWorker = getEntityWorker(conn.to_entity_name || '');
      
      issues.push({
        type: 'error',
        category: 'worker_isolation_violation',
        message: `Cross-worker connection detected: ${conn.from_entity_name} (${fromWorker}) → ${conn.to_entity_name} (${toWorker})`,
        details: {
          connectionId: conn.id,
          fromWorker,
          toWorker,
          fromEntity: conn.from_entity_name,
          toEntity: conn.to_entity_name
        }
      });
    });

    return {
      issues,
      violations: crossWorkerConnections.length
    };
  }

  /**
   * Assess performance impact of current database state
   */
  private async assessPerformanceImpact(entities: any[], connections: any[]): Promise<{
    issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }>;
  }> {
    const issues: Array<{
      type: 'error' | 'warning' | 'info';
      category: string;
      message: string;
      details?: any;
    }> = [];

    // Check for performance impact thresholds
    const totalItems = entities.length + connections.length;

    if (totalItems > 1000) {
      issues.push({
        type: 'warning',
        category: 'performance_impact',
        message: `Large dataset detected: ${totalItems} total items may impact test performance`,
        details: { totalItems, entities: entities.length, connections: connections.length }
      });
    }

    if (totalItems > 5000) {
      issues.push({
        type: 'error',
        category: 'performance_impact',
        message: `Very large dataset: ${totalItems} items will significantly impact performance`,
        details: { totalItems, entities: entities.length, connections: connections.length }
      });
    }

    // Check for high-degree entities (entities with many connections)
    const entityConnectionCounts = new Map<string, number>();
    connections.forEach(conn => {
      const fromCount = entityConnectionCounts.get(conn.from_entity_id) || 0;
      const toCount = entityConnectionCounts.get(conn.to_entity_id) || 0;
      entityConnectionCounts.set(conn.from_entity_id, fromCount + 1);
      entityConnectionCounts.set(conn.to_entity_id, toCount + 1);
    });

    const highDegreeEntities = Array.from(entityConnectionCounts.entries())
      .filter(([entityId, count]) => count > 50)
      .map(([entityId, count]) => ({ entityId, count }));

    highDegreeEntities.forEach(({ entityId, count }) => {
      const entity = entities.find(e => e.id === entityId);
      issues.push({
        type: 'warning',
        category: 'performance_impact',
        message: `High-degree entity detected: ${entity?.name || entityId} has ${count} connections`,
        details: { entityId, entityName: entity?.name, connectionCount: count }
      });
    });

    return { issues };
  }

  /**
   * Generate actionable recommendations based on verification results
   */
  private generateRecommendations(summary: any, issues: any[]): string[] {
    const recommendations: string[] = [];

    // Test data cleanup recommendations
    if (summary.testEntities > 0 || summary.testConnections > 0) {
      recommendations.push(`Run cleanup to remove ${summary.testEntities} test entities and ${summary.testConnections} test connections`);
    }

    // Foreign key violation recommendations
    if (summary.foreignKeyViolations > 0) {
      recommendations.push(`Fix ${summary.foreignKeyViolations} foreign key violations by cleaning orphaned connections`);
    }

    // Worker isolation recommendations
    if (summary.workerIsolationViolations > 0) {
      recommendations.push(`Resolve ${summary.workerIsolationViolations} worker isolation violations to prevent test conflicts`);
    }

    // Performance recommendations
    const totalItems = summary.totalEntities + summary.totalConnections;
    if (totalItems > 1000) {
      recommendations.push('Consider regular cleanup to maintain optimal test performance');
    }

    // Data integrity recommendations
    const integrityIssues = issues.filter(issue => issue.category === 'data_integrity').length;
    if (integrityIssues > 0) {
      recommendations.push(`Fix ${integrityIssues} data integrity issues to ensure consistent test behavior`);
    }

    // General recommendations
    if (issues.some(issue => issue.type === 'error')) {
      recommendations.push('Run emergency cleanup to resolve critical database issues');
    }

    if (recommendations.length === 0) {
      recommendations.push('Database state is healthy - no immediate action required');
    }

    return recommendations;
  }

  /**
   * Update verification metrics
   */
  private updateMetrics(verificationTime: number, issuesCount: number): void {
    this.verificationMetrics.verificationsRun++;
    this.verificationMetrics.issuesDetected += issuesCount;
    this.verificationMetrics.totalVerificationTime += verificationTime;
    this.verificationMetrics.lastVerificationTime = verificationTime;
  }

  /**
   * Assess verification efficiency
   */
  private assessVerificationEfficiency(verificationTime: number, itemCount: number): 'excellent' | 'good' | 'poor' {
    if (itemCount === 0) return 'excellent';
    
    const timePerItem = verificationTime / itemCount;
    
    if (timePerItem < 1) return 'excellent';  // < 1ms per item
    if (timePerItem < 5) return 'good';       // < 5ms per item
    return 'poor';                            // > 5ms per item
  }

  /**
   * Get verification metrics
   */
  public getVerificationMetrics(): {
    verificationsRun: number;
    issuesDetected: number;
    totalVerificationTime: number;
    lastVerificationTime: number;
    averageVerificationTime: number;
    averageIssuesPerVerification: number;
  } {
    return {
      ...this.verificationMetrics,
      averageVerificationTime: this.verificationMetrics.verificationsRun > 0 
        ? this.verificationMetrics.totalVerificationTime / this.verificationMetrics.verificationsRun 
        : 0,
      averageIssuesPerVerification: this.verificationMetrics.verificationsRun > 0 
        ? this.verificationMetrics.issuesDetected / this.verificationMetrics.verificationsRun 
        : 0
    };
  }

  /**
   * Generate verification performance report
   */
  public generateVerificationReport(): string {
    const metrics = this.getVerificationMetrics();
    
    let report = '\n🔍 Database Verification Performance Report:\n';
    report += `  • Verifications run: ${metrics.verificationsRun}\n`;
    report += `  • Total issues detected: ${metrics.issuesDetected}\n`;
    report += `  • Total verification time: ${metrics.totalVerificationTime}ms\n`;
    
    if (metrics.verificationsRun > 0) {
      report += `  • Average verification time: ${Math.round(metrics.averageVerificationTime)}ms\n`;
      report += `  • Average issues per verification: ${metrics.averageIssuesPerVerification.toFixed(1)}\n`;
      
      if (metrics.averageVerificationTime < 1000) {
        report += `  • Performance: Excellent (< 1s average)\n`;
      } else if (metrics.averageVerificationTime < 3000) {
        report += `  • Performance: Good (< 3s average)\n`;
      } else {
        report += `  • Performance: Needs improvement (> 3s average)\n`;
      }
    }
    
    return report;
  }

  // Helper methods for API access
  private async getAllEntities(): Promise<any[]> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/entities');
      if (response.ok()) {
        return await response.json();
      }
      throw new Error(`Failed to fetch entities: ${response.status()}`);
    } catch (error) {
      console.error('Error fetching entities:', error);
      return [];
    }
  }

  private async getAllConnections(): Promise<any[]> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/connections');
      if (response.ok()) {
        return await response.json();
      }
      throw new Error(`Failed to fetch connections: ${response.status()}`);
    } catch (error) {
      console.error('Error fetching connections:', error);
      return [];
    }
  }
}