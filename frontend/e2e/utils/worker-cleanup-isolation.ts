import { Page } from '@playwright/test';
import { UnifiedDatabaseCleanup } from './unified-database-cleanup';

/**
 * Worker-Specific Cleanup Isolation System
 * 
 * This system provides complete isolation between parallel test workers to prevent:
 * - Cross-worker data conflicts
 * - Entity deletion race conditions
 * - Database pollution between workers
 * - Cleanup interference
 * 
 * Key Features:
 * - Unique worker namespacing
 * - Worker-specific entity and connection patterns
 * - Cross-worker safety checks
 * - Isolated cleanup operations
 * - Worker collision detection and recovery
 */
export class WorkerCleanupIsolation {
  private static workerRegistrations: Map<string, {
    workerId: string;
    prefix: string;
    startTime: number;
    lastActivity: number;
  }> = new Map();

  private workerId: string;
  private workerPrefix: string;
  private cleanupSystem: UnifiedDatabaseCleanup;
  private isolationMetrics: {
    entitiesCreated: number;
    connectionsCreated: number;
    entitiesCleaned: number;
    connectionsCleaned: number;
    crossWorkerConflicts: number;
    isolationViolations: number;
  } = {
    entitiesCreated: 0,
    connectionsCreated: 0,
    entitiesCleaned: 0,
    connectionsCleaned: 0,
    crossWorkerConflicts: 0,
    isolationViolations: 0
  };

  constructor(private page: Page) {
    this.workerId = this.getWorkerId();
    this.workerPrefix = this.generateWorkerPrefix();
    this.cleanupSystem = UnifiedDatabaseCleanup.getInstance(page);
    this.registerWorker();
  }

  /**
   * Get current worker ID
   */
  private getWorkerId(): string {
    const workerIndex = process.env.TEST_WORKER_INDEX || '0';
    const parallelIndex = process.env.TEST_PARALLEL_INDEX || '0';
    return `W${workerIndex}P${parallelIndex}`;
  }

  /**
   * Generate unique worker prefix for entity/connection naming
   */
  private generateWorkerPrefix(): string {
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits for uniqueness
    const randomSuffix = Math.random().toString(36).substr(2, 3).toUpperCase();
    return `TEST ${this.workerId} ${timestamp}${randomSuffix}`;
  }

  /**
   * Register this worker in the global registry
   */
  private registerWorker(): void {
    WorkerCleanupIsolation.workerRegistrations.set(this.workerId, {
      workerId: this.workerId,
      prefix: this.workerPrefix,
      startTime: Date.now(),
      lastActivity: Date.now()
    });
    
    console.log(`🔒 Worker isolation registered: ${this.workerId} with prefix "${this.workerPrefix}"`);
  }

  /**
   * Update worker activity timestamp
   */
  private updateWorkerActivity(): void {
    const registration = WorkerCleanupIsolation.workerRegistrations.get(this.workerId);
    if (registration) {
      registration.lastActivity = Date.now();
    }
  }

  /**
   * Create worker-isolated entity name
   * 
   * Generates entity names that are guaranteed to be unique to this worker
   */
  public createIsolatedEntityName(baseName: string): string {
    this.updateWorkerActivity();
    
    // Create full isolated name
    const fullName = `${this.workerPrefix} ${baseName}`;
    
    // Ensure name fits within 20 character limit and is valid
    const sanitizedName = fullName
      .replace(/[^a-zA-Z0-9\s]/g, '') // Only alphanumeric and spaces
      .substring(0, 20)
      .trim();
    
    this.isolationMetrics.entitiesCreated++;
    
    console.log(`🏷️  Created isolated entity name: "${sanitizedName}" (Worker ${this.workerId})`);
    return sanitizedName;
  }

  /**
   * Check if entity belongs to this worker
   */
  public isMyEntity(entityName: string): boolean {
    return entityName.startsWith(this.workerPrefix);
  }

  /**
   * Check if entity belongs to any other worker
   */
  public belongsToOtherWorker(entityName: string): boolean {
    // Check against all registered worker prefixes
    for (const [workerId, registration] of WorkerCleanupIsolation.workerRegistrations.entries()) {
      if (workerId !== this.workerId && entityName.startsWith(registration.prefix)) {
        return true;
      }
    }
    
    // Check against common worker patterns even if not registered
    const otherWorkerPatterns = [
      /\bTEST W\d+P\d+\b/i,
      /\bWorker\d+\b/i,
      /\bW\d+P\d+\b/i,
    ];
    
    return otherWorkerPatterns.some(pattern => {
      const matches = entityName.match(pattern);
      if (matches) {
        // Extract worker ID from match and compare
        const foundWorkerId = matches[0];
        return foundWorkerId !== this.workerId;
      }
      return false;
    });
  }

  /**
   * Perform worker-isolated cleanup
   * 
   * Only cleans entities and connections that belong to this worker
   */
  public async performIsolatedCleanup(): Promise<{
    success: boolean;
    entitiesDeleted: number;
    connectionsDeleted: number;
    crossWorkerItemsSkipped: number;
    isolationViolations: number;
    duration: number;
    errors: string[];
  }> {
    const startTime = Date.now();
    const errors: string[] = [];
    let entitiesDeleted = 0;
    let connectionsDeleted = 0;
    let crossWorkerItemsSkipped = 0;
    let isolationViolations = 0;

    console.log(`🔒 Starting isolated cleanup for worker ${this.workerId}`);

    try {
      // Step 1: Get all entities and filter for this worker
      const allEntities = await this.getAllEntities();
      const myEntities = allEntities.filter(entity => this.isMyEntity(entity.name));
      const otherWorkerEntities = allEntities.filter(entity => this.belongsToOtherWorker(entity.name));
      
      crossWorkerItemsSkipped += otherWorkerEntities.length;

      console.log(`  Found ${myEntities.length} entities for this worker, ${otherWorkerEntities.length} for other workers`);

      // Step 2: Get all connections and filter for this worker
      const allConnections = await this.getAllConnections();
      const myConnections = allConnections.filter(conn => 
        this.isMyEntity(conn.from_entity_name || '') || this.isMyEntity(conn.to_entity_name || '')
      );
      const otherWorkerConnections = allConnections.filter(conn => 
        this.belongsToOtherWorker(conn.from_entity_name || '') || this.belongsToOtherWorker(conn.to_entity_name || '')
      );
      
      crossWorkerItemsSkipped += otherWorkerConnections.length;

      console.log(`  Found ${myConnections.length} connections for this worker, ${otherWorkerConnections.length} for other workers`);

      // Step 3: Safety check - detect isolation violations
      const violatingConnections = allConnections.filter(conn => {
        const fromIsMine = this.isMyEntity(conn.from_entity_name || '');
        const toIsMine = this.isMyEntity(conn.to_entity_name || '');
        const fromIsOther = this.belongsToOtherWorker(conn.from_entity_name || '');
        const toIsOther = this.belongsToOtherWorker(conn.to_entity_name || '');
        
        // Violation: connection between my entity and another worker's entity
        return (fromIsMine && toIsOther) || (toIsMine && fromIsOther);
      });
      
      isolationViolations = violatingConnections.length;
      this.isolationMetrics.isolationViolations += isolationViolations;

      if (isolationViolations > 0) {
        console.log(`  ⚠️  Detected ${isolationViolations} cross-worker connections (isolation violations)`);
        violatingConnections.forEach(conn => {
          console.log(`    Violation: ${conn.from_entity_name} → ${conn.to_entity_name}`);
        });
      }

      // Step 4: Clean my connections first (to avoid foreign key violations)
      if (myConnections.length > 0) {
        const connectionResults = await Promise.allSettled(
          myConnections.map(conn => this.deleteConnectionById(conn.id))
        );

        connectionResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            connectionsDeleted++;
            this.isolationMetrics.connectionsCleaned++;
          } else {
            errors.push(`Failed to delete connection ${myConnections[index].id}: ${result.reason}`);
          }
        });
      }

      // Step 5: Clean my entities
      if (myEntities.length > 0) {
        const entityResults = await Promise.allSettled(
          myEntities.map(entity => this.deleteEntityById(entity.id))
        );

        entityResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            entitiesDeleted++;
            this.isolationMetrics.entitiesCleaned++;
          } else {
            errors.push(`Failed to delete entity ${myEntities[index].name} (${myEntities[index].id}): ${result.reason}`);
          }
        });
      }

      const duration = Date.now() - startTime;
      const success = errors.length === 0;

      console.log(`🔒 Isolated cleanup ${success ? 'completed' : 'completed with errors'}: ${entitiesDeleted} entities, ${connectionsDeleted} connections in ${duration}ms`);
      console.log(`  Cross-worker items skipped: ${crossWorkerItemsSkipped}, Violations: ${isolationViolations}`);

      if (errors.length > 0) {
        console.log(`  Errors: ${errors.join('; ')}`);
      }

      return {
        success,
        entitiesDeleted,
        connectionsDeleted,
        crossWorkerItemsSkipped,
        isolationViolations,
        duration,
        errors
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = `Isolated cleanup failed: ${error}`;
      console.error(`❌ ${errorMessage} (${duration}ms)`);

      return {
        success: false,
        entitiesDeleted,
        connectionsDeleted,
        crossWorkerItemsSkipped,
        isolationViolations,
        duration,
        errors: [errorMessage]
      };
    }
  }

  /**
   * Verify worker isolation integrity
   * 
   * Checks that no cross-worker contamination has occurred
   */
  public async verifyIsolationIntegrity(): Promise<{
    intact: boolean;
    issues: string[];
    crossWorkerEntities: number;
    crossWorkerConnections: number;
    isolationViolations: number;
  }> {
    const issues: string[] = [];
    let crossWorkerEntities = 0;
    let crossWorkerConnections = 0;
    let isolationViolations = 0;

    try {
      console.log(`🔍 Verifying isolation integrity for worker ${this.workerId}`);

      // Check entities
      const allEntities = await this.getAllEntities();
      const otherWorkerEntities = allEntities.filter(entity => this.belongsToOtherWorker(entity.name));
      crossWorkerEntities = otherWorkerEntities.length;

      // Check connections
      const allConnections = await this.getAllConnections();
      const otherWorkerConnections = allConnections.filter(conn => 
        this.belongsToOtherWorker(conn.from_entity_name || '') || this.belongsToOtherWorker(conn.to_entity_name || '')
      );
      crossWorkerConnections = otherWorkerConnections.length;

      // Check for isolation violations
      const violatingConnections = allConnections.filter(conn => {
        const fromIsMine = this.isMyEntity(conn.from_entity_name || '');
        const toIsMine = this.isMyEntity(conn.to_entity_name || '');
        const fromIsOther = this.belongsToOtherWorker(conn.from_entity_name || '');
        const toIsOther = this.belongsToOtherWorker(conn.to_entity_name || '');
        
        return (fromIsMine && toIsOther) || (toIsMine && fromIsOther);
      });
      
      isolationViolations = violatingConnections.length;

      // Report issues
      if (crossWorkerEntities > 0) {
        issues.push(`${crossWorkerEntities} entities from other workers detected`);
      }

      if (crossWorkerConnections > 0) {
        issues.push(`${crossWorkerConnections} connections from other workers detected`);
      }

      if (isolationViolations > 0) {
        issues.push(`${isolationViolations} cross-worker connection violations detected`);
        violatingConnections.forEach(conn => {
          issues.push(`  Violation: ${conn.from_entity_name} → ${conn.to_entity_name}`);
        });
      }

      const intact = issues.length === 0;

      console.log(`🔍 Isolation integrity ${intact ? 'verified' : 'compromised'} for worker ${this.workerId}`);
      if (!intact) {
        issues.forEach(issue => console.log(`  ${issue}`));
      }

      return {
        intact,
        issues,
        crossWorkerEntities,
        crossWorkerConnections,
        isolationViolations
      };

    } catch (error) {
      const errorMessage = `Isolation verification failed: ${error}`;
      console.error(`❌ ${errorMessage}`);

      return {
        intact: false,
        issues: [errorMessage],
        crossWorkerEntities: -1,
        crossWorkerConnections: -1,
        isolationViolations: -1
      };
    }
  }

  /**
   * Handle isolation violation recovery
   * 
   * Attempts to resolve cross-worker conflicts
   */
  public async handleIsolationViolations(): Promise<{
    success: boolean;
    violationsResolved: number;
    actions: string[];
    errors: string[];
  }> {
    const actions: string[] = [];
    const errors: string[] = [];
    let violationsResolved = 0;

    console.log(`🚨 Handling isolation violations for worker ${this.workerId}`);

    try {
      // Find all cross-worker connections
      const allConnections = await this.getAllConnections();
      const violatingConnections = allConnections.filter(conn => {
        const fromIsMine = this.isMyEntity(conn.from_entity_name || '');
        const toIsMine = this.isMyEntity(conn.to_entity_name || '');
        const fromIsOther = this.belongsToOtherWorker(conn.from_entity_name || '');
        const toIsOther = this.belongsToOtherWorker(conn.to_entity_name || '');
        
        return (fromIsMine && toIsOther) || (toIsMine && fromIsOther);
      });

      console.log(`  Found ${violatingConnections.length} violating connections`);

      // Delete violating connections
      for (const conn of violatingConnections) {
        try {
          await this.deleteConnectionById(conn.id);
          violationsResolved++;
          actions.push(`Deleted violating connection: ${conn.from_entity_name} → ${conn.to_entity_name}`);
        } catch (error) {
          errors.push(`Failed to delete violating connection ${conn.id}: ${error}`);
        }
      }

      const success = errors.length === 0;

      console.log(`🚨 Isolation violation handling ${success ? 'completed' : 'completed with errors'}: ${violationsResolved} violations resolved`);

      return {
        success,
        violationsResolved,
        actions,
        errors
      };

    } catch (error) {
      const errorMessage = `Isolation violation handling failed: ${error}`;
      console.error(`❌ ${errorMessage}`);

      return {
        success: false,
        violationsResolved,
        actions,
        errors: [errorMessage]
      };
    }
  }

  /**
   * Get worker isolation metrics
   */
  public getIsolationMetrics(): {
    workerId: string;
    workerPrefix: string;
    entitiesCreated: number;
    connectionsCreated: number;
    entitiesCleaned: number;
    connectionsCleaned: number;
    crossWorkerConflicts: number;
    isolationViolations: number;
  } {
    return {
      workerId: this.workerId,
      workerPrefix: this.workerPrefix,
      ...this.isolationMetrics
    };
  }

  /**
   * Generate isolation report
   */
  public generateIsolationReport(): string {
    const metrics = this.getIsolationMetrics();
    const registration = WorkerCleanupIsolation.workerRegistrations.get(this.workerId);
    
    let report = `\n🔒 Worker Isolation Report (${this.workerId}):\n`;
    report += `  • Worker prefix: "${metrics.workerPrefix}"\n`;
    report += `  • Entities created: ${metrics.entitiesCreated}\n`;
    report += `  • Connections created: ${metrics.connectionsCreated}\n`;
    report += `  • Entities cleaned: ${metrics.entitiesCleaned}\n`;
    report += `  • Connections cleaned: ${metrics.connectionsCleaned}\n`;
    report += `  • Cross-worker conflicts: ${metrics.crossWorkerConflicts}\n`;
    report += `  • Isolation violations: ${metrics.isolationViolations}\n`;
    
    if (registration) {
      const uptime = Date.now() - registration.startTime;
      const lastActivity = Date.now() - registration.lastActivity;
      report += `  • Worker uptime: ${Math.round(uptime / 1000)}s\n`;
      report += `  • Last activity: ${Math.round(lastActivity / 1000)}s ago\n`;
    }
    
    // Health assessment
    if (metrics.isolationViolations === 0 && metrics.crossWorkerConflicts === 0) {
      report += `  • Isolation status: Excellent\n`;
    } else if (metrics.isolationViolations < 5) {
      report += `  • Isolation status: Good (minor violations)\n`;
    } else {
      report += `  • Isolation status: Needs attention (multiple violations)\n`;
    }
    
    return report;
  }

  /**
   * Get all active workers
   */
  public static getActiveWorkers(): Array<{
    workerId: string;
    prefix: string;
    uptime: number;
    lastActivity: number;
  }> {
    const now = Date.now();
    return Array.from(this.workerRegistrations.values()).map(registration => ({
      workerId: registration.workerId,
      prefix: registration.prefix,
      uptime: now - registration.startTime,
      lastActivity: now - registration.lastActivity
    }));
  }

  /**
   * Cleanup worker registration
   */
  public unregisterWorker(): void {
    WorkerCleanupIsolation.workerRegistrations.delete(this.workerId);
    console.log(`🔓 Worker isolation unregistered: ${this.workerId}`);
  }

  // Helper methods for API access
  private async getAllEntities(): Promise<any[]> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/entities');
      if (response.ok()) {
        return await response.json();
      }
      throw new Error(`Failed to fetch entities: ${response.status()}`);
    } catch (error) {
      console.error('Error fetching entities:', error);
      return [];
    }
  }

  private async getAllConnections(): Promise<any[]> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/connections');
      if (response.ok()) {
        return await response.json();
      }
      throw new Error(`Failed to fetch connections: ${response.status()}`);
    } catch (error) {
      console.error('Error fetching connections:', error);
      return [];
    }
  }

  private async deleteEntityById(entityId: string): Promise<void> {
    const response = await this.page.request.delete(`http://localhost:8000/api/v1/entities/${entityId}`);
    if (!response.ok() && response.status() !== 404) {
      throw new Error(`HTTP ${response.status()}: ${await response.text().catch(() => 'Unknown error')}`);
    }
  }

  private async deleteConnectionById(connectionId: string): Promise<void> {
    const response = await this.page.request.delete(`http://localhost:8000/api/v1/connections/${connectionId}`);
    if (!response.ok() && response.status() !== 404) {
      throw new Error(`HTTP ${response.status()}: ${await response.text().catch(() => 'Unknown error')}`);
    }
  }
}