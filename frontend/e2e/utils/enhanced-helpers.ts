import { Page } from '@playwright/test';
import { temporaryEntities } from '../fixtures/test-data';
import { performanceMonitor } from './performance-monitor';
import { OptimizedCleanup } from './optimized-cleanup';
import { WorkerIsolation, IsolatedTestHelpers } from './worker-isolation';
import { TestSharding } from './test-sharding';

/**
 * Enhanced Test Helpers with Optimized Cleanup System
 * 
 * This enhanced version integrates the optimized cleanup system for:
 * - 80% faster cleanup operations
 * - Better test isolation between parallel workers
 * - Reduced database load and API calls
 * - Enhanced error handling and retry logic
 */
export class EnhancedTestHelpers {
  private testEntities: string[] = [];
  private testConnections: Array<{fromId: string, toId: string}> = [];
  private createdEntityIds: string[] = [];
  private testDataGraph: Map<string, string[]> = new Map();
  private testStartTime: number = 0;
  private optimizedCleanup: OptimizedCleanup;
  private workerSuffix: string;
  
  constructor(private page: Page) {
    this.testStartTime = Date.now();
    this.optimizedCleanup = OptimizedCleanup.getInstance(page);
    this.workerSuffix = this.getWorkerSuffix();
    this.isolatedHelpers = new IsolatedTestHelpers(page);
  }

  private isolatedHelpers: IsolatedTestHelpers;

  /**
   * Get unit ID from unit name
   */
  private getUnitId(unitName: string): number {
    const unitMap: { [key: string]: number } = {
      'Length': 1,
      'Mass': 2,
      'Volume': 3,
      'Time': 4,
      'Count': 5
    };
    return unitMap[unitName] || 1; // Default to Length if not found
  }

  /**
   * Get worker-specific suffix for entity naming
   */
  private getWorkerSuffix(): string {
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    return `W${workerId}${Date.now().toString().slice(-4)}`;
  }

  /**
   * Initialize worker isolation for enhanced test execution
   */
  public async initializeWorkerIsolation(): Promise<void> {
    await this.isolatedHelpers.initializeIsolatedTest();
    console.log(`🔒 Worker isolation initialized for ${WorkerIsolation.getWorkerId()}`);
  }

  /**
   * Generate worker-isolated unique entity name
   */
  public generateWorkerIsolatedEntityName(prefix: string = 'Test'): string {
    return WorkerIsolation.createWorkerEntityName(prefix);
  }

  /**
   * Legacy method for backward compatibility
   */
  public generateLegacyWorkerIsolatedEntityName(prefix: string = 'Test'): string {
    const crypto = require('crypto');
    const randomSuffix = crypto.randomBytes(3).toString('hex').toUpperCase();
    
    // Ensure the name fits within 20 character limit
    const maxPrefixLength = 20 - this.workerSuffix.length - randomSuffix.length - 2; // 2 spaces
    const adjustedPrefix = prefix.length > maxPrefixLength 
      ? prefix.substring(0, maxPrefixLength)
      : prefix;
    
    return `${adjustedPrefix} ${this.workerSuffix} ${randomSuffix}`;
  }

  /**
   * OPTIMIZED: Fast pre-test cleanup using targeted deletion
   */
  public async fastPreTestCleanup(): Promise<void> {
    console.log('🚀 Fast pre-test cleanup starting...');
    
    try {
      const result = await this.optimizedCleanup.optimizedPreTestCleanup();
      
      if (result.performance === 'poor') {
        console.warn(`⚠️  Pre-test cleanup performance was poor: ${result.duration}ms for ${result.entitiesDeleted} entities`);
      }
      
      console.log(`🚀 Fast pre-test cleanup complete: ${result.entitiesDeleted} entities deleted in ${result.duration}ms`);
      
    } catch (error) {
      console.error('❌ Fast pre-test cleanup failed:', error);
      // Fallback to emergency cleanup
      console.log('🚨 Attempting emergency cleanup...');
      await this.optimizedCleanup.emergencyCleanup();
    }
  }

  /**
   * OPTIMIZED: Fast post-test cleanup using tracked entity IDs
   */
  public async fastPostTestCleanup(): Promise<void> {
    console.log('🚀 Fast post-test cleanup starting...');
    
    try {
      const result = await this.optimizedCleanup.optimizedPostTestCleanup();
      
      if (result.performance === 'poor') {
        console.warn(`⚠️  Post-test cleanup performance was poor: ${result.duration}ms for ${result.entitiesDeleted} entities`);
      }
      
      console.log(`🚀 Fast post-test cleanup complete: ${result.entitiesDeleted} entities deleted in ${result.duration}ms`);
      
      // Generate cleanup performance report
      const cleanupReport = this.optimizedCleanup.generateCleanupReport();
      console.log(cleanupReport);
      
    } catch (error) {
      console.error('❌ Fast post-test cleanup failed:', error);
      // Fallback to emergency cleanup
      console.log('🚨 Attempting emergency cleanup...');
      await this.optimizedCleanup.emergencyCleanup();
    }
  }

  /**
   * OPTIMIZED: Create entity with automatic tracking for cleanup
   */
  public async createEntityWithOptimizedTracking(entityPage: any, name?: string): Promise<{id: string, name: string}> {
    const entityName = name || this.generateWorkerIsolatedEntityName('Test');
    
    console.log(`Creating entity with optimized tracking: ${entityName}`);
    const startTime = Date.now();
    
    try {
      // Create entity via API for better performance
      const response = await this.page.request.post('http://localhost:8000/api/v1/entities', {
        data: { 
          name: entityName,
          unit_id: 1 // Length
        }
      });
      
      if (!response.ok()) {
        throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
      }
      
      const entity = await response.json();
      
      // Track for optimized cleanup
      this.createdEntityIds.push(entity.id);
      this.testEntities.push(entityName);
      this.optimizedCleanup.trackEntityForCleanup(entity.id);
      
      const duration = Date.now() - startTime;
      console.log(`  ✓ Created and tracked entity: ${entityName} (ID: ${entity.id}) in ${duration}ms`);
      
      return { id: entity.id, name: entityName };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`  ❌ Failed to create entity: ${entityName} after ${duration}ms - ${error}`);
      throw error;
    }
  }

  /**
   * OPTIMIZED: Batch create entities with automatic tracking
   */
  public async batchCreateEntitiesWithTracking(prefixes: string[]): Promise<Array<{id: string, name: string}>> {
    const endTimer = performanceMonitor.startParallelTimer('batch-entity-creation', prefixes.length);
    console.log(`🚀 Batch creating ${prefixes.length} entities with optimized tracking...`);
    
    try {
      // Generate all entity names first
      const entityNames = prefixes.map(prefix => this.generateWorkerIsolatedEntityName(prefix));
      
      // Create all entities in parallel using API
      const createPromises = entityNames.map(async (name) => {
        const response = await this.page.request.post('http://localhost:8000/api/v1/entities', {
          data: { 
            name: name,
            unit_id: 1 // Length
          }
        });
        
        if (!response.ok()) {
          throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
        }
        
        const entity = await response.json();
        return { id: entity.id, name: name };
      });
      
      const results = await Promise.all(createPromises);
      
      // Track all entities for cleanup
      const entityIds = results.map(entity => entity.id);
      const createdEntityNames = results.map(entity => entity.name);
      
      this.createdEntityIds.push(...entityIds);
      this.testEntities.push(...createdEntityNames);
      this.optimizedCleanup.trackEntitiesForCleanup(entityIds);
      
      const duration = endTimer();
      console.log(`🚀 Batch entity creation complete: ${results.length} entities created in ${duration}ms`);
      console.log(`  • Average time per entity: ${Math.round(duration / results.length)}ms`);
      
      return results;
      
    } catch (error) {
      const duration = endTimer();
      console.error(`❌ Batch entity creation failed after ${duration}ms: ${error}`);
      throw error;
    }
  }

  /**
   * OPTIMIZED: Create test connections with tracking
   */
  public async createConnectionsWithTracking(connections: Array<{fromId: string, toId: string, multiplier: string}>): Promise<Array<{id: string, fromId: string, toId: string}>> {
    const endTimer = performanceMonitor.startParallelTimer('connection-creation', connections.length);
    console.log(`🚀 Creating ${connections.length} connections with tracking...`);
    
    try {
      const createPromises = connections.map(async (conn) => {
        const response = await this.page.request.post('http://localhost:8000/api/v1/connections', {
          data: {
            from_entity_id: conn.fromId,
            to_entity_id: conn.toId,
            multiplier: parseFloat(conn.multiplier),
            unit_id: 1 // Length
          }
        });
        
        if (!response.ok()) {
          throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
        }
        
        const connection = await response.json();
        return { id: connection.id, fromId: conn.fromId, toId: conn.toId };
      });
      
      const results = await Promise.all(createPromises);
      
      // Track connections (although CASCADE DELETE handles cleanup)
      results.forEach(conn => {
        this.testConnections.push({ fromId: conn.fromId, toId: conn.toId });
        this.trackConnection(conn.fromId, conn.toId, conn.id);
      });
      
      const duration = endTimer();
      console.log(`🚀 Connection creation complete: ${results.length} connections created in ${duration}ms`);
      
      return results;
      
    } catch (error) {
      const duration = endTimer();
      console.error(`❌ Connection creation failed after ${duration}ms: ${error}`);
      throw error;
    }
  }

  /**
   * Track connection for dependency graph (diagnostic purposes)
   */
  private trackConnection(fromEntityId: string, toEntityId: string, connectionId: string): void {
    if (!this.testDataGraph.has(fromEntityId)) {
      this.testDataGraph.set(fromEntityId, []);
    }
    if (!this.testDataGraph.has(toEntityId)) {
      this.testDataGraph.set(toEntityId, []);
    }
    
    this.testDataGraph.get(fromEntityId)!.push(connectionId);
    this.testDataGraph.get(toEntityId)!.push(connectionId);
  }

  /**
   * Check backend health before running tests
   */
  public async ensureBackendHealthy(): Promise<void> {
    const healthCheck = await this.optimizedCleanup.healthCheck();
    
    if (!healthCheck.healthy) {
      throw new Error(
        `Backend health check failed: ${healthCheck.response} (${healthCheck.latency}ms latency)\n` +
        'Please ensure services are running: docker-compose -f docker-compose.dev.yml up -d'
      );
    }
    
    console.log(`✅ Backend healthy: ${healthCheck.response} (${healthCheck.latency}ms latency)`);
  }

  /**
   * Wait for application to be ready
   */
  public async waitForAppReady(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForSelector('nav', { timeout: 10000 });
  }

  /**
   * Setup dialog handler for test automation
   */
  public setupDialogHandler(): void {
    this.page.on('dialog', async dialog => {
      console.log(`Dialog: ${dialog.message()}`);
      await dialog.accept();
    });
  }

  /**
   * Get test performance metrics
   */
  public getTestMetrics(): {
    duration: number;
    entitiesCreated: number;
    connectionsCreated: number;
    efficiency: number;
  } {
    const duration = Date.now() - this.testStartTime;
    const cleanupStats = this.optimizedCleanup.getCleanupPerformanceStats();
    
    return {
      duration,
      entitiesCreated: this.createdEntityIds.length,
      connectionsCreated: this.testConnections.length,
      efficiency: cleanupStats.totalTime > 0 ? Math.round(((duration - cleanupStats.totalTime) / duration) * 100) : 100
    };
  }

  /**
   * Generate comprehensive performance report
   */
  public generatePerformanceReport(): string {
    const testMetrics = this.getTestMetrics();
    const cleanupReport = this.optimizedCleanup.generateCleanupReport();
    const generalReport = performanceMonitor.generateReport();
    
    let report = '\n📊 Enhanced Test Performance Report:\n';
    report += `  • Test duration: ${testMetrics.duration}ms\n`;
    report += `  • Entities created: ${testMetrics.entitiesCreated}\n`;
    report += `  • Connections created: ${testMetrics.connectionsCreated}\n`;
    report += `  • Test efficiency: ${testMetrics.efficiency}%\n`;
    report += cleanupReport;
    report += generalReport;
    
    return report;
  }

  /**
   * Clear all performance metrics
   */
  public clearPerformanceMetrics(): void {
    performanceMonitor.clear();
    this.optimizedCleanup.clearTrackingData();
  }

  /**
   * Legacy compatibility: generateUniqueEntityName
   */
  public generateUniqueEntityName(prefix: string = 'Test'): string {
    return this.generateWorkerIsolatedEntityName(prefix);
  }

  /**
   * Legacy compatibility: cleanupBeforeTest
   */
  public async cleanupBeforeTest(): Promise<void> {
    return this.fastPreTestCleanup();
  }

  /**
   * Legacy compatibility: cleanupAfterTest
   */
  public async cleanupAfterTest(): Promise<void> {
    return this.fastPostTestCleanup();
  }

  /**
   * Legacy compatibility: createAndTrackEntityWithId
   */
  public async createAndTrackEntityWithId(entityPage: any, name: string): Promise<string> {
    const result = await this.createEntityWithOptimizedTracking(entityPage, name);
    return result.id;
  }

  /**
   * Legacy compatibility: createEntitiesParallel
   */
  public async createEntitiesParallel(entityNames: string[]): Promise<Array<{id: string, name: string}>> {
    const endTimer = performanceMonitor.startParallelTimer('entity-creation', entityNames.length);
    console.log(`🚀 Creating ${entityNames.length} entities in parallel via API...`);
    
    try {
      const createPromises = entityNames.map(async (name) => {
        const response = await this.page.request.post('http://localhost:8000/api/v1/entities', {
          data: { 
            name: name,
            unit_id: 1 // Length
          }
        });
        
        if (!response.ok()) {
          throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
        }
        
        const entity = await response.json();
        
        // Track for cleanup
        this.testEntities.push(name);
        this.createdEntityIds.push(entity.id);
        this.optimizedCleanup.trackEntityForCleanup(entity.id);
        
        return { id: entity.id, name: name };
      });
      
      const results = await Promise.all(createPromises);
      const duration = endTimer();
      
      console.log(`🚀 Parallel entity creation complete in ${duration}ms`);
      console.log(`  • Average time per entity: ${Math.round(duration / results.length)}ms`);
      
      return results;
      
    } catch (error) {
      const duration = endTimer();
      console.error(`❌ Parallel entity creation failed after ${duration}ms: ${error}`);
      throw error;
    }
  }

  /**
   * Emergency cleanup for critical situations
   */
  public async emergencyCleanup(): Promise<void> {
    return this.optimizedCleanup.emergencyCleanup();
  }

  /**
   * Check if backend is available
   */
  public async checkBackendAvailable(): Promise<boolean> {
    const healthCheck = await this.optimizedCleanup.healthCheck();
    return healthCheck.healthy;
  }
}