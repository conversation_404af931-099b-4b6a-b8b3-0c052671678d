import { Page } from '@playwright/test';
import { performanceMonitor } from './performance-monitor';

/**
 * Cleanup Monitoring and Performance Validation System
 * 
 * This system provides comprehensive monitoring and validation of cleanup operations:
 * - Real-time cleanup performance tracking
 * - Success/failure rate monitoring
 * - Performance bottleneck detection
 * - Cleanup efficiency analysis
 * - Automated performance alerts
 * - Historical trend analysis
 * 
 * Key Features:
 * - Multi-metric performance tracking
 * - Automated threshold alerting
 * - Performance trend analysis
 * - Bottleneck identification
 * - Efficiency optimization recommendations
 */
export class CleanupMonitoring {
  private static instance: CleanupMonitoring;
  private performanceMetrics: Map<string, {
    totalOperations: number;
    totalTime: number;
    successCount: number;
    failureCount: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    lastOperation: number;
    operationHistory: Array<{
      timestamp: number;
      duration: number;
      success: boolean;
      itemsProcessed: number;
      operationType: string;
    }>;
  }> = new Map();

  private alertThresholds = {
    maxAverageTime: 5000,      // 5 seconds
    maxSingleOperation: 30000,  // 30 seconds
    minSuccessRate: 85,         // 85%
    maxFailureStreak: 3,        // 3 consecutive failures
    performanceDegradation: 50  // 50% slower than baseline
  };

  private currentSession: {
    startTime: number;
    totalOperations: number;
    totalCleanupTime: number;
    totalItemsCleaned: number;
    alerts: Array<{
      timestamp: number;
      type: 'performance' | 'reliability' | 'efficiency';
      severity: 'low' | 'medium' | 'high';
      message: string;
      details: any;
    }>;
  };

  private constructor(private page: Page) {
    this.currentSession = {
      startTime: Date.now(),
      totalOperations: 0,
      totalCleanupTime: 0,
      totalItemsCleaned: 0,
      alerts: []
    };
  }

  public static getInstance(page: Page): CleanupMonitoring {
    if (!CleanupMonitoring.instance) {
      CleanupMonitoring.instance = new CleanupMonitoring(page);
    }
    return CleanupMonitoring.instance;
  }

  /**
   * Start monitoring a cleanup operation
   */
  public startCleanupOperation(operationType: string): {
    operationId: string;
    endOperation: (success: boolean, itemsProcessed: number, errors?: string[]) => void;
  } {
    const operationId = `${operationType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    console.log(`📊 Monitoring started: ${operationType} (ID: ${operationId})`);

    const endOperation = (success: boolean, itemsProcessed: number, errors: string[] = []) => {
      const duration = Date.now() - startTime;
      
      this.recordOperation(operationType, {
        operationId,
        duration,
        success,
        itemsProcessed,
        errors
      });

      console.log(`📊 Monitoring completed: ${operationType} - ${success ? 'SUCCESS' : 'FAILURE'} in ${duration}ms (${itemsProcessed} items)`);
    };

    return { operationId, endOperation };
  }

  /**
   * Record a completed cleanup operation
   */
  private recordOperation(operationType: string, operation: {
    operationId: string;
    duration: number;
    success: boolean;
    itemsProcessed: number;
    errors: string[];
  }): void {
    // Update session metrics
    this.currentSession.totalOperations++;
    this.currentSession.totalCleanupTime += operation.duration;
    this.currentSession.totalItemsCleaned += operation.itemsProcessed;

    // Get or create metrics for this operation type
    let metrics = this.performanceMetrics.get(operationType);
    if (!metrics) {
      metrics = {
        totalOperations: 0,
        totalTime: 0,
        successCount: 0,
        failureCount: 0,
        averageTime: 0,
        minTime: Infinity,
        maxTime: 0,
        lastOperation: 0,
        operationHistory: []
      };
      this.performanceMetrics.set(operationType, metrics);
    }

    // Update metrics
    metrics.totalOperations++;
    metrics.totalTime += operation.duration;
    metrics.lastOperation = Date.now();

    if (operation.success) {
      metrics.successCount++;
    } else {
      metrics.failureCount++;
    }

    // Update timing statistics
    metrics.minTime = Math.min(metrics.minTime, operation.duration);
    metrics.maxTime = Math.max(metrics.maxTime, operation.duration);
    metrics.averageTime = metrics.totalTime / metrics.totalOperations;

    // Add to history (keep last 100 operations)
    metrics.operationHistory.push({
      timestamp: Date.now(),
      duration: operation.duration,
      success: operation.success,
      itemsProcessed: operation.itemsProcessed,
      operationType
    });

    if (metrics.operationHistory.length > 100) {
      metrics.operationHistory = metrics.operationHistory.slice(-100);
    }

    // Check for performance alerts
    this.checkPerformanceAlerts(operationType, operation, metrics);

    // Update performance monitor
    performanceMonitor.recordMetric(`cleanup-${operationType}`, operation.duration);
  }

  /**
   * Check for performance alerts and issues
   */
  private checkPerformanceAlerts(
    operationType: string, 
    operation: any, 
    metrics: any
  ): void {
    const now = Date.now();

    // Check for slow operation
    if (operation.duration > this.alertThresholds.maxSingleOperation) {
      this.addAlert({
        type: 'performance',
        severity: 'high',
        message: `Slow cleanup operation: ${operationType} took ${operation.duration}ms`,
        details: { operationType, duration: operation.duration, threshold: this.alertThresholds.maxSingleOperation }
      });
    }

    // Check for poor average performance
    if (metrics.averageTime > this.alertThresholds.maxAverageTime) {
      this.addAlert({
        type: 'performance',
        severity: 'medium',
        message: `Poor average performance: ${operationType} averaging ${Math.round(metrics.averageTime)}ms`,
        details: { operationType, averageTime: metrics.averageTime, threshold: this.alertThresholds.maxAverageTime }
      });
    }

    // Check for low success rate
    const successRate = (metrics.successCount / metrics.totalOperations) * 100;
    if (successRate < this.alertThresholds.minSuccessRate) {
      this.addAlert({
        type: 'reliability',
        severity: 'high',
        message: `Low success rate: ${operationType} at ${Math.round(successRate)}%`,
        details: { operationType, successRate, threshold: this.alertThresholds.minSuccessRate }
      });
    }

    // Check for failure streak
    const recentOperations = metrics.operationHistory.slice(-this.alertThresholds.maxFailureStreak);
    if (recentOperations.length >= this.alertThresholds.maxFailureStreak) {
      const allFailed = recentOperations.every((op: any) => !op.success);
      if (allFailed) {
        this.addAlert({
          type: 'reliability',
          severity: 'high',
          message: `Failure streak detected: ${this.alertThresholds.maxFailureStreak} consecutive ${operationType} failures`,
          details: { operationType, failureStreak: this.alertThresholds.maxFailureStreak }
        });
      }
    }

    // Check for performance degradation
    if (metrics.operationHistory.length >= 10) {
      const recent = metrics.operationHistory.slice(-5);
      const baseline = metrics.operationHistory.slice(-15, -10);
      
      const recentAvg = recent.reduce((sum: number, op: any) => sum + op.duration, 0) / recent.length;
      const baselineAvg = baseline.reduce((sum: number, op: any) => sum + op.duration, 0) / baseline.length;
      
      const degradationPercent = ((recentAvg - baselineAvg) / baselineAvg) * 100;
      
      if (degradationPercent > this.alertThresholds.performanceDegradation) {
        this.addAlert({
          type: 'performance',
          severity: 'medium',
          message: `Performance degradation: ${operationType} is ${Math.round(degradationPercent)}% slower`,
          details: { 
            operationType, 
            degradationPercent, 
            recentAvg: Math.round(recentAvg), 
            baselineAvg: Math.round(baselineAvg) 
          }
        });
      }
    }

    // Check for efficiency issues
    if (operation.itemsProcessed > 0) {
      const timePerItem = operation.duration / operation.itemsProcessed;
      if (timePerItem > 200) { // More than 200ms per item
        this.addAlert({
          type: 'efficiency',
          severity: 'medium',
          message: `Low efficiency: ${operationType} taking ${Math.round(timePerItem)}ms per item`,
          details: { operationType, timePerItem, itemsProcessed: operation.itemsProcessed }
        });
      }
    }
  }

  /**
   * Add an alert to the current session
   */
  private addAlert(alert: {
    type: 'performance' | 'reliability' | 'efficiency';
    severity: 'low' | 'medium' | 'high';
    message: string;
    details: any;
  }): void {
    const fullAlert = {
      timestamp: Date.now(),
      ...alert
    };

    this.currentSession.alerts.push(fullAlert);

    // Log alert based on severity
    const icon = alert.severity === 'high' ? '🚨' : alert.severity === 'medium' ? '⚠️' : 'ℹ️';
    console.log(`${icon} Cleanup Alert [${alert.type}/${alert.severity}]: ${alert.message}`);

    // Keep only last 50 alerts
    if (this.currentSession.alerts.length > 50) {
      this.currentSession.alerts = this.currentSession.alerts.slice(-50);
    }
  }

  /**
   * Get performance summary for a specific operation type
   */
  public getOperationPerformance(operationType: string): {
    exists: boolean;
    totalOperations: number;
    successRate: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    efficiency: 'excellent' | 'good' | 'poor';
    trend: 'improving' | 'stable' | 'degrading';
    recommendations: string[];
  } {
    const metrics = this.performanceMetrics.get(operationType);
    
    if (!metrics) {
      return {
        exists: false,
        totalOperations: 0,
        successRate: 0,
        averageTime: 0,
        minTime: 0,
        maxTime: 0,
        efficiency: 'poor',
        trend: 'stable',
        recommendations: [`No data available for ${operationType}`]
      };
    }

    const successRate = (metrics.successCount / metrics.totalOperations) * 100;
    const efficiency = this.assessEfficiency(metrics.averageTime, operationType);
    const trend = this.analyzeTrend(metrics.operationHistory);
    const recommendations = this.generateRecommendations(operationType, metrics);

    return {
      exists: true,
      totalOperations: metrics.totalOperations,
      successRate,
      averageTime: metrics.averageTime,
      minTime: metrics.minTime === Infinity ? 0 : metrics.minTime,
      maxTime: metrics.maxTime,
      efficiency,
      trend,
      recommendations
    };
  }

  /**
   * Assess operation efficiency
   */
  private assessEfficiency(averageTime: number, operationType: string): 'excellent' | 'good' | 'poor' {
    // Different thresholds for different operation types
    const thresholds = {
      'pre-test-cleanup': { excellent: 1000, good: 3000 },
      'post-test-cleanup': { excellent: 1500, good: 4000 },
      'complete-cleanup': { excellent: 2000, good: 6000 },
      'entity-deletion': { excellent: 500, good: 2000 },
      'connection-deletion': { excellent: 300, good: 1000 },
      'default': { excellent: 1000, good: 3000 }
    };

    const threshold = thresholds[operationType as keyof typeof thresholds] || thresholds.default;

    if (averageTime <= threshold.excellent) return 'excellent';
    if (averageTime <= threshold.good) return 'good';
    return 'poor';
  }

  /**
   * Analyze performance trend
   */
  private analyzeTrend(history: any[]): 'improving' | 'stable' | 'degrading' {
    if (history.length < 6) return 'stable';

    // Compare recent half vs older half
    const midpoint = Math.floor(history.length / 2);
    const older = history.slice(0, midpoint);
    const recent = history.slice(midpoint);

    const olderAvg = older.reduce((sum, op) => sum + op.duration, 0) / older.length;
    const recentAvg = recent.reduce((sum, op) => sum + op.duration, 0) / recent.length;

    const change = ((recentAvg - olderAvg) / olderAvg) * 100;

    if (change < -10) return 'improving';
    if (change > 10) return 'degrading';
    return 'stable';
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(operationType: string, metrics: any): string[] {
    const recommendations: string[] = [];
    const successRate = (metrics.successCount / metrics.totalOperations) * 100;

    // Success rate recommendations
    if (successRate < 90) {
      recommendations.push(`Improve reliability: ${operationType} has ${Math.round(successRate)}% success rate`);
    }

    // Performance recommendations
    if (metrics.averageTime > 5000) {
      recommendations.push(`Optimize performance: ${operationType} averaging ${Math.round(metrics.averageTime)}ms`);
    }

    // Efficiency recommendations
    const recentOps = metrics.operationHistory.slice(-10);
    const avgItemsPerOp = recentOps.reduce((sum: number, op: any) => sum + op.itemsProcessed, 0) / recentOps.length;
    const timePerItem = metrics.averageTime / (avgItemsPerOp || 1);

    if (timePerItem > 100) {
      recommendations.push(`Improve efficiency: ${Math.round(timePerItem)}ms per item is slow`);
    }

    // Consistency recommendations
    const variance = this.calculateVariance(metrics.operationHistory.map((op: any) => op.duration));
    if (variance > metrics.averageTime) {
      recommendations.push(`Improve consistency: ${operationType} has high timing variance`);
    }

    if (recommendations.length === 0) {
      recommendations.push(`${operationType} performance is optimal`);
    }

    return recommendations;
  }

  /**
   * Calculate variance for timing consistency analysis
   */
  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  }

  /**
   * Get current session summary
   */
  public getSessionSummary(): {
    duration: number;
    totalOperations: number;
    totalCleanupTime: number;
    totalItemsCleaned: number;
    averageOperationTime: number;
    efficiency: 'excellent' | 'good' | 'poor';
    alerts: {
      total: number;
      high: number;
      medium: number;
      low: number;
    };
    operationTypes: string[];
    overallHealth: 'healthy' | 'warning' | 'critical';
  } {
    const sessionDuration = Date.now() - this.currentSession.startTime;
    const averageOperationTime = this.currentSession.totalOperations > 0 
      ? this.currentSession.totalCleanupTime / this.currentSession.totalOperations 
      : 0;

    const efficiency = this.assessEfficiency(averageOperationTime, 'session-average');
    
    const alertCounts = {
      total: this.currentSession.alerts.length,
      high: this.currentSession.alerts.filter(a => a.severity === 'high').length,
      medium: this.currentSession.alerts.filter(a => a.severity === 'medium').length,
      low: this.currentSession.alerts.filter(a => a.severity === 'low').length
    };

    const operationTypes = Array.from(this.performanceMetrics.keys());

    // Assess overall health
    let overallHealth: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (alertCounts.high > 0) {
      overallHealth = 'critical';
    } else if (alertCounts.medium > 2 || alertCounts.total > 5) {
      overallHealth = 'warning';
    }

    return {
      duration: sessionDuration,
      totalOperations: this.currentSession.totalOperations,
      totalCleanupTime: this.currentSession.totalCleanupTime,
      totalItemsCleaned: this.currentSession.totalItemsCleaned,
      averageOperationTime,
      efficiency,
      alerts: alertCounts,
      operationTypes,
      overallHealth
    };
  }

  /**
   * Generate comprehensive monitoring report
   */
  public generateMonitoringReport(): string {
    const summary = this.getSessionSummary();
    const sessionHours = summary.duration / (1000 * 60 * 60);

    let report = '\n📊 Cleanup Monitoring Report:\n';
    report += `  • Session duration: ${Math.round(summary.duration / 1000)}s\n`;
    report += `  • Total operations: ${summary.totalOperations}\n`;
    report += `  • Total cleanup time: ${summary.totalCleanupTime}ms\n`;
    report += `  • Total items cleaned: ${summary.totalItemsCleaned}\n`;
    
    if (summary.totalOperations > 0) {
      report += `  • Average operation time: ${Math.round(summary.averageOperationTime)}ms\n`;
      report += `  • Cleanup efficiency: ${summary.efficiency}\n`;
      report += `  • Operations per hour: ${Math.round(summary.totalOperations / Math.max(sessionHours, 0.1))}\n`;
    }

    report += `\n📈 Performance by Operation Type:\n`;
    for (const operationType of summary.operationTypes) {
      const perf = this.getOperationPerformance(operationType);
      report += `  • ${operationType}:\n`;
      report += `    - Operations: ${perf.totalOperations}\n`;
      report += `    - Success rate: ${Math.round(perf.successRate)}%\n`;
      report += `    - Average time: ${Math.round(perf.averageTime)}ms\n`;
      report += `    - Efficiency: ${perf.efficiency}\n`;
      report += `    - Trend: ${perf.trend}\n`;
    }

    report += `\n🚨 Alerts Summary:\n`;
    report += `  • Total alerts: ${summary.alerts.total}\n`;
    report += `  • High severity: ${summary.alerts.high}\n`;
    report += `  • Medium severity: ${summary.alerts.medium}\n`;
    report += `  • Low severity: ${summary.alerts.low}\n`;

    if (this.currentSession.alerts.length > 0) {
      report += `\n⚠️  Recent Alerts:\n`;
      const recentAlerts = this.currentSession.alerts.slice(-5);
      recentAlerts.forEach(alert => {
        const timeAgo = Math.round((Date.now() - alert.timestamp) / 1000);
        report += `  • [${alert.severity}] ${alert.message} (${timeAgo}s ago)\n`;
      });
    }

    report += `\n🎯 Overall Health: ${summary.overallHealth.toUpperCase()}\n`;

    return report;
  }

  /**
   * Reset session metrics
   */
  public resetSession(): void {
    this.currentSession = {
      startTime: Date.now(),
      totalOperations: 0,
      totalCleanupTime: 0,
      totalItemsCleaned: 0,
      alerts: []
    };
    console.log('📊 Cleanup monitoring session reset');
  }

  /**
   * Update alert thresholds
   */
  public updateThresholds(newThresholds: Partial<typeof this.alertThresholds>): void {
    this.alertThresholds = { ...this.alertThresholds, ...newThresholds };
    console.log('📊 Alert thresholds updated', newThresholds);
  }

  /**
   * Get recent alerts
   */
  public getRecentAlerts(count: number = 10): Array<{
    timestamp: number;
    type: 'performance' | 'reliability' | 'efficiency';
    severity: 'low' | 'medium' | 'high';
    message: string;
    details: any;
  }> {
    return this.currentSession.alerts.slice(-count);
  }

  /**
   * Check if system is healthy
   */
  public isHealthy(): boolean {
    const summary = this.getSessionSummary();
    return summary.overallHealth === 'healthy';
  }
}