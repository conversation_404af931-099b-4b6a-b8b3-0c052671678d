import { TestInfo } from '@playwright/test';

/**
 * Test Sharding and Distribution System
 * 
 * Optimizes test distribution across workers for:
 * - Balanced load distribution
 * - Reduced test execution time
 * - Better resource utilization
 * - Minimized worker conflicts
 */
export class TestSharding {
  private static shardingConfig = {
    // Fast tests (< 10 seconds)
    fast: [
      'navigation.spec.ts',
      'setup-verification.spec.ts',
      'error-handling.spec.ts',
    ],
    // Medium tests (10-30 seconds)
    medium: [
      'entities.spec.ts',
      'connections.spec.ts',
      'debug-validation.spec.ts',
    ],
    // Slow tests (> 30 seconds)
    slow: [
      'comparisons.spec.ts',
      'performance-benchmark.spec.ts',
      'cleanup-performance-validation.spec.ts',
      'sequential-entity-creation.spec.ts',
    ]
  };
  
  /**
   * Get optimal test distribution for current worker count
   */
  static getOptimalDistribution(workerCount: number): Map<number, string[]> {
    const distribution = new Map<number, string[]>();
    const allTests = [
      ...this.shardingConfig.fast,
      ...this.shardingConfig.medium,
      ...this.shardingConfig.slow
    ];
    
    if (workerCount === 1) {
      // Single worker gets all tests
      distribution.set(0, allTests);
    } else if (workerCount === 2) {
      // Two workers: balance by complexity
      distribution.set(0, [...this.shardingConfig.fast, ...this.shardingConfig.medium.slice(0, 2)]);
      distribution.set(1, [...this.shardingConfig.medium.slice(2), ...this.shardingConfig.slow]);
    } else if (workerCount === 3) {
      // Three workers: optimal distribution
      distribution.set(0, [...this.shardingConfig.fast, 'entities.spec.ts']); // Worker 0: Fast + Entity tests
      distribution.set(1, ['connections.spec.ts', 'debug-validation.spec.ts', 'comparisons.spec.ts']); // Worker 1: Medium complexity
      distribution.set(2, [...this.shardingConfig.slow.slice(1)]); // Worker 2: Slow tests
    } else {
      // Round-robin distribution for more workers
      for (let i = 0; i < allTests.length; i++) {
        const workerId = i % workerCount;
        if (!distribution.has(workerId)) {
          distribution.set(workerId, []);
        }
        distribution.get(workerId)!.push(allTests[i]);
      }
    }
    
    return distribution;
  }
  
  /**
   * Get test execution priority for current worker
   */
  static getTestPriority(testInfo: TestInfo): 'high' | 'medium' | 'low' {
    const testFile = testInfo.title;
    
    if (this.shardingConfig.fast.some(test => testFile.includes(test))) {
      return 'high';
    } else if (this.shardingConfig.medium.some(test => testFile.includes(test))) {
      return 'medium';
    } else {
      return 'low';
    }
  }
  
  /**
   * Get estimated test duration
   */
  static getEstimatedDuration(testFile: string): number {
    if (this.shardingConfig.fast.some(test => testFile.includes(test))) {
      return 8000; // 8 seconds
    } else if (this.shardingConfig.medium.some(test => testFile.includes(test))) {
      return 20000; // 20 seconds
    } else {
      return 45000; // 45 seconds
    }
  }
  
  /**
   * Should test be retried based on worker load?
   */
  static shouldRetryTest(testInfo: TestInfo, workerIndex: number): boolean {
    const priority = this.getTestPriority(testInfo);
    const retryCount = testInfo.retry;
    
    // High priority tests get more retries
    if (priority === 'high' && retryCount < 2) {
      return true;
    }
    
    // Medium priority tests get standard retries
    if (priority === 'medium' && retryCount < 1) {
      return true;
    }
    
    // Low priority tests get minimal retries
    if (priority === 'low' && retryCount < 1) {
      return true;
    }
    
    return false;
  }
  
  /**
   * Get worker-specific timeout multiplier
   */
  static getWorkerTimeoutMultiplier(workerIndex: number): number {
    // Worker 0: Standard timeouts (fastest)
    // Worker 1: 1.2x timeouts (medium)
    // Worker 2: 1.5x timeouts (handles slow tests)
    const multipliers = [1.0, 1.2, 1.5];
    return multipliers[workerIndex] || 1.0;
  }
  
  /**
   * Get optimization recommendations for current configuration
   */
  static getOptimizationRecommendations(workerCount: number): {
    recommendation: string;
    expectedSpeedup: string;
    riskLevel: 'low' | 'medium' | 'high';
  } {
    if (workerCount === 1) {
      return {
        recommendation: 'Consider increasing to 2-3 workers for better parallelization',
        expectedSpeedup: '50-70% faster execution',
        riskLevel: 'low'
      };
    } else if (workerCount === 2) {
      return {
        recommendation: 'Increase to 3 workers for optimal performance',
        expectedSpeedup: '30-50% faster execution',
        riskLevel: 'low'
      };
    } else if (workerCount === 3) {
      return {
        recommendation: 'Current configuration is optimal for most setups',
        expectedSpeedup: 'Already optimized',
        riskLevel: 'low'
      };
    } else {
      return {
        recommendation: 'Consider reducing workers to 3 for better stability',
        expectedSpeedup: 'More stable execution',
        riskLevel: 'medium'
      };
    }
  }
  
  /**
   * Log current sharding configuration
   */
  static logShardingInfo(workerCount: number): void {
    const distribution = this.getOptimalDistribution(workerCount);
    const recommendations = this.getOptimizationRecommendations(workerCount);
    
    console.log(`📊 Test Sharding Configuration (${workerCount} workers):`);
    
    for (const [workerId, tests] of distribution.entries()) {
      const totalEstimated = tests.reduce((sum, test) => sum + this.getEstimatedDuration(test), 0);
      console.log(`  Worker ${workerId}: ${tests.length} tests (~${Math.round(totalEstimated/1000)}s estimated)`);
      console.log(`    Tests: ${tests.join(', ')}`);
    }
    
    console.log(`  Recommendation: ${recommendations.recommendation}`);
    console.log(`  Expected Impact: ${recommendations.expectedSpeedup}`);
    console.log(`  Risk Level: ${recommendations.riskLevel}`);
  }
}

/**
 * Test execution optimizer
 */
export class TestExecutionOptimizer {
  private static executionMetrics: Map<string, {
    averageDuration: number;
    successRate: number;
    lastRun: number;
  }> = new Map();
  
  /**
   * Record test execution metrics
   */
  static recordExecution(testFile: string, duration: number, success: boolean): void {
    const current = this.executionMetrics.get(testFile) || {
      averageDuration: 0,
      successRate: 0,
      lastRun: 0
    };
    
    // Update average duration (exponential moving average)
    current.averageDuration = current.averageDuration * 0.8 + duration * 0.2;
    
    // Update success rate
    current.successRate = current.successRate * 0.9 + (success ? 1 : 0) * 0.1;
    
    // Update last run timestamp
    current.lastRun = Date.now();
    
    this.executionMetrics.set(testFile, current);
  }
  
  /**
   * Get test reliability score
   */
  static getReliabilityScore(testFile: string): number {
    const metrics = this.executionMetrics.get(testFile);
    if (!metrics) return 0.5; // Default neutral score
    
    // Combine success rate and consistency
    const recency = Math.max(0, 1 - (Date.now() - metrics.lastRun) / (24 * 60 * 60 * 1000));
    return metrics.successRate * 0.8 + recency * 0.2;
  }
  
  /**
   * Get performance analytics
   */
  static getPerformanceAnalytics(): {
    totalTests: number;
    averageSuccessRate: number;
    fastestTest: string;
    slowestTest: string;
    recommendations: string[];
  } {
    const metrics = Array.from(this.executionMetrics.entries());
    
    if (metrics.length === 0) {
      return {
        totalTests: 0,
        averageSuccessRate: 0,
        fastestTest: 'none',
        slowestTest: 'none',
        recommendations: ['No execution data available']
      };
    }
    
    const totalTests = metrics.length;
    const averageSuccessRate = metrics.reduce((sum, [, m]) => sum + m.successRate, 0) / totalTests;
    
    const sortedByDuration = metrics.sort((a, b) => a[1].averageDuration - b[1].averageDuration);
    const fastestTest = sortedByDuration[0][0];
    const slowestTest = sortedByDuration[sortedByDuration.length - 1][0];
    
    const recommendations = [];
    
    // Identify problematic tests
    const problematicTests = metrics.filter(([, m]) => m.successRate < 0.8);
    if (problematicTests.length > 0) {
      recommendations.push(`${problematicTests.length} tests have low success rates and need attention`);
    }
    
    // Identify slow tests
    const slowTests = metrics.filter(([, m]) => m.averageDuration > 30000);
    if (slowTests.length > 0) {
      recommendations.push(`${slowTests.length} tests are slow and could benefit from optimization`);
    }
    
    return {
      totalTests,
      averageSuccessRate,
      fastestTest,
      slowestTest,
      recommendations
    };
  }
}