import { Page } from '@playwright/test';

export class EnhancedTestCleanup {
  private static cleanupQueue: Set<string> = new Set();
  private static cleanupLock = false;
  
  // DevOps Phase B.2: Browser-specific cleanup timeouts
  private static getBrowserCleanupTimeout(page: Page): number {
    const browserName = page.context().browser()?.browserType().name();
    
    switch (browserName) {
      case 'chromium':
        return 5000; // Chrome: Fast cleanup
      case 'firefox':  
        return 7000; // Firefox: Moderate cleanup timing
      case 'webkit':
        return 10000; // WebKit: More time for cleanup operations
      default:
        return 7000; // Default fallback
    }
  }
  
  static async cleanupTestEntities(page: Page, maxRetries = 3): Promise<void> {
    // Implement cleanup queue to prevent conflicts
    while (this.cleanupLock) {
      await page.waitForLoadState('domcontentloaded');
    }
    
    this.cleanupLock = true;
    
    try {
      const entities = await this.getTestEntities(page);
      console.log(`EnhancedTestCleanup: Found ${entities.length} test entities to clean up`);
      
      for (const entity of entities) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            await this.deleteEntity(page, entity);
            console.log(`  ✓ Deleted entity: ${entity.name} (ID: ${entity.id})`);
            break;
          } catch (error) {
            if (attempt === maxRetries) {
              console.error(`Failed to delete ${entity.name} after ${maxRetries} attempts:`, error);
            } else {
              console.log(`  Retry ${attempt}/${maxRetries} for entity: ${entity.name}`);
              await page.waitForLoadState('networkidle'); // Wait for previous operation to complete
            }
          }
        }
      }
    } finally {
      this.cleanupLock = false;
    }
  }
  
  private static async getTestEntities(page: Page): Promise<Array<{id: string, name: string}>> {
    try {
      const response = await page.request.get('http://localhost:8000/api/v1/entities');
      if (!response.ok()) {
        throw new Error(`Failed to fetch entities: ${response.status()}`);
      }
      
      const entities = await response.json();
      
      // Filter for test entities using patterns from helpers.ts
      const testPatterns = [
        /Test\s+[A-Z]+/i,  // Matches "Test ABCD", "Test Entity XYZW"
        /Human\s+[A-Z]+/i, // Matches "Human ABCD"
        /Ball\s+[A-Z]+/i,  // Matches "Ball ABCD" 
        /Build\s+[A-Z]+/i, // Matches "Build ABCD"
        /Car\s+[A-Z]+/i,   // Matches "Car ABCD"
        /Eleph\s+[A-Z]+/i, // Matches "Eleph ABCD"
        /Mouse\s+[A-Z]+/i, // Matches "Mouse ABCD"
        /Basketball Test/i,
        /Connection Test/i,
        /Sample Entity/i,
        /Rapid [A-Z]/i,    // Matches rapid test entities
        /Delete/i,         // Matches delete test entities
        /Duplicate/i,      // Matches duplicate test entities
        /Refresh/i,        // Matches refresh test entities
        /Edit Updated/i,   // Matches edit test entities
        /Large Building/i, // Matches large test entities
        /Small Object/i,   // Matches small test entities
        /Multi From/i,     // Matches multi-connection test entities
        /Multi To/i,       // Matches multi-connection test entities
        /Large Num/i,      // Matches large number test entities
        /Sci Not/i,        // Matches scientific notation test entities
        /Concurrent/i      // Matches concurrent test entities
      ];
      
      return entities.filter((entity: any) => 
        testPatterns.some(pattern => pattern.test(entity.name))
      );
      
    } catch (error) {
      console.error(`Error fetching test entities: ${error}`);
      return [];
    }
  }
  
  private static async deleteEntity(page: Page, entity: {id: string, name: string}): Promise<void> {
    const response = await page.request.delete(`http://localhost:8000/api/v1/entities/${entity.id}`);
    
    if (!response.ok()) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`Failed to delete entity ${entity.name} (ID: ${entity.id}): ${response.status()} - ${errorText}`);
    }
  }
  
  /**
   * Quick cleanup for immediate use - removes obvious test entities
   */
  static async quickCleanup(page: Page): Promise<number> {
    try {
      const entities = await this.getTestEntities(page);
      let deleted = 0;
      
      for (const entity of entities) {
        try {
          await this.deleteEntity(page, entity);
          deleted++;
        } catch (error) {
          console.log(`Quick cleanup failed for ${entity.name}: ${error}`);
        }
      }
      
      return deleted;
    } catch (error) {
      console.error(`Quick cleanup error: ${error}`);
      return 0;
    }
  }
  
  /**
   * Verify cleanup was successful
   */
  static async verifyCleanup(page: Page): Promise<boolean> {
    try {
      const remainingTestEntities = await this.getTestEntities(page);
      const cleanupSuccessful = remainingTestEntities.length === 0;
      
      if (!cleanupSuccessful) {
        console.warn(`Cleanup verification failed: ${remainingTestEntities.length} test entities remain`);
        remainingTestEntities.forEach(entity => {
          console.warn(`  Remaining: ${entity.name} (ID: ${entity.id})`);
        });
      }
      
      return cleanupSuccessful;
    } catch (error) {
      console.error(`Cleanup verification error: ${error}`);
      return false;
    }
  }
}