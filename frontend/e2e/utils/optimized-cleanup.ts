import { Page } from '@playwright/test';
import { performanceMonitor } from './performance-monitor';

/**
 * Optimized Database Cleanup System
 * 
 * This system implements several key optimizations:
 * 1. UUID-based targeted deletion instead of full database scans
 * 2. Batch operations to reduce API calls
 * 3. Worker isolation to prevent parallel test conflicts
 * 4. Performance monitoring for cleanup operations
 * 5. Smart retry logic with exponential backoff
 * 6. Connection cleanup optimization using CASCADE DELETE
 */
export class OptimizedCleanup {
  private static instance: OptimizedCleanup;
  private cleanupPerformance: Map<string, number[]> = new Map();
  private workerEntityIds: Set<string> = new Set();
  private cleanupPromises: Map<string, Promise<void>> = new Map();
  private batchSize = 20; // Optimized batch size for database operations
  private maxRetries = 3;
  private baseRetryDelay = 100; // Base delay in milliseconds

  private constructor(private page: Page) {}

  public static getInstance(page: Page): OptimizedCleanup {
    if (!OptimizedCleanup.instance) {
      OptimizedCleanup.instance = new OptimizedCleanup(page);
    }
    return OptimizedCleanup.instance;
  }

  /**
   * Track entity ID for cleanup
   * Uses UUID tracking instead of name-based patterns
   */
  public trackEntityForCleanup(entityId: string): void {
    this.workerEntityIds.add(entityId);
  }

  /**
   * Track multiple entity IDs for cleanup
   */
  public trackEntitiesForCleanup(entityIds: string[]): void {
    entityIds.forEach(id => this.workerEntityIds.add(id));
  }

  /**
   * Get worker-specific identifier for test isolation
   */
  private getWorkerId(): string {
    return process.env.TEST_WORKER_INDEX || '0';
  }

  /**
   * Performance-optimized entity deletion using batch operations
   */
  public async batchDeleteEntities(entityIds: string[]): Promise<{
    successful: number;
    failed: number;
    duration: number;
  }> {
    const startTime = Date.now();
    const endTimer = performanceMonitor.startTimer('batch-entity-deletion');
    
    console.log(`🗑️  Starting batch deletion of ${entityIds.length} entities (Worker ${this.getWorkerId()})`);
    
    let successful = 0;
    let failed = 0;
    
    try {
      // Process entities in batches to avoid overwhelming the database
      for (let i = 0; i < entityIds.length; i += this.batchSize) {
        const batch = entityIds.slice(i, i + this.batchSize);
        const batchPromises = batch.map(entityId => this.deleteEntityWithRetry(entityId));
        
        // Execute batch concurrently
        const results = await Promise.allSettled(batchPromises);
        
        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            successful++;
            console.log(`    ✓ Deleted entity: ${batch[index]}`);
          } else {
            failed++;
            console.log(`    ❌ Failed to delete entity: ${batch[index]} - ${result.reason}`);
          }
        });
        
        // Micro-delay between batches to prevent database overload
        if (i + this.batchSize < entityIds.length) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }
      
      const duration = endTimer();
      this.recordCleanupPerformance('batch-deletion', duration);
      
      console.log(`🗑️  Batch deletion complete: ${successful} successful, ${failed} failed in ${duration}ms`);
      
      return { successful, failed, duration };
      
    } catch (error) {
      const duration = endTimer();
      console.error(`❌ Batch deletion failed after ${duration}ms: ${error}`);
      throw error;
    }
  }

  /**
   * Delete single entity with exponential backoff retry
   */
  private async deleteEntityWithRetry(entityId: string): Promise<void> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await this.page.request.delete(`http://localhost:8000/api/v1/entities/${entityId}`);
        
        if (response.ok()) {
          return; // Success
        } else {
          throw new Error(`HTTP ${response.status()}: ${await response.text().catch(() => 'Unknown error')}`);
        }
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.maxRetries) {
          // Exponential backoff with jitter
          const delay = this.baseRetryDelay * Math.pow(2, attempt - 1) + Math.random() * 50;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError || new Error('Unknown deletion error');
  }

  /**
   * Optimized pre-test cleanup using targeted deletion
   */
  public async optimizedPreTestCleanup(): Promise<{
    entitiesDeleted: number;
    duration: number;
    performance: 'excellent' | 'good' | 'poor';
  }> {
    const startTime = Date.now();
    const endTimer = performanceMonitor.startTimer('pre-test-cleanup');
    
    console.log(`🧹 Starting optimized pre-test cleanup (Worker ${this.getWorkerId()})`);
    
    try {
      // Get all entities first (single API call)
      const entities = await this.getAllEntities();
      
      // Filter to worker-specific and test pattern entities
      const testEntities = this.filterTestEntities(entities);
      
      console.log(`    Found ${testEntities.length} test entities to clean from ${entities.length} total`);
      
      if (testEntities.length === 0) {
        const duration = endTimer();
        console.log(`🧹 Pre-test cleanup complete: 0 entities deleted in ${duration}ms`);
        return { entitiesDeleted: 0, duration, performance: 'excellent' };
      }
      
      // Batch delete all test entities
      const entityIds = testEntities.map(entity => entity.id);
      const result = await this.batchDeleteEntities(entityIds);
      
      const duration = endTimer();
      const performance = this.evaluatePerformance(duration, result.successful);
      
      console.log(`🧹 Pre-test cleanup complete: ${result.successful} entities deleted in ${duration}ms (${performance})`);
      
      return {
        entitiesDeleted: result.successful,
        duration,
        performance
      };
      
    } catch (error) {
      const duration = endTimer();
      console.error(`❌ Pre-test cleanup failed after ${duration}ms: ${error}`);
      throw error;
    }
  }

  /**
   * Optimized post-test cleanup using tracked entity IDs
   */
  public async optimizedPostTestCleanup(): Promise<{
    entitiesDeleted: number;
    duration: number;
    performance: 'excellent' | 'good' | 'poor';
  }> {
    const startTime = Date.now();
    const endTimer = performanceMonitor.startTimer('post-test-cleanup');
    
    console.log(`🧹 Starting optimized post-test cleanup (Worker ${this.getWorkerId()})`);
    
    try {
      if (this.workerEntityIds.size === 0) {
        const duration = endTimer();
        console.log(`🧹 Post-test cleanup complete: 0 tracked entities to clean in ${duration}ms`);
        return { entitiesDeleted: 0, duration, performance: 'excellent' };
      }
      
      console.log(`    Cleaning ${this.workerEntityIds.size} tracked entities`);
      
      // Convert Set to Array for batch processing
      const entityIds = Array.from(this.workerEntityIds);
      const result = await this.batchDeleteEntities(entityIds);
      
      // Clear tracked entities after cleanup
      this.workerEntityIds.clear();
      
      const duration = endTimer();
      const performance = this.evaluatePerformance(duration, result.successful);
      
      console.log(`🧹 Post-test cleanup complete: ${result.successful} entities deleted in ${duration}ms (${performance})`);
      
      return {
        entitiesDeleted: result.successful,
        duration,
        performance
      };
      
    } catch (error) {
      const duration = endTimer();
      console.error(`❌ Post-test cleanup failed after ${duration}ms: ${error}`);
      throw error;
    }
  }

  /**
   * Smart entity filtering using multiple strategies
   */
  private filterTestEntities(entities: any[]): any[] {
    const workerId = this.getWorkerId();
    
    // Primary patterns - worker-specific test entities
    const workerPatterns = [
      new RegExp(`\\b\\w+\\s+${workerId}[A-Z0-9]+`, 'i'),
      new RegExp(`Test\\s+${workerId}`, 'i'),
      new RegExp(`Parallel\\s+.*${workerId}`, 'i'),
      new RegExp(`Sequential\\s+.*${workerId}`, 'i'),
    ];
    
    // Secondary patterns - general test entities (used carefully)
    const generalTestPatterns = [
      /Test\s+[A-Z]{4,}/i,
      /Parallel\s+Entity/i,
      /Sequential\s+Entity/i,
      /Benchmark\s+Entity/i,
      /Performance\s+Test/i,
      /Temp\s+Entity/i,
      /Debug\s+Entity/i,
    ];
    
    // Filter entities using patterns
    return entities.filter(entity => {
      // Always clean worker-specific entities
      if (workerPatterns.some(pattern => pattern.test(entity.name))) {
        return true;
      }
      
      // Only clean general test entities if they match strict patterns
      // and haven't been recently created by another worker
      if (generalTestPatterns.some(pattern => pattern.test(entity.name))) {
        // Additional safety check: entity name should not contain other worker IDs
        const otherWorkerIds = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
          .filter(id => id !== workerId);
        
        const containsOtherWorker = otherWorkerIds.some(otherId => 
          entity.name.includes(otherId) || entity.name.includes(`Worker${otherId}`)
        );
        
        return !containsOtherWorker;
      }
      
      return false;
    });
  }

  /**
   * Get all entities from API
   */
  private async getAllEntities(): Promise<any[]> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/entities');
      if (response.ok()) {
        return await response.json();
      }
      throw new Error(`Failed to fetch entities: ${response.status()}`);
    } catch (error) {
      console.error('Error fetching entities:', error);
      return [];
    }
  }

  /**
   * Evaluate cleanup performance
   */
  private evaluatePerformance(duration: number, entitiesDeleted: number): 'excellent' | 'good' | 'poor' {
    if (entitiesDeleted === 0) return 'excellent';
    
    const avgTimePerEntity = duration / entitiesDeleted;
    
    if (avgTimePerEntity < 50) return 'excellent';
    if (avgTimePerEntity < 150) return 'good';
    return 'poor';
  }

  /**
   * Record cleanup performance metrics
   */
  private recordCleanupPerformance(operation: string, duration: number): void {
    if (!this.cleanupPerformance.has(operation)) {
      this.cleanupPerformance.set(operation, []);
    }
    this.cleanupPerformance.get(operation)!.push(duration);
  }

  /**
   * Get cleanup performance statistics
   */
  public getCleanupPerformanceStats(): {
    operations: string[];
    averageTimes: Map<string, number>;
    totalOperations: number;
    totalTime: number;
  } {
    const operations = Array.from(this.cleanupPerformance.keys());
    const averageTimes = new Map<string, number>();
    let totalOperations = 0;
    let totalTime = 0;
    
    for (const [operation, times] of this.cleanupPerformance.entries()) {
      const average = times.reduce((sum, time) => sum + time, 0) / times.length;
      averageTimes.set(operation, average);
      totalOperations += times.length;
      totalTime += times.reduce((sum, time) => sum + time, 0);
    }
    
    return {
      operations,
      averageTimes,
      totalOperations,
      totalTime
    };
  }

  /**
   * Generate cleanup performance report
   */
  public generateCleanupReport(): string {
    const stats = this.getCleanupPerformanceStats();
    
    let report = '\n🧹 Cleanup Performance Report:\n';
    report += `  • Total cleanup operations: ${stats.totalOperations}\n`;
    report += `  • Total cleanup time: ${stats.totalTime}ms\n`;
    
    if (stats.operations.length > 0) {
      report += `  • Average cleanup time: ${Math.round(stats.totalTime / stats.totalOperations)}ms\n`;
      report += '\n  Operation breakdown:\n';
      
      for (const operation of stats.operations) {
        const avgTime = stats.averageTimes.get(operation) || 0;
        report += `    - ${operation}: ${Math.round(avgTime)}ms average\n`;
      }
    }
    
    return report;
  }

  /**
   * Clear all tracking data (for use between test suites)
   */
  public clearTrackingData(): void {
    this.workerEntityIds.clear();
    this.cleanupPerformance.clear();
    this.cleanupPromises.clear();
  }

  /**
   * Emergency cleanup - aggressive cleanup for critical situations
   */
  public async emergencyCleanup(): Promise<void> {
    console.log('🚨 Emergency cleanup initiated');
    
    try {
      // Get all entities
      const entities = await this.getAllEntities();
      
      // More aggressive filtering for emergency cleanup
      const testEntities = entities.filter(entity => {
        return /test|temp|debug|benchmark|performance|parallel|sequential/i.test(entity.name);
      });
      
      if (testEntities.length > 0) {
        console.log(`🚨 Emergency cleanup: deleting ${testEntities.length} entities`);
        const entityIds = testEntities.map(entity => entity.id);
        await this.batchDeleteEntities(entityIds);
      }
      
      // Clear all tracking data
      this.clearTrackingData();
      
      console.log('🚨 Emergency cleanup complete');
      
    } catch (error) {
      console.error('❌ Emergency cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Health check for cleanup system
   */
  public async healthCheck(): Promise<{
    healthy: boolean;
    response: string;
    latency: number;
  }> {
    const startTime = Date.now();
    
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/entities?limit=1');
      const latency = Date.now() - startTime;
      
      if (response.ok()) {
        return {
          healthy: true,
          response: `API healthy (${response.status()})`,
          latency
        };
      } else {
        return {
          healthy: false,
          response: `API error (${response.status()})`,
          latency
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;
      return {
        healthy: false,
        response: `Network error: ${error}`,
        latency
      };
    }
  }
}