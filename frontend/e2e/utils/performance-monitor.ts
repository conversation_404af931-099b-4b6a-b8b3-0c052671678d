// Enhanced performance monitoring utility for E2E tests with parallel vs sequential analysis
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  private parallelMetrics: Map<string, {duration: number, count: number, avgPerItem: number}[]> = new Map();
  private sequentialMetrics: Map<string, {duration: number, count: number, avgPerItem: number}[]> = new Map();
  private comparisons: Map<string, {parallel: number[], sequential: number[], speedupRatios: number[]}> = new Map();

  recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    this.metrics.get(operation)!.push(duration);
    console.log(`Performance: ${operation} completed in ${duration}ms`);
  }

  /**
   * Record parallel operation performance
   */
  recordParallelOperation(operation: string, duration: number, itemCount: number): void {
    if (!this.parallelMetrics.has(operation)) {
      this.parallelMetrics.set(operation, []);
    }
    
    const avgPerItem = itemCount > 0 ? duration / itemCount : duration;
    this.parallelMetrics.get(operation)!.push({
      duration,
      count: itemCount,
      avgPerItem
    });
    
    console.log(`🚀 Parallel ${operation}: ${duration}ms for ${itemCount} items (${avgPerItem.toFixed(1)}ms/item)`);
  }

  /**
   * Record sequential operation performance
   */
  recordSequentialOperation(operation: string, duration: number, itemCount: number): void {
    if (!this.sequentialMetrics.has(operation)) {
      this.sequentialMetrics.set(operation, []);
    }
    
    const avgPerItem = itemCount > 0 ? duration / itemCount : duration;
    this.sequentialMetrics.get(operation)!.push({
      duration,
      count: itemCount,
      avgPerItem
    });
    
    console.log(`📋 Sequential ${operation}: ${duration}ms for ${itemCount} items (${avgPerItem.toFixed(1)}ms/item)`);
  }

  /**
   * Compare parallel vs sequential performance for the same operation
   */
  comparePerformance(operation: string, parallelDuration: number, sequentialDuration: number, itemCount: number): number {
    const speedupRatio = sequentialDuration / parallelDuration;
    
    if (!this.comparisons.has(operation)) {
      this.comparisons.set(operation, {
        parallel: [],
        sequential: [],
        speedupRatios: []
      });
    }
    
    const comparison = this.comparisons.get(operation)!;
    comparison.parallel.push(parallelDuration);
    comparison.sequential.push(sequentialDuration);
    comparison.speedupRatios.push(speedupRatio);
    
    console.log(`⚡ Performance Comparison for ${operation}:`);
    console.log(`  • Parallel: ${parallelDuration}ms for ${itemCount} items (${(parallelDuration/itemCount).toFixed(1)}ms/item)`);
    console.log(`  • Sequential: ${sequentialDuration}ms for ${itemCount} items (${(sequentialDuration/itemCount).toFixed(1)}ms/item)`);
    console.log(`  • Speedup: ${speedupRatio.toFixed(2)}x faster with parallel approach`);
    
    return speedupRatio;
  }

  /**
   * Start timing an operation
   */
  startTimer(operation: string): () => void {
    const startTime = Date.now();
    return () => {
      const duration = Date.now() - startTime;
      this.recordMetric(operation, duration);
      return duration;
    };
  }

  /**
   * Start timing a parallel operation
   */
  startParallelTimer(operation: string, itemCount: number): () => number {
    const startTime = Date.now();
    return () => {
      const duration = Date.now() - startTime;
      this.recordParallelOperation(operation, duration, itemCount);
      return duration;
    };
  }

  /**
   * Start timing a sequential operation
   */
  startSequentialTimer(operation: string, itemCount: number): () => number {
    const startTime = Date.now();
    return () => {
      const duration = Date.now() - startTime;
      this.recordSequentialOperation(operation, duration, itemCount);
      return duration;
    };
  }

  getAverageTime(operation: string): number {
    const times = this.metrics.get(operation) || [];
    if (times.length === 0) return 0;
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  getMetrics(operation: string): number[] {
    return this.metrics.get(operation) || [];
  }

  /**
   * Get average speedup ratio for an operation
   */
  getAverageSpeedup(operation: string): number {
    const comparison = this.comparisons.get(operation);
    if (!comparison || comparison.speedupRatios.length === 0) return 0;
    
    return comparison.speedupRatios.reduce((sum, ratio) => sum + ratio, 0) / comparison.speedupRatios.length;
  }

  /**
   * Generate comprehensive performance report including parallel comparisons
   */
  generateReport(): string {
    let report = '\n📊 Enhanced Performance Report:\n';
    
    // Standard metrics
    if (this.metrics.size > 0) {
      report += '\n🔹 General Operations:\n';
      for (const [operation, times] of this.metrics.entries()) {
        const avg = this.getAverageTime(operation);
        const min = Math.min(...times);
        const max = Math.max(...times);
        report += `  ${operation}: avg ${avg.toFixed(0)}ms (min: ${min}ms, max: ${max}ms, samples: ${times.length})\n`;
      }
    }

    // Parallel operations
    if (this.parallelMetrics.size > 0) {
      report += '\n🚀 Parallel Operations:\n';
      for (const [operation, metrics] of this.parallelMetrics.entries()) {
        const avgDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
        const avgPerItem = metrics.reduce((sum, m) => sum + m.avgPerItem, 0) / metrics.length;
        const totalItems = metrics.reduce((sum, m) => sum + m.count, 0);
        report += `  ${operation}: avg ${avgDuration.toFixed(0)}ms total, ${avgPerItem.toFixed(1)}ms/item (${totalItems} items, ${metrics.length} samples)\n`;
      }
    }

    // Sequential operations
    if (this.sequentialMetrics.size > 0) {
      report += '\n📋 Sequential Operations:\n';
      for (const [operation, metrics] of this.sequentialMetrics.entries()) {
        const avgDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
        const avgPerItem = metrics.reduce((sum, m) => sum + m.avgPerItem, 0) / metrics.length;
        const totalItems = metrics.reduce((sum, m) => sum + m.count, 0);
        report += `  ${operation}: avg ${avgDuration.toFixed(0)}ms total, ${avgPerItem.toFixed(1)}ms/item (${totalItems} items, ${metrics.length} samples)\n`;
      }
    }

    // Performance comparisons
    if (this.comparisons.size > 0) {
      report += '\n⚡ Parallel vs Sequential Comparisons:\n';
      for (const [operation, comparison] of this.comparisons.entries()) {
        const avgSpeedup = this.getAverageSpeedup(operation);
        const minSpeedup = Math.min(...comparison.speedupRatios);
        const maxSpeedup = Math.max(...comparison.speedupRatios);
        const samples = comparison.speedupRatios.length;
        
        const avgParallel = comparison.parallel.reduce((sum, d) => sum + d, 0) / comparison.parallel.length;
        const avgSequential = comparison.sequential.reduce((sum, d) => sum + d, 0) / comparison.sequential.length;
        
        report += `  ${operation}:\n`;
        report += `    • Average speedup: ${avgSpeedup.toFixed(2)}x faster (${minSpeedup.toFixed(1)}x - ${maxSpeedup.toFixed(1)}x, ${samples} samples)\n`;
        report += `    • Parallel avg: ${avgParallel.toFixed(0)}ms vs Sequential avg: ${avgSequential.toFixed(0)}ms\n`;
        report += `    • Time saved per operation: ${(avgSequential - avgParallel).toFixed(0)}ms\n`;
      }
    }

    // Summary statistics
    if (this.comparisons.size > 0) {
      const allSpeedups = Array.from(this.comparisons.values()).flatMap(c => c.speedupRatios);
      const overallAvgSpeedup = allSpeedups.reduce((sum, ratio) => sum + ratio, 0) / allSpeedups.length;
      const totalTimeSaved = Array.from(this.comparisons.values()).reduce((total, comparison) => {
        const avgParallel = comparison.parallel.reduce((sum, d) => sum + d, 0) / comparison.parallel.length;
        const avgSequential = comparison.sequential.reduce((sum, d) => sum + d, 0) / comparison.sequential.length;
        return total + (avgSequential - avgParallel) * comparison.parallel.length;
      }, 0);
      
      report += '\n📈 Overall Performance Gains:\n';
      report += `  • Average speedup across all operations: ${overallAvgSpeedup.toFixed(2)}x\n`;
      report += `  • Total time saved through parallelization: ${totalTimeSaved.toFixed(0)}ms\n`;
      report += `  • Performance efficiency gain: ${((overallAvgSpeedup - 1) * 100).toFixed(1)}%\n`;
    }

    return report;
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
    this.parallelMetrics.clear();
    this.sequentialMetrics.clear();
    this.comparisons.clear();
  }

  /**
   * Export performance data for analysis
   */
  exportData(): {
    general: Map<string, number[]>,
    parallel: Map<string, any[]>,
    sequential: Map<string, any[]>,
    comparisons: Map<string, any>
  } {
    return {
      general: new Map(this.metrics),
      parallel: new Map(this.parallelMetrics),
      sequential: new Map(this.sequentialMetrics),
      comparisons: new Map(this.comparisons)
    };
  }
}

export const performanceMonitor = new PerformanceMonitor();