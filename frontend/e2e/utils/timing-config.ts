/**
 * Enhanced Timing Configuration for E2E Tests
 * Addresses UI rendering timing issues and synchronization problems
 */

export interface TimingConfig {
  // API-UI Synchronization
  apiToUiSyncDelay: number;
  entityCreationTimeout: number;
  connectionCreationTimeout: number;
  
  // Autocomplete Component
  autocompleteDropdownTimeout: number;
  autocompleteOptionTimeout: number;
  autocompleteRetryDelay: number;
  
  // Form Validation
  formValidationTimeout: number;
  submitButtonTimeout: number;
  errorMessageTimeout: number;
  
  // UI Refresh
  pageLoadTimeout: number;
  networkIdleTimeout: number;
  elementVisibilityTimeout: number;
  
  // Progressive Timeouts
  baseRetryDelay: number;
  maxRetryAttempts: number;
  exponentialBackoffMultiplier: number;
}

/**
 * Browser-specific timing configurations
 * Based on observed performance differences in functional tests
 */
export class BrowserTimingConfig {
  
  static getConfig(browserName?: string): TimingConfig {
    const browser = browserName?.toLowerCase() || 'chromium';
    
    switch (browser) {
      case 'chromium':
      case 'chrome':
        return {
          // API-UI Sync (Chrome is fastest)
          apiToUiSyncDelay: 500,
          entityCreationTimeout: 5000,
          connectionCreationTimeout: 8000,
          
          // Autocomplete (Chrome handles dropdowns well)
          autocompleteDropdownTimeout: 5000,
          autocompleteOptionTimeout: 3000,
          autocompleteRetryDelay: 200,
          
          // Form Validation (Chrome is responsive)
          formValidationTimeout: 3000,
          submitButtonTimeout: 3000,
          errorMessageTimeout: 2000,
          
          // UI Refresh (Chrome is fast)
          pageLoadTimeout: 10000,
          networkIdleTimeout: 5000,
          elementVisibilityTimeout: 5000,
          
          // Progressive Timeouts
          baseRetryDelay: 500,
          maxRetryAttempts: 5,
          exponentialBackoffMultiplier: 1.5
        };
        
      case 'firefox':
        return {
          // API-UI Sync (Firefox is moderate)
          apiToUiSyncDelay: 750,
          entityCreationTimeout: 6000,
          connectionCreationTimeout: 10000,
          
          // Autocomplete (Firefox needs more time)
          autocompleteDropdownTimeout: 7000,
          autocompleteOptionTimeout: 4000,
          autocompleteRetryDelay: 300,
          
          // Form Validation (Firefox is moderate)
          formValidationTimeout: 4000,
          submitButtonTimeout: 4000,
          errorMessageTimeout: 2500,
          
          // UI Refresh (Firefox is moderate)
          pageLoadTimeout: 12000,
          networkIdleTimeout: 6000,
          elementVisibilityTimeout: 6000,
          
          // Progressive Timeouts
          baseRetryDelay: 750,
          maxRetryAttempts: 5,
          exponentialBackoffMultiplier: 1.6
        };
        
      case 'webkit':
      case 'safari':
        return {
          // API-UI Sync (WebKit is slowest)
          apiToUiSyncDelay: 1000,
          entityCreationTimeout: 8000,
          connectionCreationTimeout: 12000,
          
          // Autocomplete (WebKit struggles with dropdowns)
          autocompleteDropdownTimeout: 10000,
          autocompleteOptionTimeout: 6000,
          autocompleteRetryDelay: 500,
          
          // Form Validation (WebKit is slow)
          formValidationTimeout: 6000,
          submitButtonTimeout: 8000,
          errorMessageTimeout: 4000,
          
          // UI Refresh (WebKit is slow)
          pageLoadTimeout: 15000,
          networkIdleTimeout: 8000,
          elementVisibilityTimeout: 8000,
          
          // Progressive Timeouts
          baseRetryDelay: 1000,
          maxRetryAttempts: 6,
          exponentialBackoffMultiplier: 1.8
        };
        
      default:
        // Default fallback configuration
        return this.getConfig('firefox'); // Use Firefox as safe default
    }
  }
  
  /**
   * Get timeout for specific operation with browser adjustment
   */
  static getOperationTimeout(operation: keyof TimingConfig, browserName?: string): number {
    const config = this.getConfig(browserName);
    return config[operation] as number;
  }
  
  /**
   * Calculate progressive timeout for retry attempts
   */
  static getProgressiveTimeout(
    baseTimeout: number, 
    attempt: number, 
    browserName?: string
  ): number {
    const config = this.getConfig(browserName);
    return Math.min(
      baseTimeout * Math.pow(config.exponentialBackoffMultiplier, attempt),
      baseTimeout * 5 // Cap at 5x base timeout
    );
  }
  
  /**
   * Get retry delay with exponential backoff
   */
  static getRetryDelay(attempt: number, browserName?: string): number {
    const config = this.getConfig(browserName);
    return config.baseRetryDelay * Math.pow(config.exponentialBackoffMultiplier, attempt);
  }
}

/**
 * Timing utilities for common test operations
 */
export class TimingUtils {
  
  /**
   * Smart wait for API-UI synchronization
   */
  static async waitForApiUiSync(page: any, browserName?: string): Promise<void> {
    const config = BrowserTimingConfig.getConfig(browserName);
    
    // Wait for network to settle
    await page.waitForLoadState('networkidle', { 
      timeout: config.networkIdleTimeout 
    });
    
    // Additional delay for UI to catch up with API changes
    await page.waitForTimeout(config.apiToUiSyncDelay);
  }
  
  /**
   * Enhanced element visibility wait with progressive timeouts
   */
  static async waitForElementVisible(
    locator: any, 
    attempt: number = 0, 
    browserName?: string
  ): Promise<void> {
    const baseTimeout = BrowserTimingConfig.getOperationTimeout('elementVisibilityTimeout', browserName);
    const timeout = BrowserTimingConfig.getProgressiveTimeout(baseTimeout, attempt, browserName);
    
    await locator.waitFor({ state: 'visible', timeout });
  }
  
  /**
   * Smart form validation wait
   */
  static async waitForFormValidation(page: any, browserName?: string): Promise<void> {
    const timeout = BrowserTimingConfig.getOperationTimeout('formValidationTimeout', browserName);
    
    // Wait for validation to complete
    await page.waitForFunction(() => {
      const forms = document.querySelectorAll('form');
      return Array.from(forms).every(form => !form.classList.contains('validating'));
    }, { timeout });
  }
}
