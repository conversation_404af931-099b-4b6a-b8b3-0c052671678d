import { Page } from '@playwright/test';

/**
 * Worker-Specific Database Isolation System
 * 
 * This system provides:
 * - Database namespace isolation per worker
 * - Concurrent test execution without data conflicts
 * - Worker-specific cleanup strategies
 * - Enhanced resource management
 */
export class WorkerIsolation {
  private static workerPrefixes: Map<string, string> = new Map();
  private static currentWorkerIndex = 0;
  
  /**
   * Get unique worker identifier
   */
  static getWorkerId(): string {
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    const parallelIndex = process.env.TEST_PARALLEL_INDEX || '0';
    return `w${workerId}_p${parallelIndex}`;
  }
  
  /**
   * Get worker-specific database prefix
   */
  static getWorkerPrefix(): string {
    const workerId = this.getWorkerId();
    
    if (!this.workerPrefixes.has(workerId)) {
      // Create unique prefix compatible with backend validation (alphanumeric + spaces only)
      const timestamp = Date.now().toString().slice(-6); // Last 6 digits
      const randomSuffix = Math.random().toString(36).substr(2, 3).toUpperCase();
      const prefix = `TEST W${workerId} ${timestamp} ${randomSuffix}`;
      this.workerPrefixes.set(workerId, prefix);
    }
    
    return this.workerPrefixes.get(workerId)!;
  }
  
  /**
   * Create worker-specific entity name
   */
  static createWorkerEntityName(baseName: string): string {
    const prefix = this.getWorkerPrefix();
    const fullName = `${prefix} ${baseName}`;
    
    // Ensure name fits within 20 character limit and only contains alphanumeric + spaces
    const sanitizedName = fullName.replace(/[^a-zA-Z\s]/g, '').substring(0, 20).trim();
    
    return sanitizedName;
  }
  
  /**
   * Check if entity belongs to current worker
   */
  static isWorkerEntity(entityName: string): boolean {
    const prefix = this.getWorkerPrefix();
    return entityName.startsWith(prefix);
  }
  
  /**
   * Get all entities belonging to current worker
   */
  static async getWorkerEntities(page: Page): Promise<string[]> {
    const prefix = this.getWorkerPrefix();
    
    try {
      // Make API call to get all entities
      const response = await page.request.get('http://localhost:8000/entities');
      const entities = await response.json();
      
      // Filter entities that belong to this worker
      return entities
        .filter((entity: any) => entity.name.startsWith(prefix))
        .map((entity: any) => entity.name);
    } catch (error) {
      console.error(`Failed to get worker entities for ${prefix}:`, error);
      return [];
    }
  }
  
  /**
   * Clean up all entities for current worker
   */
  static async cleanupWorkerEntities(page: Page): Promise<{
    deleted: number;
    failed: number;
    duration: number;
  }> {
    const startTime = Date.now();
    const workerId = this.getWorkerId();
    const prefix = this.getWorkerPrefix();
    
    console.log(`🧹 Starting worker cleanup for ${workerId} (${prefix})`);
    
    let deleted = 0;
    let failed = 0;
    
    try {
      // Get all worker entities
      const workerEntities = await this.getWorkerEntities(page);
      
      console.log(`  Found ${workerEntities.length} entities for worker ${workerId}`);
      
      // Delete entities in batches to avoid overwhelming the backend
      const batchSize = 5;
      for (let i = 0; i < workerEntities.length; i += batchSize) {
        const batch = workerEntities.slice(i, i + batchSize);
        
        await Promise.all(batch.map(async (entityName) => {
          try {
            const response = await page.request.delete(`http://localhost:8000/entities/${entityName}`);
            if (response.ok()) {
              deleted++;
            } else {
              failed++;
              console.warn(`  Failed to delete entity ${entityName}: ${response.status()}`);
            }
          } catch (error) {
            failed++;
            console.warn(`  Error deleting entity ${entityName}:`, error);
          }
        }));
        
        // Brief pause between batches
        await page.waitForTimeout(100);
      }
      
      const duration = Date.now() - startTime;
      console.log(`🧹 Worker cleanup complete for ${workerId}: ${deleted} deleted, ${failed} failed in ${duration}ms`);
      
      return { deleted, failed, duration };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Worker cleanup failed for ${workerId} after ${duration}ms:`, error);
      return { deleted, failed, duration };
    }
  }
  
  /**
   * Initialize worker isolation for a test
   */
  static async initializeWorkerIsolation(page: Page): Promise<void> {
    const workerId = this.getWorkerId();
    const prefix = this.getWorkerPrefix();
    
    console.log(`🔒 Initializing worker isolation for ${workerId} (${prefix})`);
    
    // Set worker-specific headers
    await page.setExtraHTTPHeaders({
      'x-test-worker-id': workerId,
      'x-test-worker-prefix': prefix,
      'x-test-isolation': 'enabled'
    });
  }
  
  /**
   * Validate worker isolation is working
   */
  static async validateWorkerIsolation(page: Page): Promise<boolean> {
    const workerId = this.getWorkerId();
    const prefix = this.getWorkerPrefix();
    
    try {
      // Check that we can identify our own entities
      const workerEntities = await this.getWorkerEntities(page);
      
      // Check that all entities have the correct prefix
      const invalidEntities = workerEntities.filter(name => !name.startsWith(prefix));
      
      if (invalidEntities.length > 0) {
        console.error(`❌ Worker isolation validation failed for ${workerId}: found invalid entities`, invalidEntities);
        return false;
      }
      
      console.log(`✅ Worker isolation validated for ${workerId}: ${workerEntities.length} entities with correct prefix`);
      return true;
      
    } catch (error) {
      console.error(`❌ Worker isolation validation error for ${workerId}:`, error);
      return false;
    }
  }
}

/**
 * Enhanced test helpers with worker isolation
 */
export class IsolatedTestHelpers {
  private workerEntities: string[] = [];
  private testStartTime: number;
  
  constructor(private page: Page) {
    this.testStartTime = Date.now();
  }
  
  /**
   * Initialize isolated test environment
   */
  async initializeIsolatedTest(): Promise<void> {
    await WorkerIsolation.initializeWorkerIsolation(this.page);
    await this.cleanupWorkerEntities();
  }
  
  /**
   * Create entity with worker isolation
   */
  async createIsolatedEntity(baseName: string): Promise<string> {
    const isolatedName = WorkerIsolation.createWorkerEntityName(baseName);
    this.workerEntities.push(isolatedName);
    return isolatedName;
  }
  
  /**
   * Clean up entities for current worker
   */
  async cleanupWorkerEntities(): Promise<void> {
    const result = await WorkerIsolation.cleanupWorkerEntities(this.page);
    
    if (result.failed > 0) {
      console.warn(`⚠️  Worker cleanup had ${result.failed} failures`);
    }
    
    // Clear tracked entities
    this.workerEntities = [];
  }
  
  /**
   * Get test summary with worker isolation info
   */
  getTestSummary(): {
    duration: number;
    workerId: string;
    entitiesTracked: number;
    workerPrefix: string;
  } {
    return {
      duration: Date.now() - this.testStartTime,
      workerId: WorkerIsolation.getWorkerId(),
      entitiesTracked: this.workerEntities.length,
      workerPrefix: WorkerIsolation.getWorkerPrefix()
    };
  }
}