import { Page } from '@playwright/test';
import { temporaryEntities } from '../fixtures/test-data';
import { performanceMonitor } from './performance-monitor';
import { TestLifecycleCleanup, CleanupHelpers } from './test-lifecycle-cleanup';
import { UnifiedDatabaseCleanup } from './unified-database-cleanup';
import { WorkerCleanupIsolation } from './worker-cleanup-isolation';
import { CleanupMonitoring } from './cleanup-monitoring';

export class TestHelpers {
  // Phase B.1: Enhanced test data tracking with dependency management
  private testEntities: string[] = [];
  private testConnections: Array<{fromId: string, toId: string}> = [];
  private createdEntityIds: string[] = [];
  private testDataGraph: Map<string, string[]> = new Map(); // Entity ID -> dependent connection IDs
  private testStartTime: number = 0;
  private testCleanupMetrics: { totalTime: number, entitiesDeleted: number, connectionsDeleted: number } = {
    totalTime: 0,
    entitiesDeleted: 0,
    connectionsDeleted: 0
  };
  
  // New unified cleanup system integration
  private cleanupHelpers: CleanupHelpers;
  private workerIsolation: WorkerCleanupIsolation;
  private cleanupMonitoring: CleanupMonitoring;
  
  // DevOps Phase B.2: Browser-specific test operation timeouts
  private getBrowserTestTimeout(): number {
    const browserName = this.page.context().browser()?.browserType().name();
    
    switch (browserName) {
      case 'chromium':
        return 5000; // Chrome: Fast operations
      case 'firefox':  
        return 7000; // Firefox: Moderate timing
      case 'webkit':
        return 10000; // WebKit: More time for test operations
      default:
        return 7000; // Default fallback
    }
  }
  
  constructor(private page: Page) {
    this.testStartTime = Date.now();
    // Initialize new cleanup systems
    this.cleanupHelpers = new CleanupHelpers(page);
    this.workerIsolation = new WorkerCleanupIsolation(page);
    this.cleanupMonitoring = CleanupMonitoring.getInstance(page);
  }

  /**
   * Wait for the application to fully load
   */
  async waitForAppReady() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForSelector('nav', { timeout: 10000 });
  }

  /**
   * Clean up test entities created during test runs
   */
  async cleanupTestEntities() {
    await this.page.goto('/entities');
    await this.waitForAppReady();

    for (const entityName of temporaryEntities) {
      try {
        const deleteButton = this.page.locator(`button:near(:text("${entityName}")):has-text("Delete")`);
        if (await deleteButton.isVisible()) {
          await deleteButton.click();
          // Handle confirmation dialog
          this.page.on('dialog', dialog => dialog.accept());
          await this.page.waitForTimeout(100); // Brief pause between deletions
        }
      } catch (error) {
        // Entity might not exist, continue cleanup
        console.log(`Could not delete entity ${entityName}:`, error);
      }
    }
  }

  /**
   * Reset the application state for testing
   */
  async resetAppState() {
    await this.cleanupTestEntities();
    await this.page.goto('/');
    await this.waitForAppReady();
  }

  /**
   * Create and track entity for proper cleanup
   */
  async createAndTrackEntity(entityPage: any, name: string) {
    await entityPage.createEntity(name);
    this.testEntities.push(name);
  }

  /**
   * Clean up all test entities created during current test
   */
  async cleanupCurrentTestEntities() {
    if (this.testEntities.length === 0) {
      console.log('No test entities to clean up');
      return;
    }
    
    console.log(`Cleaning up ${this.testEntities.length} test entities`);
    await this.page.goto('/entities');
    await this.waitForAppReady();
    
    // Delete entities in reverse order (last created first)
    const entitiesToDelete = [...this.testEntities].reverse();
    
    for (const entityName of entitiesToDelete) {
      try {
        // Try multiple selectors for delete button
        const deleteSelectors = [
          `[data-testid="delete-entity-${entityName}"]`,
          `button:near(:text("${entityName}")):has-text("Delete")`,
          `button[aria-label="Delete ${entityName}"]`
        ];
        
        let deleted = false;
        for (const selector of deleteSelectors) {
          try {
            const deleteButton = this.page.locator(selector);
            if (await deleteButton.isVisible({ timeout: 2000 })) {
              // Set up response listener before clicking
              const deletePromise = this.page.waitForResponse(response => 
                response.url().includes('/api/v1/entities') && 
                (response.status() === 204 || response.status() === 200)
              , { timeout: 10000 }).catch(() => null);
              
              await deleteButton.click();
              await deletePromise;
              
              // Wait for entity to disappear from DOM
              await this.page.waitForSelector(`:text("${entityName}")`, { state: 'hidden', timeout: 5000 })
                .catch(() => console.log(`Entity ${entityName} may still be visible`));
              
              deleted = true;
              console.log(`Deleted entity: ${entityName}`);
              break;
            }
          } catch (error) {
            // Try next selector
          }
        }
        
        if (!deleted) {
          console.log(`Could not find delete button for entity: ${entityName}`);
        }
      } catch (error) {
        console.log(`Error deleting entity ${entityName}:`, error);
      }
      
      // Wait for deletion to complete before next operation
      await this.page.waitForLoadState('networkidle');
    }
    
    this.testEntities = []; // Reset for next test
    console.log('Test entity cleanup completed');
  }

  /**
   * Generate cryptographically unique entity names with database verification
   * Phase B.1 improvement: True uniqueness with collision detection
   */
  generateUniqueEntityName(prefix: string = 'Test'): string {
    // Generate unique identifier using only letters (to comply with validation)
    const crypto = require('crypto');
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    
    // Convert numbers to letters: 0->A, 1->B, etc.
    const workerLetter = String.fromCharCode(65 + parseInt(workerId)); // A, B, C...
    
    // Generate random letters-only suffix
    const randomBytes = crypto.randomBytes(4);
    let lettersSuffix = '';
    for (let i = 0; i < 4; i++) {
      // Convert each byte to a letter A-Z
      lettersSuffix += String.fromCharCode(65 + (randomBytes[i] % 26));
    }
    
    // Create unique suffix with only letters
    const uniqueSuffix = `${workerLetter}${lettersSuffix}`;
    
    // Calculate available space within 20-character limit
    const maxNameLength = 20;
    const spaceLength = 1;
    const minSuffixLength = 8; // Ensure sufficient uniqueness
    
    // Adjust prefix if too long, ensuring room for space + minimum suffix
    const maxPrefixLength = maxNameLength - spaceLength - minSuffixLength;
    const adjustedPrefix = prefix.length > maxPrefixLength 
      ? prefix.substring(0, maxPrefixLength)
      : prefix;
    
    // Calculate actual available space for suffix
    const availableForSuffix = maxNameLength - adjustedPrefix.length - spaceLength;
    const finalSuffix = uniqueSuffix.substring(0, availableForSuffix);
    
    const name = `${adjustedPrefix} ${finalSuffix}`;
    
    // DevOps Phase B.2: Sanitize name to prevent consecutive spaces validation errors
    const sanitizedName = name.replace(/\s+/g, ' ').trim();
    
    // Ensure exact length compliance
    return sanitizedName.substring(0, maxNameLength);
  }

  /**
   * Generate unique connection name for testing
   */
  generateUniqueConnectionName(prefix: string = 'Test Connection'): string {
    // Generate unique identifier using only letters (to comply with validation)
    const crypto = require('crypto');
    const workerId = process.env.TEST_WORKER_INDEX || '0';

    // Convert numbers to letters: 0->A, 1->B, etc.
    const workerLetter = String.fromCharCode(65 + parseInt(workerId)); // A, B, C...

    // Generate random letters-only suffix
    const randomBytes = crypto.randomBytes(3);
    let lettersSuffix = '';
    for (let i = 0; i < 3; i++) {
      // Convert each byte to a letter A-Z
      lettersSuffix += String.fromCharCode(65 + (randomBytes[i] % 26));
    }

    // Combine: prefix + space + worker + random letters
    const uniqueName = `${prefix} ${workerLetter}${lettersSuffix}`;

    return uniqueName;
  }

  /**
   * Generate unique entity name with database collision detection
   * Phase B.1 enhancement: Verify uniqueness against actual database state
   */
  async generateVerifiedUniqueEntityName(prefix: string = 'Test', maxRetries: number = 5): Promise<string> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const candidateName = this.generateUniqueEntityName(prefix);
      
      // Check if this name already exists in database
      const isUnique = await this.verifyEntityNameUnique(candidateName);
      if (isUnique) {
        console.log(`  ✓ Generated unique entity name: "${candidateName}" (attempt ${attempt})`);
        return candidateName;
      }
      
      console.log(`  ⚠️  Entity name collision detected: "${candidateName}" (attempt ${attempt}/${maxRetries})`);
      
      if (attempt === maxRetries) {
        // Fallback: add additional entropy
        const crypto = require('crypto');
        const extraEntropy = crypto.randomBytes(2).toString('hex').toUpperCase();
        const fallbackName = this.generateUniqueEntityName(`${prefix}${extraEntropy}`);
        console.log(`  🔄 Using fallback name: "${fallbackName}"`);
        return fallbackName;
      }
    }
    
    throw new Error(`Failed to generate unique entity name after ${maxRetries} attempts`);
  }

  /**
   * Verify that an entity name is unique in the database
   * Phase B.1 addition: Database-level uniqueness verification
   */
  async verifyEntityNameUnique(name: string): Promise<boolean> {
    try {
      const entities = await this.getAllEntities();
      const exists = entities.some(entity => entity.name === name);
      return !exists;
    } catch (error) {
      console.warn(`Warning: Could not verify entity name uniqueness: ${error}`);
      // If we can't verify, assume it's unique to avoid blocking tests
      return true;
    }
  }

  /**
   * Generate a unique connection value for testing
   */
  generateUniqueMultiplier(): string {
    return (Math.random() * 10 + 0.1).toFixed(1);
  }

  /**
   * Check if backend services are available
   */
  async checkBackendHealth(): Promise<boolean> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/units');
      return response.ok();
    } catch (error) {
      return false;
    }
  }

  /**
   * Wait for API response to complete
   */
  async waitForApiResponse(urlPattern: string | RegExp, timeout: number = 5000) {
    await this.page.waitForResponse(
      response => {
        const url = response.url();
        if (typeof urlPattern === 'string') {
          return url.includes(urlPattern);
        }
        return urlPattern.test(url);
      },
      { timeout }
    );
  }

  /**
   * Handle dialog confirmations automatically
   */
  setupDialogHandler() {
    this.page.on('dialog', async dialog => {
      console.log(`Dialog: ${dialog.message()}`);
      await dialog.accept();
    });
  }

  /**
   * Take a screenshot with timestamp for debugging
   */
  async takeDebugScreenshot(name: string) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    await this.page.screenshot({ 
      path: `e2e/screenshots/debug-${name}-${timestamp}.png`,
      fullPage: true 
    });
  }

  /**
   * Create multiple entities in parallel via API for improved test performance
   * Enhanced with UI synchronization strategies
   */
  async createEntitiesParallel(entityNames: string[]): Promise<Array<{id: string, name: string}>> {
    console.log(`🚀 Creating ${entityNames.length} entities in parallel via API...`);
    
    const createStartTime = Date.now();
    const createdEntities: Array<{id: string, name: string}> = [];
    
    try {
      // Create entities in parallel via API
      const createPromises = entityNames.map(async (name, index) => {
        const response = await this.page.request.post('http://localhost:8000/api/v1/entities', {
          data: { name: name, unit_id: 1 }
        });
        
        if (!response.ok()) {
          throw new Error(`Failed to create entity ${name}: ${response.status()}`);
        }
        
        const entity = await response.json();
        console.log(`  ✓ [${index + 1}/${entityNames.length}] Created via API: ${name} (ID: ${entity.id})`);
        
        // Track for cleanup
        this.testEntities.push(entity.name);
        this.createdEntityIds.push(entity.id.toString());
        
        return { id: entity.id.toString(), name: entity.name };
      });
      
      const results = await Promise.all(createPromises);
      createdEntities.push(...results);
      
      const createDuration = Date.now() - createStartTime;
      const avgTimePerEntity = createDuration / entityNames.length;
      const sequentialTimeEstimate = entityNames.length * 500; // Estimate for sequential creation
      
      console.log(`🚀 Parallel entity-creation: ${createDuration}ms for ${entityNames.length} items (${avgTimePerEntity.toFixed(1)}ms/item)`);
      console.log(`🚀 Parallel entity creation complete in ${createDuration}ms:`);
      console.log(`  • Created ${entityNames.length} entities concurrently`);
      console.log(`  • Average time per entity: ${Math.round(avgTimePerEntity)}ms`);
      console.log(`  • Speed improvement vs sequential: ~${Math.round(sequentialTimeEstimate / createDuration)}x faster`);
      
      // UI SYNCHRONIZATION STRATEGY 1: Force backend cache refresh
      await this.refreshBackendCache();
      
      // UI SYNCHRONIZATION STRATEGY 2: Navigate to entities page and verify visibility
      console.log('Verifying entities are available in UI...');
      try {
        await this.verifyEntitiesInUI(createdEntities);
        console.log('✅ All entities verified in UI - using parallel creation');
        return createdEntities;
      } catch (verificationError) {
        console.log(`❌ Parallel entity setup failed after ${Date.now() - createStartTime}ms: ${verificationError}`);
        console.log('Falling back to sequential entity creation...');
        
        // UI SYNCHRONIZATION STRATEGY 3: Fallback to sequential creation with UI steps
        return await this.createEntitiesSequentialFallback(entityNames);
      }
      
    } catch (error) {
      console.error(`❌ Parallel entity creation failed: ${error}`);
      console.log('Falling back to sequential entity creation...');
      return await this.createEntitiesSequentialFallback(entityNames);
    }
  }

  /**
   * Force backend cache refresh to ensure UI synchronization
   */
  private async refreshBackendCache(): Promise<void> {
    try {
      // Method 1: Make a fresh API call to force cache refresh
      await this.page.request.get('http://localhost:8000/api/v1/entities?refresh=true');
      
      // Method 2: Add small delay for backend processing
      await this.page.waitForTimeout(100);
    } catch (error) {
      console.warn('Cache refresh failed:', error);
    }
  }

  /**
   * Verify entities are visible in UI with enhanced timing strategies
   */
  private async verifyEntitiesInUI(entities: Array<{id: string, name: string}>): Promise<void> {
    // Navigate to entities page for verification
    await this.page.goto('/entities');
    await this.waitForAppReady();
    
    // STRATEGY 1: Wait for network to settle
    await this.page.waitForLoadState('networkidle');
    
    // STRATEGY 2: Wait for entity list to be populated
    await this.page.waitForSelector('[data-testid="entity-list"], .entity-list', { timeout: 5000 });
    
    // STRATEGY 3: Verify each entity with progressive timeouts
    for (let i = 0; i < entities.length; i++) {
      const entity = entities[i];
      const timeout = 3000 + (i * 200); // Progressive timeout: 3s, 3.2s, 3.4s...
      
      await expect(this.page.locator(`:text("${entity.name}")`)).toBeVisible({ timeout });
    }
  }

  /**
   * Sequential fallback with UI-aware entity creation
   */
  private async createEntitiesSequentialFallback(entityNames: string[]): Promise<Array<{id: string, name: string}>> {
    const createdEntities: Array<{id: string, name: string}> = [];
    
    // Navigate to entities page for UI-based creation
    await this.page.goto('/entities');
    await this.waitForAppReady();
    
    for (let i = 0; i < entityNames.length; i++) {
      const name = entityNames[i];
      console.log(`Creating test entity ${i + 1}/${entityNames.length}: ${name}`);
      
      try {
        // Use the page object method for UI creation
        const tempId = await this.createEntityForComplexTest(null, name);
        createdEntities.push({ id: tempId, name: name });
        
        console.log(`✅ Test entity ${i + 1} created successfully: ${name}`);
      } catch (error) {
        console.error(`❌ Failed to create entity ${name}:`, error);
        throw error;
      }
    }
    
    console.log(`✅ All ${entityNames.length} test entities created successfully for connection tests (fallback)`);
    return createdEntities;
  }

  /**
   * Create entity for complex test scenarios with UI synchronization
   */
  async createEntityForComplexTest(entityPage: any, entityName: string): Promise<string> {
    // If no entityPage provided, use direct method
    if (!entityPage) {
      console.log(`Creating entity for complex test: ${entityName}`);
      
      // Method 1: Try API creation first (faster)
      try {
        const response = await this.page.request.post('http://localhost:8000/api/v1/entities', {
          data: { name: entityName, unit_id: 1 }
        });
        
        if (response.ok()) {
          const entity = await response.json();
          this.testEntities.push(entity.name);
          this.createdEntityIds.push(entity.id.toString());
          
          // Wait for UI to catch up
          await this.page.waitForTimeout(100);
          
          console.log(`  ✅ Complex test entity created: ${entityName} (ID: ${entity.id}) in 0ms`);
          return entity.id.toString();
        }
      } catch (error) {
        console.log(`API creation failed for ${entityName}, falling back to UI creation`);
      }
    }
    
    // Method 2: Fallback to UI creation
    const entityPageInstance = entityPage || new (await import('../fixtures/page-objects')).EntityManagerPage(this.page);
    await entityPageInstance.createEntity(entityName, false);
    this.testEntities.push(entityName);
    
    // Generate temporary ID for tracking
    const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(7)}`;
    this.createdEntityIds.push(tempId);
    
    console.log(`  ✅ Complex test entity created: ${entityName} (ID: ${tempId}) in 0ms`);
    return tempId;
  }

  /**
   * Get console errors from the page
   */
  getConsoleErrors(): string[] {
    const errors: string[] = [];
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    return errors;
  }

  /**
   * Wait for element to be stable (not moving/changing)
   */
  async waitForElementStable(selector: string, timeout: number = 2000) {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible' });
    
    // Wait for element to stop changing position/size
    let previousBox = await element.boundingBox();
    let stableCount = 0;
    const requiredStableChecks = 5;
    
    while (stableCount < requiredStableChecks) {
      await this.page.waitForTimeout(100);
      const currentBox = await element.boundingBox();
      
      if (previousBox && currentBox && 
          previousBox.x === currentBox.x && 
          previousBox.y === currentBox.y &&
          previousBox.width === currentBox.width &&
          previousBox.height === currentBox.height) {
        stableCount++;
      } else {
        stableCount = 0;
      }
      
      previousBox = currentBox;
    }
  }

  /**
   * Retry an action if it fails
   */
  async retryAction<T>(
    action: () => Promise<T>, 
    maxRetries: number = 3, 
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await action();
      } catch (error) {
        lastError = error as Error;
        if (i < maxRetries - 1) {
          await this.page.waitForTimeout(delay);
        }
      }
    }
    
    throw lastError!;
  }

  /**
   * Get unit ID from unit name
   */
  private getUnitId(unitName: string): number {
    const unitMap: { [key: string]: number } = {
      'Length': 1,
      'Mass': 2,
      'Volume': 3,
      'Time': 4,
      'Count': 5
    };
    return unitMap[unitName] || 1; // Default to Length if not found
  }

  /**
   * Check if backend is available and ready for testing
   */
  async checkBackendAvailable(): Promise<boolean> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/health');
      const isAvailable = response.ok();
      if (isAvailable) {
        console.log('✅ Backend is available and ready');
      } else {
        console.error(`❌ Backend health check failed: ${response.status()}`);
      }
      return isAvailable;
    } catch (error) {
      console.error('❌ Backend not available:', error);
      return false;
    }
  }

  /**
   * Ensure backend is available before running tests
   */
  async ensureBackendReady(): Promise<void> {
    const isAvailable = await this.checkBackendAvailable();
    if (!isAvailable) {
      throw new Error(
        'Backend is not available. Please ensure services are running:\n' +
        'docker-compose -f docker-compose.dev.yml up -d'
      );
    }
  }

  /**
   * Get all entities from the API
   */
  async getAllEntities(): Promise<any[]> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/entities');
      if (response.ok()) {
        return await response.json();
      }
      return [];
    } catch (error) {
      console.error('Error fetching entities:', error);
      return [];
    }
  }

  /**
   * Get all connections from the API
   */
  async getAllConnections(): Promise<any[]> {
    try {
      const response = await this.page.request.get('http://localhost:8000/api/v1/connections');
      if (response.ok()) {
        return await response.json();
      }
      return [];
    } catch (error) {
      console.error('Error fetching connections:', error);
      return [];
    }
  }

  /**
   * Delete entity by ID via API
   */
  async deleteEntityById(entityId: string): Promise<boolean> {
    try {
      const response = await this.page.request.delete(`http://localhost:8000/api/v1/entities/${entityId}`);
      return response.ok();
    } catch (error) {
      console.error(`Error deleting entity ${entityId}:`, error);
      return false;
    }
  }

  /**
   * Delete connection via API (DEPRECATED - use entity deletion with CASCADE)
   * Note: Connection deletion is now handled automatically by CASCADE DELETE
   * when entities are deleted. Manual connection deletion is error-prone.
   */
  async deleteConnection(fromEntityId: string, toEntityId: string): Promise<boolean> {
    console.warn('deleteConnection is deprecated - connections are auto-deleted via CASCADE DELETE when entities are removed');
    return true; // Always return success since CASCADE DELETE handles this
  }

  /**
   * Clean all test data before each test (defensive)
   * Phase B.1 improvement: Enhanced pattern matching and worker isolation
   */
  async cleanupBeforeTest(): Promise<void> {
    console.log('🧹 Starting pre-test cleanup...');
    const startTime = Date.now();
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    
    try {
      // Clean entities that match test patterns
      const entities = await this.getAllEntities();
      const initialCount = entities.length;
      console.log(`  Found ${initialCount} total entities in database (Worker ${workerId})`);
      
      // Enhanced test patterns with worker isolation and cryptographic patterns
      const testPatterns = [
        // Original patterns
        /Test\s+[A-Z0-9]+/i,  // Matches "Test ABCD", "Test ABC123"
        /Human\s+[A-Z0-9]+/i, // Matches "Human ABCD", "Human 123ABC"
        /Ball\s+[A-Z0-9]+/i,  // Matches "Ball ABCD" 
        /Build\s+[A-Z0-9]+/i, // Matches "Build ABCD"
        /Car\s+[A-Z0-9]+/i,   // Matches "Car ABCD"
        /Eleph\s+[A-Z0-9]+/i, // Matches "Eleph ABCD"
        /Mouse\s+[A-Z0-9]+/i, // Matches "Mouse ABCD"
        /Multi\s+(From|To)\s+[A-Z0-9]+/i, // Matches "Multi From ABCD", "Multi To XYZW"
        
        // Test scenario patterns
        /Basketball Test/i,
        /Connection Test/i,
        /Sample Entity/i,
        /Rapid [A-Z]/i,    // Matches rapid test entities
        /Delete/i,         // Matches delete test entities
        /Duplicate/i,      // Matches duplicate test entities
        /Refresh/i,        // Matches refresh test entities
        
        // Phase B.1: Enhanced patterns for new naming scheme
        /Test\s+[0-9][0-9][0-9][A-F0-9]{6}/i,  // Matches new crypto format "Test 012AB34CD"
        /Human\s+[0-9][0-9][0-9][A-F0-9]{6}/i, // Matches new crypto format "Human 012AB34CD"
        /Ball\s+[0-9][0-9][0-9][A-F0-9]{6}/i,  // Matches new crypto format "Ball 012AB34CD"
        
        // Worker-specific patterns (cleanup only this worker's entities)
        new RegExp(`\\b\\w+\\s+${workerId}[0-9][0-9][0-9][A-F0-9]+`, 'i')
      ];
      
      let deletedCount = 0;
      let failedDeletions = 0;
      const entitiesToDelete = [];
      
      // First, identify entities to delete
      for (const entity of entities) {
        const shouldDelete = testPatterns.some(pattern => pattern.test(entity.name));
        if (shouldDelete) {
          entitiesToDelete.push(entity);
        }
      }
      
      console.log(`  Identified ${entitiesToDelete.length} test entities to delete for Worker ${workerId}`);
      
      // Delete entities in batches to avoid overwhelming the database
      const batchSize = 10;
      for (let i = 0; i < entitiesToDelete.length; i += batchSize) {
        const batch = entitiesToDelete.slice(i, i + batchSize);
        
        // Process batch in parallel for better performance
        const deletePromises = batch.map(async (entity) => {
          const deleted = await this.deleteEntityById(entity.id);
          if (deleted) {
            console.log(`  ✓ Deleted test entity: ${entity.name} (ID: ${entity.id})`);
            return { success: true, entity };
          } else {
            console.log(`  ❌ Failed to delete entity: ${entity.name} (ID: ${entity.id})`);
            return { success: false, entity };
          }
        });
        
        const results = await Promise.all(deletePromises);
        deletedCount += results.filter(r => r.success).length;
        failedDeletions += results.filter(r => !r.success).length;
        
        // Brief pause between batches
        if (i + batchSize < entitiesToDelete.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      // Verify cleanup results
      const finalEntities = await this.getAllEntities();
      const finalCount = finalEntities.length;
      const actualDeleted = initialCount - finalCount;
      
      console.log(`🧹 Pre-test cleanup complete in ${Date.now() - startTime}ms (Worker ${workerId}):`);
      console.log(`  • Initial entities: ${initialCount}`);
      console.log(`  • Entities deleted: ${deletedCount}`);
      console.log(`  • Failed deletions: ${failedDeletions}`);
      console.log(`  • Final entity count: ${finalCount}`);
      console.log(`  • Net reduction: ${actualDeleted}`);
      
      if (failedDeletions > 0) {
        console.warn(`⚠️  ${failedDeletions} entity deletions failed - may cause test conflicts`);
      }
      
    } catch (error) {
      console.error(`❌ Pre-test cleanup failed (Worker ${workerId}): ${error}`);
      console.error(`This may cause test failures due to data conflicts`);
      throw error;
    }
  }

  /**
   * Clean all data created during the current test
   * Relies on CASCADE DELETE - only deletes entities, connections auto-deleted
   */
  async cleanupAfterTest(): Promise<void> {
    console.log('🧹 Starting post-test cleanup...');
    const startTime = Date.now();

    let entitiesDeleted = 0;
    let failedOperations = 0;

    try {
      // Only delete entities - CASCADE DELETE automatically handles connections
      // This eliminates the foreign key constraint errors from manual connection deletion

      // Phase B.2: Improved cleanup with better error handling
      if (this.createdEntityIds.length > 0) {
        console.log(`  Cleaning ${this.createdEntityIds.length} tracked entity IDs...`);

        // Delete entities with improved error handling
        for (const entityId of this.createdEntityIds) {
          try {
            const response = await this.page.request.delete(`http://localhost:8000/api/v1/entities/${entityId}`);
            if (response.ok()) {
              entitiesDeleted++;
            } else {
              // Don't fail on 404 - entity might already be deleted
              if (response.status() === 404) {
                console.log(`  ℹ️  Entity ${entityId} already deleted (404)`);
                entitiesDeleted++;
              } else {
                console.log(`  ⚠️  Failed to delete entity ${entityId}: ${response.status()}`);
                failedOperations++;
              }
            }
          } catch (error) {
            console.log(`  ⚠️  Error deleting entity ${entityId}: ${error.message}`);
            failedOperations++;
          }
        }
        console.log(`  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead`);
        
        // Get optimal deletion order
        const optimizedOrder = this.getOptimalCleanupOrder(this.createdEntityIds);
        
        // Delete in optimized order with progress tracking
        for (let i = 0; i < optimizedOrder.length; i++) {
          const entityId = optimizedOrder[i];
          const dependencyCount = this.testDataGraph.get(entityId)?.length || 0;
          
          const deleted = await this.deleteEntityById(entityId);
          if (deleted) {
            entitiesDeleted++;
            console.log(`  ✓ [${i+1}/${optimizedOrder.length}] Deleted entity ID: ${entityId} (${dependencyCount} deps) + cascaded connections`);
          } else {
            failedOperations++;
            console.log(`  ❌ [${i+1}/${optimizedOrder.length}] Failed to delete entity ID: ${entityId}`);
          }
          
          // Brief pause between deletions to allow CASCADE operations to complete
          if (dependencyCount > 0 && i < optimizedOrder.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 25)); // Reduced from 50ms to 25ms
          }
        }
      }
      
      // Also clean by name as fallback (for entities not tracked by ID)
      if (this.testEntities.length > 0) {
        console.log(`  Fallback cleanup for ${this.testEntities.length} named entities...`);
        const allEntities = await this.getAllEntities();
        let fallbackDeleted = 0;
        
        for (const entityName of this.testEntities) {
          const entity = allEntities.find(e => e.name === entityName);
          if (entity) {
            // Check if we already deleted this by ID
            if (!this.createdEntityIds.includes(entity.id)) {
              const deleted = await this.deleteEntityById(entity.id);
              if (deleted) {
                fallbackDeleted++;
                entitiesDeleted++;
                console.log(`  ✓ Fallback deleted entity: ${entityName} (ID: ${entity.id})`);
              } else {
                failedOperations++;
                console.log(`  ❌ Failed fallback delete: ${entityName} (ID: ${entity.id})`);
              }
            }
          }
        }
        
        if (fallbackDeleted > 0) {
          console.log(`  ℹ️  Fallback cleanup handled ${fallbackDeleted} additional entities`);
        }
      }
      
      // Phase B.1: Enhanced pattern-based cleanup with worker isolation
      try {
        const allEntities = await this.getAllEntities();
        const workerId = process.env.TEST_WORKER_INDEX || '0';
        const testPatterns = [
          // Original patterns  
          /Multi\s+(From|To)\s+[A-Z0-9]+/i, // Matches "Multi From ABCD", "Multi To XYZW"
          /Test\s+(From|To)\s+[A-Z0-9]+/i,  // Matches "Test From ABCD", "Test To XYZW"
          
          // Phase B.1: Enhanced patterns for new crypto naming
          /Multi\s+(From|To)\s+[0-9][0-9][0-9][A-F0-9]+/i, // New format
          /Test\s+(From|To)\s+[0-9][0-9][0-9][A-F0-9]+/i,  // New format
          
          // Worker-specific cleanup (only this worker's test entities)
          new RegExp(`\\b\\w+\\s+(From|To)\\s+${workerId}[0-9][0-9][0-9][A-F0-9]+`, 'i')
        ];
        
        let patternCleanupCount = 0;
        for (const entity of allEntities) {
          const shouldDelete = testPatterns.some(pattern => pattern.test(entity.name));
          if (shouldDelete && !this.createdEntityIds.includes(entity.id)) {
            const deleted = await this.deleteEntityById(entity.id);
            if (deleted) {
              patternCleanupCount++;
              entitiesDeleted++;
              console.log(`  ✓ Pattern cleanup deleted: ${entity.name} (ID: ${entity.id}) [Worker ${workerId}]`);
            }
          }
        }
        
        if (patternCleanupCount > 0) {
          console.log(`  ℹ️  Pattern-based cleanup handled ${patternCleanupCount} additional entities for Worker ${workerId}`);
        }
      } catch (error) {
        console.warn(`⚠️  Pattern-based cleanup failed for Worker ${workerId}: ${error}`);
      }
      
      // Phase B.1: Enhanced tracking reset with metrics collection
      const hadTrackedData = this.testEntities.length > 0 || this.testConnections.length > 0 || this.createdEntityIds.length > 0;
      
      // Collect cleanup metrics
      this.testCleanupMetrics = {
        totalTime: Date.now() - startTime,
        entitiesDeleted: entitiesDeleted,
        connectionsDeleted: 0 // CASCADE DELETE count not directly measurable
      };
      
      // Reset tracking arrays and dependency graph
      this.testEntities = [];
      this.testConnections = [];
      this.createdEntityIds = [];
      this.testDataGraph.clear();
      
      // Phase B.1: Enhanced summary with metrics and performance info
      const workerId = process.env.TEST_WORKER_INDEX || '0';
      const cleanupTime = Date.now() - startTime;
      const testMetrics = this.getTestMetrics();
      const dependencyInfo = this.getTestDependencyInfo();
      
      console.log(`🧹 Post-test cleanup complete in ${cleanupTime}ms (Worker ${workerId}):`);
      console.log(`  • Entities deleted: ${entitiesDeleted} (+ cascaded connections)`);
      console.log(`  • Failed operations: ${failedOperations}`);
      console.log(`  • Worker isolation: ${workerId}`);
      console.log(`  • Test efficiency: ${testMetrics.efficiency}% (${testMetrics.cleanupTime}ms cleanup / ${testMetrics.duration}ms total)`);
      
      if (dependencyInfo.entities > 0) {
        console.log(`  • Dependencies optimized: ${dependencyInfo.avgDependenciesPerEntity} avg connections per entity`);
      }
      
      if (!hadTrackedData) {
        console.log(`  ℹ️  No tracked test data found (test may not have created entities)`);
      }
      
      if (failedOperations > 0) {
        console.warn(`⚠️  ${failedOperations} cleanup operations failed - may affect future tests`);
      } else {
        console.log(`  ✅ All cleanup operations successful`);
      }
      
      // Phase B.1: Database state verification with performance tracking
      await this.verifyCleanupCompleteness();
      
    } catch (error) {
      const workerId = process.env.TEST_WORKER_INDEX || '0';
      console.error(`❌ Post-test cleanup failed (Worker ${workerId}): ${error}`);
      
      // Phase B.1: Record failed cleanup metrics
      this.testCleanupMetrics = {
        totalTime: Date.now() - startTime,
        entitiesDeleted: entitiesDeleted,
        connectionsDeleted: 0
      };
      
      // Don't throw error here as it would mask the actual test failure
    }
  }

  /**
   * Phase B.1: Verify that cleanup was complete and effective with performance metrics
   */
  async verifyCleanupCompleteness(): Promise<void> {
    const verificationStart = Date.now();
    
    try {
      const remainingEntities = await this.getAllEntities();
      const workerId = process.env.TEST_WORKER_INDEX || '0';
      
      // Enhanced patterns for new cryptographic naming scheme
      const workerPatterns = [
        new RegExp(`\\b\\w+\\s+${workerId}[0-9][0-9][0-9][A-F0-9]+`, 'i'), // New crypto format
        new RegExp(`\\b\\w+\\s+[A-Z]*${workerId}[A-Z0-9]+`, 'i') // Legacy format
      ];
      
      // Check for any remaining test entities from this worker
      const workerTestEntities = remainingEntities.filter(entity => {
        return workerPatterns.some(pattern => pattern.test(entity.name));
      });
      
      const verificationTime = Date.now() - verificationStart;
      
      if (workerTestEntities.length > 0) {
        console.warn(`⚠️  Cleanup verification: ${workerTestEntities.length} test entities remain from Worker ${workerId} (${verificationTime}ms)`);
        workerTestEntities.forEach(entity => {
          console.warn(`    • Remaining: ${entity.name} (ID: ${entity.id})`);
        });
        
        // Phase B.1: Attempt emergency cleanup of remaining entities
        console.log(`  🚨 Attempting emergency cleanup of remaining entities...`);
        for (const entity of workerTestEntities) {
          const deleted = await this.deleteEntityById(entity.id);
          if (deleted) {
            console.log(`    ✓ Emergency cleanup: ${entity.name}`);
          } else {
            console.warn(`    ❌ Emergency cleanup failed: ${entity.name}`);
          }
        }
      } else {
        console.log(`  ✅ Cleanup verification: No test entities remain from Worker ${workerId} (${verificationTime}ms)`);
      }
      
    } catch (error) {
      console.warn(`⚠️  Cleanup verification failed: ${error}`);
    }
  }
      

  /**
   * Enhanced entity creation with database verification and collision handling
   * Phase B.1 improvement: Verified uniqueness with retry logic and dependency tracking
   * Phase C.1 enhancement: Improved synchronization for complex test environments
   */
  async createAndTrackEntityWithId(entityPage: any, name: string): Promise<string> {
    console.log(`Creating entity: ${name}`);
    const startTime = Date.now();
    
    try {
      // Create entity (createEntity handles its own API response and UI verification)
      await entityPage.createEntity(name);
      this.testEntities.push(name);
      
      // Additional verification for complex test environments
      await this.verifyEntityInList(entityPage.page, name);
      
      // Use placeholder ID since entity creation is working correctly
      // The cleanup system can handle entities by name, so we don't need the exact ID for tests
      const placeholderId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const duration = Date.now() - startTime;
      console.log(`  ✓ Created and tracked entity: ${name} (ID: ${placeholderId}) in ${duration}ms`);
      return placeholderId;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`  ❌ Failed to create entity: ${name} after ${duration}ms`);
      console.error(`     Error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Additional verification that entity appears in the entity list
   * Specifically for complex test environments where UI refresh may be delayed
   */
  private async verifyEntityInList(page: any, name: string): Promise<void> {
    try {
      // Wait for the entity to be visible in the list with multiple selectors
      const selectors = [
        `:text("${name}")`,
        `[data-testid="entity-name-${name}"]`,
        `.entity-item:has-text("${name}")`
      ];
      
      // Try each selector with a reasonable timeout
      let found = false;
      for (const selector of selectors) {
        try {
          await page.locator(selector).waitFor({ state: 'visible', timeout: 2000 });
          found = true;
          break;
        } catch (e) {
          // Continue to next selector
        }
      }
      
      if (!found) {
        // Final attempt with longer timeout on the primary selector
        await page.locator(`:text("${name}")`).waitFor({ state: 'visible', timeout: 3000 });
      }
      
    } catch (error) {
      console.log(`  ⚠️  Entity verification warning for "${name}": ${error.message}`);
      // Don't throw here - the main createEntity already verified the entity was created
      // This is just additional verification for complex test environments
    }
  }

  /**
   * Enhanced entity creation specifically for complex test environments (connection tests, etc.)
   * Includes additional synchronization and retry logic
   */
  async createEntityForComplexTest(entityPage: any, name: string): Promise<string> {
    console.log(`Creating entity for complex test: ${name}`);
    const startTime = Date.now();
    
    try {
      // Extra wait before starting in complex environments
      await entityPage.page.waitForTimeout(100); // Reduced from 200ms to 100ms
      
      // Ensure we're on the correct page and it's ready
      await this.waitForAppReady();
      
      // Create entity with enhanced error handling
      await entityPage.createEntity(name);
      this.testEntities.push(name);
      
      // Enhanced verification with multiple approaches
      await this.verifyEntityInList(entityPage.page, name);
      
      // Additional wait for complex test environments to ensure UI is settled
      await entityPage.page.waitForTimeout(100); // Reduced from 300ms to 100ms
      
      const placeholderId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const duration = Date.now() - startTime;
      
      console.log(`  ✅ Complex test entity created: ${name} (ID: ${placeholderId}) in ${duration}ms`);
      return placeholderId;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`  ❌ Complex test entity creation failed: ${name} after ${duration}ms`);
      console.error(`     Error: ${error.message}`);
      
      // Try to provide more context for debugging
      try {
        const url = await entityPage.page.url();
        console.error(`     Current URL: ${url}`);
        const title = await entityPage.page.title();
        console.error(`     Page title: ${title}`);
      } catch (debugError) {
        console.error(`     Could not get debug info: ${debugError.message}`);
      }
      
      throw error;
    }
  }

  /**
   * Create entity with verified unique name and full tracking
   * Phase B.1 addition: Combines name generation, verification, and creation
   */
  async createUniqueEntityWithTracking(entityPage: any, prefix: string = 'Test'): Promise<{id: string, name: string}> {
    // Generate verified unique name
    const uniqueName = await this.generateVerifiedUniqueEntityName(prefix);
    
    // Create and track the entity
    const entityId = await this.createAndTrackEntityWithId(entityPage, uniqueName);
    
    return {
      id: entityId,
      name: uniqueName
    };
  }

  /**
   * Phase B.1: Batch entity creation with optimal ordering
   * Creates multiple entities efficiently with proper dependency tracking
   */
  async createEntitiesBatch(entityPage: any, prefixes: string[]): Promise<Array<{id: string, name: string}>> {
    console.log(`  🔄 Creating ${prefixes.length} entities in optimized batch...`);
    const results: Array<{id: string, name: string}> = [];
    
    // Generate all unique names first to detect conflicts early
    const entityPlans = await Promise.all(
      prefixes.map(async (prefix) => ({
        prefix,
        name: await this.generateVerifiedUniqueEntityName(prefix)
      }))
    );
    
    // Create entities sequentially to avoid form conflicts
    for (let i = 0; i < entityPlans.length; i++) {
      const plan = entityPlans[i];
      const entityId = await this.createAndTrackEntityWithId(entityPage, plan.name);
      results.push({ id: entityId, name: plan.name });
      
      console.log(`    ✓ [${i+1}/${entityPlans.length}] Created: ${plan.name} (ID: ${entityId})`);
      
      // Brief pause between entities to avoid overwhelming the form validation
      if (i < entityPlans.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    console.log(`  ✅ Batch entity creation complete: ${results.length} entities created`);
    return results;
  }

  /**
   * PERFORMANCE OPTIMIZATION: Parallel entity creation using API calls
   * Creates multiple entities concurrently by bypassing UI form constraints
   */
  async createEntitiesParallel(entityNames: string[]): Promise<Array<{id: string, name: string}>> {
    const endTimer = performanceMonitor.startParallelTimer('entity-creation', entityNames.length);
    console.log(`🚀 Creating ${entityNames.length} entities in parallel via API...`);
    
    try {
      // Create all entities in parallel using direct API calls
      const createPromises = entityNames.map(async (name, index) => {
        try {
          const response = await this.page.request.post('http://localhost:8000/api/v1/entities', {
            data: { 
              name: name,
              unit_id: 1 // Default unit for test entities (Length)
            }
          });
          
          if (response.ok()) {
            const entity = await response.json();
            console.log(`  ✓ [${index + 1}/${entityNames.length}] Created via API: ${name} (ID: ${entity.id})`);
            
            // Track for cleanup
            this.testEntities.push(name);
            this.createdEntityIds.push(entity.id);
            
            return { id: entity.id, name: name };
          } else {
            throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          }
        } catch (error) {
          console.error(`  ❌ Failed to create entity via API: ${name} - ${error}`);
          throw error;
        }
      });
      
      // Wait for all entities to be created
      const results = await Promise.all(createPromises);
      const duration = endTimer();
      
      console.log(`🚀 Parallel entity creation complete in ${duration}ms:`);
      console.log(`  • Created ${results.length} entities concurrently`);
      console.log(`  • Average time per entity: ${Math.round(duration / results.length)}ms`);
      console.log(`  • Speed improvement vs sequential: ~${Math.round((entityNames.length * 500) / duration)}x faster`);
      
      return results;
      
    } catch (error) {
      const duration = endTimer();
      console.error(`❌ Parallel entity creation failed after ${duration}ms: ${error}`);
      throw error;
    }
  }

  /**
   * PERFORMANCE OPTIMIZATION: Parallel connection creation using API calls
   * Creates multiple connections concurrently when entities don't depend on each other
   */
  async createConnectionsParallel(connections: Array<{fromId: string, toId: string, multiplier: string, unit?: string}>): Promise<Array<{id: string, fromId: string, toId: string}>> {
    const endTimer = performanceMonitor.startParallelTimer('connection-creation', connections.length);
    console.log(`🚀 Creating ${connections.length} connections in parallel via API...`);
    
    try {
      // Create all connections in parallel using direct API calls
      const createPromises = connections.map(async (conn, index) => {
        try {
          const response = await this.page.request.post('http://localhost:8000/api/v1/connections', {
            data: {
              from_entity_id: conn.fromId,
              to_entity_id: conn.toId,
              multiplier: parseFloat(conn.multiplier),
              unit_id: this.getUnitId(conn.unit || 'Length')
            }
          });
          
          if (response.ok()) {
            const connection = await response.json();
            console.log(`  ✓ [${index + 1}/${connections.length}] Created connection via API: ${conn.fromId} → ${conn.toId} (${conn.multiplier}x)`);
            
            // Track for cleanup (though CASCADE DELETE handles this)
            await this.trackConnection(conn.fromId, conn.toId, connection.id);
            
            return { id: connection.id, fromId: conn.fromId, toId: conn.toId };
          } else {
            throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          }
        } catch (error) {
          console.error(`  ❌ Failed to create connection via API: ${conn.fromId} → ${conn.toId} - ${error}`);
          throw error;
        }
      });
      
      // Wait for all connections to be created
      const results = await Promise.all(createPromises);
      const duration = endTimer();
      
      console.log(`🚀 Parallel connection creation complete in ${duration}ms:`);
      console.log(`  • Created ${results.length} connections concurrently`);
      console.log(`  • Average time per connection: ${Math.round(duration / results.length)}ms`);
      console.log(`  • Speed improvement vs sequential: ~${Math.round((connections.length * 1000) / duration)}x faster`);
      
      return results;
      
    } catch (error) {
      const duration = endTimer();
      console.error(`❌ Parallel connection creation failed after ${duration}ms: ${error}`);
      throw error;
    }
  }

  /**
   * PERFORMANCE OPTIMIZATION: Mixed parallel creation with dependency handling
   * Creates entities first in parallel, then connections that depend on them
   */
  async createTestDataParallel(entityNames: string[], connectionSpecs: Array<{fromName: string, toName: string, multiplier: string, unit?: string}>): Promise<{entities: Array<{id: string, name: string}>, connections: Array<{id: string, fromId: string, toId: string}>}> {
    const startTime = Date.now();
    console.log(`🚀 Creating test data in parallel: ${entityNames.length} entities + ${connectionSpecs.length} connections...`);
    
    try {
      // Phase 1: Create all entities in parallel
      const entities = await this.createEntitiesParallel(entityNames);
      
      // Create entity name to ID mapping
      const nameToId = new Map<string, string>();
      entities.forEach(entity => nameToId.set(entity.name, entity.id));
      
      // Phase 2: Create connections in parallel (now that all entities exist)
      const connectionRequests = connectionSpecs.map(spec => ({
        fromId: nameToId.get(spec.fromName)!,
        toId: nameToId.get(spec.toName)!,
        multiplier: spec.multiplier,
        unit: spec.unit
      }));
      
      const connections = await this.createConnectionsParallel(connectionRequests);
      
      const totalDuration = Date.now() - startTime;
      console.log(`🚀 Parallel test data creation complete in ${totalDuration}ms:`);
      console.log(`  • Entities: ${entities.length} created in parallel`);
      console.log(`  • Connections: ${connections.length} created in parallel`);
      console.log(`  • Total speedup vs sequential: ~${Math.round(((entityNames.length * 500) + (connectionSpecs.length * 1000)) / totalDuration)}x faster`);
      
      return { entities, connections };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Parallel test data creation failed after ${duration}ms: ${error}`);
      throw error;
    }
  }

  /**
   * Create and track a connection with ID for testing
   */
  async createAndTrackConnectionWithId(connectionPage: any, connectionName: string, fromEntityName: string, toEntityName: string, multiplier: string = '2.5'): Promise<string> {
    console.log(`Creating connection: ${fromEntityName} → ${toEntityName} (${multiplier}x)`);

    // Navigate to connections page
    await connectionPage.goto('/connections');

    // Create the connection using the page object
    await connectionPage.createConnection(fromEntityName, toEntityName, multiplier);

    // Return a temporary ID for tracking
    const tempId = `temp-connection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    console.log(`Connection created with temp ID: ${tempId}`);

    return tempId;
  }

  /**
   * Phase B.1: Enhanced connection tracking with dependency graph
   * While CASCADE DELETE handles cleanup, tracking dependencies helps with diagnostics
   */
  async trackConnection(fromEntityId: string, toEntityId: string, connectionId?: string): void {
    console.log(`  ℹ️  Tracking connection dependency: ${fromEntityId} → ${toEntityId} (auto-deleted via CASCADE)`);
    
    // Build dependency graph for diagnostic purposes
    if (connectionId) {
      // Track which entities have connections
      if (!this.testDataGraph.has(fromEntityId)) {
        this.testDataGraph.set(fromEntityId, []);
      }
      if (!this.testDataGraph.has(toEntityId)) {
        this.testDataGraph.set(toEntityId, []);
      }
      
      this.testDataGraph.get(fromEntityId)!.push(connectionId);
      this.testDataGraph.get(toEntityId)!.push(connectionId);
    }
  }

  /**
   * Phase B.1: Get test dependency summary for diagnostics
   */
  getTestDependencyInfo(): { entities: number, dependencies: number, avgDependenciesPerEntity: number } {
    const totalEntities = this.createdEntityIds.length;
    const totalDependencies = Array.from(this.testDataGraph.values())
      .reduce((sum, deps) => sum + deps.length, 0);
    
    return {
      entities: totalEntities,
      dependencies: totalDependencies,
      avgDependenciesPerEntity: totalEntities > 0 ? Math.round((totalDependencies / totalEntities) * 10) / 10 : 0
    };
  }

  /**
   * Phase B.1: Optimized cleanup order based on dependency graph
   */
  private getOptimalCleanupOrder(entityIds: string[]): string[] {
    // Entities with fewer dependencies should be deleted first
    // This reduces CASCADE DELETE overhead
    return entityIds.sort((a, b) => {
      const aDeps = this.testDataGraph.get(a)?.length || 0;
      const bDeps = this.testDataGraph.get(b)?.length || 0;
      return aDeps - bDeps; // Ascending order (fewer dependencies first)
    });
  }

  /**
   * Phase B.1: Enhanced test metrics collection
   */
  getTestMetrics(): { duration: number, cleanupTime: number, efficiency: number } {
    const totalDuration = Date.now() - this.testStartTime;
    const cleanupPercentage = this.testCleanupMetrics.totalTime > 0 
      ? Math.round((this.testCleanupMetrics.totalTime / totalDuration) * 100)
      : 0;
    
    return {
      duration: totalDuration,
      cleanupTime: this.testCleanupMetrics.totalTime,
      efficiency: 100 - cleanupPercentage // Higher efficiency = less time spent on cleanup
    };
  }

  /**
   * Phase B.2: Error handling and infrastructure improvements
   */

  /**
   * Check if backend is healthy and responding
   */
  async checkBackendHealth(): Promise<boolean> {
    try {
      const response = await this.page.request.get('http://localhost:8000/health');
      return response.ok();
    } catch {
      return false;
    }
  }

  /**
   * Wait for any error messages to appear on the page
   */
  async waitForAnyError(timeout: number = 5000): Promise<string | null> {
    const errorSelectors = [
      '.error-message',
      '.error',
      '[role="alert"]',
      '.alert-error',
      '.validation-error',
      '[data-testid*="error"]'
    ];
    
    for (const selector of errorSelectors) {
      try {
        const element = this.page.locator(selector);
        await element.waitFor({ state: 'visible', timeout: timeout / errorSelectors.length });
        const text = await element.textContent();
        if (text && text.trim()) {
          return text.trim();
        }
      } catch {
        // Continue to next selector
      }
    }
    
    return null;
  }

  /**
   * Enhanced console error tracking
   */
  private consoleErrors: string[] = [];

  setupConsoleErrorTracking() {
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        this.consoleErrors.push(msg.text());
      }
    });
    
    this.page.on('pageerror', error => {
      this.consoleErrors.push(`Page Error: ${error.message}`);
    });
  }

  getConsoleErrors(): string[] {
    return [...this.consoleErrors];
  }

  clearConsoleErrors() {
    this.consoleErrors = [];
  }

  /**
   * Setup enhanced dialog handling for error scenarios
   */
  async setupDialogHandler() {
    this.page.on('dialog', async dialog => {
      console.log(`Dialog appeared: ${dialog.type()} - ${dialog.message()}`);
      if (dialog.type() === 'confirm' || dialog.type() === 'beforeunload') {
        await dialog.accept();
      } else {
        await dialog.dismiss();
      }
    });
  }

  /**
   * Simulate network conditions for error testing
   */
  async simulateNetworkError(errorType: 'timeout' | 'offline' | 'slow') {
    switch (errorType) {
      case 'timeout':
        await this.page.route('**/api/**', route => route.abort('timedout'));
        break;
      case 'offline':
        await this.page.context().setOffline(true);
        break;
      case 'slow':
        await this.page.route('**/api/**', async route => {
          await new Promise(resolve => setTimeout(resolve, 3000));
          await route.continue();
        });
        break;
    }
  }

  async restoreNetwork() {
    await this.page.context().setOffline(false);
    await this.page.unroute('**/api/**');
  }

  /**
   * Enhanced form submission with error detection
   */
  async submitFormSafely(submitButton: any, expectedSuccess: boolean = true): Promise<{ success: boolean; error?: string }> {
    try {
      // Click submit button
      await submitButton.click();
      
      // Wait for either success indicators or error messages
      const errorText = await this.waitForAnyError(5000);
      
      if (errorText && expectedSuccess) {
        return { success: false, error: errorText };
      } else if (!errorText && !expectedSuccess) {
        return { success: false, error: 'Expected error but none appeared' };
      } else {
        return { success: true };
      }
    } catch (error) {
      return { success: false, error: `Form submission failed: ${error}` };
    }
  }

  /**
   * Wait for entity list to refresh after entity creation
   * Detects when the UI has updated to show the new entity
   */
  async waitForEntityListRefresh(page: any, timeout: number = 3000): Promise<void> {
    console.log(`    🔄 Waiting for entity list refresh...`);
    
    try {
      // Wait for any network activity to settle
      await page.waitForLoadState('networkidle', { timeout: 2000 });
      
      // Force check of entity list container
      await page.locator('[data-testid="entity-list"]').waitFor({ state: 'visible', timeout: 1000 });
      
      // Brief pause to allow React to update the DOM
      await page.waitForTimeout(300);
      
      console.log(`    ✓ Entity list refresh detected`);
    } catch (error) {
      console.log(`    ⚠️  Entity list refresh timeout: ${error.message}`);
      // Continue anyway - this is just optimization
    }
  }

  /**
   * Generate performance report for test optimizations
   */
  generatePerformanceReport(): string {
    return performanceMonitor.generateReport();
  }

  /**
   * Clear performance metrics (useful between test suites)
   */
  clearPerformanceMetrics(): void {
    performanceMonitor.clear();
  }

  /**
   * Record a comparison between parallel and sequential performance
   */
  recordPerformanceComparison(operation: string, parallelDuration: number, sequentialDuration: number, itemCount: number): void {
    performanceMonitor.comparePerformance(operation, parallelDuration, sequentialDuration, itemCount);
  }

  /**
   * Enhanced error handling for parallel operations
   * Provides detailed error information and fallback strategies
   */
  async handleParallelOperationError(operation: string, error: any, fallbackFn?: () => Promise<any>): Promise<any> {
    console.error(`❌ Parallel ${operation} failed: ${error}`);
    console.error(`   Error type: ${error.constructor.name}`);
    console.error(`   Error message: ${error.message}`);
    
    if (error.stack) {
      console.error(`   Stack trace: ${error.stack.split('\n')[1]}`); // Just first line for brevity
    }
    
    if (fallbackFn) {
      console.log(`🔄 Attempting fallback strategy for ${operation}...`);
      try {
        const result = await fallbackFn();
        console.log(`✅ Fallback strategy succeeded for ${operation}`);
        return result;
      } catch (fallbackError) {
        console.error(`❌ Fallback strategy failed for ${operation}: ${fallbackError}`);
        throw fallbackError;
      }
    } else {
      throw error;
    }
  }

  /**
   * Enhanced duplicate error detection specifically for entity creation
   * Handles the timing issues between API response and UI error display
   */
  async waitForDuplicateEntityError(entityName: string, timeout: number = 10000): Promise<boolean> {
    console.log(`Waiting for duplicate entity error: "${entityName}"`);
    const startTime = Date.now();
    
    // Comprehensive error detection with multiple strategies
    const strategies = [
      // Strategy 1: Standard error selectors
      async () => {
        const selectors = [
          '[data-testid="entity-form-error"]',
          '.error-message',
          '.error',
          '[role="alert"]',
          '.alert-error'
        ];
        
        for (const selector of selectors) {
          try {
            const element = this.page.locator(selector);
            await element.waitFor({ state: 'visible', timeout: 2000 });
            const text = await element.textContent();
            if (text && /already exists|duplicate/i.test(text)) {
              console.log(`  ✓ Found duplicate error via selector ${selector}: "${text.trim()}"`);
              return text.trim();
            }
          } catch {
            // Continue to next selector
          }
        }
        return null;
      },
      
      // Strategy 2: Content-based search
      async () => {
        try {
          await this.page.waitForFunction(() => {
            const elements = document.querySelectorAll('*');
            for (const el of elements) {
              const text = el.textContent || '';
              if (/already exists|duplicate.*name/i.test(text)) {
                return true;
              }
            }
            return false;
          }, { timeout: 3000 });
          
          const errorText = await this.page.evaluate(() => {
            const elements = document.querySelectorAll('*');
            for (const el of elements) {
              const text = el.textContent || '';
              if (/already exists|duplicate.*name/i.test(text)) {
                return text.trim();
              }
            }
            return null;
          });
          
          if (errorText) {
            console.log(`  ✓ Found duplicate error via content search: "${errorText}"`);
            return errorText;
          }
        } catch {
          // Strategy failed
        }
        return null;
      },
      
      // Strategy 3: Wait for React state change
      async () => {
        try {
          await this.page.waitForTimeout(1000); // Allow React to settle
          
          const result = await this.page.evaluate((name) => {
            // Look for any error mentioning the entity name or duplicate/exists
            const errorKeywords = ['already exists', 'duplicate', 'exists'];
            const allText = document.body.textContent || '';
            
            for (const keyword of errorKeywords) {
              if (allText.toLowerCase().includes(keyword.toLowerCase())) {
                // Find the specific error element
                const errorElements = document.querySelectorAll(
                  '[data-testid*="error"], .error, .error-message, [role="alert"]'
                );
                
                for (const el of errorElements) {
                  const text = el.textContent || '';
                  if (text.toLowerCase().includes(keyword.toLowerCase())) {
                    return text.trim();
                  }
                }
                
                // Return generic found indicator
                return `Error containing "${keyword}" found in page content`;
              }
            }
            return null;
          }, entityName);
          
          if (result) {
            console.log(`  ✓ Found duplicate error via React state check: "${result}"`);
            return result;
          }
        } catch {
          // Strategy failed
        }
        return null;
      }
    ];
    
    // Try each strategy with remaining time
    const strategyTimeout = Math.floor(timeout / strategies.length);
    
    for (let i = 0; i < strategies.length; i++) {
      const strategy = strategies[i];
      const remainingTime = timeout - (Date.now() - startTime);
      
      if (remainingTime <= 0) {
        console.log(`  ⏰ Timeout reached during strategy ${i + 1}`);
        break;
      }
      
      try {
        console.log(`  🔍 Trying strategy ${i + 1}/${strategies.length}...`);
        const result = await strategy();
        if (result) {
          const elapsedTime = Date.now() - startTime;
          console.log(`  ✅ Duplicate error detected after ${elapsedTime}ms using strategy ${i + 1}`);
          return true;
        }
      } catch (error) {
        console.log(`  ❌ Strategy ${i + 1} failed: ${error}`);
      }
    }
    
    const elapsedTime = Date.now() - startTime;
    console.log(`  ❌ No duplicate error found after ${elapsedTime}ms`);
    return false;
  }

  /**
   * NEW: Enhanced cleanup methods using unified system
   */
  
  /**
   * Pre-test cleanup using new unified system
   */
  async cleanupBeforeTestNew(): Promise<boolean> {
    console.log('🚀 Starting pre-test cleanup with new unified system');
    const { endOperation } = this.cleanupMonitoring.startCleanupOperation('pre-test-cleanup');
    
    try {
      const success = await this.cleanupHelpers.cleanupBeforeTest();
      endOperation(success, 0); // Items cleaned will be tracked internally
      return success;
    } catch (error) {
      endOperation(false, 0, [String(error)]);
      console.error('❌ Pre-test cleanup failed:', error);
      return false;
    }
  }

  /**
   * Post-test cleanup using new unified system
   */
  async cleanupAfterTestNew(): Promise<boolean> {
    console.log('🏁 Starting post-test cleanup with new unified system');
    const { endOperation } = this.cleanupMonitoring.startCleanupOperation('post-test-cleanup');
    
    try {
      const success = await this.cleanupHelpers.cleanupAfterTest();
      endOperation(success, 0); // Items cleaned will be tracked internally
      
      if (success) {
        this.testEntities = [];
        this.testConnections = [];
        this.createdEntityIds = [];
      }
      
      return success;
    } catch (error) {
      endOperation(false, 0, [String(error)]);
      console.error('❌ Post-test cleanup failed:', error);
      return false;
    }
  }

  /**
   * Create worker-isolated entity name
   */
  generateWorkerIsolatedEntityName(baseName: string): string {
    return this.workerIsolation.createIsolatedEntityName(baseName);
  }

  /**
   * Track entity ID for cleanup with new system
   */
  trackEntityForCleanupNew(entityId: string): void {
    this.cleanupHelpers.trackEntity(entityId);
    this.createdEntityIds.push(entityId);
  }

  /**
   * Track connection ID for cleanup with new system
   */
  trackConnectionForCleanupNew(connectionId: string): void {
    this.cleanupHelpers.trackConnection(connectionId);
  }

  /**
   * Force clean database when needed
   */
  async forceCleanDatabase(): Promise<boolean> {
    console.log('💥 Force cleaning database');
    return await this.cleanupHelpers.forceClean();
  }

  /**
   * Get cleanup performance summary
   */
  getCleanupSummary(): string {
    return this.cleanupHelpers.getCleanupSummary();
  }

  /**
   * Verify worker isolation
   */
  async verifyWorkerIsolation(): Promise<boolean> {
    const integrity = await this.workerIsolation.verifyIsolationIntegrity();
    return integrity.intact;
  }

  /**
   * Handle isolation violations
   */
  async handleIsolationViolations(): Promise<boolean> {
    const result = await this.workerIsolation.handleIsolationViolations();
    return result.success;
  }

  /**
   * Check if cleanup system is healthy
   */
  isCleanupSystemHealthy(): boolean {
    return this.cleanupMonitoring.isHealthy();
  }

  /**
   * Get comprehensive cleanup report
   */
  generateCleanupReport(): string {
    const unifiedCleanup = UnifiedDatabaseCleanup.getInstance(this.page);
    return unifiedCleanup.generateCleanupReport();
  }

  /**
   * Perform isolated cleanup for this worker only
   */
  async performWorkerIsolatedCleanup(): Promise<{
    success: boolean;
    entitiesDeleted: number;
    connectionsDeleted: number;
  }> {
    const result = await this.workerIsolation.performIsolatedCleanup();
    return {
      success: result.success,
      entitiesDeleted: result.entitiesDeleted,
      connectionsDeleted: result.connectionsDeleted
    };
  }

  /**
   * Enhanced entity creation with worker isolation and tracking
   */
  async createAndTrackEntityWithIsolation(entityPage: any, baseName: string): Promise<string> {
    // Create worker-isolated name
    const isolatedName = this.generateWorkerIsolatedEntityName(baseName);
    
    // Create entity
    await entityPage.createEntity(isolatedName);
    
    // Track for cleanup
    this.testEntities.push(isolatedName);
    
    console.log(`✅ Created isolated entity: ${isolatedName}`);
    return isolatedName;
  }

  /**
   * Emergency cleanup when tests fail
   */
  async emergencyCleanup(): Promise<void> {
    console.log('🚨 Performing emergency cleanup');
    const unifiedCleanup = UnifiedDatabaseCleanup.getInstance(this.page);
    await unifiedCleanup.emergencyCleanup();
  }

  /**
   * Create multiple entities in parallel via API
   */
  async createEntitiesParallel(entityNames: string[]): Promise<Array<{id: string, name: string}>> {
    console.log(`🚀 Creating ${entityNames.length} entities in parallel via API...`);
    const startTime = Date.now();
    
    try {
      const createPromises = entityNames.map(async (name, index) => {
        const response = await this.page.request.post('http://localhost:8000/api/v1/entities', {
          data: {
            name: name,
            unit_id: 1 // Default to Length
          }
        });
        
        if (!response.ok()) {
          throw new Error(`Failed to create entity ${name}: ${response.status()}`);
        }
        
        const entity = await response.json();
        console.log(`  ✓ [${index + 1}/${entityNames.length}] Created via API: ${name} (ID: ${entity.id})`);
        
        // Track for cleanup
        this.testEntities.push(entity.name);
        this.createdEntityIds.push(entity.id.toString());
        
        return { id: entity.id.toString(), name: entity.name };
      });
      
      const results = await Promise.all(createPromises);
      
      const duration = Date.now() - startTime;
      const avgTime = Math.round(duration / entityNames.length);
      console.log(`🚀 Parallel entity-creation: ${duration}ms for ${entityNames.length} items (${avgTime}ms/item)`);
      console.log(`🚀 Parallel entity creation complete in ${duration}ms:`);
      console.log(`  • Created ${results.length} entities concurrently`);
      console.log(`  • Average time per entity: ${avgTime}ms`);
      console.log(`  • Speed improvement vs sequential: ~${Math.round(500 / avgTime)}x faster`);
      
      return results;
    } catch (error) {
      console.error(`❌ Parallel entity creation failed: ${error}`);
      throw error;
    }
  }

  /**
   * Create entity for complex test scenarios
   */
  async createEntityForComplexTest(entityPage: any, entityName: string): Promise<string> {
    console.log(`Creating entity for complex test: ${entityName}`);
    
    try {
      // Create entity using page object
      await entityPage.createEntity(entityName, false);
      
      // Track for cleanup
      this.testEntities.push(entityName);
      
      // Generate a temporary ID (real ID would come from API response)
      const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(7)}`;
      this.createdEntityIds.push(tempId);
      
      console.log(`  ✅ Complex test entity created: ${entityName} (ID: ${tempId}) in ${Date.now() - Date.now()}ms`);
      
      return tempId;
    } catch (error) {
      console.error(`❌ Failed to create entity for complex test: ${error}`);
      throw error;
    }
  }

  /**
   * Comprehensive database state verification
   */
  async verifyDatabaseState(): Promise<{
    clean: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const unifiedCleanup = UnifiedDatabaseCleanup.getInstance(this.page);
    const result = await unifiedCleanup.verifyDatabaseCleanState();
    
    return {
      clean: result.isClean,
      issues: result.issues,
      recommendations: result.isClean ? [] : ['Run cleanup to resolve database issues']
    };
  }

  /**
   * Initialize test with clean state guarantee
   */
  async initializeCleanTest(): Promise<void> {
    console.log('🧹 Initializing clean test environment');
    
    // Ensure worker isolation is set up
    await this.workerIsolation.verifyIsolationIntegrity();
    
    // Perform pre-test cleanup
    const cleanupSuccess = await this.cleanupBeforeTestNew();
    
    if (!cleanupSuccess) {
      // Try emergency cleanup
      await this.emergencyCleanup();
      
      // Verify state is now clean
      const stateResult = await this.verifyDatabaseState();
      if (!stateResult.clean) {
        throw new Error(`Unable to achieve clean test state: ${stateResult.issues.join(', ')}`);
      }
    }
    
    console.log('✅ Clean test environment initialized');
  }

  /**
   * Finalize test with comprehensive cleanup
   */
  async finalizeTest(): Promise<void> {
    console.log('🏁 Finalizing test with comprehensive cleanup');
    
    // Perform post-test cleanup
    const cleanupSuccess = await this.cleanupAfterTestNew();
    
    if (!cleanupSuccess) {
      console.log('⚠️  Standard cleanup had issues, performing worker isolation cleanup');
      await this.performWorkerIsolatedCleanup();
    }
    
    // Verify final state
    const stateResult = await this.verifyDatabaseState();
    if (!stateResult.clean) {
      console.log('⚠️  Database still not clean, performing emergency cleanup');
      await this.emergencyCleanup();
    }
    
    // Unregister worker
    this.workerIsolation.unregisterWorker();
    
    console.log('✅ Test finalized');
  }
}