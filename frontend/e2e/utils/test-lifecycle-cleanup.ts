import { Page } from '@playwright/test';
import { UnifiedDatabaseCleanup } from './unified-database-cleanup';
import { performanceMonitor } from './performance-monitor';

/**
 * Test Lifecycle Cleanup System
 * 
 * Provides robust pre-test and post-test cleanup procedures that:
 * - Ensure clean database state before each test
 * - Clean up after each test to prevent pollution
 * - Handle worker-specific isolation
 * - Provide comprehensive error recovery
 * - Monitor and report cleanup performance
 * 
 * Key Features:
 * - Pre-test database verification and cleanup
 * - Post-test cleanup with tracking
 * - Test suite level cleanup
 * - Worker isolation enforcement
 * - Performance monitoring and reporting
 */
export class TestLifecycleCleanup {
  private static instances: Map<string, TestLifecycleCleanup> = new Map();
  private cleanupSystem: UnifiedDatabaseCleanup;
  private testSessionMetrics: {
    testsRun: number;
    totalCleanupTime: number;
    preTestCleanups: number;
    postTestCleanups: number;
    cleanupFailures: number;
  } = {
    testsRun: 0,
    totalCleanupTime: 0,
    preTestCleanups: 0,
    postTestCleanups: 0,
    cleanupFailures: 0
  };

  private constructor(private page: Page) {
    this.cleanupSystem = UnifiedDatabaseCleanup.getInstance(page);
  }

  /**
   * Get singleton instance for current worker
   */
  public static getInstance(page: Page): TestLifecycleCleanup {
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    if (!this.instances.has(workerId)) {
      this.instances.set(workerId, new TestLifecycleCleanup(page));
    }
    return this.instances.get(workerId)!;
  }

  /**
   * COMPREHENSIVE PRE-TEST CLEANUP
   * 
   * This method ensures the database is in a clean state before each test:
   * 1. Verify system health
   * 2. Perform complete cleanup
   * 3. Verify clean state
   * 4. Report results
   */
  public async performPreTestCleanup(): Promise<{
    success: boolean;
    duration: number;
    itemsCleaned: number;
    errors: string[];
    databaseStateClean: boolean;
  }> {
    const startTime = Date.now();
    const endTimer = performanceMonitor.startTimer('pre-test-cleanup');
    const workerId = process.env.TEST_WORKER_INDEX || '0';

    console.log(`🚀 Starting pre-test cleanup (Worker ${workerId})`);

    try {
      // Step 1: Health check
      const healthCheck = await this.cleanupSystem.healthCheck();
      if (!healthCheck.healthy) {
        throw new Error(`System health check failed: ${healthCheck.response}`);
      }
      console.log(`  ✓ System health check passed (${healthCheck.latency}ms)`);

      // Step 2: Perform complete cleanup
      const cleanupResult = await this.cleanupSystem.performCompleteCleanup();
      
      // Step 3: Verify clean state
      const verificationResult = await this.cleanupSystem.verifyDatabaseCleanState();

      const duration = endTimer();
      this.testSessionMetrics.preTestCleanups++;
      this.testSessionMetrics.totalCleanupTime += duration;

      if (!cleanupResult.success) {
        this.testSessionMetrics.cleanupFailures++;
      }

      const success = cleanupResult.success && verificationResult.isClean;
      const itemsCleaned = cleanupResult.entitiesDeleted + cleanupResult.connectionsDeleted;

      console.log(`🚀 Pre-test cleanup ${success ? 'completed' : 'completed with issues'}: ${itemsCleaned} items cleaned in ${duration}ms`);
      
      if (!success) {
        console.log(`  Issues: ${[...cleanupResult.errors, ...verificationResult.issues].join('; ')}`);
      }

      return {
        success,
        duration,
        itemsCleaned,
        errors: [...cleanupResult.errors, ...verificationResult.issues],
        databaseStateClean: verificationResult.isClean
      };

    } catch (error) {
      const duration = endTimer();
      this.testSessionMetrics.cleanupFailures++;
      const errorMessage = `Pre-test cleanup failed: ${error}`;
      
      console.error(`❌ ${errorMessage} (${duration}ms)`);

      return {
        success: false,
        duration,
        itemsCleaned: 0,
        errors: [errorMessage],
        databaseStateClean: false
      };
    }
  }

  /**
   * COMPREHENSIVE POST-TEST CLEANUP
   * 
   * This method cleans up after each test:
   * 1. Clean tracked items (if any)
   * 2. Perform general cleanup as backup
   * 3. Verify clean state
   * 4. Report results
   */
  public async performPostTestCleanup(): Promise<{
    success: boolean;
    duration: number;
    itemsCleaned: number;
    errors: string[];
    databaseStateClean: boolean;
  }> {
    const startTime = Date.now();
    const endTimer = performanceMonitor.startTimer('post-test-cleanup');
    const workerId = process.env.TEST_WORKER_INDEX || '0';

    console.log(`🏁 Starting post-test cleanup (Worker ${workerId})`);

    try {
      // Step 1: Clean tracked items first (most efficient)
      const trackedCleanupResult = await this.cleanupSystem.cleanupTrackedItems();
      let totalItemsCleaned = trackedCleanupResult.entitiesDeleted + trackedCleanupResult.connectionsDeleted;
      const allErrors: string[] = [...trackedCleanupResult.errors];

      // Step 2: Perform general cleanup as backup (in case some items weren't tracked)
      const generalCleanupResult = await this.cleanupSystem.performCompleteCleanup();
      totalItemsCleaned += generalCleanupResult.entitiesDeleted + generalCleanupResult.connectionsDeleted;
      allErrors.push(...generalCleanupResult.errors);

      // Step 3: Verify clean state
      const verificationResult = await this.cleanupSystem.verifyDatabaseCleanState();
      if (!verificationResult.isClean) {
        allErrors.push(...verificationResult.issues);
      }

      const duration = endTimer();
      this.testSessionMetrics.postTestCleanups++;
      this.testSessionMetrics.totalCleanupTime += duration;
      this.testSessionMetrics.testsRun++;

      const success = allErrors.length === 0 && verificationResult.isClean;
      
      if (!success) {
        this.testSessionMetrics.cleanupFailures++;
      }

      console.log(`🏁 Post-test cleanup ${success ? 'completed' : 'completed with issues'}: ${totalItemsCleaned} items cleaned in ${duration}ms`);
      
      if (!success) {
        console.log(`  Issues: ${allErrors.join('; ')}`);
      }

      return {
        success,
        duration,
        itemsCleaned: totalItemsCleaned,
        errors: allErrors,
        databaseStateClean: verificationResult.isClean
      };

    } catch (error) {
      const duration = endTimer();
      this.testSessionMetrics.cleanupFailures++;
      const errorMessage = `Post-test cleanup failed: ${error}`;
      
      console.error(`❌ ${errorMessage} (${duration}ms)`);

      return {
        success: false,
        duration,
        itemsCleaned: 0,
        errors: [errorMessage],
        databaseStateClean: false
      };
    }
  }

  /**
   * TEST SUITE CLEANUP
   * 
   * Comprehensive cleanup at the end of a test suite
   */
  public async performTestSuiteCleanup(): Promise<{
    success: boolean;
    duration: number;
    summary: string;
  }> {
    const startTime = Date.now();
    const endTimer = performanceMonitor.startTimer('test-suite-cleanup');
    const workerId = process.env.TEST_WORKER_INDEX || '0';

    console.log(`🎭 Starting test suite cleanup (Worker ${workerId})`);

    try {
      // Perform thorough cleanup
      const cleanupResult = await this.cleanupSystem.performCompleteCleanup();
      
      // Emergency cleanup if needed
      if (!cleanupResult.success) {
        console.log(`  ⚠️  Standard cleanup had issues, performing emergency cleanup...`);
        await this.cleanupSystem.emergencyCleanup();
      }

      // Final verification
      const verificationResult = await this.cleanupSystem.verifyDatabaseCleanState();

      const duration = endTimer();
      const success = verificationResult.isClean;

      // Generate summary
      const summary = this.generateTestSessionSummary(duration);

      console.log(`🎭 Test suite cleanup ${success ? 'completed' : 'completed with issues'} in ${duration}ms`);
      console.log(summary);

      return {
        success,
        duration,
        summary
      };

    } catch (error) {
      const duration = endTimer();
      const errorMessage = `Test suite cleanup failed: ${error}`;
      console.error(`❌ ${errorMessage} (${duration}ms)`);

      return {
        success: false,
        duration,
        summary: errorMessage
      };
    }
  }

  /**
   * FORCE CLEAN DATABASE
   * 
   * Emergency method to force database into clean state
   */
  public async forceCleanDatabase(): Promise<{
    success: boolean;
    duration: number;
    actions: string[];
  }> {
    const startTime = Date.now();
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    const actions: string[] = [];

    console.log(`💥 Force cleaning database (Worker ${workerId})`);

    try {
      // Step 1: Emergency cleanup
      actions.push('Performed emergency cleanup');
      await this.cleanupSystem.emergencyCleanup();

      // Step 2: Multiple cleanup passes
      for (let pass = 1; pass <= 3; pass++) {
        const cleanupResult = await this.cleanupSystem.performCompleteCleanup();
        actions.push(`Cleanup pass ${pass}: ${cleanupResult.entitiesDeleted + cleanupResult.connectionsDeleted} items cleaned`);
        
        if (cleanupResult.entitiesDeleted === 0 && cleanupResult.connectionsDeleted === 0) {
          break; // No more items to clean
        }
      }

      // Step 3: Final verification
      const verificationResult = await this.cleanupSystem.verifyDatabaseCleanState();
      actions.push(`Final verification: ${verificationResult.isClean ? 'Clean' : 'Issues remain'}`);

      const duration = Date.now() - startTime;
      const success = verificationResult.isClean;

      console.log(`💥 Force clean ${success ? 'completed' : 'completed with issues'} in ${duration}ms`);
      actions.forEach(action => console.log(`  ${action}`));

      return {
        success,
        duration,
        actions
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorAction = `Force clean failed: ${error}`;
      actions.push(errorAction);
      
      console.error(`❌ ${errorAction} (${duration}ms)`);

      return {
        success: false,
        duration,
        actions
      };
    }
  }

  /**
   * Track entity for cleanup during test
   */
  public trackEntityForCleanup(entityId: string): void {
    this.cleanupSystem.trackEntityForCleanup(entityId);
  }

  /**
   * Track connection for cleanup during test
   */
  public trackConnectionForCleanup(connectionId: string): void {
    this.cleanupSystem.trackConnectionForCleanup(connectionId);
  }

  /**
   * Get current test session metrics
   */
  public getTestSessionMetrics(): {
    testsRun: number;
    totalCleanupTime: number;
    preTestCleanups: number;
    postTestCleanups: number;
    cleanupFailures: number;
    averageCleanupTime: number;
    successRate: number;
  } {
    const totalCleanups = this.testSessionMetrics.preTestCleanups + this.testSessionMetrics.postTestCleanups;
    
    return {
      ...this.testSessionMetrics,
      averageCleanupTime: totalCleanups > 0 ? this.testSessionMetrics.totalCleanupTime / totalCleanups : 0,
      successRate: totalCleanups > 0 ? ((totalCleanups - this.testSessionMetrics.cleanupFailures) / totalCleanups) * 100 : 0
    };
  }

  /**
   * Generate test session summary
   */
  private generateTestSessionSummary(finalCleanupDuration: number): string {
    const metrics = this.getTestSessionMetrics();
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    
    let summary = `\n📊 Test Session Summary (Worker ${workerId}):\n`;
    summary += `  • Tests run: ${metrics.testsRun}\n`;
    summary += `  • Pre-test cleanups: ${metrics.preTestCleanups}\n`;
    summary += `  • Post-test cleanups: ${metrics.postTestCleanups}\n`;
    summary += `  • Total cleanup time: ${metrics.totalCleanupTime}ms\n`;
    summary += `  • Average cleanup time: ${Math.round(metrics.averageCleanupTime)}ms\n`;
    summary += `  • Cleanup success rate: ${Math.round(metrics.successRate)}%\n`;
    summary += `  • Cleanup failures: ${metrics.cleanupFailures}\n`;
    summary += `  • Final cleanup time: ${finalCleanupDuration}ms\n`;
    
    // Performance assessment
    if (metrics.averageCleanupTime < 1000) {
      summary += `  • Performance: Excellent (< 1s average)\n`;
    } else if (metrics.averageCleanupTime < 3000) {
      summary += `  • Performance: Good (< 3s average)\n`;
    } else {
      summary += `  • Performance: Needs improvement (> 3s average)\n`;
    }
    
    // Reliability assessment
    if (metrics.successRate >= 95) {
      summary += `  • Reliability: Excellent (${Math.round(metrics.successRate)}% success)\n`;
    } else if (metrics.successRate >= 85) {
      summary += `  • Reliability: Good (${Math.round(metrics.successRate)}% success)\n`;
    } else {
      summary += `  • Reliability: Needs improvement (${Math.round(metrics.successRate)}% success)\n`;
    }

    return summary;
  }

  /**
   * Reset session metrics
   */
  public resetSessionMetrics(): void {
    this.testSessionMetrics = {
      testsRun: 0,
      totalCleanupTime: 0,
      preTestCleanups: 0,
      postTestCleanups: 0,
      cleanupFailures: 0
    };
  }

  /**
   * Validate cleanup system health
   */
  public async validateCleanupHealth(): Promise<{
    healthy: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // Check system health
      const healthCheck = await this.cleanupSystem.healthCheck();
      if (!healthCheck.healthy) {
        issues.push(`System health check failed: ${healthCheck.response}`);
        recommendations.push('Check backend API connectivity');
      }

      // Check database state
      const stateCheck = await this.cleanupSystem.verifyDatabaseCleanState();
      if (!stateCheck.isClean) {
        issues.push(`Database not in clean state: ${stateCheck.issues.length} issues`);
        recommendations.push('Run force clean database operation');
      }

      // Check metrics for performance issues
      const metrics = this.getTestSessionMetrics();
      if (metrics.averageCleanupTime > 5000) {
        issues.push(`Cleanup performance is slow: ${Math.round(metrics.averageCleanupTime)}ms average`);
        recommendations.push('Review cleanup patterns and consider optimization');
      }

      if (metrics.successRate < 90) {
        issues.push(`Cleanup reliability is low: ${Math.round(metrics.successRate)}% success rate`);
        recommendations.push('Investigate recurring cleanup failures');
      }

      const healthy = issues.length === 0;

      return {
        healthy,
        issues,
        recommendations
      };

    } catch (error) {
      return {
        healthy: false,
        issues: [`Health validation failed: ${error}`],
        recommendations: ['Check system connectivity and try again']
      };
    }
  }
}

/**
 * Helper class for easy integration with existing test helpers
 */
export class CleanupHelpers {
  private lifecycle: TestLifecycleCleanup;

  constructor(page: Page) {
    this.lifecycle = TestLifecycleCleanup.getInstance(page);
  }

  /**
   * Simple pre-test cleanup for test beforeEach hooks
   */
  async cleanupBeforeTest(): Promise<boolean> {
    const result = await this.lifecycle.performPreTestCleanup();
    return result.success;
  }

  /**
   * Simple post-test cleanup for test afterEach hooks
   */
  async cleanupAfterTest(): Promise<boolean> {
    const result = await this.lifecycle.performPostTestCleanup();
    return result.success;
  }

  /**
   * Track entity by ID for cleanup
   */
  trackEntity(entityId: string): void {
    this.lifecycle.trackEntityForCleanup(entityId);
  }

  /**
   * Track connection by ID for cleanup
   */
  trackConnection(connectionId: string): void {
    this.lifecycle.trackConnectionForCleanup(connectionId);
  }

  /**
   * Force clean database when needed
   */
  async forceClean(): Promise<boolean> {
    const result = await this.lifecycle.forceCleanDatabase();
    return result.success;
  }

  /**
   * Get cleanup performance summary
   */
  getCleanupSummary(): string {
    const metrics = this.lifecycle.getTestSessionMetrics();
    return `Cleanup: ${metrics.testsRun} tests, ${Math.round(metrics.averageCleanupTime)}ms avg, ${Math.round(metrics.successRate)}% success`;
  }
}