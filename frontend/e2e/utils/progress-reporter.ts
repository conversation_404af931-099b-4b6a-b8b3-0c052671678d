import { Reporter, TestCase, TestResult, FullConfig, Suite } from '@playwright/test/reporter';

class ProgressReporter implements Reporter {
  private totalTests = 0;
  private completedTests = 0;
  private passedTests = 0;
  private failedTests = 0;
  private skippedTests = 0;
  private startTime = Date.now();
  private testTimes: number[] = [];

  onBegin(config: FullConfig, suite: Suite) {
    this.totalTests = this.countTests(suite);
    console.log('================================================================================');
    console.log(`🚀 Starting E2E test suite with ${this.totalTests} tests`);
    console.log(`🔧 Running with ${config.workers} workers`);
    console.log('================================================================================\n');
  }

  onTestBegin(test: TestCase) {
    const progress = `[${this.completedTests + 1}/${this.totalTests}]`;
    console.log(`${progress} 🏃 Starting: ${test.title}`);
  }

  onTestEnd(test: TestCase, result: TestResult) {
    this.completedTests++;
    const testTime = result.duration;
    this.testTimes.push(testTime);
    
    const avgTime = this.testTimes.reduce((a, b) => a + b, 0) / this.testTimes.length;
    const remainingTests = this.totalTests - this.completedTests;
    const estimatedTimeRemaining = (avgTime * remainingTests) / 1000 / 60; // in minutes
    
    const progress = `[${this.completedTests}/${this.totalTests}]`;
    const status = result.status === 'passed' ? '✅' : 
                  result.status === 'failed' ? '❌' : 
                  result.status === 'skipped' ? '⏭️' : '❓';
    
    if (result.status === 'passed') this.passedTests++;
    else if (result.status === 'failed') this.failedTests++;
    else if (result.status === 'skipped') this.skippedTests++;
    
    console.log(`${progress} ${status} ${test.title} (${(testTime / 1000).toFixed(1)}s)`);
    
    // Show progress summary every 10 tests
    if (this.completedTests % 10 === 0 || this.completedTests === this.totalTests) {
      this.printProgressSummary(estimatedTimeRemaining);
    }
    
    // If test failed, show error details
    if (result.status === 'failed' && result.error) {
      console.log(`   └─ Error: ${result.error.message?.split('\n')[0]}`);
    }
  }

  onEnd() {
    const duration = (Date.now() - this.startTime) / 1000 / 60; // in minutes
    console.log('\n================================================================================');
    console.log('📊 FINAL TEST RESULTS');
    console.log('================================================================================');
    console.log(`✅ Passed:  ${this.passedTests}`);
    console.log(`❌ Failed:  ${this.failedTests}`);
    console.log(`⏭️  Skipped: ${this.skippedTests}`);
    console.log(`⏱️  Total Duration: ${duration.toFixed(1)} minutes`);
    console.log(`📈 Average Test Time: ${(this.testTimes.reduce((a, b) => a + b, 0) / this.testTimes.length / 1000).toFixed(1)}s`);
    console.log('================================================================================\n');
  }

  private countTests(suite: Suite): number {
    let count = 0;
    for (const child of suite.suites) {
      count += this.countTests(child);
    }
    count += suite.tests.length;
    return count;
  }

  private printProgressSummary(estimatedTimeRemaining: number) {
    const elapsedTime = (Date.now() - this.startTime) / 1000 / 60; // in minutes
    console.log(`\n📈 Progress: ${this.completedTests}/${this.totalTests} (${Math.round(this.completedTests / this.totalTests * 100)}%)`);
    console.log(`   ✅ ${this.passedTests} passed | ❌ ${this.failedTests} failed | ⏭️ ${this.skippedTests} skipped`);
    console.log(`   ⏱️  Elapsed: ${elapsedTime.toFixed(1)}min | Remaining: ~${estimatedTimeRemaining.toFixed(1)}min\n`);
  }
}

export default ProgressReporter;