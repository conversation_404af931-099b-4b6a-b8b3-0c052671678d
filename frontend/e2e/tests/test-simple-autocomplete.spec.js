// Simple test to verify autocomplete functionality without complex setup
const { test, expect } = require('@playwright/test');

test('simple autocomplete verification', async ({ page }) => {
  console.log('🧪 Starting simple autocomplete test...');
  
  try {
    // Navigate to connections page
    await page.goto('http://localhost:3000/connections');
    await page.waitForLoadState('networkidle');
    
    // Check if page loads correctly
    const title = await page.title();
    console.log(`✅ Page loaded: ${title}`);
    
    // Look for create button
    const createButton = page.locator('[data-testid="create-new-connection-button"]');
    await createButton.waitFor({ state: 'visible', timeout: 10000 });
    console.log('✅ Create button found');
    
    // Click create button
    await createButton.click();
    
    // Wait for form to appear
    const form = page.locator('[data-testid="connection-form"]');
    await form.waitFor({ state: 'visible', timeout: 10000 });
    console.log('✅ Connection form opened');
    
    // Check if autocomplete inputs are present
    const fromInput = page.locator('[data-testid="connection-from-entity-input"]');
    const toInput = page.locator('[data-testid="connection-to-entity-input"]');
    
    await fromInput.waitFor({ state: 'visible', timeout: 5000 });
    await toInput.waitFor({ state: 'visible', timeout: 5000 });
    console.log('✅ Autocomplete inputs found');
    
    // Try typing in the first input
    await fromInput.click();
    await fromInput.fill('Test');
    console.log('✅ Successfully typed in from input');
    
    // Try typing in the second input
    await toInput.click();
    await toInput.fill('Test2');
    console.log('✅ Successfully typed in to input');
    
    console.log('🎉 Simple autocomplete test passed!');
    
  } catch (error) {
    console.error(`❌ Simple autocomplete test failed: ${error.message}`);
    throw error;
  }
});
