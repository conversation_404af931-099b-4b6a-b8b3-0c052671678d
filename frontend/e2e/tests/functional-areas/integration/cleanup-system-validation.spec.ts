import { test, expect } from '@playwright/test';
import { UnifiedDatabaseCleanup } from '../../../utils/unified-database-cleanup';
import { TestLifecycleCleanup } from '../../../utils/test-lifecycle-cleanup';
import { WorkerCleanupIsolation } from '../../../utils/worker-cleanup-isolation';
import { DatabaseStateVerifier } from '../../../utils/database-state-verifier';
import { CleanupMonitoring } from '../../../utils/cleanup-monitoring';

/**
 * Cleanup System Validation Tests
 * 
 * These tests validate that the new cleanup system works reliably:
 * - Unified database cleanup functionality
 * - Test lifecycle cleanup procedures
 * - Worker isolation effectiveness
 * - Database state verification accuracy
 * - Cleanup monitoring and performance tracking
 * 
 * Note: These are system validation tests, not regular E2E tests
 */
test.describe('Cleanup System Validation @integration @cleanup', () => {
  test('should perform unified database cleanup correctly', async ({ page }) => {
    console.log('🧪 Testing unified database cleanup system');

    const cleanup = UnifiedDatabaseCleanup.getInstance(page);
    
    // Test health check
    const healthCheck = await cleanup.healthCheck();
    expect(healthCheck.healthy).toBe(true);
    console.log(`  ✓ Health check passed: ${healthCheck.response}`);

    // Test complete cleanup
    const cleanupResult = await cleanup.performCompleteCleanup();
    expect(cleanupResult.success).toBe(true);
    console.log(`  ✓ Complete cleanup successful: ${cleanupResult.entitiesDeleted} entities, ${cleanupResult.connectionsDeleted} connections in ${cleanupResult.duration}ms`);

    // Test database verification
    const verificationResult = await cleanup.verifyDatabaseCleanState();
    expect(verificationResult.isClean).toBe(true);
    console.log(`  ✓ Database clean state verified`);

    // Test metrics
    const metrics = cleanup.getCleanupMetrics();
    expect(metrics.cleanupOperations).toBeGreaterThan(0);
    console.log(`  ✓ Cleanup metrics tracked: ${metrics.cleanupOperations} operations`);
  });

  test('should handle test lifecycle cleanup properly', async ({ page }) => {
    console.log('🧪 Testing test lifecycle cleanup system');

    const lifecycle = TestLifecycleCleanup.getInstance(page);
    
    // Test pre-test cleanup
    const preTestResult = await lifecycle.performPreTestCleanup();
    expect(preTestResult.success).toBe(true);
    expect(preTestResult.databaseStateClean).toBe(true);
    console.log(`  ✓ Pre-test cleanup successful: ${preTestResult.itemsCleaned} items in ${preTestResult.duration}ms`);

    // Test post-test cleanup
    const postTestResult = await lifecycle.performPostTestCleanup();
    expect(postTestResult.success).toBe(true);
    expect(postTestResult.databaseStateClean).toBe(true);
    console.log(`  ✓ Post-test cleanup successful: ${postTestResult.itemsCleaned} items in ${postTestResult.duration}ms`);

    // Test session metrics
    const metrics = lifecycle.getTestSessionMetrics();
    expect(metrics.preTestCleanups).toBeGreaterThan(0);
    expect(metrics.postTestCleanups).toBeGreaterThan(0);
    console.log(`  ✓ Session metrics tracked: ${metrics.testsRun} tests, ${Math.round(metrics.successRate)}% success rate`);

    // Test cleanup health validation
    const healthValidation = await lifecycle.validateCleanupHealth();
    expect(healthValidation.healthy).toBe(true);
    console.log(`  ✓ Cleanup health validation passed`);
  });

  test('should maintain worker isolation correctly', async ({ page }) => {
    console.log('🧪 Testing worker isolation system');

    const isolation = new WorkerCleanupIsolation(page);
    
    // Test isolated entity name creation
    const entityName1 = isolation.createIsolatedEntityName('TestEntity');
    const entityName2 = isolation.createIsolatedEntityName('AnotherEntity');
    
    expect(isolation.isMyEntity(entityName1)).toBe(true);
    expect(isolation.isMyEntity(entityName2)).toBe(true);
    console.log(`  ✓ Isolated entity names created: "${entityName1}", "${entityName2}"`);

    // Test isolated cleanup
    const isolatedCleanupResult = await isolation.performIsolatedCleanup();
    expect(isolatedCleanupResult.success).toBe(true);
    expect(isolatedCleanupResult.isolationViolations).toBe(0);
    console.log(`  ✓ Isolated cleanup successful: ${isolatedCleanupResult.entitiesDeleted} entities, ${isolatedCleanupResult.connectionsDeleted} connections`);

    // Test isolation integrity
    const integrityResult = await isolation.verifyIsolationIntegrity();
    expect(integrityResult.intact).toBe(true);
    expect(integrityResult.isolationViolations).toBe(0);
    console.log(`  ✓ Isolation integrity verified: ${integrityResult.crossWorkerEntities} cross-worker entities`);

    // Test metrics
    const metrics = isolation.getIsolationMetrics();
    expect(metrics.workerId).toBeDefined();
    expect(metrics.workerPrefix).toBeDefined();
    console.log(`  ✓ Isolation metrics tracked for worker ${metrics.workerId}`);

    // Cleanup
    isolation.unregisterWorker();
  });

  test('should verify database state accurately', async ({ page }) => {
    console.log('🧪 Testing database state verification system');

    const verifier = DatabaseStateVerifier.getInstance(page);
    
    // Test quick verification
    const quickResult = await verifier.verifyDatabaseState('quick');
    expect(quickResult.clean).toBe(true);
    expect(quickResult.summary).toBeDefined();
    expect(quickResult.performance.efficiency).toMatch(/excellent|good|poor/);
    console.log(`  ✓ Quick verification completed in ${quickResult.performance.verificationTime}ms (${quickResult.performance.efficiency})`);

    // Test standard verification
    const standardResult = await verifier.verifyDatabaseState('standard');
    expect(standardResult.clean).toBe(true);
    expect(standardResult.summary.totalEntities).toBeGreaterThanOrEqual(0);
    expect(standardResult.summary.totalConnections).toBeGreaterThanOrEqual(0);
    console.log(`  ✓ Standard verification found ${standardResult.summary.totalEntities} entities, ${standardResult.summary.totalConnections} connections`);

    // Test thorough verification
    const thoroughResult = await verifier.verifyDatabaseState('thorough');
    expect(thoroughResult.clean).toBe(true);
    expect(thoroughResult.recommendations).toBeInstanceOf(Array);
    console.log(`  ✓ Thorough verification completed with ${thoroughResult.recommendations.length} recommendations`);

    // Test verification metrics
    const metrics = verifier.getVerificationMetrics();
    expect(metrics.verificationsRun).toBeGreaterThan(0);
    console.log(`  ✓ Verification metrics tracked: ${metrics.verificationsRun} runs, ${Math.round(metrics.averageVerificationTime)}ms average`);
  });

  test('should monitor cleanup performance effectively', async ({ page }) => {
    console.log('🧪 Testing cleanup monitoring system');

    const monitoring = CleanupMonitoring.getInstance(page);
    
    // Test operation monitoring
    const { operationId, endOperation } = monitoring.startCleanupOperation('test-cleanup');
    expect(operationId).toBeDefined();
    console.log(`  ✓ Started monitoring operation: ${operationId}`);

    // Simulate successful operation
    await page.waitForTimeout(100); // Simulate some work
    endOperation(true, 5); // Success with 5 items processed
    console.log(`  ✓ Completed monitored operation successfully`);

    // Test performance metrics
    const performance = monitoring.getOperationPerformance('test-cleanup');
    expect(performance.exists).toBe(true);
    expect(performance.totalOperations).toBeGreaterThan(0);
    expect(performance.successRate).toBe(100);
    console.log(`  ✓ Performance metrics recorded: ${performance.totalOperations} operations, ${Math.round(performance.successRate)}% success`);

    // Test session summary
    const summary = monitoring.getSessionSummary();
    expect(summary.totalOperations).toBeGreaterThan(0);
    expect(summary.overallHealth).toMatch(/healthy|warning|critical/);
    console.log(`  ✓ Session summary: ${summary.totalOperations} operations, ${summary.overallHealth} health`);

    // Test health check
    const isHealthy = monitoring.isHealthy();
    expect(typeof isHealthy).toBe('boolean');
    console.log(`  ✓ System health check: ${isHealthy ? 'healthy' : 'unhealthy'}`);

    // Test monitoring report
    const report = monitoring.generateMonitoringReport();
    expect(report).toContain('Cleanup Monitoring Report');
    console.log(`  ✓ Monitoring report generated successfully`);
  });

  test('should handle error scenarios gracefully', async ({ page }) => {
    console.log('🧪 Testing error handling in cleanup system');

    const cleanup = UnifiedDatabaseCleanup.getInstance(page);
    
    // Test emergency cleanup
    await cleanup.emergencyCleanup();
    console.log(`  ✓ Emergency cleanup completed without errors`);

    // Test database verification after emergency cleanup
    const verificationResult = await cleanup.verifyDatabaseCleanState();
    expect(verificationResult.isClean).toBe(true);
    console.log(`  ✓ Database state clean after emergency cleanup`);

    // Test lifecycle force clean
    const lifecycle = TestLifecycleCleanup.getInstance(page);
    const forceCleanResult = await lifecycle.forceCleanDatabase();
    expect(forceCleanResult.success).toBe(true);
    console.log(`  ✓ Force clean database successful: ${forceCleanResult.actions.length} actions taken`);

    // Test verification with potentially corrupted data
    const verifier = DatabaseStateVerifier.getInstance(page);
    const verificationAfterForce = await verifier.verifyDatabaseState('thorough');
    expect(verificationAfterForce.clean).toBe(true);
    console.log(`  ✓ Database verification passed after force clean`);
  });

  test('should provide comprehensive cleanup integration', async ({ page }) => {
    console.log('🧪 Testing integrated cleanup system functionality');

    // Initialize all systems
    const cleanup = UnifiedDatabaseCleanup.getInstance(page);
    const lifecycle = TestLifecycleCleanup.getInstance(page);
    const verifier = DatabaseStateVerifier.getInstance(page);
    const monitoring = CleanupMonitoring.getInstance(page);
    
    // Test complete integrated workflow
    console.log('  🔄 Starting integrated cleanup workflow...');
    
    // 1. Monitor the entire process
    const { endOperation } = monitoring.startCleanupOperation('integrated-test');
    
    // 2. Pre-test cleanup
    const preTestResult = await lifecycle.performPreTestCleanup();
    expect(preTestResult.success).toBe(true);
    
    // 3. Verify clean state
    const verificationResult = await verifier.verifyDatabaseState('standard');
    expect(verificationResult.clean).toBe(true);
    
    // 4. Post-test cleanup
    const postTestResult = await lifecycle.performPostTestCleanup();
    expect(postTestResult.success).toBe(true);
    
    // 5. Final verification
    const finalVerification = await verifier.verifyDatabaseState('quick');
    expect(finalVerification.clean).toBe(true);
    
    // 6. End monitoring
    const totalItems = preTestResult.itemsCleaned + postTestResult.itemsCleaned;
    endOperation(true, totalItems);
    
    console.log(`  ✓ Integrated workflow successful: ${totalItems} total items cleaned`);
    
    // Test system health
    const systemHealthy = monitoring.isHealthy();
    expect(systemHealthy).toBe(true);
    console.log(`  ✓ System health after integration test: ${systemHealthy ? 'healthy' : 'unhealthy'}`);
    
    // Generate final reports
    const cleanupReport = cleanup.generateCleanupReport();
    const monitoringReport = monitoring.generateMonitoringReport();
    const verificationReport = verifier.generateVerificationReport();
    
    expect(cleanupReport).toContain('Cleanup Report');
    expect(monitoringReport).toContain('Monitoring Report');
    expect(verificationReport).toContain('Verification Performance Report');
    
    console.log(`  ✓ All system reports generated successfully`);
    
    // Test metrics collection
    const cleanupMetrics = cleanup.getCleanupMetrics();
    const sessionMetrics = lifecycle.getTestSessionMetrics();
    const verificationMetrics = verifier.getVerificationMetrics();
    const sessionSummary = monitoring.getSessionSummary();
    
    expect(cleanupMetrics.cleanupOperations).toBeGreaterThan(0);
    expect(sessionMetrics.testsRun).toBeGreaterThan(0);
    expect(verificationMetrics.verificationsRun).toBeGreaterThan(0);
    expect(sessionSummary.totalOperations).toBeGreaterThan(0);
    
    console.log(`  ✓ All metrics collected successfully`);
    console.log(`    - Cleanup operations: ${cleanupMetrics.cleanupOperations}`);
    console.log(`    - Session tests: ${sessionMetrics.testsRun}`);
    console.log(`    - Verifications: ${verificationMetrics.verificationsRun}`);
    console.log(`    - Monitoring operations: ${sessionSummary.totalOperations}`);
  });

  test('should demonstrate performance improvements', async ({ page }) => {
    console.log('🧪 Testing cleanup system performance improvements');

    const monitoring = CleanupMonitoring.getInstance(page);
    const lifecycle = TestLifecycleCleanup.getInstance(page);
    
    // Reset monitoring session for accurate measurements
    monitoring.resetSession();
    
    // Perform multiple cleanup operations to gather performance data
    const operationCount = 5;
    const operationTimes: number[] = [];
    
    for (let i = 0; i < operationCount; i++) {
      const startTime = Date.now();
      
      const result = await lifecycle.performPreTestCleanup();
      expect(result.success).toBe(true);
      
      const operationTime = Date.now() - startTime;
      operationTimes.push(operationTime);
      
      console.log(`    Operation ${i + 1}: ${operationTime}ms (${result.itemsCleaned} items)`);
    }
    
    // Calculate performance metrics
    const averageTime = operationTimes.reduce((sum, time) => sum + time, 0) / operationTimes.length;
    const minTime = Math.min(...operationTimes);
    const maxTime = Math.max(...operationTimes);
    
    console.log(`  ✓ Performance analysis complete:`);
    console.log(`    - Operations: ${operationCount}`);
    console.log(`    - Average time: ${Math.round(averageTime)}ms`);
    console.log(`    - Min time: ${minTime}ms`);
    console.log(`    - Max time: ${maxTime}ms`);
    console.log(`    - Performance consistency: ${maxTime <= minTime * 3 ? 'Good' : 'Needs improvement'}`);
    
    // Verify performance is within acceptable limits
    expect(averageTime).toBeLessThan(10000); // Should be under 10 seconds on average
    expect(maxTime).toBeLessThan(30000); // No single operation should take more than 30 seconds
    
    // Test monitoring efficiency assessment
    const sessionSummary = monitoring.getSessionSummary();
    expect(sessionSummary.efficiency).toMatch(/excellent|good|poor/);
    console.log(`  ✓ System efficiency rating: ${sessionSummary.efficiency}`);
    
    // Performance should be 'good' or better for a well-functioning system
    expect(['excellent', 'good']).toContain(sessionSummary.efficiency);
  });
});