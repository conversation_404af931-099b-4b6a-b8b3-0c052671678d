import { test, expect } from '@playwright/test';
import { EntityManagerPage, ConnectionManagerPage } from '../../../fixtures/page-objects';
import { TestHelpers } from '../../../utils/helpers';
import { BrowserTimingConfig, TimingUtils } from '../../../utils/timing-config';
import { testEntities, validConnections, invalidConnections, availableUnits } from '../../../fixtures/test-data';

test.describe('Connection Management @connections @functional', () => {
  let entityPage: EntityManagerPage;
  let connectionPage: ConnectionManagerPage;
  let helpers: TestHelpers;
  let entityIds: Record<string, string> = {};

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.setupDialogHandler();
    
    // Clean up any leftover test data
    await helpers.cleanupBeforeTest();
    
    entityPage = new EntityManagerPage(page);
    connectionPage = new ConnectionManagerPage(page);
    
    // PERFORMANCE OPTIMIZATION: Create test entities in parallel using API
    console.log('🚀 Setting up test entities for connection tests using parallel creation...');
    const setupStartTime = Date.now();
    
    try {
      // Generate unique names for all test entities
      const entityNames = await Promise.all(
        testEntities.map(async (entity) => 
          helpers.generateUniqueEntityName(`${entity.name} Test`)
        )
      );
      
      console.log(`Generated ${entityNames.length} unique entity names for parallel creation`);
      
      // Create all entities in parallel using direct API calls (much faster than UI)
      const createdEntities = await helpers.createEntitiesParallel(entityNames);
      
      // Build entity name to ID mapping for tests
      createdEntities.forEach(entity => {
        entityIds[entity.name] = entity.id;
      });
      
      const setupDuration = Date.now() - setupStartTime;
      console.log(`🚀 Parallel entity setup complete in ${setupDuration}ms (vs ~${testEntities.length * 500}ms sequential)`);
      console.log(`✅ All ${createdEntities.length} test entities created successfully for connection tests`);
      
      // Navigate to connections page and verify entities are available
      await connectionPage.goto('/connections');
      await helpers.waitForAppReady();
      
      // Enhanced verification for connections page context
      console.log('Verifying entities are available for connections page (API verification)...');
      const browserName = page.context().browser()?.browserType().name();

      // Wait for API-UI synchronization
      await TimingUtils.waitForApiUiSync(page, browserName);

      // For connections page, verify entities via API since they're not visible as text
      // They only appear in autocomplete dropdowns when typing
      try {
        const response = await page.request.get('http://localhost:8000/api/v1/entities');
        if (!response.ok()) {
          throw new Error(`API verification failed: ${response.status()}`);
        }

        const apiEntities = await response.json();
        const apiEntityNames = apiEntities.map((e: any) => e.name);

        for (let i = 0; i < Math.min(entityNames.length, 3); i++) {
          const entityName = entityNames[i];
          console.log(`  🔍 Verifying entity ${i + 1} via API: "${entityName}"`);

          if (!apiEntityNames.includes(entityName)) {
            throw new Error(`Entity "${entityName}" not found in API response`);
          }
          console.log(`  ✅ Entity ${i + 1} confirmed via API: "${entityName}"`);
        }

        console.log(`✅ All ${Math.min(entityNames.length, 3)} entities verified via API for connections page`);

      } catch (error) {
        console.error(`❌ API verification failed: ${error}`);
        throw error;
      }
      
    } catch (error) {
      const setupDuration = Date.now() - setupStartTime;
      console.error(`❌ Parallel entity setup failed after ${setupDuration}ms: ${error}`);
      console.error('Falling back to sequential entity creation...');
      
      // Fallback to original sequential approach if parallel fails
      await entityPage.goto('/entities');
      await helpers.waitForAppReady();
      
      for (let i = 0; i < testEntities.length; i++) {
        const entity = testEntities[i];
        const uniqueName = helpers.generateUniqueEntityName(`${entity.name} Test`);
        
        console.log(`Creating test entity ${i + 1}/${testEntities.length}: ${uniqueName}`);
        
        const entityId = await helpers.createEntityForComplexTest(entityPage, uniqueName);
        entityIds[uniqueName] = entityId;
        
        await expect(page.locator(`:text("${uniqueName}")`)).toBeVisible({ timeout: 8000 });
        console.log(`✅ Test entity ${i + 1} created successfully: ${uniqueName}`);
        
        if (i < testEntities.length - 1) {
          await page.waitForLoadState('networkidle');
        }
      }
      
      console.log(`✅ All ${testEntities.length} test entities created successfully for connection tests (fallback)`);
      await connectionPage.goto('/connections');
      await helpers.waitForAppReady();
    }
  });

  test.afterEach(async ({ page }) => {
    // Clean up all test data created during this test
    await helpers.cleanupAfterTest();
  });

  test('should display connection management page correctly @connections @critical @smoke', async ({ page }) => {
    await expect(page.locator('h2:has-text("Connection Management")')).toBeVisible();
    await expect(connectionPage.createButton).toBeVisible();
    await expect(connectionPage.connectionList).toBeVisible();
  });

  test('should show and hide connection form correctly', async ({ page }) => {
    // Initially form should be hidden
    await expect(connectionPage.connectionForm).toBeHidden();
    await expect(connectionPage.createButton).toBeVisible();
    
    // Click create button should show form
    await connectionPage.clickCreateNew();
    await expect(connectionPage.connectionForm).toBeVisible();
    await expect(connectionPage.createButton).toBeHidden();
    
    // Cancel should hide form
    await connectionPage.cancelForm();
    await expect(connectionPage.connectionForm).toBeHidden();
    await expect(connectionPage.createButton).toBeVisible();
  });

  test('should create bidirectional connections @connections @crud @critical @smoke', async ({ page }) => {
    const connection = validConnections[0];
    const fromEntity = helpers.generateUniqueEntityName(connection.fromEntity);
    const toEntity = helpers.generateUniqueEntityName(connection.toEntity);
    const multiplier = connection.multiplier;
    
    // Create the entities first with same unit
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);
    
    // Go back to connections
    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();
    
    await connectionPage.createConnection(fromEntity, toEntity, multiplier);
    
    // Wait for the connection list to update after creation
    await page.waitForLoadState('networkidle');
    await page.waitForResponse(response => 
      response.url().includes('/api/v1/connections') && response.status() === 200
    ).catch(() => null);
    
    // Use the page object method to check connection visibility 
    await expect(await connectionPage.isConnectionVisible(fromEntity, toEntity, multiplier, "Length")).toBe(true);
    
    // Calculate inverse multiplier and check bidirectional connection
    const inverseMultiplier = (1 / parseFloat(multiplier)).toFixed(1);
    await expect(await connectionPage.isConnectionVisible(toEntity, fromEntity, inverseMultiplier, "Length")).toBe(true);
  });

  test('should validate positive relationship values with real-time validation @connections @validation @high @functional', async ({ page }) => {
    const fromEntity = helpers.generateUniqueEntityName('Human');
    const toEntity = helpers.generateUniqueEntityName('Basketball');
    
    // Create entities with same unit
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);
    
    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();
    await connectionPage.clickCreateNew();
    
    // Fill form with valid entities first  
    await connectionPage.selectAutoCompleteOption(connectionPage.fromEntityInput, fromEntity);
    await connectionPage.selectAutoCompleteOption(connectionPage.toEntityInput, toEntity);
    
    // Select unit
    await page.locator('#unit').selectOption({ label: 'Length (m)' });
    
    // Test negative multiplier with real-time validation
    await connectionPage.multiplierInput.fill('-5.0');
    await connectionPage.multiplierInput.blur(); // Trigger validation
    await connectionPage.multiplierError.waitFor({ state: 'visible', timeout: 5000 });
    
    // Should show error immediately
    await expect(connectionPage.multiplierError).toBeVisible();
    await expect(connectionPage.multiplierError).toContainText(/positive|greater than zero/i);
    await expect(connectionPage.submitButton).toBeDisabled();
    await expect(connectionPage.multiplierInput).toHaveClass(/invalid/);
    await expect(connectionPage.multiplierInput).toHaveAttribute('aria-invalid', 'true');
    
    // Test fixing the error shows valid state
    await connectionPage.multiplierInput.fill('2.5');
    await connectionPage.multiplierInput.blur();
    await connectionPage.multiplierError.waitFor({ state: 'hidden', timeout: 5000 }).catch(() => null);
    
    // Should show valid state
    await expect(connectionPage.multiplierError).not.toBeVisible();
    await expect(connectionPage.submitButton).toBeEnabled();
    await expect(connectionPage.multiplierInput).toHaveClass(/valid/);
    await expect(connectionPage.multiplierInput).toHaveAttribute('aria-invalid', 'false');
  });

  test('should validate decimal precision with real-time validation', async ({ page }) => {
    const fromEntity = helpers.generateUniqueEntityName('Human');
    const toEntity = helpers.generateUniqueEntityName('Basketball');
    
    // Create entities with same unit
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);
    
    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();
    await connectionPage.clickCreateNew();
    
    // Fill form with valid entities first  
    await connectionPage.selectAutoCompleteOption(connectionPage.fromEntityInput, fromEntity);
    await connectionPage.selectAutoCompleteOption(connectionPage.toEntityInput, toEntity);
    
    // Select unit
    await page.locator('#unit').selectOption({ label: 'Length (m)' });
    
    // Test too many decimal places with real-time validation
    await connectionPage.multiplierInput.fill('2.555');
    await connectionPage.multiplierInput.blur(); // Trigger validation

    // Use browser-specific validation timeout
    const browserName = page.context().browser()?.browserType().name();
    const validationTimeout = BrowserTimingConfig.getOperationTimeout('formValidationTimeout', browserName);

    // Wait for specific multiplier error (not general error message)
    await connectionPage.multiplierError.waitFor({ state: 'visible', timeout: validationTimeout });

    // Should show specific multiplier error
    await expect(connectionPage.multiplierError).toBeVisible();
    await expect(connectionPage.multiplierError).toContainText(/decimal|precision/i);
    await expect(connectionPage.submitButton).toBeDisabled();
    await expect(connectionPage.multiplierInput).toHaveClass(/invalid/);

    // Test valid decimal precision
    await connectionPage.multiplierInput.fill('2.5');
    await connectionPage.multiplierInput.blur();

    // Wait for form validation to complete
    await TimingUtils.waitForFormValidation(page, browserName);
    await connectionPage.multiplierError.waitFor({ state: 'hidden', timeout: validationTimeout }).catch(() => null);

    // Should show valid state (no multiplier error)
    await expect(connectionPage.multiplierError).not.toBeVisible();
    await expect(connectionPage.submitButton).toBeEnabled();
    await expect(connectionPage.multiplierInput).toHaveClass(/valid/);
  });

  test('should prevent zero multiplier values', async ({ page }) => {
    const fromEntity = helpers.generateUniqueEntityName('Human');
    const toEntity = helpers.generateUniqueEntityName('Basketball');
    
    // Create entities with same unit
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);
    
    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();
    await connectionPage.clickCreateNew();
    
    // Fill form with valid entities first  
    await connectionPage.selectAutoCompleteOption(connectionPage.fromEntityInput, fromEntity);
    await connectionPage.selectAutoCompleteOption(connectionPage.toEntityInput, toEntity);
    
    // Select unit
    await page.locator('#unit').selectOption({ label: 'Length (m)' });
    
    // Test zero multiplier - submit button should be disabled and error shown
    await connectionPage.multiplierInput.fill('0');
    await connectionPage.multiplierInput.blur();

    // Use browser-specific validation timeout
    const browserName = page.context().browser()?.browserType().name();
    const validationTimeout = BrowserTimingConfig.getOperationTimeout('formValidationTimeout', browserName);

    // Wait for specific multiplier error (not general error message)
    await connectionPage.multiplierError.waitFor({ state: 'visible', timeout: validationTimeout });

    // Submit button should be disabled
    await expect(connectionPage.submitButton).toBeDisabled();

    // Should show specific multiplier error message
    await expect(connectionPage.multiplierError).toBeVisible();
    await expect(connectionPage.multiplierError).toContainText(/positive|greater than zero/i);
  });

  test('should validate required unit selection', async ({ page }) => {
    const fromEntity = helpers.generateUniqueEntityName('Human');
    const toEntity = helpers.generateUniqueEntityName('Car');

    // Create entities
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);

    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();
    await connectionPage.clickCreateNew();

    // Fill in entities and multiplier but leave unit empty
    await connectionPage.selectAutoCompleteOption(connectionPage.fromEntityInput, fromEntity);
    await connectionPage.selectAutoCompleteOption(connectionPage.toEntityInput, toEntity);
    await connectionPage.multiplierInput.fill('2.0');

    // Try to submit without selecting unit
    await connectionPage.submitButton.click();

    // Should show error message about required unit
    await expect(connectionPage.unitError).toBeVisible();
    await expect(connectionPage.unitError).toContainText(/unit.*required/i);
    await expect(connectionPage.submitButton).toBeDisabled();
  });

  test('should handle autocomplete in entity selection', async ({ page }) => {
    const fromEntity = await helpers.generateUniqueEntityName('Human Test');
    const toEntity = await helpers.generateUniqueEntityName('Basketball Test');

    // Create entities using API for reliability (avoid UI timing issues)
    console.log('Creating entities via API for autocomplete test...');
    try {
      const entities = await helpers.createEntitiesParallel([fromEntity, toEntity]);
      console.log(`✅ Created entities for autocomplete test: ${entities.map(e => e.name).join(', ')}`);
    } catch (error) {
      console.log('⚠️ API creation failed, falling back to UI creation...');
      await entityPage.goto('/entities');
      await helpers.waitForAppReady();

      // Create entities with enhanced error handling
      await entityPage.createEntity(fromEntity);
      await page.waitForTimeout(1000); // Allow UI to settle
      await entityPage.createEntity(toEntity);
      await page.waitForTimeout(1000); // Allow UI to settle
    }

    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();

    // Wait for page to be fully loaded before starting autocomplete test
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000); // Additional settling time

    await connectionPage.clickCreateNew();

    // Wait for form to be fully ready
    await connectionPage.fromEntityInput.waitFor({ state: 'visible', timeout: 10000 });
    await connectionPage.toEntityInput.waitFor({ state: 'visible', timeout: 10000 });

    // Use proper AutoComplete selection with enhanced error handling
    console.log(`Testing autocomplete with entities: "${fromEntity}" and "${toEntity}"`);

    try {
      await connectionPage.selectAutoCompleteOption(connectionPage.fromEntityInput, fromEntity);
      await connectionPage.fromEntityInput.waitFor({ state: 'attached' });
      console.log(`✅ From entity autocomplete successful: ${fromEntity}`);

      await connectionPage.selectAutoCompleteOption(connectionPage.toEntityInput, toEntity);
      await connectionPage.toEntityInput.waitFor({ state: 'attached' });
      console.log(`✅ To entity autocomplete successful: ${toEntity}`);
    } catch (autocompleteError) {
      console.log(`⚠️ Autocomplete failed: ${autocompleteError.message}`);
      throw autocompleteError;
    }
    
    // Select unit
    const unitSelect = page.locator('#unit');
    await unitSelect.selectOption({ label: 'Length (m)' });
    
    // Fill multiplier and trigger validation
    await connectionPage.multiplierInput.fill('2.5');
    await connectionPage.multiplierInput.blur(); // Trigger validation
    await connectionPage.submitButton.waitFor({ state: 'attached' });
    
    // Wait for submit button to be enabled
    await page.waitForFunction(() => {
      const submitBtn = document.querySelector('[data-testid="connection-submit-button"]') as HTMLButtonElement;
      return submitBtn && !submitBtn.disabled;
    }, { timeout: 5000 });
    
    await connectionPage.submitButton.click();
    
    // Should create connection successfully (no errors visible)
    await expect(connectionPage.multiplierError).not.toBeVisible();
    await expect(connectionPage.entitiesError).not.toBeVisible();

    // Verify connection appears in list using enhanced verification
    await connectionPage.verifyConnection(fromEntity, toEntity, '2.5', 'Length');
  });

  test('should delete a connection successfully', async ({ page }) => {
    const fromEntity = helpers.generateUniqueEntityName('Delete From');
    const toEntity = helpers.generateUniqueEntityName('Delete To');
    
    // Create entities and connection
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);
    
    await connectionPage.goto('/connections');
    await connectionPage.createConnection(fromEntity, toEntity, '3.0');

    // Verify connection exists using enhanced verification
    await connectionPage.verifyConnection(fromEntity, toEntity, '3.0', 'Length');

    // Delete the connection
    await connectionPage.deleteConnection(fromEntity, toEntity);

    // Wait for deletion to process
    await page.waitForLoadState('networkidle');

    // Verify connection is removed - use try/catch since verifyConnection expects connection to exist
    try {
      await connectionPage.verifyConnection(fromEntity, toEntity, '3.0', 'Length');
      throw new Error('Connection should have been deleted but still exists');
    } catch (error) {
      if (error.message.includes('Connection not found')) {
        console.log('✅ Connection successfully deleted');
      } else {
        throw error;
      }
    }
  });

  test('should handle connection form validation for required fields with real-time feedback', async ({ page }) => {
    await connectionPage.clickCreateNew();
    
    // Submit button should be disabled with empty fields
    await expect(connectionPage.submitButton).toBeDisabled();
    
    // Test entity selection validation
    const fromEntity = helpers.generateUniqueEntityName('Test From');
    const toEntity = helpers.generateUniqueEntityName('Test To');
    
    // Create test entities first
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);
    await connectionPage.goto('/connections');
    await connectionPage.clickCreateNew();
    
    // Test same entity selection error
    await connectionPage.selectAutoCompleteOption(connectionPage.fromEntityInput, fromEntity);
    await connectionPage.selectAutoCompleteOption(connectionPage.toEntityInput, fromEntity); // Same entity
    await connectionPage.errorMessage.waitFor({ state: 'visible', timeout: 5000 });
    
    // Should show "Both entities must be selected" error
    await expect(connectionPage.errorMessage).toBeVisible();
    await expect(connectionPage.errorMessage).toContainText(/Both entities must be selected/i);
    await expect(connectionPage.submitButton).toBeDisabled();
    
    // Fix by selecting different entity
    await connectionPage.selectAutoCompleteOption(connectionPage.toEntityInput, toEntity);
    await connectionPage.errorMessage.waitFor({ state: 'hidden', timeout: 5000 }).catch(() => null);
    
    // Should clear the error
    await expect(connectionPage.errorMessage).not.toBeVisible();
    
    // But submit should still be disabled due to missing unit and multiplier
    await expect(connectionPage.submitButton).toBeDisabled();
    
    // Add unit selection
    await page.locator('#unit').selectOption({ label: 'Length (m)' });
    await page.locator('#unit').waitFor({ state: 'attached' });
    
    // Still disabled due to missing multiplier
    await expect(connectionPage.submitButton).toBeDisabled();
    
    // Add valid multiplier
    await connectionPage.multiplierInput.fill('2.5');
    await connectionPage.multiplierInput.blur();
    await connectionPage.submitButton.waitFor({ state: 'attached' });
    
    // Now submit should be enabled
    await expect(connectionPage.submitButton).toBeEnabled();
    await expect(connectionPage.multiplierInput).toHaveClass(/valid/);
  });

  test('should show real-time validation states across all connection form fields', async ({ page }) => {
    await connectionPage.clickCreateNew();
    
    // All fields should start in neutral state
    await expect(connectionPage.fromEntityInput).not.toHaveClass(/valid|invalid/);
    await expect(connectionPage.toEntityInput).not.toHaveClass(/valid|invalid/);
    await expect(connectionPage.multiplierInput).not.toHaveClass(/valid|invalid/);
    
    // Create test entities
    const fromEntity = helpers.generateUniqueEntityName('Multi From');
    const toEntity = helpers.generateUniqueEntityName('Multi To');
    
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);
    await connectionPage.goto('/connections');
    await connectionPage.clickCreateNew();
    
    // Test progressive form completion with validation states
    
    // 1. Select from entity - should complete successfully
    await connectionPage.selectAutoCompleteOption(connectionPage.fromEntityInput, fromEntity);
    await connectionPage.fromEntityInput.waitFor({ state: 'attached' });
    // Validation: Check that entity was selected (functional test, not CSS class)
    await expect(connectionPage.fromEntityInput).toHaveValue(fromEntity);
    
    // 2. Select same entity for to - should show error
    await connectionPage.selectAutoCompleteOption(connectionPage.toEntityInput, fromEntity);
    await connectionPage.errorMessage.waitFor({ state: 'visible', timeout: 5000 });
    await expect(connectionPage.errorMessage).toBeVisible();
    await expect(connectionPage.errorMessage).toContainText(/Both entities must be selected/i);
    
    // 3. Fix by selecting different entity - should clear error
    await connectionPage.selectAutoCompleteOption(connectionPage.toEntityInput, toEntity);
    await connectionPage.errorMessage.waitFor({ state: 'hidden', timeout: 5000 }).catch(() => null);
    await expect(connectionPage.errorMessage).not.toBeVisible();
    // Validation: Check that different entity was selected
    await expect(connectionPage.toEntityInput).toHaveValue(toEntity);
    
    // 4. Test multiplier validation progression
    await connectionPage.multiplierInput.fill('-1'); // Invalid
    await connectionPage.multiplierInput.blur();
    await connectionPage.errorMessage.waitFor({ state: 'visible', timeout: 5000 });
    // Check submit button is disabled for invalid input
    await expect(connectionPage.submitButton).toBeDisabled();
    
    await connectionPage.multiplierInput.fill('2.555'); // Too many decimals
    await connectionPage.multiplierInput.blur();
    await connectionPage.errorMessage.waitFor({ state: 'visible', timeout: 5000 });
    // Check submit button is still disabled
    await expect(connectionPage.submitButton).toBeDisabled();
    
    await connectionPage.multiplierInput.fill('2.5'); // Valid
    await connectionPage.multiplierInput.blur();
    await connectionPage.errorMessage.waitFor({ state: 'hidden', timeout: 5000 }).catch(() => null);
    // Check submit button becomes enabled with valid input
    await expect(connectionPage.submitButton).toBeEnabled();
    
    // 5. Add unit selection to complete form
    await page.locator('#unit').selectOption({ label: 'Length (m)' });
    await page.locator('#unit').waitFor({ state: 'attached' });
    
    // Final state: all fields valid, submit enabled
    await expect(connectionPage.submitButton).toBeEnabled();
    await expect(connectionPage.fromEntityInput).toHaveClass(/valid/);
    await expect(connectionPage.toEntityInput).toHaveClass(/valid/);
    await expect(connectionPage.multiplierInput).toHaveClass(/valid/);
  });

  test('should prevent duplicate connections', async ({ page }) => {
    const fromEntity = helpers.generateUniqueEntityName('Duplicate From');
    const toEntity = helpers.generateUniqueEntityName('Duplicate To');
    
    // Create entities
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);
    
    await connectionPage.goto('/connections');
    
    // Create first connection
    await connectionPage.createConnection(fromEntity, toEntity, '2.0');
    await connectionPage.verifyConnection(fromEntity, toEntity, '2.0', 'Length');
    
    // Try to create duplicate connection
    await connectionPage.createConnection(fromEntity, toEntity, '3.0');
    
    // Should show error message
    await expect(connectionPage.errorMessage).toBeVisible();
    await expect(connectionPage.errorMessage).toContainText(/already exists|duplicate/i);
  });

  test('should handle rapid connection creation', async ({ page }) => {
    const connections = [
      { from: helpers.generateUniqueEntityName('Rapid A'), to: helpers.generateUniqueEntityName('Rapid B'), mult: '2.0' },
      { from: helpers.generateUniqueEntityName('Rapid C'), to: helpers.generateUniqueEntityName('Rapid D'), mult: '3.0' },
      { from: helpers.generateUniqueEntityName('Rapid E'), to: helpers.generateUniqueEntityName('Rapid F'), mult: '1.5' },
    ];
    
    // Create all entities first
    await entityPage.goto('/entities');
    for (const conn of connections) {
      await entityPage.createEntity(conn.from);
      await entityPage.createEntity(conn.to);
    }
    
    // Create all connections
    await connectionPage.goto('/connections');
    for (const conn of connections) {
      await connectionPage.createConnection(conn.from, conn.to, conn.mult);
      await connectionPage.verifyConnection(conn.from, conn.to, conn.mult, 'Length');
    }
  });

  test('should maintain connection list after page refresh', async ({ page }) => {
    const fromEntity = helpers.generateUniqueEntityName('Refresh From');
    const toEntity = helpers.generateUniqueEntityName('Refresh To');
    
    // Create entities and connection
    await entityPage.goto('/entities');
    await entityPage.createEntity(fromEntity);
    await entityPage.createEntity(toEntity);
    
    await connectionPage.goto('/connections');
    await connectionPage.createConnection(fromEntity, toEntity, '4.0');
    
    // Verify connection exists using enhanced verification
    await connectionPage.verifyConnection(fromEntity, toEntity, '4.0', 'Length');

    // Refresh page
    await page.reload();
    await helpers.waitForAppReady();

    // Connection should still be visible after refresh
    await connectionPage.verifyConnection(fromEntity, toEntity, '4.0', 'Length');
  });
});