import { test, expect } from '@playwright/test';

test.describe('Setup Verification @navigation @integration', () => {
  test('should verify basic Playwright setup @integration @low @smoke', async ({ page }) => {
    // Test that we can navigate to a basic page
    await page.goto('https://example.com');
    await expect(page).toHaveTitle(/Example Domain/);
  });

  test('should verify localhost accessibility @integration @medium @functional', async ({ page }) => {
    // Test that we can access localhost (assuming frontend is running)
    try {
      await page.goto('http://localhost:3000', { timeout: 5000 });
      // If this succeeds, the frontend is running
      await expect(page.locator('body')).toBeVisible();
    } catch (error) {
      // Frontend might not be running, which is fine for this verification
      console.log('Frontend not running at localhost:3000, which is expected during setup verification');
    }
  });

  test('should verify backend API accessibility @integration @medium @functional', async ({ page }) => {
    // Test that we can check backend health
    try {
      const response = await page.request.get('http://localhost:8000/api/v1/units');
      console.log('Backend API response status:', response.status());
      // Backend is accessible
    } catch (error) {
      // Backend might not be running, log for information
      console.log('Backend not accessible at localhost:8000, which is expected if services are not running');
    }
  });
});