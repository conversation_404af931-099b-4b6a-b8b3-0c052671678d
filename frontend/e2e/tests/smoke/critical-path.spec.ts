import { test, expect } from '@playwright/test';
import { BasePage, EntityManagerPage, ConnectionManagerPage } from '../../fixtures/page-objects';
import { TestHelpers } from '../../utils/helpers';

/**
 * Critical Path Smoke Tests
 * 
 * These tests cover the most essential functionality that must work
 * for the application to be considered functional. They should run
 * quickly (<2 minutes total) and catch major regressions.
 * 
 * Tags: @smoke @critical @functional
 */
test.describe('Critical Path Smoke Tests @smoke @critical @functional', () => {
  let helpers: TestHelpers;
  let basePage: BasePage;
  let entityPage: EntityManagerPage;
  let connectionPage: ConnectionManagerPage;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.setupDialogHandler();
    await helpers.cleanupBeforeTest();
    
    basePage = new BasePage(page);
    entityPage = new EntityManagerPage(page);
    connectionPage = new ConnectionManagerPage(page);
  });

  test.afterEach(async ({ page }) => {
    await helpers.cleanupAfterTest();
  });

  test('should load application and navigate to main pages @navigation @smoke @critical', async ({ page }) => {
    // Load homepage
    await basePage.goto();
    await helpers.waitForAppReady();
    await expect(page).toHaveTitle(/SIMILE/);
    await expect(page.locator('h2:has-text("Entity Comparison")')).toBeVisible();
    
    // Navigate to entities page
    await basePage.navigateTo('entities');
    await expect(page).toHaveURL(/\/entities/);
    await expect(page.locator('h2:has-text("Entity Management")')).toBeVisible();
    
    // Navigate to connections page
    await basePage.navigateTo('connections');
    await expect(page).toHaveURL(/\/connections/);
    await expect(page.locator('h2:has-text("Connection Management")')).toBeVisible();
  });

  test('should create entity and connection successfully @entities @connections @crud @smoke @critical', async ({ page }) => {
    // Create first entity
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    const entity1Name = helpers.generateUniqueEntityName('Smoke Test Entity 1');
    await helpers.createAndTrackEntityWithId(entityPage, entity1Name);
    await expect(page.locator(`:text("${entity1Name}")`)).toBeVisible();
    
    // Create second entity
    const entity2Name = helpers.generateUniqueEntityName('Smoke Test Entity 2');
    await helpers.createAndTrackEntityWithId(entityPage, entity2Name);
    await expect(page.locator(`:text("${entity2Name}")`)).toBeVisible();
    
    // Create connection between entities
    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();
    
    const connectionName = helpers.generateUniqueConnectionName('Smoke Test Connection');
    await helpers.createAndTrackConnectionWithId(connectionPage, connectionName, entity1Name, entity2Name);

    // Verify connection is visible in the connection list (by entity names, not connection name)
    const isVisible = await connectionPage.isConnectionVisible(entity1Name, entity2Name, '2.5');
    expect(isVisible).toBe(true);
  });

  test('should handle basic form validation @validation @smoke @medium', async ({ page }) => {
    // Test entity form validation
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    await entityPage.clickCreateNew();
    await expect(entityPage.entityForm).toBeVisible();
    
    // Try to submit empty form (force click on disabled button to test validation)
    await entityPage.submitButton.click({ force: true });

    // Should show validation error or button should remain disabled
    // Check if validation error is shown OR button remains disabled (both are valid validation behaviors)
    try {
      await expect(entityPage.nameInput).toHaveClass(/error|invalid/, { timeout: 2000 });
      console.log('Validation error class found on input');
    } catch {
      // If no error class, check that button is still disabled (also valid validation)
      await expect(entityPage.submitButton).toBeDisabled();
      console.log('Submit button remains disabled - validation working');
    }
    
    // Cancel form
    await entityPage.cancelForm();
    await expect(entityPage.createButton).toBeVisible();
  });

  test('should display entity and connection lists @entities @connections @smoke @medium', async ({ page }) => {
    // Check entities page loads with empty state or existing entities
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    await expect(entityPage.createButton).toBeVisible();
    await expect(entityPage.entityList).toBeVisible();
    
    // Check connections page loads with empty state or existing connections
    await connectionPage.goto('/connections');
    await helpers.waitForAppReady();
    
    await expect(connectionPage.createButton).toBeVisible();
    await expect(connectionPage.connectionList).toBeVisible();
  });
});
