#!/bin/bash

# SIMILE E2E Test Environment Setup
# Ensures both frontend and backend are running before tests

echo "🔧 SIMILE E2E Test Environment Setup"
echo "===================================="
echo ""

# Function to check if a port is in use
check_port() {
  local port=$1
  if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
    return 0  # Port is in use
  else
    return 1  # Port is free
  fi
}

# Function to wait for service to be ready
wait_for_service() {
  local url=$1
  local service_name=$2
  local max_attempts=30
  local attempt=1
  
  echo "⏳ Waiting for $service_name to be ready at $url..."
  
  while [ $attempt -le $max_attempts ]; do
    if curl -s "$url" > /dev/null 2>&1; then
      echo "✅ $service_name is ready!"
      return 0
    fi
    
    echo "   Attempt $attempt/$max_attempts - waiting..."
    sleep 2
    attempt=$((attempt + 1))
  done
  
  echo "❌ $service_name failed to start within $((max_attempts * 2)) seconds"
  return 1
}

# Check backend (port 8000)
echo "🔍 Checking backend server..."
if check_port 8000; then
  echo "✅ Backend server detected on port 8000"
  
  # Verify backend health
  if curl -s http://localhost:8000/api/v1/health > /dev/null 2>&1; then
    echo "✅ Backend health check passed"
  else
    echo "⚠️  Backend is running but health check failed"
    echo "   This might indicate backend issues"
  fi
else
  echo "❌ Backend server not running on port 8000"
  echo ""
  echo "🚀 Starting backend server..."
  echo "   Command: cd ../../backend && python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000"
  echo ""
  echo "   Please start the backend manually in another terminal:"
  echo "   1. cd ../../backend"
  echo "   2. python -m uvicorn main:app --reload"
  echo ""
  echo "   Or use the backend startup script if available."
  exit 1
fi

# Check frontend (port 3000)
echo ""
echo "🔍 Checking frontend server..."
if check_port 3000; then
  echo "✅ Frontend server detected on port 3000"
  
  # Verify frontend accessibility
  if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend accessibility check passed"
  else
    echo "⚠️  Frontend is running but not accessible"
  fi
else
  echo "ℹ️  Frontend server not running - Playwright will start it automatically"
fi

echo ""
echo "🎯 Environment Status Summary:"
echo "   Backend (port 8000): $(check_port 8000 && echo "✅ Running" || echo "❌ Not running")"
echo "   Frontend (port 3000): $(check_port 3000 && echo "✅ Running" || echo "ℹ️  Will be started by Playwright")"
echo ""

# Final check
if check_port 8000; then
  echo "🎉 Test environment is ready!"
  echo "   You can now run E2E tests with confidence."
  exit 0
else
  echo "❌ Test environment is not ready"
  echo "   Please start the backend server before running tests."
  exit 1
fi
