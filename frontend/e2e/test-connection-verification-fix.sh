#!/bin/bash

# Test script to verify connection verification fixes
echo "🧪 Testing Connection Verification Fixes"
echo "========================================"

# Set working directory
cd "$(dirname "$0")"

# Run specific connection tests that were failing due to verification issues
echo "🔍 Running connection tests with enhanced verification..."

# Test 1: Page refresh test (was failing with connection format issues)
echo "📋 Test 1: Connection list after page refresh..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should maintain connection list after page refresh" \
  --reporter=line \
  --headed

echo ""

# Test 2: Connection deletion test (was failing with verification)
echo "📋 Test 2: Connection deletion..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should delete a connection successfully" \
  --reporter=line \
  --headed

echo ""

# Test 3: Duplicate connection prevention (was failing with verification)
echo "📋 Test 3: Duplicate connection prevention..."
npx playwright test tests/functional-areas/connections/connections.spec.ts \
  -g "should prevent duplicate connections" \
  --reporter=line \
  --headed

echo ""
echo "✅ Connection verification fix testing completed!"
echo "Check the output above to see if connection verification issues are resolved."
