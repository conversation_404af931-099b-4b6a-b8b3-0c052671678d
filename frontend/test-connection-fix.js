#!/usr/bin/env node

/**
 * Quick validation script for E2E connection creation fix
 * Tests the autocomplete interaction improvement
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 Testing E2E Connection Creation + Comparison Form Fix...\n');
console.log('📋 Expected improvements:');
console.log('   - ✅ Connection creation: Using autocomplete prefixes (FIXED)');
console.log('   - 🔧 Comparison form: Using full entity names instead of prefixes (NEW FIX)');
console.log('');

// Run a single test to validate the connection creation fix
const testCommand = 'npx';
const testArgs = [
  'playwright', 'test', 
  'e2e/tests/comparisons.spec.ts',
  '--project=chromium',
  '--grep="should calculate direct relationships"',
  '--headed',
  '--timeout=60000'
];

console.log(`Running: ${testCommand} ${testArgs.join(' ')}\n`);

const testProcess = spawn(testCommand, testArgs, {
  cwd: process.cwd(),
  stdio: 'inherit',
  shell: true
});

testProcess.on('close', (code) => {
  console.log(`\n📊 Test completed with exit code: ${code}`);
  
  if (code === 0) {
    console.log('✅ SUCCESS: Both connection creation AND comparison form fixes working!');
    console.log('   - ✅ Entities created successfully');
    console.log('   - ✅ Connection autocomplete using prefixes (Fixed)');
    console.log('   - ✅ Connections created with full entity names');
    console.log('   - ✅ Comparison form using full entity names (NEW FIX)');
    console.log('   - ✅ Comparison calculation working - no more "No path found"');
  } else {
    console.log('❌ FAILED: Still has issues');
    console.log('   - Check the test output above for details');
    console.log('   - Connection creation: Should be working (prefixes)');
    console.log('   - Comparison form: May need further refinement (full names)');
  }
  
  console.log('\n🔍 Next steps:');
  console.log('   - If successful: Run full E2E test suite');
  console.log('   - If failed: Review the specific failure in test output');
  console.log('   - The fix focused on autocomplete prefix typing instead of full names');
});

testProcess.on('error', (error) => {
  console.error(`❌ Error running test: ${error.message}`);
  console.log('\n💡 Make sure Playwright is installed and backend services are running:');
  console.log('   npm install');
  console.log('   docker-compose -f docker-compose.dev.yml up -d');
});