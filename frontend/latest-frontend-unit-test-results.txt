
> simile-frontend@0.1.0 test
> react-scripts test --coverage --watchAll=false --passWithNoTests

  console.error
    Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.

      76 |
      77 | const renderApp = (initialEntries = ['/']) => {
    > 78 |   return render(
         |                ^
      79 |     <MemoryRouter initialEntries={initialEntries}>
      80 |       <ErrorBoundary>
      81 |         <TestApp />

      at printWarning (node_modules/react-dom/cjs/react-dom-test-utils.development.js:71:30)
      at error (node_modules/react-dom/cjs/react-dom-test-utils.development.js:45:7)
      at actWithWarning (node_modules/react-dom/cjs/react-dom-test-utils.development.js:1736:7)
      at node_modules/@testing-library/react/dist/act-compat.js:63:25
      at renderRoot (node_modules/@testing-library/react/dist/pure.js:159:26)
      at render (node_modules/@testing-library/react/dist/pure.js:246:10)
      at renderApp (src/tests/integration.test.tsx:78:16)
      at Object.<anonymous> (src/tests/integration.test.tsx:100:7)
      at TestScheduler.scheduleTests (node_modules/@jest/core/build/TestScheduler.js:333:13)
      at runJest (node_modules/@jest/core/build/runJest.js:404:19)
      at _run10000 (node_modules/@jest/core/build/cli/index.js:320:7)
      at runCLI (node_modules/@jest/core/build/cli/index.js:173:3)

  console.warn
    ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.

      76 |
      77 | const renderApp = (initialEntries = ['/']) => {
    > 78 |   return render(
         |                ^
      79 |     <MemoryRouter initialEntries={initialEntries}>
      80 |       <ErrorBoundary>
      81 |         <TestApp />

      at warnOnce (node_modules/react-router/lib/deprecations.ts:9:13)
      at logDeprecation (node_modules/react-router/lib/deprecations.ts:14:3)
      at logV6DeprecationWarnings (node_modules/react-router/lib/deprecations.ts:26:5)
      at node_modules/react-router/lib/components.tsx:257:25
      at commitHookEffectListMount (node_modules/react-dom/cjs/react-dom.development.js:23189:26)
      at commitPassiveMountOnFiber (node_modules/react-dom/cjs/react-dom.development.js:24970:11)
      at commitPassiveMountEffects_complete (node_modules/react-dom/cjs/react-dom.development.js:24930:9)
      at commitPassiveMountEffects_begin (node_modules/react-dom/cjs/react-dom.development.js:24917:7)
      at commitPassiveMountEffects (node_modules/react-dom/cjs/react-dom.development.js:24905:3)
      at flushPassiveEffectsImpl (node_modules/react-dom/cjs/react-dom.development.js:27078:3)
      at flushPassiveEffects (node_modules/react-dom/cjs/react-dom.development.js:27023:14)
      at node_modules/react-dom/cjs/react-dom.development.js:26808:9
      at flushActQueue (node_modules/react/cjs/react.development.js:2667:24)
      at act (node_modules/react/cjs/react.development.js:2582:11)
      at actWithWarning (node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
      at node_modules/@testing-library/react/dist/act-compat.js:63:25
      at renderRoot (node_modules/@testing-library/react/dist/pure.js:159:26)
      at render (node_modules/@testing-library/react/dist/pure.js:246:10)
      at renderApp (src/tests/integration.test.tsx:78:16)
      at Object.<anonymous> (src/tests/integration.test.tsx:100:7)
      at TestScheduler.scheduleTests (node_modules/@jest/core/build/TestScheduler.js:333:13)
      at runJest (node_modules/@jest/core/build/runJest.js:404:19)
      at _run10000 (node_modules/@jest/core/build/cli/index.js:320:7)
      at runCLI (node_modules/@jest/core/build/cli/index.js:173:3)

  console.warn
    ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

      76 |
      77 | const renderApp = (initialEntries = ['/']) => {
    > 78 |   return render(
         |                ^
      79 |     <MemoryRouter initialEntries={initialEntries}>
      80 |       <ErrorBoundary>
      81 |         <TestApp />

      at warnOnce (node_modules/react-router/lib/deprecations.ts:9:13)
      at logDeprecation (node_modules/react-router/lib/deprecations.ts:14:3)
      at logV6DeprecationWarnings (node_modules/react-router/lib/deprecations.ts:37:5)
      at node_modules/react-router/lib/components.tsx:257:25
      at commitHookEffectListMount (node_modules/react-dom/cjs/react-dom.development.js:23189:26)
      at commitPassiveMountOnFiber (node_modules/react-dom/cjs/react-dom.development.js:24970:11)
      at commitPassiveMountEffects_complete (node_modules/react-dom/cjs/react-dom.development.js:24930:9)
      at commitPassiveMountEffects_begin (node_modules/react-dom/cjs/react-dom.development.js:24917:7)
      at commitPassiveMountEffects (node_modules/react-dom/cjs/react-dom.development.js:24905:3)
      at flushPassiveEffectsImpl (node_modules/react-dom/cjs/react-dom.development.js:27078:3)
      at flushPassiveEffects (node_modules/react-dom/cjs/react-dom.development.js:27023:14)
      at node_modules/react-dom/cjs/react-dom.development.js:26808:9
      at flushActQueue (node_modules/react/cjs/react.development.js:2667:24)
      at act (node_modules/react/cjs/react.development.js:2582:11)
      at actWithWarning (node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
      at node_modules/@testing-library/react/dist/act-compat.js:63:25
      at renderRoot (node_modules/@testing-library/react/dist/pure.js:159:26)
      at render (node_modules/@testing-library/react/dist/pure.js:246:10)
      at renderApp (src/tests/integration.test.tsx:78:16)
      at Object.<anonymous> (src/tests/integration.test.tsx:100:7)
      at TestScheduler.scheduleTests (node_modules/@jest/core/build/TestScheduler.js:333:13)
      at runJest (node_modules/@jest/core/build/runJest.js:404:19)
      at _run10000 (node_modules/@jest/core/build/cli/index.js:320:7)
      at runCLI (node_modules/@jest/core/build/cli/index.js:173:3)

  console.error
    Warning: An update to EntityManager inside a test was not wrapped in act(...).
    
    When testing, code that causes React state updates should be wrapped into act(...):
    
    act(() => {
      /* fire events that update state */
    });
    /* assert on the output */
    
    This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
        at EntityManager (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/EntityManager.tsx:9:43)
        at RenderedRoute (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/hooks.tsx:658:26)
        at Routes (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:512:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at main
        at div
        at TestApp
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at Router (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:421:13)
        at MemoryRouter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:225:3)

      25 |
      26 |   const handleFormSuccess = (entity: Entity) => {
    > 27 |     setShowForm(false);
         |     ^
      28 |     setEditingEntity(null);
      29 |     setRefreshKey(prev => prev + 1);
      30 |     // Note: Don't increment formKey here - it will be incremented when form is next shown

      at printWarning (node_modules/react-dom/cjs/react-dom.development.js:86:30)
      at error (node_modules/react-dom/cjs/react-dom.development.js:60:7)
      at warnIfUpdatesNotWrappedWithActDEV (node_modules/react-dom/cjs/react-dom.development.js:27628:9)
      at scheduleUpdateOnFiber (node_modules/react-dom/cjs/react-dom.development.js:25547:5)
      at setShowForm (node_modules/react-dom/cjs/react-dom.development.js:16708:7)
      at onSuccess (src/components/EntityManager.tsx:27:5)
      at handleSubmit (src/components/EntityForm.tsx:153:7)

  console.error
    Warning: An update to EntityManager inside a test was not wrapped in act(...).
    
    When testing, code that causes React state updates should be wrapped into act(...):
    
    act(() => {
      /* fire events that update state */
    });
    /* assert on the output */
    
    This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
        at EntityManager (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/EntityManager.tsx:9:43)
        at RenderedRoute (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/hooks.tsx:658:26)
        at Routes (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:512:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at main
        at div
        at TestApp
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at Router (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:421:13)
        at MemoryRouter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:225:3)

      26 |   const handleFormSuccess = (entity: Entity) => {
      27 |     setShowForm(false);
    > 28 |     setEditingEntity(null);
         |     ^
      29 |     setRefreshKey(prev => prev + 1);
      30 |     // Note: Don't increment formKey here - it will be incremented when form is next shown
      31 |   };

      at printWarning (node_modules/react-dom/cjs/react-dom.development.js:86:30)
      at error (node_modules/react-dom/cjs/react-dom.development.js:60:7)
      at warnIfUpdatesNotWrappedWithActDEV (node_modules/react-dom/cjs/react-dom.development.js:27628:9)
      at scheduleUpdateOnFiber (node_modules/react-dom/cjs/react-dom.development.js:25547:5)
      at setEditingEntity (node_modules/react-dom/cjs/react-dom.development.js:16708:7)
      at onSuccess (src/components/EntityManager.tsx:28:5)
      at handleSubmit (src/components/EntityForm.tsx:153:7)

  console.error
    Warning: An update to EntityManager inside a test was not wrapped in act(...).
    
    When testing, code that causes React state updates should be wrapped into act(...):
    
    act(() => {
      /* fire events that update state */
    });
    /* assert on the output */
    
    This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
        at EntityManager (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/EntityManager.tsx:9:43)
        at RenderedRoute (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/hooks.tsx:658:26)
        at Routes (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:512:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at main
        at div
        at TestApp
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at Router (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:421:13)
        at MemoryRouter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:225:3)

      27 |     setShowForm(false);
      28 |     setEditingEntity(null);
    > 29 |     setRefreshKey(prev => prev + 1);
         |     ^
      30 |     // Note: Don't increment formKey here - it will be incremented when form is next shown
      31 |   };
      32 |

      at printWarning (node_modules/react-dom/cjs/react-dom.development.js:86:30)
      at error (node_modules/react-dom/cjs/react-dom.development.js:60:7)
      at warnIfUpdatesNotWrappedWithActDEV (node_modules/react-dom/cjs/react-dom.development.js:27628:9)
      at scheduleUpdateOnFiber (node_modules/react-dom/cjs/react-dom.development.js:25547:5)
      at setRefreshKey (node_modules/react-dom/cjs/react-dom.development.js:16708:7)
      at onSuccess (src/components/EntityManager.tsx:29:5)
      at handleSubmit (src/components/EntityForm.tsx:153:7)

  console.error
    Warning: An update to EntityForm inside a test was not wrapped in act(...).
    
    When testing, code that causes React state updates should be wrapped into act(...):
    
    act(() => {
      /* fire events that update state */
    });
    /* assert on the output */
    
    This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
        at EntityForm (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/EntityForm.tsx:12:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at div
        at EntityManager (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/EntityManager.tsx:9:43)
        at RenderedRoute (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/hooks.tsx:658:26)
        at Routes (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:512:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at main
        at div
        at TestApp
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at Router (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:421:13)
        at MemoryRouter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:225:3)

      176 |       console.error('Error saving entity:', err);
      177 |     } finally {
    > 178 |       setLoading(false);
          |       ^
      179 |     }
      180 |   };
      181 |

      at printWarning (node_modules/react-dom/cjs/react-dom.development.js:86:30)
      at error (node_modules/react-dom/cjs/react-dom.development.js:60:7)
      at warnIfUpdatesNotWrappedWithActDEV (node_modules/react-dom/cjs/react-dom.development.js:27628:9)
      at scheduleUpdateOnFiber (node_modules/react-dom/cjs/react-dom.development.js:25547:5)
      at setLoading (node_modules/react-dom/cjs/react-dom.development.js:16708:7)
      at handleSubmit (src/components/EntityForm.tsx:178:7)

  console.log
    ConnectionList mounted, loading connections...

      at src/components/ConnectionList.tsx:28:13

  console.log
    Clearing all cache before loading connections

      at loadConnections (src/components/ConnectionList.tsx:53:15)

  console.error
    Warning: An update to ConnectionList inside a test was not wrapped in act(...).
    
    When testing, code that causes React state updates should be wrapped into act(...):
    
    act(() => {
      /* fire events that update state */
    });
    /* assert on the output */
    
    This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
        at ConnectionList (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ConnectionList.tsx:18:3)
        at div
        at ConnectionManager (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ConnectionManager.tsx:7:43)
        at RenderedRoute (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/hooks.tsx:658:26)
        at Routes (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:512:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at main
        at div
        at TestApp
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at Router (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:421:13)
        at MemoryRouter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:225:3)

       97 |         console.log('Loaded connections:', connectionsWithDetails.length, 'connections');
       98 |       }
    >  99 |       setConnections(connectionsWithDetails);
          |       ^
      100 |       setError(null);
      101 |     } catch (err: any) {
      102 |       // Enhanced error handling

      at printWarning (node_modules/react-dom/cjs/react-dom.development.js:86:30)
      at error (node_modules/react-dom/cjs/react-dom.development.js:60:7)
      at warnIfUpdatesNotWrappedWithActDEV (node_modules/react-dom/cjs/react-dom.development.js:27628:9)
      at scheduleUpdateOnFiber (node_modules/react-dom/cjs/react-dom.development.js:25547:5)
      at setConnections (node_modules/react-dom/cjs/react-dom.development.js:16708:7)
      at loadConnections (src/components/ConnectionList.tsx:99:7)

  console.error
    Warning: An update to ConnectionList inside a test was not wrapped in act(...).
    
    When testing, code that causes React state updates should be wrapped into act(...):
    
    act(() => {
      /* fire events that update state */
    });
    /* assert on the output */
    
    This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
        at ConnectionList (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ConnectionList.tsx:18:3)
        at div
        at ConnectionManager (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ConnectionManager.tsx:7:43)
        at RenderedRoute (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/hooks.tsx:658:26)
        at Routes (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:512:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at main
        at div
        at TestApp
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at Router (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:421:13)
        at MemoryRouter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:225:3)

       98 |       }
       99 |       setConnections(connectionsWithDetails);
    > 100 |       setError(null);
          |       ^
      101 |     } catch (err: any) {
      102 |       // Enhanced error handling
      103 |       if (err.code === 'NETWORK_ERROR' || !err.response) {

      at printWarning (node_modules/react-dom/cjs/react-dom.development.js:86:30)
      at error (node_modules/react-dom/cjs/react-dom.development.js:60:7)
      at warnIfUpdatesNotWrappedWithActDEV (node_modules/react-dom/cjs/react-dom.development.js:27628:9)
      at scheduleUpdateOnFiber (node_modules/react-dom/cjs/react-dom.development.js:25547:5)
      at setError (node_modules/react-dom/cjs/react-dom.development.js:16708:7)
      at loadConnections (src/components/ConnectionList.tsx:100:7)

  console.error
    Warning: An update to ConnectionList inside a test was not wrapped in act(...).
    
    When testing, code that causes React state updates should be wrapped into act(...):
    
    act(() => {
      /* fire events that update state */
    });
    /* assert on the output */
    
    This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
        at ConnectionList (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ConnectionList.tsx:18:3)
        at div
        at ConnectionManager (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ConnectionManager.tsx:7:43)
        at RenderedRoute (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/hooks.tsx:658:26)
        at Routes (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:512:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at main
        at div
        at TestApp
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at Router (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:421:13)
        at MemoryRouter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:225:3)

      110 |       console.error('Error loading connections:', err);
      111 |     } finally {
    > 112 |       setLoading(false);
          |       ^
      113 |     }
      114 |   };
      115 |

      at printWarning (node_modules/react-dom/cjs/react-dom.development.js:86:30)
      at error (node_modules/react-dom/cjs/react-dom.development.js:60:7)
      at warnIfUpdatesNotWrappedWithActDEV (node_modules/react-dom/cjs/react-dom.development.js:27628:9)
      at scheduleUpdateOnFiber (node_modules/react-dom/cjs/react-dom.development.js:25547:5)
      at setLoading (node_modules/react-dom/cjs/react-dom.development.js:16708:7)
      at loadConnections (src/components/ConnectionList.tsx:112:7)

  console.log
    ConnectionList mounted, loading connections...

      at src/components/ConnectionList.tsx:28:13

  console.log
    Clearing all cache before loading connections

      at loadConnections (src/components/ConnectionList.tsx:53:15)

  console.log
    Multiplier validation: value="0.2", isValid=true, message="undefined"

      at src/components/ConnectionForm.tsx:191:13

  console.log
    Multiplier changed: "0.2", validation: true, message: "undefined"

      at src/components/ConnectionForm.tsx:237:15

  console.log
    ConnectionList mounted, loading connections...

      at src/components/ConnectionList.tsx:28:13

  console.log
    Clearing all cache before loading connections

      at loadConnections (src/components/ConnectionList.tsx:53:15)

  console.error
    Warning: An update to ComparisonForm inside a test was not wrapped in act(...).
    
    When testing, code that causes React state updates should be wrapped into act(...):
    
    act(() => {
      /* fire events that update state */
    });
    /* assert on the output */
    
    This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
        at ComparisonForm (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ComparisonForm.tsx:10:58)
        at div
        at div
        at ComparisonManager (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ComparisonManager.tsx:7:39)
        at RenderedRoute (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/hooks.tsx:658:26)
        at Routes (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:512:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at main
        at div
        at TestApp
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at Router (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:421:13)
        at MemoryRouter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:225:3)

      37 |       setError(null);
      38 |     } catch (err) {
    > 39 |       setError('Failed to load entities and units');
         |       ^
      40 |       console.error('Error loading data:', err);
      41 |     } finally {
      42 |       setDataLoading(false);

      at printWarning (node_modules/react-dom/cjs/react-dom.development.js:86:30)
      at error (node_modules/react-dom/cjs/react-dom.development.js:60:7)
      at warnIfUpdatesNotWrappedWithActDEV (node_modules/react-dom/cjs/react-dom.development.js:27628:9)
      at scheduleUpdateOnFiber (node_modules/react-dom/cjs/react-dom.development.js:25547:5)
      at setError (node_modules/react-dom/cjs/react-dom.development.js:16708:7)
      at loadData (src/components/ComparisonForm.tsx:39:7)

  console.error
    Error loading data: Error: Network Error
        at Object.<anonymous> (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/tests/integration.test.tsx:293:54)
        at Promise.then.completed (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/utils.js:391:28)
        at new Promise (<anonymous>)
        at callAsyncCircusFn (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/utils.js:316:10)
        at _callCircusTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/run.js:218:40)
        at _runTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/run.js:155:3)
        at _runTestsForDescribeBlock (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/run.js:66:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/run.js:60:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/run.js:60:9)
        at run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/run.js:25:3)
        at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
        at jestAdapter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
        at runTestInternal (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-runner/build/runTest.js:389:16)
        at runTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-runner/build/runTest.js:475:34)
        at TestRunner.runTests (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-runner/build/index.js:101:12)
        at TestScheduler.scheduleTests (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/@jest/core/build/TestScheduler.js:333:13)
        at runJest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/@jest/core/build/runJest.js:404:19)
        at _run10000 (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/@jest/core/build/cli/index.js:320:7)
        at runCLI (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/@jest/core/build/cli/index.js:173:3)
        at Object.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/jest-cli/build/cli/index.js:155:37)

      38 |     } catch (err) {
      39 |       setError('Failed to load entities and units');
    > 40 |       console.error('Error loading data:', err);
         |               ^
      41 |     } finally {
      42 |       setDataLoading(false);
      43 |     }

      at loadData (src/components/ComparisonForm.tsx:40:15)

  console.error
    Warning: An update to ComparisonForm inside a test was not wrapped in act(...).
    
    When testing, code that causes React state updates should be wrapped into act(...):
    
    act(() => {
      /* fire events that update state */
    });
    /* assert on the output */
    
    This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
        at ComparisonForm (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ComparisonForm.tsx:10:58)
        at div
        at div
        at ComparisonManager (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ComparisonManager.tsx:7:39)
        at RenderedRoute (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/hooks.tsx:658:26)
        at Routes (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:512:3)
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at main
        at div
        at TestApp
        at ErrorBoundary (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/src/components/ErrorBoundary.tsx:41:5)
        at Router (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:421:13)
        at MemoryRouter (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/react-router/lib/components.tsx:225:3)

      40 |       console.error('Error loading data:', err);
      41 |     } finally {
    > 42 |       setDataLoading(false);
         |       ^
      43 |     }
      44 |   };
      45 |

      at printWarning (node_modules/react-dom/cjs/react-dom.development.js:86:30)
      at error (node_modules/react-dom/cjs/react-dom.development.js:60:7)
      at warnIfUpdatesNotWrappedWithActDEV (node_modules/react-dom/cjs/react-dom.development.js:27628:9)
      at scheduleUpdateOnFiber (node_modules/react-dom/cjs/react-dom.development.js:25547:5)
      at setDataLoading (node_modules/react-dom/cjs/react-dom.development.js:16708:7)
      at loadData (src/components/ComparisonForm.tsx:42:7)

PASS src/tests/integration.test.tsx
  SIMILE Integration Tests
    Entity Management Flow
      ✓ should display entity list on entities page (48 ms)
      ✓ should allow creating a new entity (18 ms)
    Connection Management Flow
      ✓ should display connection list on connections page (9 ms)
      ✓ should allow creating a new connection (26 ms)
    Comparison Flow
      ✓ should display comparison form on home page (5 ms)
      ✓ should perform comparison when form is submitted (5 ms)
      ✓ should handle comparison errors gracefully (4 ms)
    Navigation Flow
      ✓ should navigate between all pages (9 ms)
    Error Handling
      ✓ should handle API errors gracefully (9 ms)

------------------------|---------|----------|---------|---------|---------------------------------------------------------------------------------------------------------------------------------------------
File                    | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s                                                                                                                           
------------------------|---------|----------|---------|---------|---------------------------------------------------------------------------------------------------------------------------------------------
All files               |   36.55 |    25.77 |   31.72 |   36.98 |                                                                                                                                             
 src                    |       0 |      100 |       0 |       0 |                                                                                                                                             
  App.tsx               |       0 |      100 |       0 |       0 | 12                                                                                                                                          
  index.tsx             |       0 |      100 |     100 |       0 | 6-9                                                                                                                                         
 src/components         |   44.33 |    27.93 |   38.91 |   45.59 |                                                                                                                                             
  ApiErrorFallback.tsx  |    7.69 |        0 |       0 |    7.69 | 8-35                                                                                                                                        
  AutoComplete.tsx      |   51.28 |     22.8 |   43.47 |   50.68 | 9,67-68,87-93,102,107-108,113-123,129-151,193-199                                                                                           
  ComparisonForm.tsx    |   48.93 |    26.31 |      50 |   48.91 | 47-77,85-94,103-109,121-127,136,160-177,197,218-227                                                                                         
  ComparisonManager.tsx |     100 |       50 |     100 |     100 | 22                                                                                                                                          
  ComparisonResult.tsx  |    7.14 |        0 |       0 |    7.14 | 9-68                                                                                                                                        
  ConnectionForm.tsx    |   32.11 |     23.4 |   26.02 |   34.06 | 27-41,50-64,89-91,94-96,113-120,129,134,138,142,149-169,173-176,199-209,216-218,242-245,252-264,269-272,279-281,285-395,401-412,421-432,444 
  ConnectionList.tsx    |   44.94 |       25 |    40.9 |   43.52 | 34,40-42,64,77,97,103-110,117-128,134-135,139-140,144-171,179-248                                                                           
  ConnectionManager.tsx |   45.45 |       50 |   22.22 |   52.63 | 15-20,24,28,32-36                                                                                                                           
  EntityForm.tsx        |   53.98 |    41.02 |   57.14 |   53.63 | 46-51,68,73,78,82,111,116-118,122-124,134-136,148,156-176,240-254                                                                           
  EntityList.tsx        |   57.14 |    56.25 |   44.44 |   58.82 | 38-39,46-55,72,94-111                                                                                                                       
  EntityManager.tsx     |   64.28 |      100 |      40 |   70.83 | 21-23,34-36,40                                                                                                                              
  ErrorBoundary.tsx     |   46.15 |    16.66 |   33.33 |   46.15 | 23,46-54,61,70-71                                                                                                                           
  Navigation.tsx        |     100 |      100 |     100 |     100 |                                                                                                                                             
  Skeleton.tsx          |     100 |    33.33 |     100 |     100 | 11,26-72                                                                                                                                    
 src/services           |       0 |        0 |       0 |       0 |                                                                                                                                             
  api.ts                |       0 |        0 |       0 |       0 | 14-138                                                                                                                                      
  cache.ts              |       0 |        0 |       0 |       0 | 12-111                                                                                                                                      
  cachedApi.ts          |       0 |        0 |       0 |       0 | 21-237                                                                                                                                      
 src/types              |       0 |        0 |       0 |       0 |                                                                                                                                             
  api.ts                |       0 |        0 |       0 |       0 |                                                                                                                                             
------------------------|---------|----------|---------|---------|---------------------------------------------------------------------------------------------------------------------------------------------
Test Suites: 1 passed, 1 total
Tests:       9 passed, 9 total
Snapshots:   0 total
Time:        0.999 s, estimated 1 s
Ran all test suites.
