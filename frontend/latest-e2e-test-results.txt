
> simile-frontend@0.1.0 test:e2e
> playwright test

================================================================================
🚀 Starting E2E test suite with 240 tests
🔧 Running with 3 workers
================================================================================


Running 240 tests using 3 workers

[1/240] 🏃 Starting: should demonstrate 80% performance improvement in cleanup operations
[1/240] 🏃 Starting: should validate batch cleanup operations efficiency
[1/240] 🏃 Starting: should validate worker isolation in parallel test environment
✅ Backend healthy: API healthy (200) (38ms latency)
✅ Backend healthy: API healthy (200) (38ms latency)
🏁 Performance Validation: Testing cleanup of 15 entities
Measuring enhanced cleanup performance vs baseline...

=== Phase 1: Creating Test Data ===
🚀 Batch Cleanup Validation: Testing 10 entities
🚀 Batch creating 15 entities with optimized tracking...
🚀 Batch creating 10 entities with optimized tracking...
✅ Backend healthy: API healthy (200) (56ms latency)
👥 Worker Isolation Test: Worker 2 creating 8 entities
🚀 Batch creating 8 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 39ms for 10 items (3.9ms/item)
❌ Batch entity creation failed after 39ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  J BatchTes' already exists"}
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 1)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 139ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 39ms total, 3.9ms/item (10 items, 1 samples)

🚀 Parallel batch-entity-creation: 118ms for 15 items (7.9ms/item)
❌ Batch entity creation failed after 118ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  BFJ PerfTe' already exists"}
🚀 Parallel batch-entity-creation: 126ms for 8 items (15.8ms/item)
❌ Batch entity creation failed after 126ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  A WorkerEn' already exists"}
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 0)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 200ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 118ms total, 7.9ms/item (15 items, 1 samples)

[1/240] ❌ should validate batch cleanup operations efficiency (0.5s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  J BatchTes' already exists"}
  ✘  2 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:100:7 › Cleanup Performance Validation › should validate batch cleanup operations efficiency (452ms)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 2)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 216ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 126ms total, 15.8ms/item (8 items, 1 samples)

[2/240] ❌ should demonstrate 80% performance improvement in cleanup operations (0.5s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  BFJ PerfTe' already exists"}
  ✘  1 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:40:7 › Cleanup Performance Validation › should demonstrate 80% performance improvement in cleanup operations (496ms)
[3/240] ❌ should validate worker isolation in parallel test environment (0.5s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  A WorkerEn' already exists"}
  ✘  3 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:135:7 › Cleanup Performance Validation › should validate worker isolation in parallel test environment (513ms)
[4/240] 🏃 Starting: should validate batch cleanup operations efficiency
[4/240] 🏃 Starting: should demonstrate 80% performance improvement in cleanup operations
[4/240] 🏃 Starting: should validate worker isolation in parallel test environment
✅ Backend healthy: API healthy (200) (441ms latency)
🚀 Batch Cleanup Validation: Testing 10 entities
🚀 Batch creating 10 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 189ms for 10 items (18.9ms/item)
❌ Batch entity creation failed after 189ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  JW BatchTe' already exists"}
✅ Backend healthy: API healthy (200) (47ms latency)
👥 Worker Isolation Test: Worker 5 creating 8 entities
🚀 Batch creating 8 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 158ms for 8 items (19.8ms/item)
❌ Batch entity creation failed after 158ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  LYU Worker' already exists"}
✅ Backend healthy: API healthy (200) (106ms latency)
🏁 Performance Validation: Testing cleanup of 15 entities
Measuring enhanced cleanup performance vs baseline...

=== Phase 1: Creating Test Data ===
🚀 Batch creating 15 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 707ms for 15 items (47.1ms/item)
❌ Batch entity creation failed after 707ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  DKB PerfTe' already exists"}
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 3)
Performance: post-test-cleanup completed in 1ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 1ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 1ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 2200ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 1ms (min: 1ms, max: 1ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 189ms total, 18.9ms/item (10 items, 1 samples)

🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 5)
Performance: post-test-cleanup completed in 1ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 1ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 1ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 2853ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 1ms (min: 1ms, max: 1ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 158ms total, 19.8ms/item (8 items, 1 samples)

🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 4)
Performance: post-test-cleanup completed in 1ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 1ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 1ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 3186ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 1ms (min: 1ms, max: 1ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 707ms total, 47.1ms/item (15 items, 1 samples)

[4/240] ❌ should validate batch cleanup operations efficiency (6.3s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  JW BatchTe' already exists"}
  ✘  4 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:100:7 › Cleanup Performance Validation › should validate batch cleanup operations efficiency (retry #1) (6.3s)
[5/240] ❌ should demonstrate 80% performance improvement in cleanup operations (5.9s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  DKB PerfTe' already exists"}
  ✘  5 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:40:7 › Cleanup Performance Validation › should demonstrate 80% performance improvement in cleanup operations (retry #1) (5.9s)
[6/240] ❌ should validate worker isolation in parallel test environment (6.7s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  LYU Worker' already exists"}
  ✘  6 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:135:7 › Cleanup Performance Validation › should validate worker isolation in parallel test environment (retry #1) (6.7s)
[7/240] 🏃 Starting: should validate error handling and recovery in cleanup operations
[7/240] 🏃 Starting: should validate cleanup performance monitoring and reporting
[7/240] 🏃 Starting: should validate health check and system status monitoring
✅ Backend healthy: API healthy (200) (157ms latency)
📊 Performance Monitoring Validation
🚀 Batch creating 3 entities with optimized tracking...
✅ Backend healthy: API healthy (200) (172ms latency)
🛡️  Error Handling Validation: Testing cleanup resilience
🚀 Batch creating 2 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 182ms for 3 items (60.7ms/item)
❌ Batch entity creation failed after 182ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  VW Monitor' already exists"}
🚀 Parallel batch-entity-creation: 322ms for 2 items (161.0ms/item)
❌ Batch entity creation failed after 322ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  HX ErrorTe' already exists"}
✅ Backend healthy: API healthy (200) (359ms latency)
🏥 Health Check Validation
📊 Health Check Results:
  • Healthy: true
  • Response: API healthy (200)
  • Latency: 50ms
✅ Health check validated
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 8)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 504ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 6)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 1171ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 322ms total, 161.0ms/item (2 items, 1 samples)

🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 7)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 1142ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 182ms total, 60.7ms/item (3 items, 1 samples)

[7/240] ✅ should validate health check and system status monitoring (2.0s)
  ✓  9 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:240:7 › Cleanup Performance Validation › should validate health check and system status monitoring (2.0s)
[8/240] 🏃 Starting: should demonstrate overall performance gains across all operations
[8/240] ❌ should validate error handling and recovery in cleanup operations (2.4s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  HX ErrorTe' already exists"}
  ✘  7 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:171:7 › Cleanup Performance Validation › should validate error handling and recovery in cleanup operations (2.4s)
[9/240] ❌ should validate cleanup performance monitoring and reporting (2.4s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  VW Monitor' already exists"}
  ✘  8 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:209:7 › Cleanup Performance Validation › should validate cleanup performance monitoring and reporting (2.4s)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 8)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 562ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 2)

[10/240] ❌ should demonstrate overall performance gains across all operations (1.3s)

📈 Progress: 10/240 (4%)
   ✅ 1 passed | ❌ 9 failed | ⏭️ 0 skipped
   ⏱️  Elapsed: 0.3min | Remaining: ~10.9min

   └─ Error: Error: Backend health check failed: Network error: Error: apiRequestContext.get: Test ended. (1ms latency)
  ✘  10 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:257:7 › Cleanup Performance Validation › should demonstrate overall performance gains across all operations (1.3s)
[11/240] 🏃 Starting: should validate error handling and recovery in cleanup operations
[11/240] 🏃 Starting: should validate cleanup performance monitoring and reporting
[11/240] 🏃 Starting: should demonstrate overall performance gains across all operations
✅ Backend healthy: API healthy (200) (174ms latency)
🛡️  Error Handling Validation: Testing cleanup resilience
🚀 Batch creating 2 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 120ms for 2 items (60.0ms/item)
❌ Batch entity creation failed after 120ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  IR ErrorTe' already exists"}
✅ Backend healthy: API healthy (200) (235ms latency)
📊 Performance Monitoring Validation
🚀 Batch creating 3 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 237ms for 3 items (79.0ms/item)
❌ Batch entity creation failed after 237ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  TQ Monitor' already exists"}
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 9)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 1045ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 120ms total, 60.0ms/item (2 items, 1 samples)

🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 10)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 1166ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 237ms total, 79.0ms/item (3 items, 1 samples)

✅ Backend healthy: API healthy (200) (640ms latency)
🎯 Overall Performance Validation
  Testing 5 operations with 4 entities each
  Operation 1/5
🚀 Batch creating 4 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 518ms for 4 items (129.5ms/item)
❌ Batch entity creation failed after 518ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  NOG Overal' already exists"}
[11/240] ❌ should validate error handling and recovery in cleanup operations (3.2s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  IR ErrorTe' already exists"}
  ✘  11 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:171:7 › Cleanup Performance Validation › should validate error handling and recovery in cleanup operations (retry #1) (3.2s)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 11)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 1768ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 518ms total, 129.5ms/item (4 items, 1 samples)

[12/240] ❌ should validate cleanup performance monitoring and reporting (4.0s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  TQ Monitor' already exists"}
  ✘  12 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:209:7 › Cleanup Performance Validation › should validate cleanup performance monitoring and reporting (retry #1) (4.0s)
[13/240] ❌ should demonstrate overall performance gains across all operations (3.1s)
   └─ Error: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  NOG Overal' already exists"}
  ✘  13 [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:257:7 › Cleanup Performance Validation › should demonstrate overall performance gains across all operations (retry #1) (3.1s)
[14/240] 🏃 Starting: should display comparison page correctly
[14/240] 🏃 Starting: should calculate direct relationships
🧹 Starting pre-test cleanup...
[14/240] 🏃 Starting: should calculate transitive relationships
  Found 14 total entities in database (Worker 12)
  Identified 11 test entities to delete for Worker 12
  ✓ Deleted test entity: TEST Wwp  A WorkerEn (ID: 518)
  ✓ Deleted test entity: TEST Wwp  BFJ PerfTe (ID: 517)
  ✓ Deleted test entity: TEST Wwp  HX ErrorTe (ID: 578)
  ✓ Deleted test entity: TEST Wwp  IR ErrorTe (ID: 582)
  ✓ Deleted test entity: TEST Wwp  J BatchTes (ID: 511)
  ✓ Deleted test entity: TEST Wwp  DKB PerfTe (ID: 562)
  ✓ Deleted test entity: TEST Wwp  LYU Worker (ID: 554)
  ✓ Deleted test entity: TEST Wwp  NOG Overal (ID: 587)
  ✓ Deleted test entity: TEST Wwp  TQ Monitor (ID: 584)
  ✓ Deleted test entity: TEST Wwp  JW BatchTe (ID: 544)
🧹 Starting pre-test cleanup...
  Found 4 total entities in database (Worker 13)
  Identified 1 test entities to delete for Worker 13
  ✓ Deleted test entity: TEST Wwp  VW Monitor (ID: 577)
  ❌ Failed to delete entity: TEST Wwp  VW Monitor (ID: 577)
🧹 Pre-test cleanup complete in 433ms (Worker 13):
  • Initial entities: 4
  • Entities deleted: 0
  • Failed deletions: 1
  • Final entity count: 3
  • Net reduction: 1
⚠️  1 entity deletions failed - may cause test conflicts
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
🧹 Pre-test cleanup complete in 1227ms (Worker 12):
  • Initial entities: 14
  • Entities deleted: 11
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 11
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [1/6] Created via API: Human CZLVA (ID: 591)
  ✓ [2/6] Created via API: Ball CZLVB (ID: 593)
  ✓ [1/6] Created via API: Human GPYOA (ID: 592)
  ✓ [2/6] Created via API: Ball GPYOB (ID: 594)
  ✓ [5/6] Created via API: Eleph GPYOE (ID: 597)
  ✓ [3/6] Created via API: Build CZLVC (ID: 595)
  ✓ [4/6] Created via API: Car CZLVD (ID: 596)
  ✓ [5/6] Created via API: Eleph CZLVE (ID: 598)
  ✓ [6/6] Created via API: Mouse CZLVF (ID: 601)
🚀 Parallel entity-creation: 411ms for 6 items (68.5ms/item)
🚀 Parallel entity creation complete in 411ms:
  • Created 6 entities concurrently
  • Average time per entity: 69ms
  • Speed improvement vs sequential: ~7x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [4/6] Created via API: Car GPYOD (ID: 599)
  ✓ [6/6] Created via API: Mouse GPYOF (ID: 600)
  ✓ [3/6] Created via API: Build GPYOC (ID: 602)
🚀 Parallel entity-creation: 488ms for 6 items (81.3ms/item)
🚀 Parallel entity creation complete in 488ms:
  • Created 6 entities concurrently
  • Average time per entity: 81ms
  • Speed improvement vs sequential: ~6x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [1/4] Created connection via API: 591 → 593 (10.0x)
  ℹ️  Tracking connection dependency: 591 → 593 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 595 → 601 (0.1x)
  ℹ️  Tracking connection dependency: 595 → 601 (auto-deleted via CASCADE)
  ✓ [1/4] Created connection via API: 592 → 594 (10.0x)
  ℹ️  Tracking connection dependency: 592 → 594 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 594 → 602 (50.0x)
  ℹ️  Tracking connection dependency: 594 → 602 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 596 → 598 (0.3x)
  ℹ️  Tracking connection dependency: 596 → 598 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 593 → 595 (50.0x)
  ℹ️  Tracking connection dependency: 593 → 595 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 331ms for 4 items (82.8ms/item)
🚀 Parallel connection creation complete in 331ms:
  • Created 4 connections concurrently
  • Average time per connection: 83ms
  • Speed improvement vs sequential: ~12x faster
🚀 Parallel test data creation complete in 745ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~9x faster
🚀 Parallel test data setup complete in 745ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
  ✓ [3/4] Created connection via API: 602 → 600 (0.1x)
  ℹ️  Tracking connection dependency: 602 → 600 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 599 → 597 (0.3x)
  ℹ️  Tracking connection dependency: 599 → 597 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 316ms for 4 items (79.0ms/item)
🚀 Parallel connection creation complete in 316ms:
  • Created 4 connections concurrently
  • Average time per connection: 79ms
  • Speed improvement vs sequential: ~13x faster
🚀 Parallel test data creation complete in 805ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~9x faster
🚀 Parallel test data setup complete in 805ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
🧹 Starting pre-test cleanup...
  Found 15 total entities in database (Worker 14)
  Identified 12 test entities to delete for Worker 14
  ✓ Deleted test entity: Ball CZLVB (ID: 593)
  ✓ Deleted test entity: Car GPYOD (ID: 599)
  ✓ Deleted test entity: Ball GPYOB (ID: 594)
  ✓ Deleted test entity: Car CZLVD (ID: 596)
  ✓ Deleted test entity: Build GPYOC (ID: 602)
  ✓ Deleted test entity: Human CZLVA (ID: 591)
  ✓ Deleted test entity: Build CZLVC (ID: 595)
  ✓ Deleted test entity: Eleph GPYOE (ID: 597)
  ✓ Deleted test entity: Eleph CZLVE (ID: 598)
  ✓ Deleted test entity: Human GPYOA (ID: 592)
  ✓ Deleted test entity: Mouse CZLVF (ID: 601)
  ✓ Deleted test entity: Mouse GPYOF (ID: 600)
🧹 Pre-test cleanup complete in 9962ms (Worker 14):
  • Initial entities: 15
  • Entities deleted: 12
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 12
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [1/6] Created via API: Human LOXTA (ID: 603)
  ✓ [2/6] Created via API: Ball LOXTB (ID: 604)
  ✓ [3/6] Created via API: Build LOXTC (ID: 605)
  ✓ [4/6] Created via API: Car LOXTD (ID: 606)
  ✓ [5/6] Created via API: Eleph LOXTE (ID: 607)
  ✓ [6/6] Created via API: Mouse LOXTF (ID: 608)
🚀 Parallel entity-creation: 377ms for 6 items (62.8ms/item)
🚀 Parallel entity creation complete in 377ms:
  • Created 6 entities concurrently
  • Average time per entity: 63ms
  • Speed improvement vs sequential: ~8x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [1/4] Created connection via API: 603 → 604 (10.0x)
  ℹ️  Tracking connection dependency: 603 → 604 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 605 → 608 (0.1x)
  ℹ️  Tracking connection dependency: 605 → 608 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 606 → 607 (0.3x)
  ℹ️  Tracking connection dependency: 606 → 607 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 604 → 605 (50.0x)
  ℹ️  Tracking connection dependency: 604 → 605 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 487ms for 4 items (121.8ms/item)
🚀 Parallel connection creation complete in 487ms:
  • Created 4 connections concurrently
  • Average time per connection: 122ms
  • Speed improvement vs sequential: ~8x faster
🚀 Parallel test data creation complete in 914ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~8x faster
🚀 Parallel test data setup complete in 914ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
✅ Comparison test setup complete - all test data ready
✅ Comparison test setup complete - all test data ready
Comparing: Human GPYOA to Ball GPYOB (count: 1, unit: Length)
Selecting comparison entity for from field: Human GPYOA
Using prefix "Human" for comparison from entity
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 591
✅ Comparison test setup complete - all test data ready
Comparing: Human LOXTA to Build LOXTC (count: 1, unit: Length)
Selecting comparison entity for from field: Human LOXTA
Using prefix "Human" for comparison from entity
  ❌ [2/6] Failed to delete entity ID: 596
  ❌ [3/6] Failed to delete entity ID: 598
  ❌ [4/6] Failed to delete entity ID: 601
  ❌ [5/6] Failed to delete entity ID: 593
  ❌ [6/6] Failed to delete entity ID: 595
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 2109ms (Worker 12):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 12
  • Test efficiency: 93% (2109ms cleanup / 31457ms total)
⚠️  6 cleanup operations failed - may affect future tests
  ✅ Cleanup verification: No test entities remain from Worker 12 (5ms)
[14/240] ✅ should display comparison page correctly (32.2s)
  ✓  14 [chromium] › e2e/tests/comparisons.spec.ts:186:7 › Entity Comparisons and Pathfinding › should display comparison page correctly (32.2s)
[15/240] 🏃 Starting: should calculate complex multi-hop paths
🧹 Starting pre-test cleanup...
  Found 9 total entities in database (Worker 12)
  Identified 6 test entities to delete for Worker 12
  ✓ Deleted test entity: Ball LOXTB (ID: 604)
  ✓ Deleted test entity: Car LOXTD (ID: 606)
  ✓ Deleted test entity: Human LOXTA (ID: 603)
  ✓ Deleted test entity: Mouse LOXTF (ID: 608)
  ✓ Deleted test entity: Eleph LOXTE (ID: 607)
  ✓ Deleted test entity: Build LOXTC (ID: 605)
🧹 Pre-test cleanup complete in 21ms (Worker 12):
  • Initial entities: 9
  • Entities deleted: 6
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 6
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [1/6] Created via API: Human XCDKA (ID: 609)
  ✓ [4/6] Created via API: Car XCDKD (ID: 610)
  ✓ [5/6] Created via API: Eleph XCDKE (ID: 611)
  ✓ [3/6] Created via API: Build XCDKC (ID: 612)
  ✓ [6/6] Created via API: Mouse XCDKF (ID: 614)
  ✓ [2/6] Created via API: Ball XCDKB (ID: 613)
🚀 Parallel entity-creation: 16ms for 6 items (2.7ms/item)
🚀 Parallel entity creation complete in 16ms:
  • Created 6 entities concurrently
  • Average time per entity: 3ms
  • Speed improvement vs sequential: ~188x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [1/4] Created connection via API: 609 → 613 (10.0x)
  ℹ️  Tracking connection dependency: 609 → 613 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 612 → 614 (0.1x)
  ℹ️  Tracking connection dependency: 612 → 614 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 613 → 612 (50.0x)
  ℹ️  Tracking connection dependency: 613 → 612 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 610 → 611 (0.3x)
  ℹ️  Tracking connection dependency: 610 → 611 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 14ms for 4 items (3.5ms/item)
🚀 Parallel connection creation complete in 14ms:
  • Created 4 connections concurrently
  • Average time per connection: 4ms
  • Speed improvement vs sequential: ~286x faster
🚀 Parallel test data creation complete in 30ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~233x faster
🚀 Parallel test data setup complete in 30ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
✅ Comparison test setup complete - all test data ready
Comparing: Human XCDKA to Mouse XCDKF (count: 1, unit: Length)
Selecting comparison entity for from field: Human XCDKA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human GPYOA"
Comparison from entity completed: "Human GPYOA" (expected: "Human GPYOA")
Selecting comparison entity for to field: Ball GPYOB
Using prefix "Ball" for comparison to entity
Selected from entity using direct input: "Human LOXTA"
Comparison from entity completed: "Human LOXTA" (expected: "Human LOXTA")
Selecting comparison entity for to field: Build LOXTC
Using prefix "Build" for comparison to entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human XCDKA"
Comparison from entity completed: "Human XCDKA" (expected: "Human XCDKA")
Selecting comparison entity for to field: Mouse XCDKF
Using prefix "Mouse" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Build LOXTC"
Selected to entity using direct input: "Ball GPYOB"
Comparison to entity completed: "Build LOXTC" (expected: "Build LOXTC")
Comparison to entity completed: "Ball GPYOB" (expected: "Ball GPYOB")
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Mouse XCDKF"
Comparison to entity completed: "Mouse XCDKF" (expected: "Mouse XCDKF")
Path API Response: http://localhost:8000/api/v1/compare/?from=609&to=614&unit=1 - Status: 200
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 592
  ❌ [2/6] Failed to delete entity ID: 597
  ❌ [3/6] Failed to delete entity ID: 599
  ❌ [4/6] Failed to delete entity ID: 600
  ❌ [5/6] Failed to delete entity ID: 594
  ❌ [6/6] Failed to delete entity ID: 602
  Fallback cleanup for 6 named entities...
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 603
  ❌ [2/6] Failed to delete entity ID: 606
🧹 Post-test cleanup complete in 704ms (Worker 13):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 13
  • Test efficiency: 99% (704ms cleanup / 54870ms total)
⚠️  6 cleanup operations failed - may affect future tests
  ❌ [3/6] Failed to delete entity ID: 607
  ✅ Cleanup verification: No test entities remain from Worker 13 (204ms)
  ❌ [4/6] Failed to delete entity ID: 608
  ❌ [5/6] Failed to delete entity ID: 604
  ❌ [6/6] Failed to delete entity ID: 605
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 888ms (Worker 14):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 14
  • Test efficiency: 98% (887ms cleanup / 53070ms total)
⚠️  6 cleanup operations failed - may affect future tests
  ✅ Cleanup verification: No test entities remain from Worker 14 (76ms)
[15/240] ❌ should calculate transitive relationships (57.0s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.
  ✘  16 [chromium] › e2e/tests/comparisons.spec.ts:215:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships (57.0s)
[16/240] ❌ should calculate direct relationships (58.4s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.
  ✘  15 [chromium] › e2e/tests/comparisons.spec.ts:195:7 › Entity Comparisons and Pathfinding › should calculate direct relationships (58.4s)
[17/240] 🏃 Starting: should calculate transitive relationships
[17/240] 🏃 Starting: should calculate direct relationships
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 9 total entities in database (Worker 15)
  Identified 6 test entities to delete for Worker 15
  Found 9 total entities in database (Worker 16)
  Identified 6 test entities to delete for Worker 16
  ✓ Deleted test entity: Ball XCDKB (ID: 613)
  ❌ Failed to delete entity: Ball XCDKB (ID: 613)
  ✓ Deleted test entity: Eleph XCDKE (ID: 611)
  ✓ Deleted test entity: Build XCDKC (ID: 612)
  ✓ Deleted test entity: Car XCDKD (ID: 610)
  ✓ Deleted test entity: Car XCDKD (ID: 610)
  ✓ Deleted test entity: Mouse XCDKF (ID: 614)
  ❌ Failed to delete entity: Build XCDKC (ID: 612)
  ✓ Deleted test entity: Human XCDKA (ID: 609)
  ❌ Failed to delete entity: Eleph XCDKE (ID: 611)
  ❌ Failed to delete entity: Mouse XCDKF (ID: 614)
  ❌ Failed to delete entity: Human XCDKA (ID: 609)
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
🧹 Pre-test cleanup complete in 785ms (Worker 15):
  • Initial entities: 9
  • Entities deleted: 6
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 6
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ❌ [1/6] Failed to delete entity ID: 609
🧹 Pre-test cleanup complete in 821ms (Worker 16):
  • Initial entities: 9
  • Entities deleted: 1
  • Failed deletions: 5
  • Final entity count: 3
  • Net reduction: 6
⚠️  5 entity deletions failed - may cause test conflicts
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ❌ [2/6] Failed to delete entity ID: 610
  ❌ [3/6] Failed to delete entity ID: 611
  ❌ [4/6] Failed to delete entity ID: 614
  ❌ [5/6] Failed to delete entity ID: 612
  ✓ [1/6] Created via API: Human UHWBA (ID: 615)
  ✓ [2/6] Created via API: Ball UHWBB (ID: 616)
  ✓ [3/6] Created via API: Build UHWBC (ID: 617)
  ✓ [4/6] Created via API: Car UHWBD (ID: 618)
  ❌ [6/6] Failed to delete entity ID: 613
  Fallback cleanup for 6 named entities...
  ✓ [1/6] Created via API: Human RKPUA (ID: 619)
  ✓ [2/6] Created via API: Ball RKPUB (ID: 620)
  ✓ [3/6] Created via API: Build RKPUC (ID: 621)
  ✓ [5/6] Created via API: Eleph UHWBE (ID: 622)
  ✓ [6/6] Created via API: Mouse UHWBF (ID: 623)
🚀 Parallel entity-creation: 405ms for 6 items (67.5ms/item)
🚀 Parallel entity creation complete in 405ms:
  • Created 6 entities concurrently
  • Average time per entity: 68ms
  • Speed improvement vs sequential: ~7x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [4/6] Created via API: Car RKPUD (ID: 624)
  ✓ [5/6] Created via API: Eleph RKPUE (ID: 625)
  ✓ [6/6] Created via API: Mouse RKPUF (ID: 626)
🚀 Parallel entity-creation: 392ms for 6 items (65.3ms/item)
🚀 Parallel entity creation complete in 392ms:
  • Created 6 entities concurrently
  • Average time per entity: 65ms
  • Speed improvement vs sequential: ~8x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [4/4] Created connection via API: 618 → 622 (0.3x)
  ℹ️  Tracking connection dependency: 618 → 622 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 616 → 617 (50.0x)
  ℹ️  Tracking connection dependency: 616 → 617 (auto-deleted via CASCADE)
  ✓ [1/4] Created connection via API: 615 → 616 (10.0x)
  ℹ️  Tracking connection dependency: 615 → 616 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 617 → 623 (0.1x)
  ℹ️  Tracking connection dependency: 617 → 623 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 37ms for 4 items (9.3ms/item)
🚀 Parallel connection creation complete in 37ms:
  • Created 4 connections concurrently
  • Average time per connection: 9ms
  • Speed improvement vs sequential: ~108x faster
🚀 Parallel test data creation complete in 443ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~16x faster
🚀 Parallel test data setup complete in 443ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
  ✓ [3/4] Created connection via API: 621 → 626 (0.1x)
  ℹ️  Tracking connection dependency: 621 → 626 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 624 → 625 (0.3x)
  ℹ️  Tracking connection dependency: 624 → 625 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 620 → 621 (50.0x)
  ℹ️  Tracking connection dependency: 620 → 621 (auto-deleted via CASCADE)
  ✓ [1/4] Created connection via API: 619 → 620 (10.0x)
  ℹ️  Tracking connection dependency: 619 → 620 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 22ms for 4 items (5.5ms/item)
🚀 Parallel connection creation complete in 22ms:
  • Created 4 connections concurrently
  • Average time per connection: 6ms
  • Speed improvement vs sequential: ~182x faster
🚀 Parallel test data creation complete in 414ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~17x faster
🚀 Parallel test data setup complete in 414ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
🧹 Post-test cleanup complete in 466ms (Worker 12):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 12
  • Test efficiency: 99% (466ms cleanup / 33864ms total)
⚠️  6 cleanup operations failed - may affect future tests
  ✅ Cleanup verification: No test entities remain from Worker 12 (15ms)
[17/240] ❌ should calculate complex multi-hop paths (34.1s)
   └─ Error: Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m
  ✘  17 [chromium] › e2e/tests/comparisons.spec.ts:229:7 › Entity Comparisons and Pathfinding › should calculate complex multi-hop paths (34.1s)
[18/240] 🏃 Starting: should calculate complex multi-hop paths
✅ Comparison test setup complete - all test data ready
✅ Comparison test setup complete - all test data ready
Comparing: Human RKPUA to Ball RKPUB (count: 1, unit: Length)
Comparing: Human UHWBA to Build UHWBC (count: 1, unit: Length)
Selecting comparison entity for from field: Human RKPUA
Using prefix "Human" for comparison from entity
Selecting comparison entity for from field: Human UHWBA
Using prefix "Human" for comparison from entity
🧹 Starting pre-test cleanup...
  Found 15 total entities in database (Worker 17)
  Identified 12 test entities to delete for Worker 17
  ✓ Deleted test entity: Ball RKPUB (ID: 620)
  ✓ Deleted test entity: Build UHWBC (ID: 617)
  ✓ Deleted test entity: Build RKPUC (ID: 621)
  ✓ Deleted test entity: Eleph RKPUE (ID: 625)
  ✓ Deleted test entity: Human RKPUA (ID: 619)
  ✓ Deleted test entity: Ball UHWBB (ID: 616)
  ✓ Deleted test entity: Car RKPUD (ID: 624)
  ✓ Deleted test entity: Car UHWBD (ID: 618)
  ✓ Deleted test entity: Human UHWBA (ID: 615)
  ✓ Deleted test entity: Eleph UHWBE (ID: 622)
  ✓ Deleted test entity: Mouse UHWBF (ID: 623)
  ✓ Deleted test entity: Mouse RKPUF (ID: 626)
🧹 Pre-test cleanup complete in 156ms (Worker 17):
  • Initial entities: 15
  • Entities deleted: 12
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 12
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [2/6] Created via API: Ball YQIJB (ID: 627)
  ✓ [4/6] Created via API: Car YQIJD (ID: 629)
  ✓ [1/6] Created via API: Human YQIJA (ID: 628)
  ✓ [5/6] Created via API: Eleph YQIJE (ID: 631)
  ✓ [3/6] Created via API: Build YQIJC (ID: 630)
  ✓ [6/6] Created via API: Mouse YQIJF (ID: 632)
🚀 Parallel entity-creation: 15ms for 6 items (2.5ms/item)
🚀 Parallel entity creation complete in 15ms:
  • Created 6 entities concurrently
  • Average time per entity: 3ms
  • Speed improvement vs sequential: ~200x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [2/4] Created connection via API: 627 → 630 (50.0x)
  ℹ️  Tracking connection dependency: 627 → 630 (auto-deleted via CASCADE)
  ✓ [1/4] Created connection via API: 628 → 627 (10.0x)
  ℹ️  Tracking connection dependency: 628 → 627 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 629 → 631 (0.3x)
  ℹ️  Tracking connection dependency: 629 → 631 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 630 → 632 (0.1x)
  ℹ️  Tracking connection dependency: 630 → 632 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 13ms for 4 items (3.3ms/item)
🚀 Parallel connection creation complete in 13ms:
  • Created 4 connections concurrently
  • Average time per connection: 3ms
  • Speed improvement vs sequential: ~308x faster
🚀 Parallel test data creation complete in 28ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~250x faster
🚀 Parallel test data setup complete in 28ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
✅ Comparison test setup complete - all test data ready
Comparing: Human YQIJA to Mouse YQIJF (count: 1, unit: Length)
Selecting comparison entity for from field: Human YQIJA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human UHWBA"
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human RKPUA"
Comparison from entity completed: "Human UHWBA" (expected: "Human UHWBA")
Selecting comparison entity for to field: Build UHWBC
Using prefix "Build" for comparison to entity
Comparison from entity completed: "Human RKPUA" (expected: "Human RKPUA")
Selecting comparison entity for to field: Ball RKPUB
Using prefix "Ball" for comparison to entity
Selected from entity using direct input: "Human YQIJA"
Comparison from entity completed: "Human YQIJA" (expected: "Human YQIJA")
Selecting comparison entity for to field: Mouse YQIJF
Using prefix "Mouse" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Build UHWBC"
Comparison to entity completed: "Build UHWBC" (expected: "Build UHWBC")
Selected to entity using direct input: "Ball RKPUB"
Comparison to entity completed: "Ball RKPUB" (expected: "Ball RKPUB")
Selected to entity using direct input: "Mouse YQIJF"
Path API Response: http://localhost:8000/api/v1/compare/?from=615&to=617&unit=1 - Status: 404
Comparison to entity completed: "Mouse YQIJF" (expected: "Mouse YQIJF")
Path API Response: http://localhost:8000/api/v1/compare/?from=628&to=632&unit=1 - Status: 200
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 615
  ❌ [2/6] Failed to delete entity ID: 618
  ❌ [3/6] Failed to delete entity ID: 622
  ❌ [4/6] Failed to delete entity ID: 623
  ❌ [5/6] Failed to delete entity ID: 616
  ❌ [6/6] Failed to delete entity ID: 617
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 1324ms (Worker 15):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 15
  • Test efficiency: 95% (1324ms cleanup / 28633ms total)
⚠️  6 cleanup operations failed - may affect future tests
  ✅ Cleanup verification: No test entities remain from Worker 15 (605ms)
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 619
  ❌ [2/6] Failed to delete entity ID: 624
  ❌ [3/6] Failed to delete entity ID: 625
  ❌ [4/6] Failed to delete entity ID: 626
  ❌ [5/6] Failed to delete entity ID: 620
  ❌ [6/6] Failed to delete entity ID: 621
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 979ms (Worker 16):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 16
  • Test efficiency: 97% (979ms cleanup / 30464ms total)
⚠️  6 cleanup operations failed - may affect future tests
  ✅ Cleanup verification: No test entities remain from Worker 16 (443ms)
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ✓ [1/6] Deleted entity ID: 629 (1 deps) + cascaded connections
  ✓ [2/6] Deleted entity ID: 628 (1 deps) + cascaded connections
  ✓ [3/6] Deleted entity ID: 631 (1 deps) + cascaded connections
  ✓ [4/6] Deleted entity ID: 632 (1 deps) + cascaded connections
  ✓ [5/6] Deleted entity ID: 627 (2 deps) + cascaded connections
  ✓ [6/6] Deleted entity ID: 630 (2 deps) + cascaded connections
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 1173ms (Worker 17):
  • Entities deleted: 6 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 17
  • Test efficiency: 97% (1173ms cleanup / 34888ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 17 (102ms)
[18/240] ❌ should calculate transitive relationships (34.4s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.
  ✘  18 [chromium] › e2e/tests/comparisons.spec.ts:215:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships (retry #1) (34.4s)
[19/240] ❌ should calculate direct relationships (34.8s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.
  ✘  19 [chromium] › e2e/tests/comparisons.spec.ts:195:7 › Entity Comparisons and Pathfinding › should calculate direct relationships (retry #1) (34.8s)
[20/240] ❌ should calculate complex multi-hop paths (39.5s)

📈 Progress: 20/240 (8%)
   ✅ 2 passed | ❌ 18 failed | ⏭️ 0 skipped
   ⏱️  Elapsed: 2.3min | Remaining: ~60.4min

   └─ Error: Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m
  ✘  20 [chromium] › e2e/tests/comparisons.spec.ts:229:7 › Entity Comparisons and Pathfinding › should calculate complex multi-hop paths (retry #1) (39.5s)
[21/240] 🏃 Starting: should handle different unit entities with no connection
[21/240] 🏃 Starting: should handle reverse path calculations
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 3 total entities in database (Worker 19)
  Identified 0 test entities to delete for Worker 19
  Found 3 total entities in database (Worker 18)
  Identified 0 test entities to delete for Worker 18
🧹 Pre-test cleanup complete in 28ms (Worker 19):
🧹 Pre-test cleanup complete in 28ms (Worker 18):
  • Initial entities: 3
  • Entities deleted: 0
  • Failed deletions: 0
  • Final entity count: 3
  • Initial entities: 3
  • Entities deleted: 0
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 0
  • Net reduction: 0
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
🚀 Creating 6 entities in parallel via API...
  ✓ [1/6] Created via API: Human MBKWA (ID: 634)
  ✓ [2/6] Created via API: Ball MBKWB (ID: 636)
  ✓ [3/6] Created via API: Build XVOFC (ID: 635)
  ✓ [1/6] Created via API: Human XVOFA (ID: 633)
  ✓ [3/6] Created via API: Build MBKWC (ID: 637)
  ✓ [2/6] Created via API: Ball XVOFB (ID: 639)
  ✓ [5/6] Created via API: Eleph XVOFE (ID: 638)
  ✓ [6/6] Created via API: Mouse XVOFF (ID: 644)
  ✓ [4/6] Created via API: Car XVOFD (ID: 643)
🚀 Parallel entity-creation: 22ms for 6 items (3.7ms/item)
🚀 Parallel entity creation complete in 22ms:
  • Created 6 entities concurrently
  • Average time per entity: 4ms
  • Speed improvement vs sequential: ~136x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [4/6] Created via API: Car MBKWD (ID: 640)
  ✓ [6/6] Created via API: Mouse MBKWF (ID: 642)
  ✓ [5/6] Created via API: Eleph MBKWE (ID: 641)
🚀 Parallel entity-creation: 23ms for 6 items (3.8ms/item)
🚀 Parallel entity creation complete in 23ms:
  • Created 6 entities concurrently
  • Average time per entity: 4ms
  • Speed improvement vs sequential: ~130x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [1/4] Created connection via API: 633 → 639 (10.0x)
  ℹ️  Tracking connection dependency: 633 → 639 (auto-deleted via CASCADE)
  ✓ [1/4] Created connection via API: 634 → 636 (10.0x)
  ℹ️  Tracking connection dependency: 634 → 636 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 640 → 641 (0.3x)
  ℹ️  Tracking connection dependency: 640 → 641 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 639 → 635 (50.0x)
  ℹ️  Tracking connection dependency: 639 → 635 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 643 → 638 (0.3x)
  ℹ️  Tracking connection dependency: 643 → 638 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 635 → 644 (0.1x)
  ℹ️  Tracking connection dependency: 635 → 644 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 17ms for 4 items (4.3ms/item)
🚀 Parallel connection creation complete in 17ms:
  • Created 4 connections concurrently
  • Average time per connection: 4ms
  • Speed improvement vs sequential: ~235x faster
🚀 Parallel test data creation complete in 39ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~179x faster
🚀 Parallel test data setup complete in 39ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
  ✓ [3/4] Created connection via API: 637 → 642 (0.1x)
  ℹ️  Tracking connection dependency: 637 → 642 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 636 → 637 (50.0x)
  ℹ️  Tracking connection dependency: 636 → 637 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 16ms for 4 items (4.0ms/item)
🚀 Parallel connection creation complete in 16ms:
  • Created 4 connections concurrently
  • Average time per connection: 4ms
  • Speed improvement vs sequential: ~250x faster
🚀 Parallel test data creation complete in 39ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~179x faster
🚀 Parallel test data setup complete in 39ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
[21/240] 🏃 Starting: should handle same entity comparison
🧹 Starting pre-test cleanup...
  Found 15 total entities in database (Worker 20)
  Identified 12 test entities to delete for Worker 20
  ✓ Deleted test entity: Ball MBKWB (ID: 636)
  ✓ Deleted test entity: Build XVOFC (ID: 635)
  ✓ Deleted test entity: Build MBKWC (ID: 637)
  ✓ Deleted test entity: Ball XVOFB (ID: 639)
  ✓ Deleted test entity: Car MBKWD (ID: 640)
  ✓ Deleted test entity: Car XVOFD (ID: 643)
  ✓ Deleted test entity: Eleph MBKWE (ID: 641)
  ✓ Deleted test entity: Eleph XVOFE (ID: 638)
  ✓ Deleted test entity: Human MBKWA (ID: 634)
  ✓ Deleted test entity: Human XVOFA (ID: 633)
  ✓ Deleted test entity: Mouse XVOFF (ID: 644)
  ✓ Deleted test entity: Mouse MBKWF (ID: 642)
🧹 Pre-test cleanup complete in 143ms (Worker 20):
  • Initial entities: 15
  • Entities deleted: 12
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 12
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [1/6] Created via API: Human VGRCA (ID: 645)
  ✓ [3/6] Created via API: Build VGRCC (ID: 647)
  ✓ [2/6] Created via API: Ball VGRCB (ID: 646)
  ✓ [5/6] Created via API: Eleph VGRCE (ID: 649)
  ✓ [6/6] Created via API: Mouse VGRCF (ID: 648)
  ✓ [4/6] Created via API: Car VGRCD (ID: 650)
🚀 Parallel entity-creation: 15ms for 6 items (2.5ms/item)
🚀 Parallel entity creation complete in 15ms:
  • Created 6 entities concurrently
  • Average time per entity: 3ms
  • Speed improvement vs sequential: ~200x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [1/4] Created connection via API: 645 → 646 (10.0x)
  ℹ️  Tracking connection dependency: 645 → 646 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 646 → 647 (50.0x)
  ℹ️  Tracking connection dependency: 646 → 647 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 650 → 649 (0.3x)
  ℹ️  Tracking connection dependency: 650 → 649 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 647 → 648 (0.1x)
  ℹ️  Tracking connection dependency: 647 → 648 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 14ms for 4 items (3.5ms/item)
🚀 Parallel connection creation complete in 14ms:
  • Created 4 connections concurrently
  • Average time per connection: 4ms
  • Speed improvement vs sequential: ~286x faster
🚀 Parallel test data creation complete in 29ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~241x faster
🚀 Parallel test data setup complete in 29ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
✅ Comparison test setup complete - all test data ready
✅ Comparison test setup complete - all test data ready
Comparing: Human MBKWA to Car MBKWD (count: 1, unit: Length)
Comparing: Ball XVOFB to Human XVOFA (count: 1, unit: Length)
Selecting comparison entity for from field: Ball XVOFB
Using prefix "Ball" for comparison from entity
Selecting comparison entity for from field: Human MBKWA
Using prefix "Human" for comparison from entity
✅ Comparison test setup complete - all test data ready
Comparing: Human VGRCA to Human VGRCA (count: 1, unit: Length)
Selecting comparison entity for from field: Human VGRCA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Ball XVOFB"
Selected from entity using direct input: "Human MBKWA"
Selected from entity using direct input: "Human VGRCA"
Comparison from entity completed: "Ball XVOFB" (expected: "Ball XVOFB")
Selecting comparison entity for to field: Human XVOFA
Using prefix "Human" for comparison to entity
Comparison from entity completed: "Human MBKWA" (expected: "Human MBKWA")
Selecting comparison entity for to field: Car MBKWD
Using prefix "Car" for comparison to entity
Comparison from entity completed: "Human VGRCA" (expected: "Human VGRCA")
Selecting comparison entity for to field: Human VGRCA
Using prefix "Human" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Human XVOFA"
Comparison to entity completed: "Human XVOFA" (expected: "Human XVOFA")
Selected to entity using direct input: "Car MBKWD"
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Comparison to entity completed: "Car MBKWD" (expected: "Car MBKWD")
Selected to entity using direct input: "Human VGRCA"
Comparison to entity completed: "Human VGRCA" (expected: "Human VGRCA")
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 633
  ❌ [2/6] Failed to delete entity ID: 638
  ❌ [3/6] Failed to delete entity ID: 644
  ❌ [4/6] Failed to delete entity ID: 643
  ❌ [5/6] Failed to delete entity ID: 635
  ❌ [6/6] Failed to delete entity ID: 639
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 1455ms (Worker 18):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 18
  • Test efficiency: 95% (1454ms cleanup / 32184ms total)
⚠️  6 cleanup operations failed - may affect future tests
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 634
  ✅ Cleanup verification: No test entities remain from Worker 18 (256ms)
  ❌ [2/6] Failed to delete entity ID: 640
  ❌ [3/6] Failed to delete entity ID: 642
  ❌ [4/6] Failed to delete entity ID: 641
  ❌ [5/6] Failed to delete entity ID: 636
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [6/6] Failed to delete entity ID: 637
  Fallback cleanup for 6 named entities...
  ✓ [1/6] Deleted entity ID: 645 (1 deps) + cascaded connections
  ✓ [2/6] Deleted entity ID: 649 (1 deps) + cascaded connections
🧹 Post-test cleanup complete in 978ms (Worker 19):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 19
  • Test efficiency: 97% (978ms cleanup / 33384ms total)
⚠️  6 cleanup operations failed - may affect future tests
  ✓ [3/6] Deleted entity ID: 648 (1 deps) + cascaded connections
  ✓ [4/6] Deleted entity ID: 650 (1 deps) + cascaded connections
  ✅ Cleanup verification: No test entities remain from Worker 19 (202ms)
  ✓ [5/6] Deleted entity ID: 647 (2 deps) + cascaded connections
  ✓ [6/6] Deleted entity ID: 646 (2 deps) + cascaded connections
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 1395ms (Worker 20):
  • Entities deleted: 6 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 20
  • Test efficiency: 96% (1393ms cleanup / 34116ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 20 (255ms)
[21/240] ❌ should handle reverse path calculations (43.4s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.
  ✘  22 [chromium] › e2e/tests/comparisons.spec.ts:244:7 › Entity Comparisons and Pathfinding › should handle reverse path calculations (43.4s)
[22/240] ❌ should handle different unit entities with no connection (44.5s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.
  ✘  21 [chromium] › e2e/tests/comparisons.spec.ts:258:7 › Entity Comparisons and Pathfinding › should handle different unit entities with no connection (44.5s)
[23/240] ❌ should handle same entity comparison (45.9s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.
  ✘  23 [chromium] › e2e/tests/comparisons.spec.ts:275:7 › Entity Comparisons and Pathfinding › should handle same entity comparison (45.9s)
[24/240] 🏃 Starting: should handle reverse path calculations
[24/240] 🏃 Starting: should handle different unit entities with no connection
[24/240] 🏃 Starting: should handle same entity comparison
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 3 total entities in database (Worker 21)
  Identified 0 test entities to delete for Worker 21
  Found 3 total entities in database (Worker 22)
  Identified 0 test entities to delete for Worker 22
  Found 3 total entities in database (Worker 23)
  Identified 0 test entities to delete for Worker 23
🧹 Pre-test cleanup complete in 5153ms (Worker 21):
  • Initial entities: 3
  • Entities deleted: 0
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 0
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
🧹 Pre-test cleanup complete in 6122ms (Worker 22):
  • Initial entities: 3
  • Entities deleted: 0
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 0
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
🧹 Pre-test cleanup complete in 6276ms (Worker 23):
  • Initial entities: 3
  • Entities deleted: 0
  • Failed deletions: 0
  • Final entity count: 3
  • Net reduction: 0
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [3/6] Created via API: Build HCEHC (ID: 651)
  ✓ [1/6] Created via API: Human HCEHA (ID: 654)
  ✓ [5/6] Created via API: Eleph HCEHE (ID: 652)
  ❌ Failed to create entity via API: Ball HCEHB - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m

🚀 Parallel entity-creation: 8040ms for 6 items (1340.0ms/item)
❌ Parallel entity creation failed after 8040ms: Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m

❌ Parallel test data creation failed after 8040ms: Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m

❌ Parallel test data setup failed after 8040ms: Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m

Falling back to sequential entity and connection creation...
  ❌ Failed to create entity via API: Car HCEHD - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 32[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 32[22m

  ❌ Failed to create entity via API: Mouse HCEHF - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

  ❌ Failed to create entity via API: Human GVANA - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

🚀 Parallel entity-creation: 8282ms for 6 items (1380.3ms/item)
❌ Parallel entity creation failed after 8282ms: Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

❌ Parallel test data creation failed after 8283ms: Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

❌ Parallel test data setup failed after 8291ms: Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

Falling back to sequential entity and connection creation...
  ❌ Failed to create entity via API: Ball GVANB - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m

  ❌ Failed to create entity via API: Build GVANC - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

  ❌ Failed to create entity via API: Car GVAND - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 32[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 32[22m

  ❌ Failed to create entity via API: Eleph GVANE - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

  ❌ Failed to create entity via API: Mouse GVANF - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 22[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

  ❌ Failed to create entity via API: Human EWXNA - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

🚀 Parallel entity-creation: 8031ms for 6 items (1338.5ms/item)
❌ Parallel entity creation failed after 8031ms: Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

❌ Parallel test data creation failed after 8031ms: Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

❌ Parallel test data setup failed after 8031ms: Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:36 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

Falling back to sequential entity and connection creation...
  ❌ Failed to create entity via API: Car EWXND - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 32[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:38 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 32[22m

  ❌ Failed to create entity via API: Build EWXNC - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:38 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

  ❌ Failed to create entity via API: Eleph EWXNE - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:38 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

  ❌ Failed to create entity via API: Mouse EWXNF - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:38 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 34[22m

  ❌ Failed to create entity via API: Ball EWXNB - Error: apiRequestContext.post: Request timed out after 8000ms
Call log:
[2m  - → POST http://localhost:8000/api/v1/entities[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m
[2m  - ← 307 Temporary Redirect[22m
[2m    - date: Sat, 05 Jul 2025 23:32:38 GMT[22m
[2m    - server: uvicorn[22m
[2m    - content-length: 0[22m
[2m    - location: http://localhost:8000/api/v1/entities/[22m
[2m  - → POST http://localhost:8000/api/v1/entities/[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 23[22m
[2m    - x-test-isolation: enabled[22m
[2m    - content-type: application/json[22m
[2m    - content-length: 33[22m

🧹 Starting post-test cleanup...
🧹 Starting post-test cleanup...
🧹 Starting post-test cleanup...
  Cleaning 3 tracked entity IDs...
  ℹ️  Dependencies: 0 connections, avg 0 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
🧹 Post-test cleanup complete in 5651ms (Worker 22):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 22
  • Test efficiency: 86% (5651ms cleanup / 40654ms total)
  ℹ️  No tracked test data found (test may not have created entities)
  ✅ All cleanup operations successful
🧹 Post-test cleanup complete in 6548ms (Worker 23):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 23
  • Test efficiency: 84% (6548ms cleanup / 41600ms total)
  ℹ️  No tracked test data found (test may not have created entities)
  ✅ All cleanup operations successful
Error deleting entity 651: apiRequestContext.delete: Request timed out after 8000ms
Call log:
[2m  - → DELETE http://localhost:8000/api/v1/entities/651[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m

    at TestHelpers.deleteEntityById [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:452:54[90m)[39m
    at TestHelpers.cleanupAfterTest [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:609:38[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/comparisons.spec.ts:183:19 {
  name: [32m'Error'[39m,
  [32mSymbol(step)[39m: {
    stepId: [32m'pw:api@21'[39m,
    location: {
      file: [32m'/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts'[39m,
      line: [33m452[39m,
      column: [33m54[39m,
      function: [32m'TestHelpers.deleteEntityById'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'DELETE "/api/v1/entities/651"'[39m,
    apiName: [32m'apiRequestContext.delete'[39m,
    params: {
      url: [32m'http://localhost:8000/api/v1/entities/651'[39m,
      params: [90mundefined[39m,
      encodedParams: [90mundefined[39m,
      method: [32m'DELETE'[39m,
      headers: [90mundefined[39m,
      postData: [90mundefined[39m,
      jsonData: [90mundefined[39m,
      formData: [90mundefined[39m,
      multipartData: [90mundefined[39m,
      timeout: [33m8000[39m,
      failOnStatusCode: [90mundefined[39m,
      ignoreHTTPSErrors: [90mundefined[39m,
      maxRedirects: [90mundefined[39m,
      maxRetries: [90mundefined[39m,
      __testHookLookup: [90mundefined[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@21'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1751758397079[39m,
    error: {
      message: [32m'Error: apiRequestContext.delete: Request timed out after 8000ms\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - → DELETE http://localhost:8000/api/v1/entities/651\x1B[22m\n'[39m +
        [32m'\x1B[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36\x1B[22m\n'[39m +
        [32m'\x1B[2m    - accept: */*\x1B[22m\n'[39m +
        [32m'\x1B[2m    - accept-encoding: gzip,deflate,br\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-backend-url: http://localhost:8000\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-worker-id: 21\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-isolation: enabled\x1B[22m\n'[39m,
      stack: [32m'Error: apiRequestContext.delete: Request timed out after 8000ms\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - → DELETE http://localhost:8000/api/v1/entities/651\x1B[22m\n'[39m +
        [32m'\x1B[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36\x1B[22m\n'[39m +
        [32m'\x1B[2m    - accept: */*\x1B[22m\n'[39m +
        [32m'\x1B[2m    - accept-encoding: gzip,deflate,br\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-backend-url: http://localhost:8000\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-worker-id: 21\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-isolation: enabled\x1B[22m\n'[39m +
        [32m'\n'[39m +
        [32m'    at TestHelpers.deleteEntityById (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:452:54)\n'[39m +
        [32m'    at TestHelpers.cleanupAfterTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:609:38)\n'[39m +
        [32m'    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:183:19'[39m,
      cause: [90mundefined[39m
    }
  }
}
  ❌ [1/3] Failed to delete entity ID: 651
  ✅ Cleanup verification: No test entities remain from Worker 22 (5563ms)
  ✅ Cleanup verification: No test entities remain from Worker 23 (5207ms)
  ✓ [2/3] Deleted entity ID: 654 (0 deps) + cascaded connections
[24/240] ❌ should handle different unit entities with no connection (53.2s)
   └─ Error: TimeoutError: page.goto: Timeout 15000ms exceeded.
  ✘  25 [chromium] › e2e/tests/comparisons.spec.ts:258:7 › Entity Comparisons and Pathfinding › should handle different unit entities with no connection (retry #1) (53.2s)
[31mTesting stopped early after 10 maximum allowed failures.[39m
Error deleting entity 652: apiRequestContext.delete: Target page, context or browser has been closed
Call log:
[2m  - → DELETE http://localhost:8000/api/v1/entities/652[22m
[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36[22m
[2m    - accept: */*[22m
[2m    - accept-encoding: gzip,deflate,br[22m
[2m    - x-test-backend-url: http://localhost:8000[22m
[2m    - x-test-worker-id: 21[22m
[2m    - x-test-isolation: enabled[22m

    at TestHelpers.deleteEntityById [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:452:54[90m)[39m
    at TestHelpers.cleanupAfterTest [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:609:38[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/comparisons.spec.ts:183:5 {
  [32mSymbol(step)[39m: {
    stepId: [32m'pw:api@23'[39m,
    location: {
      file: [32m'/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts'[39m,
      line: [33m452[39m,
      column: [33m54[39m,
      function: [32m'TestHelpers.deleteEntityById'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'DELETE "/api/v1/entities/652"'[39m,
    apiName: [32m'apiRequestContext.delete'[39m,
    params: {
      url: [32m'http://localhost:8000/api/v1/entities/652'[39m,
      params: [90mundefined[39m,
      encodedParams: [90mundefined[39m,
      method: [32m'DELETE'[39m,
      headers: [90mundefined[39m,
      postData: [90mundefined[39m,
      jsonData: [90mundefined[39m,
      formData: [90mundefined[39m,
      multipartData: [90mundefined[39m,
      timeout: [33m8000[39m,
      failOnStatusCode: [90mundefined[39m,
      ignoreHTTPSErrors: [90mundefined[39m,
      maxRedirects: [90mundefined[39m,
      maxRetries: [90mundefined[39m,
      __testHookLookup: [90mundefined[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@23'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1751758407100[39m,
    error: {
      message: [32m'Error: apiRequestContext.delete: Target page, context or browser has been closed\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - → DELETE http://localhost:8000/api/v1/entities/652\x1B[22m\n'[39m +
        [32m'\x1B[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36\x1B[22m\n'[39m +
        [32m'\x1B[2m    - accept: */*\x1B[22m\n'[39m +
        [32m'\x1B[2m    - accept-encoding: gzip,deflate,br\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-backend-url: http://localhost:8000\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-worker-id: 21\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-isolation: enabled\x1B[22m\n'[39m,
      stack: [32m'Error: apiRequestContext.delete: Target page, context or browser has been closed\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - → DELETE http://localhost:8000/api/v1/entities/652\x1B[22m\n'[39m +
        [32m'\x1B[2m    - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36\x1B[22m\n'[39m +
        [32m'\x1B[2m    - accept: */*\x1B[22m\n'[39m +
        [32m'\x1B[2m    - accept-encoding: gzip,deflate,br\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-backend-url: http://localhost:8000\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-worker-id: 21\x1B[22m\n'[39m +
        [32m'\x1B[2m    - x-test-isolation: enabled\x1B[22m\n'[39m +
        [32m'\n'[39m +
        [32m'    at TestHelpers.deleteEntityById (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:452:54)\n'[39m +
        [32m'    at TestHelpers.cleanupAfterTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:609:38)\n'[39m +
        [32m'    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:183:5'[39m,
      cause: [90mundefined[39m
    }
  }
}
  ❌ [3/3] Failed to delete entity ID: 652
  Fallback cleanup for 3 named entities...
Error fetching entities: apiRequestContext.get: Test ended.
    at TestHelpers.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:420:48[90m)[39m
    at TestHelpers.cleanupAfterTest [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:628:40[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/comparisons.spec.ts:183:5
Error fetching entities: apiRequestContext.get: Test ended.
    at TestHelpers.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:420:48[90m)[39m
    at TestHelpers.cleanupAfterTest [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:656:40[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/comparisons.spec.ts:183:5
🧹 Post-test cleanup complete in 18110ms (Worker 21):
  • Entities deleted: 1 (+ cascaded connections)
  • Failed operations: 2
  • Worker isolation: 21
  • Test efficiency: 67% (18110ms cleanup / 55110ms total)
⚠️  2 cleanup operations failed - may affect future tests
Error fetching entities: apiRequestContext.get: Test ended.
    at TestHelpers.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:420:48[90m)[39m
    at TestHelpers.verifyCleanupCompleteness [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:758:44[90m)[39m
    at TestHelpers.cleanupAfterTest [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/helpers.ts:734:18[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/comparisons.spec.ts:183:5
  ✅ Cleanup verification: No test entities remain from Worker 21 (1ms)
[25/240] ❓ should handle same entity comparison (52.6s)
  ✘  26 [chromium] › e2e/tests/comparisons.spec.ts:275:7 › Entity Comparisons and Pathfinding › should handle same entity comparison (retry #1) (52.6s)
[26/240] ❓ should handle reverse path calculations (60.4s)
  ✘  24 [chromium] › e2e/tests/comparisons.spec.ts:244:7 › Entity Comparisons and Pathfinding › should handle reverse path calculations (retry #1) (1.0m)

================================================================================
📊 FINAL TEST RESULTS
================================================================================
✅ Passed:  2
❌ Failed:  22
⏭️  Skipped: 0
⏱️  Total Duration: 4.3 minutes
📈 Average Test Time: 24.2s
================================================================================



  1) [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:40:7 › Cleanup Performance Validation › should demonstrate 80% performance improvement in cleanup operations 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  BFJ PerfTe' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:52:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  DKB PerfTe' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:52:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  2) [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:100:7 › Cleanup Performance Validation › should validate batch cleanup operations efficiency 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  J BatchTes' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:106:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  JW BatchTe' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:106:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  3) [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:135:7 › Cleanup Performance Validation › should validate worker isolation in parallel test environment 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  A WorkerEn' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:142:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  LYU Worker' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:142:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  4) [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:171:7 › Cleanup Performance Validation › should validate error handling and recovery in cleanup operations 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  HX ErrorTe' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:175:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  IR ErrorTe' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:175:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  5) [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:209:7 › Cleanup Performance Validation › should validate cleanup performance monitoring and reporting 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  VW Monitor' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:213:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  TQ Monitor' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:213:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  6) [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:257:7 › Cleanup Performance Validation › should demonstrate overall performance gains across all operations 

    Error: Backend health check failed: Network error: Error: apiRequestContext.get: Test ended. (1ms latency)
    Please ensure services are running: docker-compose -f docker-compose.dev.yml up -d

       at ../utils/enhanced-helpers.ts:298

      296 |     
      297 |     if (!healthCheck.healthy) {
    > 298 |       throw new Error(
          |             ^
      299 |         `Backend health check failed: ${healthCheck.response} (${healthCheck.latency}ms latency)\n` +
      300 |         'Please ensure services are running: docker-compose -f docker-compose.dev.yml up -d'
      301 |       );
        at EnhancedTestHelpers.ensureBackendHealthy (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:298:13)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:24:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  NOG Overal' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:274:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  7) [chromium] › e2e/tests/comparisons.spec.ts:195:7 › Entity Comparisons and Pathfinding › should calculate direct relationships 

    TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.

       at ../fixtures/page-objects.ts:1510

      1508 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1509 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1510 |     await this.page.waitForFunction(() => {
           |                     ^
      1511 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1512 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1513 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1510:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:199:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.

       at ../fixtures/page-objects.ts:1510

      1508 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1509 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1510 |     await this.page.waitForFunction(() => {
           |                     ^
      1511 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1512 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1513 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1510:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:199:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  8) [chromium] › e2e/tests/comparisons.spec.ts:215:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships 

    TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.

       at ../fixtures/page-objects.ts:1510

      1508 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1509 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1510 |     await this.page.waitForFunction(() => {
           |                     ^
      1511 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1512 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1513 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1510:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:219:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.

       at ../fixtures/page-objects.ts:1510

      1508 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1509 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1510 |     await this.page.waitForFunction(() => {
           |                     ^
      1511 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1512 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1513 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1510:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:219:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  9) [chromium] › e2e/tests/comparisons.spec.ts:229:7 › Entity Comparisons and Pathfinding › should calculate complex multi-hop paths 

    Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

    Expected substring: [32m"Human"[39m
    Received string:    [31m"Did you know thatis asmeasurebigtallheavylongvoluminousas50.0?"[39m

      238 |     const result = await comparisonPage.getComparisonResult();
      239 |     expect(result).toContain('50.0');
    > 240 |     expect(result).toContain('Human');
          |                    ^
      241 |     expect(result).toContain('Mouse');
      242 |   });
      243 |
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:240:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

    Expected substring: [32m"Human"[39m
    Received string:    [31m"Did you know thatis asmeasurebigtallheavylongvoluminousas50.0?"[39m

      238 |     const result = await comparisonPage.getComparisonResult();
      239 |     expect(result).toContain('50.0');
    > 240 |     expect(result).toContain('Human');
          |                    ^
      241 |     expect(result).toContain('Mouse');
      242 |   });
      243 |
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:240:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium-retry1/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  10) [chromium] › e2e/tests/comparisons.spec.ts:244:7 › Entity Comparisons and Pathfinding › should handle reverse path calculations 

    TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.

       at ../fixtures/page-objects.ts:1510

      1508 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1509 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1510 |     await this.page.waitForFunction(() => {
           |                     ^
      1511 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1512 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1513 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1510:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:248:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test was interrupted.

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  11) [chromium] › e2e/tests/comparisons.spec.ts:258:7 › Entity Comparisons and Pathfinding › should handle different unit entities with no connection 

    TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.

       at ../fixtures/page-objects.ts:1510

      1508 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1509 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1510 |     await this.page.waitForFunction(() => {
           |                     ^
      1511 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1512 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1513 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1510:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:263:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.goto: Timeout 15000ms exceeded.
    Call log:
    [2m  - navigating to "http://localhost:3000/entities", waiting until "load"[22m


       at ../fixtures/page-objects.ts:91

      89 |
      90 |   async goto(path: string = '/') {
    > 91 |     await this.page.goto(path);
         |                     ^
      92 |     // Wait for page to be loaded
      93 |     await this.page.waitForLoadState('networkidle');
      94 |   }
        at EntityManagerPage.goto (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:91:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:115:24

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/error-context.md

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  12) [chromium] › e2e/tests/comparisons.spec.ts:275:7 › Entity Comparisons and Pathfinding › should handle same entity comparison 

    TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.

       at ../fixtures/page-objects.ts:1510

      1508 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1509 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1510 |     await this.page.waitForFunction(() => {
           |                     ^
      1511 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1512 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1513 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1510:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:278:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Test was interrupted.

    Error Context: test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium-retry1/error-context.md

    attachment #2: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  12 failed
    [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:40:7 › Cleanup Performance Validation › should demonstrate 80% performance improvement in cleanup operations 
    [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:100:7 › Cleanup Performance Validation › should validate batch cleanup operations efficiency 
    [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:135:7 › Cleanup Performance Validation › should validate worker isolation in parallel test environment 
    [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:171:7 › Cleanup Performance Validation › should validate error handling and recovery in cleanup operations 
    [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:209:7 › Cleanup Performance Validation › should validate cleanup performance monitoring and reporting 
    [chromium] › e2e/tests/cleanup-performance-validation.spec.ts:257:7 › Cleanup Performance Validation › should demonstrate overall performance gains across all operations 
    [chromium] › e2e/tests/comparisons.spec.ts:195:7 › Entity Comparisons and Pathfinding › should calculate direct relationships 
    [chromium] › e2e/tests/comparisons.spec.ts:215:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships 
    [chromium] › e2e/tests/comparisons.spec.ts:229:7 › Entity Comparisons and Pathfinding › should calculate complex multi-hop paths 
    [chromium] › e2e/tests/comparisons.spec.ts:244:7 › Entity Comparisons and Pathfinding › should handle reverse path calculations 
    [chromium] › e2e/tests/comparisons.spec.ts:258:7 › Entity Comparisons and Pathfinding › should handle different unit entities with no connection 
    [chromium] › e2e/tests/comparisons.spec.ts:275:7 › Entity Comparisons and Pathfinding › should handle same entity comparison 
  226 did not run
  2 passed (4.3m)
  1 error was not a part of any test, see above for details

To open last HTML report run:
[36m[39m
[36m  npx playwright show-report[39m
[36m[39m
