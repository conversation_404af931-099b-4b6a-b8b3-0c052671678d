
> simile-frontend@0.1.0 test:e2e
> playwright test --project=chromium --grep=^(setup-verification|navigation|entities|connections) --reporter=json

{
  "config": {
    "configFile": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/playwright.config.ts",
    "rootDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
    "forbidOnly": false,
    "fullyParallel": true,
    "globalSetup": null,
    "globalTeardown": null,
    "globalTimeout": 7200000,
    "grep": {},
    "grepInvert": null,
    "maxFailures": 10,
    "metadata": {},
    "preserveOutput": "always",
    "reporter": [
      [
        "json"
      ]
    ],
    "reportSlowTests": {
      "max": 5,
      "threshold": 300000
    },
    "quiet": false,
    "projects": [
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {},
        "id": "chromium",
        "name": "chromium",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {},
        "id": "firefox",
        "name": "firefox",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {},
        "id": "webkit",
        "name": "webkit",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      }
    ],
    "shard": null,
    "updateSnapshots": "missing",
    "updateSourceMethod": "patch",
    "version": "1.53.1",
    "workers": 3,
    "webServer": {
      "command": "npm start",
      "url": "http://localhost:3000",
      "reuseExistingServer": true,
      "timeout": 120000
    }
  },
  "suites": [],
  "errors": [
    {
      "message": "Error: No tests found",
      "stack": "Error: No tests found"
    }
  ],
  "stats": {
    "startTime": "2025-06-28T17:27:22.147Z",
    "duration": 44.97300000000001,
    "expected": 0,
    "skipped": 0,
    "unexpected": 0,
    "flaky": 0
  }
}
