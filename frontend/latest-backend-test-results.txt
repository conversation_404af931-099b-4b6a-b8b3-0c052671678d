✅ Using virtual environment: /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv

Checking required services...
Checking test environment...
----------------------------------------
✅ Running in virtual environment
✅ PostgreSQL is running on localhost:5432
----------------------------------------
✅ All checks passed!

Setting up test database...
INFO:__main__:simile_test database already exists
INFO:__main__:Dropping existing tables...
INFO:__main__:Creating database tables...
INFO:__main__:Inserting initial units...
INFO:__main__:Database schema setup completed successfully
INFO:__main__:✅ Database verification passed. Found tables: connections, entities, units
INFO:__main__:✅ Found 5 units
INFO:__main__:✅ Test database setup completed successfully!

Running tests...
============================= test session starts ==============================
platform darwin -- Python 3.11.13, pytest-7.4.4, pluggy-1.6.0
rootdir: /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend
configfile: pyproject.toml
testpaths: tests
plugins: anyio-4.9.0, asyncio-0.23.3, cov-4.1.0, xdist-3.5.0
asyncio: mode=Mode.STRICT
created: 16/16 workers
16 workers [125 items]

.....FF..F..FFFFF..F.F.......FF.FFFFFFF....F.FF.F.FFFF..FF.F.FFFF..FF..F [ 57%]
....FFFF...FFF..F.FF...F.FF.F.....FF.FF.FFF.F.FF.....                    [100%]
=================================== FAILURES ===================================
__________________ TestEntityCRUD.test_create_entity_success ___________________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d16fbd0>
test_client = <httpx.AsyncClient object at 0x10d2f5350>

    @pytest.mark.asyncio
    async def test_create_entity_success(self, test_client):
        """Test successful entity creation."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Test Entity Create Success"}
        )
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        assert response.status_code == 201
        data = response.json()
>       assert data["name"] == "Test Entity Create Success"
E       AssertionError: assert 'Test Entity ...CRUD uahdjlbn' == 'Test Entity Create Success'
E         - Test Entity Create Success
E         + Test Entity Integration CRUD uahdjlbn

tests/test_comprehensive_entities.py:25: AssertionError
----------------------------- Captured stdout call -----------------------------
Response status: 201
Response body: {"name":"Test Entity Integration CRUD uahdjlbn","id":2,"created_at":"2025-07-05T23:23:55.494010","updated_at":"2025-07-05T23:23:55.494012"}
________ TestPhase3ConnectionValidation.test_connection_creation_basic _________
[gw11] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_connection_validation.TestPhase3ConnectionValidation object at 0x109d8e110>
test_client = <httpx.AsyncClient object at 0x109e5ca10>

    @pytest.mark.asyncio
    async def test_connection_creation_basic(self, test_client):
        """Test basic connection creation with inverse."""
        # Create two test entities
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"TestEntityOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"TestEntityTwo {suffix}"}
        )
    
        if entity1_response.status_code != 200:
            print(f"Entity 1 creation failed: {entity1_response.status_code} - {entity1_response.text}")
    
        if entity2_response.status_code != 200:
            print(f"Entity 2 creation failed: {entity2_response.status_code} - {entity2_response.text}")
    
>       assert entity1_response.status_code == 201
E       assert 400 == 201
E        +  where 400 = <Response [400 Bad Request]>.status_code

tests/test_phase3_connection_validation.py:32: AssertionError
----------------------------- Captured stdout call -----------------------------
Entity 1 creation failed: 400 - {"detail":"Entity 'TestEntityOne xxzejerz' already exists"}
Entity 2 creation failed: 201 - {"name":"TestEntityTwo xxzejerz","id":1,"created_at":"2025-07-05T23:23:55.522190","updated_at":"2025-07-05T23:23:55.522192"}
________________ TestEntityCRUD.test_create_entity_with_spaces _________________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d178510>
test_client = <httpx.AsyncClient object at 0x10d376210>

    @pytest.mark.asyncio
    async def test_create_entity_with_spaces(self, test_client):
        """Test entity creation with spaces in name."""
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "New York City"}
        )
>       assert response.status_code == 201
E       assert 400 == 201
E        +  where 400 = <Response [400 Bad Request]>.status_code

tests/test_comprehensive_entities.py:37: AssertionError
_____________ TestPathfindingAlgorithm.test_direct_path_comparison _____________
[gw8] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_pathfinding.TestPathfindingAlgorithm object at 0x10d925fd0>
test_client = <httpx.AsyncClient object at 0x10d9e5550>

    @pytest.mark.asyncio
    async def test_direct_path_comparison(self, test_client):
        """Test direct connection pathfinding."""
        # Create test entities
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DirectPathOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DirectPathTwo {suffix}"}
        )
    
        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201
    
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
    
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
    
        # Create direct connection A -> B with multiplier 2.5
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.5
            }
        )
>       assert connection_response.status_code == 201
E       assert 422 == 201
E        +  where 422 = <Response [422 Unprocessable Entity]>.status_code

tests/test_phase3_pathfinding.py:46: AssertionError
____________________ TestPathfinding.test_direct_comparison ____________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0b83d0>
test_client = <httpx.AsyncClient object at 0x10b2258d0>

    @pytest.mark.asyncio
    async def test_direct_comparison(self, test_client):
        """Test direct comparison between connected entities."""
>       data = await self.create_linear_chain_setup(test_client)

tests/test_comprehensive_pathfinding.py:106: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0b83d0>
test_client = <httpx.AsyncClient object at 0x10b2258d0>

    async def create_linear_chain_setup(self, test_client):
        """Helper to create a linear chain of entities: A -> B -> C -> D."""
        # Create entities with unique names to avoid conflicts
        import string
        import random
    
        # Generate random suffix with only letters (no numbers)
        suffix = ''.join(random.choices(string.ascii_letters, k=8))
    
        entities = {}
        for name in ["A", "B", "C", "D"]:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"Chain Entity {name} {suffix}"}
            )
>           entities[name] = response.json()["id"]
E           KeyError: 'id'

tests/test_comprehensive_pathfinding.py:28: KeyError
______________ TestConnectionCRUD.test_create_connection_success _______________
[gw1] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_connections.TestConnectionCRUD object at 0x108e8e890>
test_client = <httpx.AsyncClient object at 0x109e25a10>

    @pytest.mark.asyncio
    async def test_create_connection_success(self, test_client):
        """Test successful connection creation."""
        data = await self.create_test_entities_and_unit(test_client)
    
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": data["entity1_id"],
                "to_entity_id": data["entity2_id"],
                "unit_id": data["unit_id"],
                "multiplier": 2.5
            }
        )
    
>       assert response.status_code == 201
E       assert 422 == 201
E        +  where 422 = <Response [422 Unprocessable Entity]>.status_code

tests/test_comprehensive_connections.py:62: AssertionError
_____________ TestPhase2Verification.test_entity_creation_success ______________
[gw7] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase2_verification.TestPhase2Verification object at 0x109c9f810>
test_client = <httpx.AsyncClient object at 0x109ddead0>

    @pytest.mark.asyncio
    async def test_entity_creation_success(self, test_client):
        """Test successful entity creation with unique name."""
        # Use letters-only suffix to ensure uniqueness
        suffix = generate_valid_entity_suffix()
        unique_name = f"PhaseTest{suffix}"
    
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": unique_name}
        )
    
        if response.status_code != 201:
            print(f"Error: {response.status_code} - {response.text}")
    
>       assert response.status_code == 201
E       assert 400 == 201
E        +  where 400 = <Response [400 Bad Request]>.status_code

tests/test_phase2_verification.py:37: AssertionError
----------------------------- Captured stdout call -----------------------------
Error: 400 - {"detail":"Entity 'PhaseTestdzshgroz' already exists"}
________ TestInverseConnectionCreation.test_automatic_inverse_creation _________
[gw12] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_inverse_connections.TestInverseConnectionCreation object at 0x10d63aed0>
test_client = <httpx.AsyncClient object at 0x10d7117d0>

    @pytest.mark.asyncio
    async def test_automatic_inverse_creation(self, test_client):
        """Test that creating A->B also creates B->A with 1/multiplier."""
        # Create test entities
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"InverseTestOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"InverseTestTwo {suffix}"}
        )
    
        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201
    
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
    
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
    
        # Create connection A -> B with multiplier 4.0
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 4.0
            }
        )
    
>       assert connection_response.status_code == 201
E       assert 404 == 201
E        +  where 404 = <Response [404 Not Found]>.status_code

tests/test_phase3_inverse_connections.py:47: AssertionError
------------------------------ Captured log call -------------------------------
ERROR    src.routes.connections:connections.py:39 From entity 1 not found. Available entities: []
______________ TestEntityCRUD.test_create_entity_case_insensitive ______________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d178e10>
test_client = <httpx.AsyncClient object at 0x10d3de010>

    @pytest.mark.asyncio
    async def test_create_entity_case_insensitive(self, test_client):
        """Test case-insensitive entity name uniqueness."""
        # Create first entity
        response1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "TestCase"}
        )
>       assert response1.status_code == 201
E       assert 400 == 201
E        +  where 400 = <Response [400 Bad Request]>.status_code

tests/test_comprehensive_entities.py:48: AssertionError
______________ TestPathfindingAlgorithm.test_two_hop_pathfinding _______________
[gw8] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_pathfinding.TestPathfindingAlgorithm object at 0x10d926dd0>
test_client = <httpx.AsyncClient object at 0x10da9bc10>

    @pytest.mark.asyncio
    async def test_two_hop_pathfinding(self, test_client):
        """Test pathfinding through two hops: A -> B -> C."""
        # Create test entities
        suffix = generate_valid_entity_suffix()
    
        entities = []
        for i in range(3):
            entity_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"TwoHop {chr(65+i)} {suffix}"}
            )
>           assert entity_response.status_code == 201
E           assert 400 == 201
E            +  where 400 = <Response [400 Bad Request]>.status_code

tests/test_phase3_pathfinding.py:91: AssertionError
_________________ TestPathfindingAlgorithm.test_no_path_exists _________________
[gw8] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_pathfinding.TestPathfindingAlgorithm object at 0x10d9276d0>
test_client = <httpx.AsyncClient object at 0x10daeabd0>

    @pytest.mark.asyncio
    async def test_no_path_exists(self, test_client):
        """Test pathfinding when no path exists between entities."""
        # Create isolated entities with no connections
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"IsolatedOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"IsolatedTwo {suffix}"}
        )
    
        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201
    
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
    
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
    
        # Test comparison with no connections
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity2_id}&unit={unit_id}"
        )
    
        assert compare_response.status_code == 404
        result = compare_response.json()
    
        print(f"No path result: {result}")
    
        assert "detail" in result
>       assert "no path found" in result["detail"].lower()
E       AssertionError: assert 'no path found' in 'from entity not found'
E        +  where 'from entity not found' = <built-in method lower of str object at 0x10d965200>()
E        +    where <built-in method lower of str object at 0x10d965200> = 'From entity not found'.lower

tests/test_phase3_pathfinding.py:187: AssertionError
----------------------------- Captured stdout call -----------------------------
No path result: {'detail': 'From entity not found'}
__________ TestPhase3ConnectionValidation.test_pathfinding_api_basic ___________
[gw11] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_connection_validation.TestPhase3ConnectionValidation object at 0x109d872d0>
test_client = <httpx.AsyncClient object at 0x109f40450>

    @pytest.mark.asyncio
    async def test_pathfinding_api_basic(self, test_client):
        """Test basic pathfinding/compare API."""
        # Create entities and connection first
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"PathTestOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"PathTestTwo {suffix}"}
        )
    
        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201
    
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
    
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
    
        # Test same entity comparison
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity1_id}&unit={unit_id}"
        )
    
        if compare_response.status_code != 200:
            print(f"Compare failed: {compare_response.status_code} - {compare_response.text}")
    
>       assert compare_response.status_code == 200
E       assert 404 == 200
E        +  where 404 = <Response [404 Not Found]>.status_code

tests/test_phase3_connection_validation.py:113: AssertionError
----------------------------- Captured stdout call -----------------------------
Compare failed: 404 - {"detail":"From entity not found"}
_______________ TestEntityIntegration.test_entity_crud_workflow ________________
[gw5] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_integration.TestEntityIntegration object at 0x10d65a650>
test_client = <httpx.AsyncClient object at 0x10d799910>

    async def test_entity_crud_workflow(self, test_client):
        """Test complete entity CRUD workflow."""
        # Create entity
        create_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Test Entity Integration CRUD {generate_valid_entity_suffix()}"}
        )
        print(f"Create response status: {create_response.status_code}")
        print(f"Create response text: {create_response.text}")
        assert create_response.status_code == 201
        entity_data = create_response.json()
        entity_id = entity_data["id"]
        original_name = entity_data["name"]
    
        # Read entity
        get_response = await test_client.get(f"/api/v1/entities/{entity_id}")
        assert get_response.status_code == 200
        assert get_response.json()["name"] == original_name
    
        # Update entity
>       update_response = await test_client.put(
            f"/api/v1/entities/{entity_id}",
            json={"name": f"Updated Entity {generate_valid_entity_suffix()}"}
        )

tests/test_integration.py:33: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1914: in put
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:94: in update_entity
    await db.commit()
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:1011: in commit
    await greenlet_spawn(self.sync_session.commit)
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:1969: in commit
    trans.commit(_to_root=True)
<string>:2: in commit
    ???
venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:1256: in commit
    self._prepare_impl()
<string>:2: in _prepare_impl
    ???
venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:1231: in _prepare_impl
    self.session.flush()
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:4312: in flush
    self._flush(objects)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:4447: in _flush
    with util.safe_reraise():
venv/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py:146: in __exit__
    raise exc_value.with_traceback(exc_tb)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:4408: in _flush
    flush_context.execute()
venv/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
venv/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
venv/lib/python3.11/site-packages/sqlalchemy/orm/persistence.py:85: in save_obj
    _emit_update_statements(
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

base_mapper = <Mapper at 0x10ce78cd0; Entity>
uowtransaction = <sqlalchemy.orm.unitofwork.UOWTransaction object at 0x10d7e7150>
mapper = <Mapper at 0x10ce78cd0; Entity>
table = Table('entities', MetaData(), Column('id', Integer(), table=<entities>, primary_key=True, nullable=False), Column('nam...tetime.utcnow at 0x10d426ac0>), default=CallableColumnDefault(<function datetime.utcnow at 0x10d426a20>)), schema=None)
update = <generator object _collect_update_commands at 0x10d5baf20>

    def _emit_update_statements(
        base_mapper,
        uowtransaction,
        mapper,
        table,
        update,
        *,
        bookkeeping=True,
        use_orm_update_stmt=None,
        enable_check_rowcount=True,
    ):
        """Emit UPDATE statements corresponding to value lists collected
        by _collect_update_commands()."""
    
        needs_version_id = (
            mapper.version_id_col is not None
            and mapper.version_id_col in mapper._cols_by_table[table]
        )
    
        execution_options = {"compiled_cache": base_mapper._compiled_cache}
    
        def update_stmt(existing_stmt=None):
            clauses = BooleanClauseList._construct_raw(operators.and_)
    
            for col in mapper._pks_by_table[table]:
                clauses._append_inplace(
                    col == sql.bindparam(col._label, type_=col.type)
                )
    
            if needs_version_id:
                clauses._append_inplace(
                    mapper.version_id_col
                    == sql.bindparam(
                        mapper.version_id_col._label,
                        type_=mapper.version_id_col.type,
                    )
                )
    
            if existing_stmt is not None:
                stmt = existing_stmt.where(clauses)
            else:
                stmt = table.update().where(clauses)
            return stmt
    
        if use_orm_update_stmt is not None:
            cached_stmt = update_stmt(use_orm_update_stmt)
    
        else:
            cached_stmt = base_mapper._memo(("update", table), update_stmt)
    
        for (
            (connection, paramkeys, hasvalue, has_all_defaults, has_all_pks),
            records,
        ) in groupby(
            update,
            lambda rec: (
                rec[4],  # connection
                set(rec[2]),  # set of parameter keys
                bool(rec[5]),  # whether or not we have "value" parameters
                rec[6],  # has_all_defaults
                rec[7],  # has all pks
            ),
        ):
            rows = 0
            records = list(records)
    
            statement = cached_stmt
    
            if use_orm_update_stmt is not None:
                statement = statement._annotate(
                    {
                        "_emit_update_table": table,
                        "_emit_update_mapper": mapper,
                    }
                )
    
            return_defaults = False
    
            if not has_all_pks:
                statement = statement.return_defaults(*mapper._pks_by_table[table])
                return_defaults = True
    
            if (
                bookkeeping
                and not has_all_defaults
                and mapper.base_mapper.eager_defaults is True
                # change as of #8889 - if RETURNING is not going to be used anyway,
                # (applies to MySQL, MariaDB which lack UPDATE RETURNING) ensure
                # we can do an executemany UPDATE which is more efficient
                and table.implicit_returning
                and connection.dialect.update_returning
            ):
                statement = statement.return_defaults(
                    *mapper._server_onupdate_default_cols[table]
                )
                return_defaults = True
    
            if mapper._version_id_has_server_side_value:
                statement = statement.return_defaults(mapper.version_id_col)
                return_defaults = True
    
            assert_singlerow = connection.dialect.supports_sane_rowcount
    
            assert_multirow = (
                assert_singlerow
                and connection.dialect.supports_sane_multi_rowcount
            )
    
            # change as of #8889 - if RETURNING is not going to be used anyway,
            # (applies to MySQL, MariaDB which lack UPDATE RETURNING) ensure
            # we can do an executemany UPDATE which is more efficient
            allow_executemany = not return_defaults and not needs_version_id
    
            if hasvalue:
                for (
                    state,
                    state_dict,
                    params,
                    mapper,
                    connection,
                    value_params,
                    has_all_defaults,
                    has_all_pks,
                ) in records:
                    c = connection.execute(
                        statement.values(value_params),
                        params,
                        execution_options=execution_options,
                    )
                    if bookkeeping:
                        _postfetch(
                            mapper,
                            uowtransaction,
                            table,
                            state,
                            state_dict,
                            c,
                            c.context.compiled_parameters[0],
                            value_params,
                            True,
                            c.returned_defaults,
                        )
                    rows += c.rowcount
                    check_rowcount = enable_check_rowcount and assert_singlerow
            else:
                if not allow_executemany:
                    check_rowcount = enable_check_rowcount and assert_singlerow
                    for (
                        state,
                        state_dict,
                        params,
                        mapper,
                        connection,
                        value_params,
                        has_all_defaults,
                        has_all_pks,
                    ) in records:
                        c = connection.execute(
                            statement, params, execution_options=execution_options
                        )
    
                        # TODO: why with bookkeeping=False?
                        if bookkeeping:
                            _postfetch(
                                mapper,
                                uowtransaction,
                                table,
                                state,
                                state_dict,
                                c,
                                c.context.compiled_parameters[0],
                                value_params,
                                True,
                                c.returned_defaults,
                            )
                        rows += c.rowcount
                else:
                    multiparams = [rec[2] for rec in records]
    
                    check_rowcount = enable_check_rowcount and (
                        assert_multirow
                        or (assert_singlerow and len(multiparams) == 1)
                    )
    
                    c = connection.execute(
                        statement, multiparams, execution_options=execution_options
                    )
    
                    rows += c.rowcount
    
                    for (
                        state,
                        state_dict,
                        params,
                        mapper,
                        connection,
                        value_params,
                        has_all_defaults,
                        has_all_pks,
                    ) in records:
                        if bookkeeping:
                            _postfetch(
                                mapper,
                                uowtransaction,
                                table,
                                state,
                                state_dict,
                                c,
                                c.context.compiled_parameters[0],
                                value_params,
                                True,
                                c.returned_defaults
                                if not c.context.executemany
                                else None,
                            )
    
            if check_rowcount:
                if rows != len(records):
>                   raise orm_exc.StaleDataError(
                        "UPDATE statement on table '%s' expected to "
                        "update %d row(s); %d were matched."
                        % (table.description, len(records), rows)
                    )
E                   sqlalchemy.orm.exc.StaleDataError: UPDATE statement on table 'entities' expected to update 1 row(s); 0 were matched.

venv/lib/python3.11/site-packages/sqlalchemy/orm/persistence.py:944: StaleDataError
----------------------------- Captured stdout call -----------------------------
Create response status: 201
Create response text: {"name":"Test Entity Integration CRUD uahdjlbn","id":2,"created_at":"2025-07-05T23:23:55.494010","updated_at":"2025-07-05T23:23:55.494012"}
___________________ TestPathfinding.test_two_hop_comparison ____________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0bad90>
test_client = <httpx.AsyncClient object at 0x10b35e290>

    @pytest.mark.asyncio
    async def test_two_hop_comparison(self, test_client):
        """Test comparison through one intermediate entity."""
>       data = await self.create_linear_chain_setup(test_client)

tests/test_comprehensive_pathfinding.py:128: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_comprehensive_pathfinding.py:24: in create_linear_chain_setup
    response = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10b360e90>
instance = <src.models.Entity object at 0x10b3612d0>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10b3612d0>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
______________ TestEdgeCases.test_concurrent_connection_creation _______________
[gw3] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_edge_cases.TestEdgeCases object at 0x109415ad0>
test_client = <httpx.AsyncClient object at 0x1095abad0>

    @pytest.mark.asyncio
    async def test_concurrent_connection_creation(self, test_client):
        """Test race condition when creating duplicate connections concurrently."""
        # Create entities
>       entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Concurrent Ent A"}
        )

tests/test_comprehensive_edge_cases.py:47: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x1095a0c90>
instance = <src.models.Entity object at 0x1095a0910>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x1095a0910>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
____________ TestConnectionCRUD.test_create_connection_auto_inverse ____________
[gw1] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_connections.TestConnectionCRUD object at 0x109c5b850>
test_client = <httpx.AsyncClient object at 0x109eb7510>

    @pytest.mark.asyncio
    async def test_create_connection_auto_inverse(self, test_client):
        """Test automatic inverse connection creation."""
>       data = await self.create_test_entities_and_unit(test_client)

tests/test_comprehensive_connections.py:74: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_comprehensive_connections.py:27: in create_test_entities_and_unit
    entity1 = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x109ee0910>
instance = <src.models.Entity object at 0x109ee0b90>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x109ee0b90>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
____________ TestPhase2Verification.test_entity_duplicate_detection ____________
[gw7] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase2_verification.TestPhase2Verification object at 0x109c98150>
test_client = <httpx.AsyncClient object at 0x109def090>

    @pytest.mark.asyncio
    async def test_entity_duplicate_detection(self, test_client):
        """Test that duplicate entity detection works."""
        suffix = generate_valid_entity_suffix()
        unique_name = f"DuplicateTest{suffix}"
    
        # Create first entity
>       response1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": unique_name}
        )

tests/test_phase2_verification.py:50: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x109e182d0>
instance = <src.models.Entity object at 0x109e18410>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x109e18410>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
________ TestInverseConnectionCreation.test_decimal_rounding_in_inverse ________
[gw12] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_inverse_connections.TestInverseConnectionCreation object at 0x10d63b650>
test_client = <httpx.AsyncClient object at 0x10d7b0450>

    @pytest.mark.asyncio
    async def test_decimal_rounding_in_inverse(self, test_client):
        """Test decimal rounding behavior in inverse connections."""
        # Create test entities
        suffix = generate_valid_entity_suffix()
    
>       entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"RoundTestOne {suffix}"}
        )

tests/test_phase3_inverse_connections.py:84: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10d773fd0>
instance = <src.models.Entity object at 0x10d7b2310>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10d7b2310>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_________________ TestEntityCRUD.test_create_entity_max_length _________________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d17b250>
test_client = <httpx.AsyncClient object at 0x10d469b50>

    @pytest.mark.asyncio
    async def test_create_entity_max_length(self, test_client):
        """Test entity creation with maximum length name."""
        # Max length is 100 characters
        long_name = "A" * 100
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": long_name}
        )
        assert response.status_code == 201
>       assert response.json()["name"] == long_name
E       AssertionError: assert 'SameEntityxpuccesu' == 'AAAAAAAAAAAA...AAAAAAAAAAAAA'
E         - AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
E         + SameEntityxpuccesu

tests/test_comprehensive_entities.py:98: AssertionError
____ TestConnectionRegression.test_no_greenlet_error_on_connection_creation ____
[gw6] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_cursor object at 0x10b03d9c0>
operation = 'INSERT INTO entities (name, created_at, updated_at) VALUES ($1::VARCHAR, $2::TIMESTAMP WITHOUT TIME ZONE, $3::TIMESTAMP WITHOUT TIME ZONE) RETURNING entities.id'
parameters = ('Greenlet Test Entity A ECBFFEEA', datetime.datetime(2025, 7, 5, 23, 23, 55, 498771), datetime.datetime(2025, 7, 5, 23, 23, 55, 498774))

    async def _prepare_and_execute(self, operation, parameters):
        adapt_connection = self._adapt_connection
    
        async with adapt_connection._execute_mutex:
            if not adapt_connection._started:
                await adapt_connection._start_transaction()
    
            if parameters is None:
                parameters = ()
    
            try:
                prepared_stmt, attributes = await adapt_connection._prepare(
                    operation, self._invalidate_schema_cache_asof
                )
    
                if attributes:
                    self.description = [
                        (
                            attr.name,
                            attr.type.oid,
                            None,
                            None,
                            None,
                            None,
                            None,
                        )
                        for attr in attributes
                    ]
                else:
                    self.description = None
    
                if self.server_side:
                    self._cursor = await prepared_stmt.cursor(*parameters)
                    self.rowcount = -1
                else:
>                   self._rows = await prepared_stmt.fetch(*parameters)

venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:546: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/asyncpg/prepared_stmt.py:176: in fetch
    data = await self.__bind_execute(args, 0, timeout)
venv/lib/python3.11/site-packages/asyncpg/prepared_stmt.py:241: in __bind_execute
    data, status, _ = await self.__do_execute(
venv/lib/python3.11/site-packages/asyncpg/prepared_stmt.py:230: in __do_execute
    return await executor(protocol)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

>   ???
E   asyncpg.exceptions.DeadlockDetectedError: deadlock detected
E   DETAIL:  Process 54639 waits for ShareLock on transaction 8383; blocked by process 54637.
E   Process 54637 waits for ShareRowExclusiveLock on relation 18854 of database 16521; blocked by process 54639.
E   HINT:  See server log for query details.

asyncpg/protocol/protocol.pyx:207: DeadlockDetectedError

The above exception was the direct cause of the following exception:

self = <sqlalchemy.engine.base.Connection object at 0x10b03ab90>
dialect = <sqlalchemy.dialects.postgresql.asyncpg.PGDialect_asyncpg object at 0x10a7922d0>
context = <sqlalchemy.dialects.postgresql.asyncpg.PGExecutionContext_asyncpg object at 0x10b03bfd0>
statement = <sqlalchemy.dialects.postgresql.asyncpg.PGCompiler_asyncpg object at 0x10a06c150>
parameters = [('Greenlet Test Entity A ECBFFEEA', datetime.datetime(2025, 7, 5, 23, 23, 55, 498771), datetime.datetime(2025, 7, 5, 23, 23, 55, 498774))]

    def _exec_single_context(
        self,
        dialect: Dialect,
        context: ExecutionContext,
        statement: Union[str, Compiled],
        parameters: Optional[_AnyMultiExecuteParams],
    ) -> CursorResult[Any]:
        """continue the _execute_context() method for a single DBAPI
        cursor.execute() or cursor.executemany() call.
    
        """
        if dialect.bind_typing is BindTyping.SETINPUTSIZES:
            generic_setinputsizes = context._prepare_set_input_sizes()
    
            if generic_setinputsizes:
                try:
                    dialect.do_set_input_sizes(
                        context.cursor, generic_setinputsizes, context
                    )
                except BaseException as e:
                    self._handle_dbapi_exception(
                        e, str(statement), parameters, None, context
                    )
    
        cursor, str_statement, parameters = (
            context.cursor,
            context.statement,
            context.parameters,
        )
    
        effective_parameters: Optional[_AnyExecuteParams]
    
        if not context.executemany:
            effective_parameters = parameters[0]
        else:
            effective_parameters = parameters
    
        if self._has_events or self.engine._has_events:
            for fn in self.dispatch.before_cursor_execute:
                str_statement, effective_parameters = fn(
                    self,
                    cursor,
                    str_statement,
                    effective_parameters,
                    context,
                    context.executemany,
                )
    
        if self._echo:
            self._log_info(str_statement)
    
            stats = context._get_cache_stats()
    
            if not self.engine.hide_parameters:
                self._log_info(
                    "[%s] %r",
                    stats,
                    sql_util._repr_params(
                        effective_parameters,
                        batches=10,
                        ismulti=context.executemany,
                    ),
                )
            else:
                self._log_info(
                    "[%s] [SQL parameters hidden due to hide_parameters=True]",
                    stats,
                )
    
        evt_handled: bool = False
        try:
            if context.execute_style is ExecuteStyle.EXECUTEMANY:
                effective_parameters = cast(
                    "_CoreMultiExecuteParams", effective_parameters
                )
                if self.dialect._has_events:
                    for fn in self.dialect.dispatch.do_executemany:
                        if fn(
                            cursor,
                            str_statement,
                            effective_parameters,
                            context,
                        ):
                            evt_handled = True
                            break
                if not evt_handled:
                    self.dialect.do_executemany(
                        cursor,
                        str_statement,
                        effective_parameters,
                        context,
                    )
            elif not effective_parameters and context.no_parameters:
                if self.dialect._has_events:
                    for fn in self.dialect.dispatch.do_execute_no_params:
                        if fn(cursor, str_statement, context):
                            evt_handled = True
                            break
                if not evt_handled:
                    self.dialect.do_execute_no_params(
                        cursor, str_statement, context
                    )
            else:
                effective_parameters = cast(
                    "_CoreSingleExecuteParams", effective_parameters
                )
                if self.dialect._has_events:
                    for fn in self.dialect.dispatch.do_execute:
                        if fn(
                            cursor,
                            str_statement,
                            effective_parameters,
                            context,
                        ):
                            evt_handled = True
                            break
                if not evt_handled:
>                   self.dialect.do_execute(
                        cursor, str_statement, effective_parameters, context
                    )

venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py:1969: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py:922: in do_execute
    cursor.execute(statement, parameters)
venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:130: in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:195: in greenlet_spawn
    value = await result
venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:509: in _handle_exception
    self._adapt_connection._handle_exception(error)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <AdaptedConnection <asyncpg.connection.Connection object at 0x10afe36a0>>
error = DeadlockDetectedError('deadlock detected')

    def _handle_exception(self, error):
        if self._connection.is_closed():
            self._transaction = None
            self._started = False
    
        if not isinstance(error, AsyncAdapt_asyncpg_dbapi.Error):
            exception_mapping = self.dbapi._asyncpg_error_translate
    
            for super_ in type(error).__mro__:
                if super_ in exception_mapping:
                    translated_error = exception_mapping[super_](
                        "%s: %s" % (type(error), error)
                    )
                    translated_error.pgcode = (
                        translated_error.sqlstate
                    ) = getattr(error, "sqlstate", None)
>                   raise translated_error from error
E                   sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.Error: <class 'asyncpg.exceptions.DeadlockDetectedError'>: deadlock detected
E                   DETAIL:  Process 54639 waits for ShareLock on transaction 8383; blocked by process 54637.
E                   Process 54637 waits for ShareRowExclusiveLock on relation 18854 of database 16521; blocked by process 54639.
E                   HINT:  See server log for query details.

venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:797: Error

The above exception was the direct cause of the following exception:

self = <backend.tests.test_connection_regression.TestConnectionRegression object at 0x10aec91d0>
test_client = <httpx.AsyncClient object at 0x10b015a90>

    @pytest.mark.asyncio
    async def test_no_greenlet_error_on_connection_creation(self, test_client):
        """Test that connection creation doesn't trigger greenlet errors."""
        # Create test entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
>       entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Greenlet Test Entity A {suffix}"}
        )

tests/test_connection_regression.py:24: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:27: in create_entity
    await db.commit()
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:1011: in commit
    await greenlet_spawn(self.sync_session.commit)
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:1969: in commit
    trans.commit(_to_root=True)
<string>:2: in commit
    ???
venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:1256: in commit
    self._prepare_impl()
<string>:2: in _prepare_impl
    ???
venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:1231: in _prepare_impl
    self.session.flush()
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:4312: in flush
    self._flush(objects)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:4447: in _flush
    with util.safe_reraise():
venv/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py:146: in __exit__
    raise exc_value.with_traceback(exc_tb)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:4408: in _flush
    flush_context.execute()
venv/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
venv/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
venv/lib/python3.11/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
venv/lib/python3.11/site-packages/sqlalchemy/orm/persistence.py:1227: in _emit_insert_statements
    result = connection.execute(
venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py:1416: in execute
    return meth(
venv/lib/python3.11/site-packages/sqlalchemy/sql/elements.py:517: in _execute_on_connection
    return connection._execute_clauseelement(
venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py:1639: in _execute_clauseelement
    ret = self._execute_context(
venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py:1848: in _execute_context
    return self._exec_single_context(
venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py:1988: in _exec_single_context
    self._handle_dbapi_exception(
venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py:2344: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py:1969: in _exec_single_context
    self.dialect.do_execute(
venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py:922: in do_execute
    cursor.execute(statement, parameters)
venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:130: in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:195: in greenlet_spawn
    value = await result
venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:509: in _handle_exception
    self._adapt_connection._handle_exception(error)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <AdaptedConnection <asyncpg.connection.Connection object at 0x10afe36a0>>
error = DeadlockDetectedError('deadlock detected')

    def _handle_exception(self, error):
        if self._connection.is_closed():
            self._transaction = None
            self._started = False
    
        if not isinstance(error, AsyncAdapt_asyncpg_dbapi.Error):
            exception_mapping = self.dbapi._asyncpg_error_translate
    
            for super_ in type(error).__mro__:
                if super_ in exception_mapping:
                    translated_error = exception_mapping[super_](
                        "%s: %s" % (type(error), error)
                    )
                    translated_error.pgcode = (
                        translated_error.sqlstate
                    ) = getattr(error, "sqlstate", None)
>                   raise translated_error from error
E                   sqlalchemy.exc.DBAPIError: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.DeadlockDetectedError'>: deadlock detected
E                   DETAIL:  Process 54639 waits for ShareLock on transaction 8383; blocked by process 54637.
E                   Process 54637 waits for ShareRowExclusiveLock on relation 18854 of database 16521; blocked by process 54639.
E                   HINT:  See server log for query details.
E                   [SQL: INSERT INTO entities (name, created_at, updated_at) VALUES ($1::VARCHAR, $2::TIMESTAMP WITHOUT TIME ZONE, $3::TIMESTAMP WITHOUT TIME ZONE) RETURNING entities.id]
E                   [parameters: ('Greenlet Test Entity A ECBFFEEA', datetime.datetime(2025, 7, 5, 23, 23, 55, 498771), datetime.datetime(2025, 7, 5, 23, 23, 55, 498774))]
E                   (Background on this error at: https://sqlalche.me/e/20/dbapi)

venv/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:797: DBAPIError
_____________ TestPathfindingAlgorithm.test_same_entity_comparison _____________
[gw8] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_pathfinding.TestPathfindingAlgorithm object at 0x10d928050>
test_client = <httpx.AsyncClient object at 0x10db4ff10>

    @pytest.mark.asyncio
    async def test_same_entity_comparison(self, test_client):
        """Test comparison of entity to itself."""
        # Create test entity
        suffix = generate_valid_entity_suffix()
    
>       entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"SameEntity{suffix}"}
        )

tests/test_phase3_pathfinding.py:195: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10db291d0>
instance = <src.models.Entity object at 0x10db29450>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10db29450>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
__________ TestEntityBasicWorkflow.test_entity_creation_and_retrieval __________
[gw2] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_integration_simple.TestEntityBasicWorkflow object at 0x10bd62a50>
test_client = <httpx.AsyncClient object at 0x10bf7fe50>

    @pytest.mark.asyncio
    async def test_entity_creation_and_retrieval(self, test_client):
        """Test creating and retrieving an entity."""
        # Create entity
        create_data = {"name": "Test Entity Basic Workflow"}
>       create_response = await test_client.post("/api/v1/entities/", json=create_data)

tests/test_integration_simple.py:88: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10bf85bd0>
instance = <src.models.Entity object at 0x10bf847d0>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10bf847d0>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
____ TestInverseConnectionCreation.test_connection_deletion_removes_inverse ____
[gw12] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_inverse_connections.TestInverseConnectionCreation object at 0x10d64c050>
test_client = <httpx.AsyncClient object at 0x10dd566d0>

    @pytest.mark.asyncio
    async def test_connection_deletion_removes_inverse(self, test_client):
        """Test that deleting a connection also removes its inverse."""
        # Create test entities
        suffix = generate_valid_entity_suffix()
    
>       entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DeleteTestOne {suffix}"}
        )

tests/test_phase3_inverse_connections.py:146: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10dbcb690>
instance = <src.models.Entity object at 0x10dd55090>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10dd55090>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_______ TestConnectionIntegration.test_connection_with_inverse_creation ________
[gw5] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_integration.TestConnectionIntegration object at 0x10d65b350>
test_client = <httpx.AsyncClient object at 0x10dfb7350>

    async def test_connection_with_inverse_creation(self, test_client):
        """Test connection creation with automatic inverse."""
        # Create test entities with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity One {suffix}"}
        )
        assert entity1_response.status_code == 201
>       entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity Two {suffix}"}
        )

tests/test_integration.py:74: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10dff0490>
instance = <src.models.Entity object at 0x10dff3050>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10dff3050>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
___________________ TestEdgeCases.test_delete_entity_in_use ____________________
[gw3] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_edge_cases.TestEdgeCases object at 0x109414dd0>
test_client = <httpx.AsyncClient object at 0x109c72890>

    @pytest.mark.asyncio
    async def test_delete_entity_in_use(self, test_client):
        """Test deleting entity that's part of active connections."""
        # Create entities
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Delete Test Source"}
        )
>       entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Delete Test Target"}
        )

tests/test_comprehensive_edge_cases.py:101: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x109c29050>
instance = <src.models.Entity object at 0x109c28e10>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x109c28e10>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
__________________ TestPathfinding.test_three_hop_comparison ___________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0bb690>
test_client = <httpx.AsyncClient object at 0x10b8c8b50>

    @pytest.mark.asyncio
    async def test_three_hop_comparison(self, test_client):
        """Test comparison through two intermediate entities."""
>       data = await self.create_linear_chain_setup(test_client)

tests/test_comprehensive_pathfinding.py:148: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_comprehensive_pathfinding.py:24: in create_linear_chain_setup
    response = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10b8b4d90>
instance = <src.models.Entity object at 0x10b8b5150>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10b8b5150>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_________ TestConnectionCRUD.test_create_connection_decimal_precision __________
[gw1] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_connections.TestConnectionCRUD object at 0x109c702d0>
test_client = <httpx.AsyncClient object at 0x10a4de310>

    @pytest.mark.asyncio
    async def test_create_connection_decimal_precision(self, test_client):
        """Test connection creation with decimal precision (1 decimal place)."""
>       data = await self.create_test_entities_and_unit(test_client)

tests/test_comprehensive_connections.py:113: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_comprehensive_connections.py:31: in create_test_entities_and_unit
    entity2 = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10a4c9b10>
instance = <src.models.Entity object at 0x10a4ca0d0>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10a4ca0d0>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
____________ TestConnectionRegression.test_cors_headers_on_success _____________
[gw6] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_connection_regression.TestConnectionRegression object at 0x10aec9a50>
test_client = <httpx.AsyncClient object at 0x10bd92d10>

    @pytest.mark.asyncio
    async def test_cors_headers_on_success(self, test_client):
        """Test CORS headers are present on successful requests."""
        # Create test entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
>       entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"CORS Test Entity A {suffix}"}
        )

tests/test_connection_regression.py:72: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10c055450>
instance = <src.models.Entity object at 0x10c0556d0>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10c0556d0>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_______________ TestComparisonIntegration.test_direct_comparison _______________
[gw5] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_integration.TestComparisonIntegration object at 0x10d66c050>
test_client = <httpx.AsyncClient object at 0x10daf6c50>

    async def test_direct_comparison(self, test_client):
        """Test direct entity comparison."""
        # Create test entities with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Small {suffix}"}
        )
        assert entity1_response.status_code == 201
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Large {suffix}"}
        )
        assert entity2_response.status_code == 201
    
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
    
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
    
        # Create connection
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 0.5
            }
        )
    
        # Compare entities
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity2_id}&unit={unit_id}"
        )
        assert compare_response.status_code == 200
        result = compare_response.json()
    
        assert result["from_entity"]["id"] == entity1_id
        assert result["to_entity"]["id"] == entity2_id
>       assert float(result["multiplier"]) == 0.5
E       AssertionError: assert 1.0 == 0.5
E        +  where 1.0 = float('1.0')

tests/test_integration.py:178: AssertionError
_____________ TestPathfindingAlgorithm.test_different_unit_no_path _____________
[gw8] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_phase3_pathfinding.TestPathfindingAlgorithm object at 0x10d928950>
test_client = <httpx.AsyncClient object at 0x10e5fb690>

    @pytest.mark.asyncio
    async def test_different_unit_no_path(self, test_client):
        """Test that connections with different units don't create paths."""
        # Create test entities
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DiffUnitOne {suffix}"}
        )
>       entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"DiffUnitTwo {suffix}"}
        )

tests/test_phase3_pathfinding.py:231: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10e051b10>
instance = <src.models.Entity object at 0x10e051d90>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10e051d90>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
________________ TestEntityCRUD.test_get_all_entities_with_data ________________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d178ed0>
test_client = <httpx.AsyncClient object at 0x10d285910>

    @pytest.mark.asyncio
    async def test_get_all_entities_with_data(self, test_client):
        """Test getting all entities."""
        # Create multiple entities
        entities = ["EntityOne", "EntityTwo", "EntityThree"]
        created_ids = []
    
        for name in entities:
>           response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )

tests/test_comprehensive_entities.py:144: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10d469290>
instance = <src.models.Entity object at 0x10d468d10>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10d468d10>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_________________ TestEdgeCases.test_extreme_multiplier_values _________________
[gw3] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_edge_cases.TestEdgeCases object at 0x109408c90>
test_client = <httpx.AsyncClient object at 0x109726050>

    @pytest.mark.asyncio
    async def test_extreme_multiplier_values(self, test_client):
        """Test connections with extreme multiplier values."""
        from .conftest import generate_valid_entity_suffix
    
        suffix = generate_valid_entity_suffix()
    
        # Create entities
>       entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Extreme Val Ent A {suffix}"}
        )

tests/test_comprehensive_edge_cases.py:173: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x109cc3750>
instance = <src.models.Entity object at 0x109cc3650>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x109cc3650>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
___________________ TestPathfinding.test_reverse_comparison ____________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0c8090>
test_client = <httpx.AsyncClient object at 0x10badfdd0>

    @pytest.mark.asyncio
    async def test_reverse_comparison(self, test_client):
        """Test comparison in reverse direction."""
>       data = await self.create_linear_chain_setup(test_client)

tests/test_comprehensive_pathfinding.py:168: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_comprehensive_pathfinding.py:24: in create_linear_chain_setup
    response = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10be61b50>
instance = <src.models.Entity object at 0x10be60350>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10be60350>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
____________ TestConnectionCRUD.test_create_connection_same_entity _____________
[gw1] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_connections.TestConnectionCRUD object at 0x109c70d50>
test_client = <httpx.AsyncClient object at 0x10a5ae750>

    @pytest.mark.asyncio
    async def test_create_connection_same_entity(self, test_client):
        """Test creating connection from entity to itself."""
>       data = await self.create_test_entities_and_unit(test_client)

tests/test_comprehensive_connections.py:161: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_comprehensive_connections.py:27: in create_test_entities_and_unit
    entity1 = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10a95fc50>
instance = <src.models.Entity object at 0x10a95cf50>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10a95cf50>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_____________________ TestEntityCRUD.test_get_entity_by_id _____________________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d1802d0>
test_client = <httpx.AsyncClient object at 0x10da8b7d0>

    @pytest.mark.asyncio
    async def test_get_entity_by_id(self, test_client):
        """Test getting a specific entity by ID."""
        # Create entity
        create_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Specific Entity"}
        )
        entity_id = create_response.json()["id"]
    
        # Get by ID
        response = await test_client.get(f"/api/v1/entities/{entity_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == entity_id
>       assert data["name"] == "Specific Entity"
E       AssertionError: assert 'Lazy Load Te...ty B BDFAEFCA' == 'Specific Entity'
E         - Specific Entity
E         + Lazy Load Test Entity B BDFAEFCA

tests/test_comprehensive_entities.py:176: AssertionError
__________ TestConnectionRegression.test_no_lazy_loading_after_commit __________
[gw6] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_connection_regression.TestConnectionRegression object at 0x10aecac90>
test_client = <httpx.AsyncClient object at 0x10b165410>

    @pytest.mark.asyncio
    async def test_no_lazy_loading_after_commit(self, test_client):
        """Test that objects don't trigger lazy loading after commit."""
        # Create test entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Lazy Load Test Entity A {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Lazy Load Test Entity B {suffix}"}
        )
    
        # Get a unit
        units = await test_client.get("/api/v1/units/")
        unit = units.json()[0]
    
        # Create connection
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1.json()["id"],
                "to_entity_id": entity2.json()["id"],
                "unit_id": unit["id"],
                "multiplier": 7.7
            }
        )
    
>       assert response.status_code == 201
E       assert 422 == 201
E        +  where 422 = <Response [422 Unprocessable Entity]>.status_code

tests/test_connection_regression.py:160: AssertionError
_____________ TestComparisonIntegration.test_transitive_comparison _____________
[gw5] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_integration.TestComparisonIntegration object at 0x10d66c910>
test_client = <httpx.AsyncClient object at 0x10d741a50>

    async def test_transitive_comparison(self, test_client):
        """Test transitive entity comparison through intermediate entity."""
        # Create test entities: A -> B -> C with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
>       entity_a_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity A {suffix}"}
        )

tests/test_integration.py:188: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10d6da450>
instance = <src.models.Entity object at 0x10d6da350>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10d6da350>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
____________ TestEdgeCases.test_special_characters_in_entity_names _____________
[gw3] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_edge_cases.TestEdgeCases object at 0x10942a150>
test_client = <httpx.AsyncClient object at 0x109a8bcd0>

    @pytest.mark.asyncio
    async def test_special_characters_in_entity_names(self, test_client):
        """Test entity names with various special characters and edge cases."""
        import time
        # Create a letters-only base suffix to avoid validation issues
        timestamp = str(int(time.time()))[-6:]  # Get last 6 digits of timestamp
        base_suffix = ''.join([chr(ord('a') + int(c)) for c in timestamp])
    
        test_cases = [
            ("Entity123", 422),  # Numbers not allowed
            ("Entity 123", 422),  # Numbers with spaces not allowed
            (f"Valid Entity {base_suffix}a", 201),  # Letters with spaces allowed
            (f"ENTITY {base_suffix}b", 201),  # All caps
            (f"entity {base_suffix}c", 201),  # All lowercase
            (f"EnTiTy {base_suffix}d", 201),  # Mixed case
            (f"A {base_suffix}e", 201),  # Single character
            ("1", 422),  # Single digit not allowed
            ("Entity_123", 422),  # Underscore not allowed
            ("Entity-123", 422),  # Hyphen not allowed
            ("Entity.123", 422),  # Period not allowed
            ("Entity@123", 422),  # @ symbol not allowed
            ("Entity#123", 422),  # # symbol not allowed
            ("Entity$123", 422),  # $ symbol not allowed
            ("Entity%123", 422),  # % symbol not allowed
            ("Entity&123", 422),  # & symbol not allowed
            ("Entity*123", 422),  # * symbol not allowed
            ("Entity+123", 422),  # + symbol not allowed
            ("Entity=123", 422),  # = symbol not allowed
            ("Entity!123", 422),  # ! symbol not allowed
            ("Entity?123", 422),  # ? symbol not allowed
            ("Entity/123", 422),  # / symbol not allowed
            ("Entity\\123", 422),  # \ symbol not allowed
            ("Entity'123", 422),  # ' symbol not allowed
            ('Entity"123', 422),  # " symbol not allowed
            ("Entity(123)", 422),  # Parentheses not allowed
            ("Entity[123]", 422),  # Brackets not allowed
            ("Entity{123}", 422),  # Braces not allowed
            ("Entity<123>", 422),  # Angle brackets not allowed
            ("Entity|123", 422),  # Pipe not allowed
            ("Entity~123", 422),  # Tilde not allowed
            ("Entity`123", 422),  # Backtick not allowed
            ("Entity^123", 422),  # Caret not allowed
            ("Entity;123", 422),  # Semicolon not allowed
            ("Entity:123", 422),  # Colon not allowed
            ("Entity,123", 422),  # Comma not allowed
            (f"   Leading Spaces {base_suffix}h", 201),  # Leading spaces (stripped and allowed)
            (f"Trailing Spaces   {base_suffix}i", 201),  # Trailing spaces (stripped and allowed)
            (f"Multiple   Spaces {base_suffix}f", 201),  # Multiple spaces in middle (might be allowed)
            ("", 422),  # Empty string
            ("   ", 422),  # Only spaces
            ("\t\n", 422),  # Only whitespace
            ("A" * 91 + base_suffix + "g", 201),  # Max length (91 + 6 + 1 + 1 = 99)
            ("A" * 101, 422),  # Over max length
        ]
    
        for name, expected_status in test_cases:
>           response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )

tests/test_comprehensive_edge_cases.py:280: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x109f34c90>
instance = <src.models.Entity object at 0x109a89090>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x109a89090>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
______________ TestComparisonIntegration.test_no_path_comparison _______________
[gw5] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_integration.TestComparisonIntegration object at 0x10d66d250>
test_client = <httpx.AsyncClient object at 0x10d8fb650>

    async def test_no_path_comparison(self, test_client):
        """Test comparison when no path exists between entities."""
        # Create disconnected entities with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Isolated One {suffix}"}
        )
        assert entity1_response.status_code == 201
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Isolated Two {suffix}"}
        )
        assert entity2_response.status_code == 201
    
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
    
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
    
        # Try to compare without any connections
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity2_id}&unit={unit_id}"
        )
        assert compare_response.status_code == 404
        result = compare_response.json()
    
        assert "detail" in result
>       assert "no path found" in result["detail"].lower()
E       AssertionError: assert 'no path found' in 'from entity not found'
E        +  where 'from entity not found' = <built-in method lower of str object at 0x10de9c120>()
E        +    where <built-in method lower of str object at 0x10de9c120> = 'From entity not found'.lower

tests/test_integration.py:275: AssertionError
_____________ TestConnectionCRUD.test_create_connection_duplicate ______________
[gw1] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_connections.TestConnectionCRUD object at 0x109c71790>
test_client = <httpx.AsyncClient object at 0x109faf4d0>

    @pytest.mark.asyncio
    async def test_create_connection_duplicate(self, test_client):
        """Test creating duplicate connection updates existing one."""
>       data = await self.create_test_entities_and_unit(test_client)

tests/test_comprehensive_connections.py:182: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_comprehensive_connections.py:31: in create_test_entities_and_unit
    entity2 = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x109fe10d0>
instance = <src.models.Entity object at 0x109fe0e90>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x109fe0e90>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
______ TestConnectionRegression.test_update_connection_no_greenlet_error _______
[gw6] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_connection_regression.TestConnectionRegression object at 0x10aecb590>
test_client = <httpx.AsyncClient object at 0x10b0c1190>

    @pytest.mark.asyncio
    async def test_update_connection_no_greenlet_error(self, test_client):
        """Test that updating connections doesn't trigger greenlet errors."""
        # Create test entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
>       entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Update Test Entity A {suffix}"}
        )

tests/test_connection_regression.py:180: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10b0c1650>
instance = <src.models.Entity object at 0x10b0c15d0>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10b0c15d0>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
__________________ TestEntityCRUD.test_update_entity_success ___________________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d181d10>
test_client = <httpx.AsyncClient object at 0x10d40b8d0>

    @pytest.mark.asyncio
    async def test_update_entity_success(self, test_client):
        """Test successful entity update."""
        # Create entity
        create_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Original Name"}
        )
        entity_id = create_response.json()["id"]
    
        # Update entity
>       response = await test_client.put(
            f"/api/v1/entities/{entity_id}",
            json={"name": "Updated Name"}
        )

tests/test_comprehensive_entities.py:202: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1914: in put
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:95: in update_entity
    await db.refresh(entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10d40a110>
instance = <src.models.Entity object at 0x10d9edcd0>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10d9edcd0>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_____________ TestEdgeCases.test_update_entity_concurrent_requests _____________
[gw3] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_edge_cases.TestEdgeCases object at 0x10942aa90>
test_client = <httpx.AsyncClient object at 0x109e9ce90>

    @pytest.mark.asyncio
    async def test_update_entity_concurrent_requests(self, test_client):
        """Test concurrent updates to the same entity."""
        # Create entity
        entity = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Concurrent Update"}
        )
        entity_id = entity.json()["id"]
    
        async def update_entity(suffix: int):
            # Convert numeric suffix to letter-based name to comply with validation
            letter_names = ["Alpha", "Beta", "Gamma", "Delta", "Epsilon"]
            return await test_client.put(
                f"/api/v1/entities/{entity_id}",
                json={"name": f"Updated Entity {letter_names[suffix]}"}
            )
    
        # Launch concurrent updates
        tasks = [update_entity(i) for i in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
        # All should succeed (last write wins)
        for result in results:
            if not isinstance(result, Exception):
                assert result.status_code == 200
    
        # Verify final state
        final_entity = await test_client.get(f"/api/v1/entities/{entity_id}")
>       assert "Updated Entity" in final_entity.json()["name"]
E       AssertionError: assert 'Updated Entity' in 'Test Source Entity koxyukVU'

tests/test_comprehensive_edge_cases.py:315: AssertionError
_____________________ TestPathfinding.test_no_path_exists ______________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0c9210>
test_client = <httpx.AsyncClient object at 0x10bef3010>

    @pytest.mark.asyncio
    async def test_no_path_exists(self, test_client):
        """Test comparison when no path exists between entities."""
        # Create isolated entities with unique suffix
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
>       entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Isolated Entity One {suffix}"}
        )

tests/test_comprehensive_pathfinding.py:215: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10bebc950>
instance = <src.models.Entity object at 0x10bebdc50>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10bebdc50>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_________________ TestPathfinding.test_different_unit_no_path __________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0c9b10>
test_client = <httpx.AsyncClient object at 0x10b68cc50>

    @pytest.mark.asyncio
    async def test_different_unit_no_path(self, test_client):
        """Test comparison with different unit than connections."""
>       data = await self.create_linear_chain_setup(test_client)

tests/test_comprehensive_pathfinding.py:246: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0c9b10>
test_client = <httpx.AsyncClient object at 0x10b68cc50>

    async def create_linear_chain_setup(self, test_client):
        """Helper to create a linear chain of entities: A -> B -> C -> D."""
        # Create entities with unique names to avoid conflicts
        import string
        import random
    
        # Generate random suffix with only letters (no numbers)
        suffix = ''.join(random.choices(string.ascii_letters, k=8))
    
        entities = {}
        for name in ["A", "B", "C", "D"]:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"Chain Entity {name} {suffix}"}
            )
>           entities[name] = response.json()["id"]
E           KeyError: 'id'

tests/test_comprehensive_pathfinding.py:28: KeyError
_____________ TestDataConsistency.test_connection_deletion_cascade _____________
[gw5] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_integration.TestDataConsistency object at 0x10d66ca50>
test_client = <httpx.AsyncClient object at 0x10db735d0>

    async def test_connection_deletion_cascade(self, test_client):
        """Test that deleting entity removes associated connections."""
        # Create entities and connection with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Entity To Delete {suffix}"}
        )
>       assert entity1_response.status_code == 201
E       assert 400 == 201
E        +  where 400 = <Response [400 Bad Request]>.status_code

tests/test_integration.py:354: AssertionError
___ TestConnectionRegression.test_multiple_rapid_connections_no_pool_issues ____
[gw6] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_connection_regression.TestConnectionRegression object at 0x10aecbe90>
test_client = <httpx.AsyncClient object at 0x10b0083d0>

    @pytest.mark.asyncio
    async def test_multiple_rapid_connections_no_pool_issues(self, test_client):
        """Test creating multiple connections rapidly doesn't cause pool issues."""
        # Create base entities with unique names
        suffix = ''.join(c for c in str(uuid.uuid4()) if c.isalpha())[:8].upper()
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Pool Test Entity A {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Pool Test Entity B {suffix}"}
        )
>       entity3 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Pool Test Entity C {suffix}"}
        )

tests/test_connection_regression.py:247: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10b089410>
instance = <src.models.Entity object at 0x10b088b10>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10b088b10>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_________ TestEdgeCases.test_path_finding_with_very_small_multipliers __________
[gw3] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_edge_cases.TestEdgeCases object at 0x10942b390>
test_client = <httpx.AsyncClient object at 0x109c84410>

    @pytest.mark.asyncio
    async def test_path_finding_with_very_small_multipliers(self, test_client):
        """Test path-finding with very small multipliers to verify precision."""
        # Create entities
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
        small_multi_names = ["Alpha", "Beta", "Gamma"]
        entities = []
        for name in small_multi_names:
>           entity = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"Small Multi Ent {name} {suffix}"}
            )

tests/test_comprehensive_edge_cases.py:326: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10a0d94d0>
instance = <src.models.Entity object at 0x10a0d95d0>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10a0d95d0>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
_________________ TestPathfinding.test_shortest_path_selection _________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0ca410>
test_client = <httpx.AsyncClient object at 0x10b71e6d0>

    @pytest.mark.asyncio
    async def test_shortest_path_selection(self, test_client):
        """Test that shortest path is selected when multiple paths exist."""
        data = await self.create_complex_graph_setup(test_client)
    
        # Compare X to W
        # Possible paths:
        # 1. X -> Y -> W (2 * 4 = 8)
        # 2. X -> Z -> W (3 * 2 = 6) <- Should be chosen
        # 3. X -> Y -> Z -> W (2 * 1.5 * 2 = 6) <- Same length but more hops
    
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["X"],
                "to": data["entities"]["W"],
                "unit": data["unit_id"]
            }
        )
    
>       assert response.status_code == 200
E       assert 404 == 200
E        +  where 404 = <Response [404 Not Found]>.status_code

tests/test_comprehensive_pathfinding.py:285: AssertionError
------------------------------ Captured log call -------------------------------
ERROR    src.routes.connections:connections.py:39 From entity 2 not found. Available entities: []
ERROR    src.routes.connections:connections.py:39 From entity 2 not found. Available entities: [1]
ERROR    src.routes.connections:connections.py:39 From entity 5 not found. Available entities: [1]
ERROR    src.routes.connections:connections.py:39 From entity 5 not found. Available entities: [1]
ERROR    src.routes.connections:connections.py:39 From entity 1 not found. Available entities: []
ERROR    src.routes.connections:connections.py:39 From entity 2 not found. Available entities: []
_______ TestConnectionCRUD.test_create_connection_very_small_multiplier ________
[gw1] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_connections.TestConnectionCRUD object at 0x109c71f90>
test_client = <httpx.AsyncClient object at 0x10a5f86d0>

    @pytest.mark.asyncio
    async def test_create_connection_very_small_multiplier(self, test_client):
        """Test creating connection with very small multiplier."""
>       data = await self.create_test_entities_and_unit(test_client)

tests/test_comprehensive_connections.py:283: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_comprehensive_connections.py:27: in create_test_entities_and_unit
    entity1 = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10a54ff10>
instance = <src.models.Entity object at 0x10a54f950>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10a54f950>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
__________________ TestEntityCRUD.test_delete_entity_success ___________________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d188150>
test_client = <httpx.AsyncClient object at 0x10de8ec90>

    @pytest.mark.asyncio
    async def test_delete_entity_success(self, test_client):
        """Test successful entity deletion."""
        # Create entity
>       create_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": "To Delete"}
        )

tests/test_comprehensive_entities.py:265: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10de8e210>
instance = <src.models.Entity object at 0x10de8f390>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10de8f390>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
__________________ TestPathfinding.test_max_path_length_limit __________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0b9090>
test_client = <httpx.AsyncClient object at 0x10bc01350>

    @pytest.mark.asyncio
    async def test_max_path_length_limit(self, test_client):
        """Test that path-finding respects maximum path length (6 hops)."""
        # Create a very long chain of entities
        entities = []
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
    
        # Create 8 entities in a chain with unique suffix
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
        for i in range(8):
            entity_name = f"Long Chain Entity {['Zero', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven'][i]} {suffix}"
>           entity = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity_name}
            )

tests/test_comprehensive_pathfinding.py:307: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10b388fd0>
instance = <src.models.Entity object at 0x10b389c50>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10b389c50>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
____________ TestConnectionCRUD.test_create_connection_invalid_unit ____________
[gw1] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_connections.TestConnectionCRUD object at 0x109c73790>
test_client = <httpx.AsyncClient object at 0x10a351310>

    @pytest.mark.asyncio
    async def test_create_connection_invalid_unit(self, test_client):
        """Test creating connection with non-existent unit."""
        data = await self.create_test_entities_and_unit(test_client)
    
    
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                    "from_entity_id": data["entity1_id"],
                    "to_entity_id": data["entity2_id"],
                    "unit_id": 99999,
                    "multiplier": 2.0
                }
        )
    
>       assert response.status_code == 404
E       assert 422 == 404
E        +  where 422 = <Response [422 Unprocessable Entity]>.status_code

tests/test_comprehensive_connections.py:334: AssertionError
______________ TestEntityCRUD.test_delete_entity_with_connections ______________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d189350>
test_client = <httpx.AsyncClient object at 0x10d2cf810>

    @pytest.mark.asyncio
    async def test_delete_entity_with_connections(self, test_client):
        """Test deleting entity that has connections."""
        # Create entities with unique names
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
>       entity1 = await test_client.post("/api/v1/entities/", json={"name": f"Entity With Connection One {suffix}"})

tests/test_comprehensive_entities.py:292: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10d2cd810>
instance = <src.models.Entity object at 0x10d2cca50>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10d2cca50>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
______________ TestEdgeCases.test_connection_with_decimal_inverse ______________
[gw3] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_edge_cases.TestEdgeCases object at 0x10943b350>
test_client = <httpx.AsyncClient object at 0x109db4f90>

    @pytest.mark.asyncio
    async def test_connection_with_decimal_inverse(self, test_client):
        """Test that inverse connections maintain decimal precision."""
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
        # Create entities with unique names (max 20 chars)
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Dec A {suffix}"}
        )
        print(f"Entity1 response status: {entity1.status_code}")
        print(f"Entity1 response text: {entity1.text}")
    
        # Add assertion to ensure entity creation succeeded
        assert entity1.status_code == 201, f"Entity1 creation failed: {entity1.text}"
    
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Dec B {suffix}"}
        )
        print(f"Entity2 response status: {entity2.status_code}")
        print(f"Entity2 response text: {entity2.text}")
    
        # Add assertion to ensure entity creation succeeded
        assert entity2.status_code == 201, f"Entity2 creation failed: {entity2.text}"
    
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
    
        # Create connection with multiplier that has complex inverse
        # 3.0 -> inverse is 0.333..., should round to 0.3
        conn_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1.json()["id"],
                "to_entity_id": entity2.json()["id"],
                "unit_id": unit_id,
                "multiplier": 3.0
            }
        )
        print(f"Connection response status: {conn_response.status_code}")
        print(f"Connection response text: {conn_response.text}")
    
        # Ensure connection was created successfully
>       assert conn_response.status_code == 201, f"Connection creation failed: {conn_response.text}"
E       AssertionError: Connection creation failed: {"detail":"From entity not found"}
E       assert 404 == 201
E        +  where 404 = <Response [404 Not Found]>.status_code

tests/test_comprehensive_edge_cases.py:588: AssertionError
----------------------------- Captured stdout call -----------------------------
Entity1 response status: 201
Entity1 response text: {"name":"Dec A mbwxdwaw","id":3,"created_at":"2025-07-05T23:24:38.855183","updated_at":"2025-07-05T23:24:38.855188"}
Entity2 response status: 201
Entity2 response text: {"name":"Dec B mbwxdwaw","id":6,"created_at":"2025-07-05T23:24:39.068043","updated_at":"2025-07-05T23:24:39.068051"}
Connection response status: 404
Connection response text: {"detail":"From entity not found"}
------------------------------ Captured log call -------------------------------
ERROR    src.routes.connections:connections.py:39 From entity 3 not found. Available entities: [1]
________________ TestPathfinding.test_decimal_precision_in_path ________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0b33d0>
test_client = <httpx.AsyncClient object at 0x10bde1010>

    @pytest.mark.asyncio
    async def test_decimal_precision_in_path(self, test_client):
        """Test decimal precision is maintained throughout path calculations."""
        # Create entities with unique suffix
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
        entities = []
        for i in range(3):
            entity_name = f"Precision Test Entity {['Alpha', 'Beta', 'Gamma'][i]} {suffix}"
            entity = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity_name}
            )
            assert entity.status_code == 201
            entities.append(entity.json()["id"])
    
        # Get unit
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
    
        # Create connections with decimal values
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entities[0],
                "to_entity_id": entities[1],
                "unit_id": unit_id,
                "multiplier": 1.1
            }
        )
    
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entities[1],
                "to_entity_id": entities[2],
                "unit_id": unit_id,
                "multiplier": 1.1
            }
        )
    
        # Compare through path (1.1 * 1.1 = 1.21, rounds to 1.2)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": entities[0],
                "to": entities[2],
                "unit": unit_id
            }
        )
    
>       assert response.status_code == 200
E       assert 404 == 200
E        +  where 404 = <Response [404 Not Found]>.status_code

tests/test_comprehensive_pathfinding.py:414: AssertionError
------------------------------ Captured log call -------------------------------
ERROR    src.routes.connections:connections.py:50 To entity 4 not found. Available entities: [1]
ERROR    src.routes.connections:connections.py:39 From entity 4 not found. Available entities: [1, 2, 3]
_________________ TestConnectionCRUD.test_get_all_connections __________________
[gw1] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_connections.TestConnectionCRUD object at 0x109c73cd0>
test_client = <httpx.AsyncClient object at 0x10a94f490>

    @pytest.mark.asyncio
    async def test_get_all_connections(self, test_client):
        """Test getting all connections."""
        data = await self.create_test_entities_and_unit(test_client)
    
    
        # Create multiple connections
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
        connection_test_names = ["Alpha", "Beta", "Gamma"]
        entities = []
        for name in connection_test_names:
>           entity = await test_client.post(
                    "/api/v1/entities/",
                    json={"name": f"Connection Test Entity {name} {suffix}"}
            )

tests/test_comprehensive_connections.py:349: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x109fe3910>
instance = <src.models.Entity object at 0x109fe1450>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x109fe1450>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
__________________ TestPathfinding.test_cyclic_graph_handling __________________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0ca690>
test_client = <httpx.AsyncClient object at 0x10b933510>

    @pytest.mark.asyncio
    async def test_cyclic_graph_handling(self, test_client):
        """Test path-finding in graphs with cycles."""
        # Create a triangle: A -> B -> C -> A with unique suffix
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
        entities = {}
        for name in ["A", "B", "C"]:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"Cycle Entity {name} {suffix}"}
            )
            assert response.status_code == 201
            entities[name] = response.json()["id"]
    
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
    
        # Create cyclic connections
        connections = [
            (entities["A"], entities["B"], 2.0),
            (entities["B"], entities["C"], 3.0),
            (entities["C"], entities["A"], 0.2),  # 1/(2*3) ≈ 0.167, rounds to 0.2
        ]
    
        for from_id, to_id, multiplier in connections:
            await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": from_id,
                    "to_entity_id": to_id,
                    "unit_id": unit_id,
                    "multiplier": multiplier
                }
            )
    
        # Test that path-finding doesn't get stuck in cycle
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": entities["A"],
                "to": entities["C"],
                "unit": unit_id
            }
        )
    
>       assert response.status_code == 200
E       assert 404 == 200
E        +  where 404 = <Response [404 Not Found]>.status_code

tests/test_comprehensive_pathfinding.py:466: AssertionError
------------------------------ Captured log call -------------------------------
ERROR    src.routes.connections:connections.py:39 From entity 6 not found. Available entities: []
_________________ TestConnectionCRUD.test_get_connection_by_id _________________
[gw1] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_connections.TestConnectionCRUD object at 0x109c80610>
test_client = <httpx.AsyncClient object at 0x109f67190>

    @pytest.mark.asyncio
    async def test_get_connection_by_id(self, test_client):
        """Test getting specific connection by ID."""
>       data = await self.create_test_entities_and_unit(test_client)

tests/test_comprehensive_connections.py:383: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_comprehensive_connections.py:31: in create_test_entities_and_unit
    entity2 = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10a07e690>
instance = <src.models.Entity object at 0x10a07de10>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10a07de10>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
____________________ TestEntityCRUD.test_entity_pagination _____________________
[gw0] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_entities.TestEntityCRUD object at 0x10d180290>
test_client = <httpx.AsyncClient object at 0x10d546e90>

    @pytest.mark.asyncio
    async def test_entity_pagination(self, test_client):
        """Test entity list pagination (if implemented)."""
        # Create many entities with valid names (letters and spaces only)
        entity_names = [
            "Paginated Entity Alpha", "Paginated Entity Beta", "Paginated Entity Gamma",
            "Paginated Entity Delta", "Paginated Entity Epsilon", "Paginated Entity Zeta",
            "Paginated Entity Eta", "Paginated Entity Theta", "Paginated Entity Iota",
            "Paginated Entity Kappa", "Paginated Entity Lambda", "Paginated Entity Mu",
            "Paginated Entity Nu", "Paginated Entity Xi", "Paginated Entity Omicron"
        ]
    
        for name in entity_names:
>           response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )

tests/test_comprehensive_entities.py:356: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sqlalchemy.orm.session.Session object at 0x10d9efd50>
instance = <src.models.Entity object at 0x10d9ed890>, attribute_names = None
with_for_update = None

    def refresh(
        self,
        instance: object,
        attribute_names: Optional[Iterable[str]] = None,
        with_for_update: ForUpdateParameter = None,
    ) -> None:
        """Expire and refresh attributes on the given instance.
    
        The selected attributes will first be expired as they would when using
        :meth:`_orm.Session.expire`; then a SELECT statement will be issued to
        the database to refresh column-oriented attributes with the current
        value available in the current transaction.
    
        :func:`_orm.relationship` oriented attributes will also be immediately
        loaded if they were already eagerly loaded on the object, using the
        same eager loading strategy that they were loaded with originally.
    
        .. versionadded:: 1.4 - the :meth:`_orm.Session.refresh` method
           can also refresh eagerly loaded attributes.
    
        :func:`_orm.relationship` oriented attributes that would normally
        load using the ``select`` (or "lazy") loader strategy will also
        load **if they are named explicitly in the attribute_names
        collection**, emitting a SELECT statement for the attribute using the
        ``immediate`` loader strategy.  If lazy-loaded relationships are not
        named in :paramref:`_orm.Session.refresh.attribute_names`, then
        they remain as "lazy loaded" attributes and are not implicitly
        refreshed.
    
        .. versionchanged:: 2.0.4  The :meth:`_orm.Session.refresh` method
           will now refresh lazy-loaded :func:`_orm.relationship` oriented
           attributes for those which are named explicitly in the
           :paramref:`_orm.Session.refresh.attribute_names` collection.
    
        .. tip::
    
            While the :meth:`_orm.Session.refresh` method is capable of
            refreshing both column and relationship oriented attributes, its
            primary focus is on refreshing of local column-oriented attributes
            on a single instance. For more open ended "refresh" functionality,
            including the ability to refresh the attributes on many objects at
            once while having explicit control over relationship loader
            strategies, use the
            :ref:`populate existing <orm_queryguide_populate_existing>` feature
            instead.
    
        Note that a highly isolated transaction will return the same values as
        were previously read in that same transaction, regardless of changes
        in database state outside of that transaction.   Refreshing
        attributes usually only makes sense at the start of a transaction
        where database rows have not yet been accessed.
    
        :param attribute_names: optional.  An iterable collection of
          string attribute names indicating a subset of attributes to
          be refreshed.
    
        :param with_for_update: optional boolean ``True`` indicating FOR UPDATE
          should be used, or may be a dictionary containing flags to
          indicate a more specific set of FOR UPDATE flags for the SELECT;
          flags should match the parameters of
          :meth:`_query.Query.with_for_update`.
          Supersedes the :paramref:`.Session.refresh.lockmode` parameter.
    
        .. seealso::
    
            :ref:`session_expire` - introductory material
    
            :meth:`.Session.expire`
    
            :meth:`.Session.expire_all`
    
            :ref:`orm_queryguide_populate_existing` - allows any ORM query
            to refresh objects as they would be loaded normally.
    
        """
        try:
            state = attributes.instance_state(instance)
        except exc.NO_STATE as err:
            raise exc.UnmappedInstanceError(instance) from err
    
        self._expire_state(state, attribute_names)
    
        # this autoflush previously used to occur as a secondary effect
        # of the load_on_ident below.   Meaning we'd organize the SELECT
        # based on current DB pks, then flush, then if pks changed in that
        # flush, crash.  this was unticketed but discovered as part of
        # #8703.  So here, autoflush up front, dont autoflush inside
        # load_on_ident.
        self._autoflush()
    
        if with_for_update == {}:
            raise sa_exc.ArgumentError(
                "with_for_update should be the boolean value "
                "True, or a dictionary with options.  "
                "A blank dictionary is ambiguous."
            )
    
        with_for_update = ForUpdateArg._from_argument(with_for_update)
    
        stmt: Select[Any] = sql.select(object_mapper(instance))
        if (
            loading.load_on_ident(
                self,
                stmt,
                state.key,
                refresh_state=state,
                with_for_update=with_for_update,
                only_load_props=attribute_names,
                require_pk_cols=True,
                # technically unnecessary as we just did autoflush
                # above, however removes the additional unnecessary
                # call to _autoflush()
                no_autoflush=True,
                is_user_refresh=True,
            )
            is None
        ):
>           raise sa_exc.InvalidRequestError(
                "Could not refresh instance '%s'" % instance_str(instance)
            )
E           sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10d9ed890>'

venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: InvalidRequestError
------------------------------ Captured log call -------------------------------
ERROR    src.routes.entities:entities.py:39 Entity verification failed: ID 9 not immediately queryable
______________ TestPathfinding.test_complex_decimal_calculations _______________
[gw4] darwin -- Python 3.11.13 /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python3.11

self = <backend.tests.test_comprehensive_pathfinding.TestPathfinding object at 0x10b0c9150>
test_client = <httpx.AsyncClient object at 0x10b5ac2d0>

    @pytest.mark.asyncio
    async def test_complex_decimal_calculations(self, test_client):
        """Test complex paths with multiple decimal multipliers."""
        # Create entities with unique suffix
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
    
        entities = []
        for i in range(5):
            entity = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"Complex Decimal Entity {['One', 'Two', 'Three', 'Four', 'Five'][i]} {suffix}"}
            )
            if entity.status_code != 201:
                print(f"Entity creation failed: {entity.status_code}, {entity.text}")
            assert entity.status_code == 201
            entities.append(entity.json()["id"])
    
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
    
        # Create connections with various decimal values
        connections = [
            (0, 1, 0.5),   # 0.5
            (1, 2, 2.3),   # 0.5 * 2.3 = 1.15
            (2, 3, 1.7),   # 1.15 * 1.7 = 1.955
            (3, 4, 0.9),   # 1.955 * 0.9 = 1.7595
        ]
    
        for from_idx, to_idx, multiplier in connections:
            await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entities[from_idx],
                    "to_entity_id": entities[to_idx],
                    "unit_id": unit_id,
                    "multiplier": multiplier
                }
            )
    
        # Test full path calculation
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": entities[0],
                "to": entities[4],
                "unit": unit_id
            }
        )
    
>       assert response.status_code == 200
E       assert 404 == 200
E        +  where 404 = <Response [404 Not Found]>.status_code

tests/test_comprehensive_pathfinding.py:553: AssertionError
------------------------------ Captured log call -------------------------------
ERROR    src.routes.connections:connections.py:39 From entity 1 not found. Available entities: []
ERROR    src.routes.connections:connections.py:39 From entity 2 not found. Available entities: []
ERROR    src.routes.connections:connections.py:39 From entity 3 not found. Available entities: []
ERROR    src.routes.connections:connections.py:39 From entity 4 not found. Available entities: [1, 2]

--------- coverage: platform darwin, python 3.11.13-final-0 ----------
Name                        Stmts   Miss  Cover   Missing
---------------------------------------------------------
src/__init__.py                 0      0   100%
src/app_factory.py             14      0   100%
src/config.py                  15      0   100%
src/database.py                21      1    95%   34
src/main.py                    13      1    92%   37
src/models.py                  27      0   100%
src/routes/__init__.py          0      0   100%
src/routes/compare.py          30     18    40%   29-74
src/routes/connections.py     135    106    21%   34-38, 40-49, 51-180, 196-204, 216-219, 230-284, 296-318
src/routes/entities.py         64     35    45%   28-44, 60-61, 71-74, 85-99, 112-118
src/routes/units.py            32     11    66%   21-22, 35-39, 52-55
src/schemas.py                 90     11    88%   50, 76, 81, 107-116
src/services.py                39     33    15%   22-115, 123-149
---------------------------------------------------------
TOTAL                         480    216    55%

=========================== short test summary info ============================
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success
FAILED tests/test_phase3_connection_validation.py::TestPhase3ConnectionValidation::test_connection_creation_basic
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_with_spaces
FAILED tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_direct_path_comparison
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_direct_comparison
FAILED tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_success
FAILED tests/test_phase2_verification.py::TestPhase2Verification::test_entity_creation_success
FAILED tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_case_insensitive
FAILED tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_two_hop_pathfinding
FAILED tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_no_path_exists
FAILED tests/test_phase3_connection_validation.py::TestPhase3ConnectionValidation::test_pathfinding_api_basic
FAILED tests/test_integration.py::TestEntityIntegration::test_entity_crud_workflow
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_two_hop_comparison
FAILED tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_connection_creation
FAILED tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse
FAILED tests/test_phase2_verification.py::TestPhase2Verification::test_entity_duplicate_detection
FAILED tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_decimal_rounding_in_inverse
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_max_length
FAILED tests/test_connection_regression.py::TestConnectionRegression::test_no_greenlet_error_on_connection_creation
FAILED tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_same_entity_comparison
FAILED tests/test_integration_simple.py::TestEntityBasicWorkflow::test_entity_creation_and_retrieval
FAILED tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_connection_deletion_removes_inverse
FAILED tests/test_integration.py::TestConnectionIntegration::test_connection_with_inverse_creation
FAILED tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_delete_entity_in_use
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_three_hop_comparison
FAILED tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_decimal_precision
FAILED tests/test_connection_regression.py::TestConnectionRegression::test_cors_headers_on_success
FAILED tests/test_integration.py::TestComparisonIntegration::test_direct_comparison
FAILED tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_different_unit_no_path
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_get_all_entities_with_data
FAILED tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_extreme_multiplier_values
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_reverse_comparison
FAILED tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_same_entity
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_get_entity_by_id
FAILED tests/test_connection_regression.py::TestConnectionRegression::test_no_lazy_loading_after_commit
FAILED tests/test_integration.py::TestComparisonIntegration::test_transitive_comparison
FAILED tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_special_characters_in_entity_names
FAILED tests/test_integration.py::TestComparisonIntegration::test_no_path_comparison
FAILED tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_duplicate
FAILED tests/test_connection_regression.py::TestConnectionRegression::test_update_connection_no_greenlet_error
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_update_entity_success
FAILED tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_update_entity_concurrent_requests
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_no_path_exists
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_different_unit_no_path
FAILED tests/test_integration.py::TestDataConsistency::test_connection_deletion_cascade
FAILED tests/test_connection_regression.py::TestConnectionRegression::test_multiple_rapid_connections_no_pool_issues
FAILED tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_path_finding_with_very_small_multipliers
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_shortest_path_selection
FAILED tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_very_small_multiplier
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_delete_entity_success
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_max_path_length_limit
FAILED tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_invalid_unit
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_delete_entity_with_connections
FAILED tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_connection_with_decimal_inverse
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_decimal_precision_in_path
FAILED tests/test_comprehensive_connections.py::TestConnectionCRUD::test_get_all_connections
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_cyclic_graph_handling
FAILED tests/test_comprehensive_connections.py::TestConnectionCRUD::test_get_connection_by_id
FAILED tests/test_comprehensive_entities.py::TestEntityCRUD::test_entity_pagination
FAILED tests/test_comprehensive_pathfinding.py::TestPathfinding::test_complex_decimal_calculations
=================== 61 failed, 64 passed in 63.01s (0:01:03) ===================
