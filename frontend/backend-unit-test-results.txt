✅ Running in virtual environment: /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv
Checking test environment...
----------------------------------------
✅ Running in virtual environment
✅ PostgreSQL is running on localhost:5432
----------------------------------------
✅ All checks passed!
python scripts/test_db_setup.py
INFO:__main__:Creating simile_test database...
INFO:__main__:simile_test database created successfully
INFO:__main__:Dropping existing tables...
INFO:__main__:Creating database tables...
INFO:__main__:Inserting initial units...
INFO:__main__:Database schema setup completed successfully
INFO:__main__:✅ Database verification passed. Found tables: connections, entities, units
INFO:__main__:✅ Found 5 units
INFO:__main__:✅ Test database setup completed successfully!
DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test" \
	TEST_DATABASE_HOST=localhost \
	PYTHONPATH=. \
	pytest -n auto --dist loadfile -v || (make test-teardown && exit 1)
============================= test session starts ==============================
platform darwin -- Python 3.11.13, pytest-7.4.4, pluggy-1.6.0
rootdir: /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend
configfile: pyproject.toml
testpaths: tests
plugins: anyio-4.9.0, asyncio-0.23.3, cov-4.1.0, xdist-3.5.0
asyncio: mode=Mode.STRICT
created: 16/16 workers
16 workers [125 items]

........................................................................ [ 57%]
.....................................................                    [100%]

--------- coverage: platform darwin, python 3.11.13-final-0 ----------
Name                        Stmts   Miss  Cover   Missing
---------------------------------------------------------
src/__init__.py                 0      0   100%
src/app_factory.py             14      0   100%
src/config.py                  15      0   100%
src/database.py                21      1    95%   34
src/main.py                    13      1    92%   37
src/models.py                  27      0   100%
src/routes/__init__.py          0      0   100%
src/routes/compare.py          30     18    40%   29-74
src/routes/connections.py     135    106    21%   34-38, 40-180, 196-204, 216-219, 233-284, 296-318
src/routes/entities.py         64     35    45%   28-44, 60-61, 71-74, 85-99, 112-118
src/routes/units.py            32     11    66%   21-22, 35-39, 52-55
src/schemas.py                 90      5    94%   50, 76, 109, 114, 116
src/services.py                39     23    41%   34-115, 124, 140-149
---------------------------------------------------------
TOTAL                         480    200    58%

============================= 125 passed in 6.82s ==============================
make test-teardown
✅ Running in virtual environment: /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv
python scripts/test_db_teardown.py
INFO:__main__:Terminating connections to simile_test...
INFO:__main__:Dropping simile_test database...
INFO:__main__:✅ simile_test database dropped successfully
INFO:__main__:✅ Test database teardown completed successfully!
