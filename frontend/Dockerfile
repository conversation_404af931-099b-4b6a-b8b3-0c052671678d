# Multi-stage build for development and production
FROM node:18-alpine AS base

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Development stage - optimized for hot reload
FROM base AS development

# Install development dependencies
RUN npm install

# Copy application code (will be overridden by volume mounts in dev)
COPY . .

# Expose port
EXPOSE 3000

# Development command with hot reload
CMD ["npm", "start"]

# Production stage - optimized for deployment
FROM base AS production

# Install only production dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Build the application
RUN npm run build

# Expose port
EXPOSE 3000

# Production command (serve build files)
CMD ["npx", "serve", "-s", "build", "-l", "3000"]