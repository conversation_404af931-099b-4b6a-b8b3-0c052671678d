
> simile-frontend@0.1.0 test:e2e
> playwright test --project=webkit --grep=should calculate direct relationships --reporter=json

{
  "config": {
    "configFile": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/playwright.config.ts",
    "rootDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
    "forbidOnly": false,
    "fullyParallel": true,
    "globalSetup": null,
    "globalTeardown": null,
    "globalTimeout": 7200000,
    "grep": {},
    "grepInvert": null,
    "maxFailures": 25,
    "metadata": {
      "actualWorkers": 1
    },
    "preserveOutput": "always",
    "reporter": [
      [
        "json"
      ]
    ],
    "reportSlowTests": {
      "max": 5,
      "threshold": 300000
    },
    "quiet": false,
    "projects": [
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {
          "actualWorkers": 1
        },
        "id": "chromium",
        "name": "chromium",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {
          "actualWorkers": 1
        },
        "id": "firefox",
        "name": "firefox",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {
          "actualWorkers": 1
        },
        "id": "webkit",
        "name": "webkit",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      }
    ],
    "shard": null,
    "updateSnapshots": "missing",
    "updateSourceMethod": "patch",
    "version": "1.53.1",
    "workers": 3,
    "webServer": {
      "command": "npm start",
      "url": "http://localhost:3000",
      "reuseExistingServer": true,
      "timeout": 120000
    }
  },
  "suites": [
    {
      "title": "comparisons.spec.ts",
      "file": "comparisons.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Entity Comparisons and Pathfinding",
          "file": "comparisons.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should calculate direct relationships",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 40902,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 2 total entities in database (Worker 0)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 0\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms (Worker 0):\n"
                        },
                        {
                          "text": "  • Initial entities: 2\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 2\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human FOIWA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human FOIWA\n"
                        },
                        {
                          "text": "Creating entity: Human FOIWA (expectFailure: false)\n"
                        },
                        {
                          "text": "Create button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Submit button is ready for interaction (attempt 1)\n"
                        },
                        {
                          "text": "Submit button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Verifying entity \"Human FOIWA\" in UI with browser-specific timeout: 4000ms\n"
                        },
                        {
                          "text": "  ✓ Entity \"Human FOIWA\" verified on first attempt\n"
                        },
                        {
                          "text": "    ⏱️  Ensuring UI refresh complete for webkit (400ms delay)\n"
                        },
                        {
                          "text": "    ✓ UI refresh complete and entity \"Human FOIWA\" confirmed visible\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human FOIWA\" in 1413ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human FOIWA (ID: temp-1751672994778-qwqr9bpqo) in 1624ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human FOIWA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball FOIWB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball FOIWB\n"
                        },
                        {
                          "text": "Creating entity: Ball FOIWB (expectFailure: false)\n"
                        },
                        {
                          "text": "Create button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Submit button is ready for interaction (attempt 1)\n"
                        },
                        {
                          "text": "Submit button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Verifying entity \"Ball FOIWB\" in UI with browser-specific timeout: 4000ms\n"
                        },
                        {
                          "text": "  ✓ Entity \"Ball FOIWB\" verified on first attempt\n"
                        },
                        {
                          "text": "    ⏱️  Ensuring UI refresh complete for webkit (400ms delay)\n"
                        },
                        {
                          "text": "    ✓ UI refresh complete and entity \"Ball FOIWB\" confirmed visible\n"
                        },
                        {
                          "text": "Entity creation completed for \"Ball FOIWB\" in 1372ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Ball FOIWB (ID: temp-1751672996572-xvmnhiqud) in 1589ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 2 created: Ball FOIWB\n"
                        },
                        {
                          "text": "Creating comparison test entity 3/6: Build FOIWC\n"
                        },
                        {
                          "text": "Creating entity for complex test: Build FOIWC\n"
                        },
                        {
                          "text": "Creating entity: Build FOIWC (expectFailure: false)\n"
                        },
                        {
                          "text": "Create button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Submit button is ready for interaction (attempt 1)\n"
                        },
                        {
                          "text": "Submit button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Verifying entity \"Build FOIWC\" in UI with browser-specific timeout: 4000ms\n"
                        },
                        {
                          "text": "  ✓ Entity \"Build FOIWC\" verified on first attempt\n"
                        },
                        {
                          "text": "    ⏱️  Ensuring UI refresh complete for webkit (400ms delay)\n"
                        },
                        {
                          "text": "    ✓ UI refresh complete and entity \"Build FOIWC\" confirmed visible\n"
                        },
                        {
                          "text": "Entity creation completed for \"Build FOIWC\" in 1384ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Build FOIWC (ID: temp-1751672998390-ub12b2w83) in 1613ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 3 created: Build FOIWC\n"
                        },
                        {
                          "text": "Creating comparison test entity 4/6: Car FOIWD\n"
                        },
                        {
                          "text": "Creating entity for complex test: Car FOIWD\n"
                        },
                        {
                          "text": "Creating entity: Car FOIWD (expectFailure: false)\n"
                        },
                        {
                          "text": "Create button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Submit button is ready for interaction (attempt 1)\n"
                        },
                        {
                          "text": "Submit button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Verifying entity \"Car FOIWD\" in UI with browser-specific timeout: 4000ms\n"
                        },
                        {
                          "text": "  ✓ Entity \"Car FOIWD\" verified on first attempt\n"
                        },
                        {
                          "text": "    ⏱️  Ensuring UI refresh complete for webkit (400ms delay)\n"
                        },
                        {
                          "text": "    ✓ UI refresh complete and entity \"Car FOIWD\" confirmed visible\n"
                        },
                        {
                          "text": "Entity creation completed for \"Car FOIWD\" in 1331ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Car FOIWD (ID: temp-1751673000147-w4t43cs8n) in 1550ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 4 created: Car FOIWD\n"
                        },
                        {
                          "text": "Creating comparison test entity 5/6: Eleph FOIWE\n"
                        },
                        {
                          "text": "Creating entity for complex test: Eleph FOIWE\n"
                        },
                        {
                          "text": "Creating entity: Eleph FOIWE (expectFailure: false)\n"
                        },
                        {
                          "text": "Create button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Submit button is ready for interaction (attempt 1)\n"
                        },
                        {
                          "text": "Submit button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Verifying entity \"Eleph FOIWE\" in UI with browser-specific timeout: 4000ms\n"
                        },
                        {
                          "text": "  ✓ Entity \"Eleph FOIWE\" verified on first attempt\n"
                        },
                        {
                          "text": "    ⏱️  Ensuring UI refresh complete for webkit (400ms delay)\n"
                        },
                        {
                          "text": "    ✓ UI refresh complete and entity \"Eleph FOIWE\" confirmed visible\n"
                        },
                        {
                          "text": "Entity creation completed for \"Eleph FOIWE\" in 1363ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Eleph FOIWE (ID: temp-1751673001932-kfsd8yr0o) in 1581ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 5 created: Eleph FOIWE\n"
                        },
                        {
                          "text": "Creating comparison test entity 6/6: Mouse FOIWF\n"
                        },
                        {
                          "text": "Creating entity for complex test: Mouse FOIWF\n"
                        },
                        {
                          "text": "Creating entity: Mouse FOIWF (expectFailure: false)\n"
                        },
                        {
                          "text": "Create button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Submit button is ready for interaction (attempt 1)\n"
                        },
                        {
                          "text": "Submit button clicked successfully with standard click (attempt 1)\n"
                        },
                        {
                          "text": "Verifying entity \"Mouse FOIWF\" in UI with browser-specific timeout: 4000ms\n"
                        },
                        {
                          "text": "  ✓ Entity \"Mouse FOIWF\" verified on first attempt\n"
                        },
                        {
                          "text": "    ⏱️  Ensuring UI refresh complete for webkit (400ms delay)\n"
                        },
                        {
                          "text": "    ✓ UI refresh complete and entity \"Mouse FOIWF\" confirmed visible\n"
                        },
                        {
                          "text": "Entity creation completed for \"Mouse FOIWF\" in 1363ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Mouse FOIWF (ID: temp-1751673003719-rn4cxo39s) in 1581ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 6 created: Mouse FOIWF\n"
                        },
                        {
                          "text": "Creating test connections...\n"
                        },
                        {
                          "text": "Creating connection: Human FOIWA → Ball FOIWB (10.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Human FOIWA\n"
                        },
                        {
                          "text": "Successfully selected: Human FOIWA using dropdown\n"
                        },
                        {
                          "text": "Successfully set input value: Human FOIWA\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball FOIWB\n"
                        },
                        {
                          "text": "Successfully selected: Ball FOIWB using dropdown\n"
                        },
                        {
                          "text": "Successfully set input value: Ball FOIWB\n"
                        },
                        {
                          "text": "Connection creation completed for \"Human FOIWA → Ball FOIWB\" in 2263ms\n"
                        },
                        {
                          "text": "  ℹ️  Tracking connection dependency: temp-1751672994778-qwqr9bpqo → temp-1751672996572-xvmnhiqud (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Connection found: Human FOIWA → Ball FOIWB in Length (any multiplier)\n"
                        },
                        {
                          "text": "Creating connection: Ball FOIWB → Build FOIWC (50.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball FOIWB\n"
                        },
                        {
                          "text": "Successfully selected: Ball FOIWB using dropdown\n"
                        },
                        {
                          "text": "Successfully set input value: Ball FOIWB\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Build FOIWC\n"
                        },
                        {
                          "text": "Successfully selected: Build FOIWC using dropdown\n"
                        },
                        {
                          "text": "Successfully set input value: Build FOIWC\n"
                        },
                        {
                          "text": "Connection creation completed for \"Ball FOIWB → Build FOIWC\" in 2285ms\n"
                        },
                        {
                          "text": "  ℹ️  Tracking connection dependency: temp-1751672996572-xvmnhiqud → temp-1751672998390-ub12b2w83 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Connection found: Ball FOIWB → Build FOIWC in Length (any multiplier)\n"
                        },
                        {
                          "text": "Creating connection: Build FOIWC → Mouse FOIWF (0.1x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Build FOIWC\n"
                        },
                        {
                          "text": "Successfully selected: Build FOIWC using dropdown\n"
                        },
                        {
                          "text": "Successfully set input value: Build FOIWC\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Mouse FOIWF\n"
                        },
                        {
                          "text": "Successfully selected: Mouse FOIWF using dropdown\n"
                        },
                        {
                          "text": "Successfully set input value: Mouse FOIWF\n"
                        },
                        {
                          "text": "Connection creation completed for \"Build FOIWC → Mouse FOIWF\" in 2300ms\n"
                        },
                        {
                          "text": "  ℹ️  Tracking connection dependency: temp-1751672998390-ub12b2w83 → temp-1751673003719-rn4cxo39s (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Connection found: Build FOIWC → Mouse FOIWF in Length (any multiplier)\n"
                        },
                        {
                          "text": "Creating connection: Car FOIWD → Eleph FOIWE (0.3x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Car FOIWD\n"
                        },
                        {
                          "text": "Successfully selected: Car FOIWD using dropdown\n"
                        },
                        {
                          "text": "Successfully set input value: Car FOIWD\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Eleph FOIWE\n"
                        },
                        {
                          "text": "Successfully selected: Eleph FOIWE using dropdown\n"
                        },
                        {
                          "text": "Successfully set input value: Eleph FOIWE\n"
                        },
                        {
                          "text": "Connection creation completed for \"Car FOIWD → Eleph FOIWE\" in 2324ms\n"
                        },
                        {
                          "text": "  ℹ️  Tracking connection dependency: temp-1751673000147-w4t43cs8n → temp-1751673001932-kfsd8yr0o (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Connection found: Car FOIWD → Eleph FOIWE in Mass (any multiplier)\n"
                        },
                        {
                          "text": "Comparing: Human FOIWA to Ball FOIWB (count: 1, unit: Length)\n"
                        },
                        {
                          "text": "Path API Response: http://localhost:8000/api/v1/compare/?from=114&to=115&unit=1 - Status: 200\n"
                        },
                        {
                          "text": "Entity names not found in result, but calculation successful: Did you know thatis asmeasurebigtallheavylongvoluminousas10.0?\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 6 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human FOIWA (ID: 114)\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Ball FOIWB (ID: 115)\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Build FOIWC (ID: 116)\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Car FOIWD (ID: 117)\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Eleph FOIWE (ID: 118)\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Mouse FOIWF (ID: 119)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 6 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 31ms (Worker 0):\n"
                        },
                        {
                          "text": "  • Entities deleted: 6 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 0\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (31ms cleanup / 40621ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 0 (15ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-04T23:49:52.038Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "0f325a1a01063f2777e1-98396ab11a92e1da98a3",
              "file": "comparisons.spec.ts",
              "line": 145,
              "column": 7
            }
          ]
        }
      ]
    }
  ],
  "errors": [],
  "stats": {
    "startTime": "2025-07-04T23:49:51.676Z",
    "duration": 41507.358,
    "expected": 1,
    "skipped": 0,
    "unexpected": 0,
    "flaky": 0
  }
}
