============================= test session starts ==============================
platform darwin -- Python 3.11.13, pytest-7.4.4, pluggy-1.6.0
rootdir: /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend
configfile: pyproject.toml
testpaths: tests
plugins: anyio-4.9.0, asyncio-0.23.3, cov-4.1.0, xdist-3.5.0
asyncio: mode=Mode.STRICT
collected 125 items

tests/test_comprehensive_connections.py ..................               [ 14%]
tests/test_comprehensive_edge_cases.py ..............                    [ 25%]
tests/test_comprehensive_entities.py .........................           [ 45%]
tests/test_comprehensive_pathfinding.py .............                    [ 56%]
tests/test_connection_regression.py ....F.                               [ 60%]
tests/test_endpoints.py ....                                             [ 64%]
tests/test_integration.py .F.......                                      [ 71%]
tests/test_integration_simple.py ................                        [ 84%]
tests/test_phase1_demo.py ....                                           [ 87%]
tests/test_phase2_verification.py .....                                  [ 91%]
tests/test_phase3_connection_validation.py ...                           [ 93%]
tests/test_phase3_inverse_connections.py FFF                             [ 96%]
tests/test_phase3_pathfinding.py .....                                   [100%]

=================================== FAILURES ===================================
______ TestConnectionRegression.test_update_connection_no_greenlet_error _______
tests/test_connection_regression.py:230: in test_update_connection_no_greenlet_error
    assert inverse is not None
E   assert None is not None
_______ TestConnectionIntegration.test_connection_with_inverse_creation ________
tests/test_integration.py:124: in test_connection_with_inverse_creation
    assert forward_connection is not None
E   assert None is not None
________ TestInverseConnectionCreation.test_automatic_inverse_creation _________
tests/test_phase3_inverse_connections.py:65: in test_automatic_inverse_creation
    assert inverse_connection is not None, "Inverse connection not found"
E   AssertionError: Inverse connection not found
E   assert None is not None
________ TestInverseConnectionCreation.test_decimal_rounding_in_inverse ________
tests/test_phase3_inverse_connections.py:129: in test_decimal_rounding_in_inverse
    assert inverse_connection is not None
E   assert None is not None
____ TestInverseConnectionCreation.test_connection_deletion_removes_inverse ____
tests/test_phase3_inverse_connections.py:192: in test_connection_deletion_removes_inverse
    assert forward_exists, "Forward connection should exist"
E   AssertionError: Forward connection should exist
E   assert False

--------- coverage: platform darwin, python 3.11.13-final-0 ----------
Name                        Stmts   Miss  Cover   Missing
---------------------------------------------------------
src/__init__.py                 0      0   100%
src/app_factory.py             14      0   100%
src/config.py                  15      0   100%
src/database.py                21      1    95%   34
src/main.py                    13      1    92%   37
src/models.py                  27      0   100%
src/routes/__init__.py          0      0   100%
src/routes/compare.py          30     18    40%   29-74
src/routes/connections.py     135    106    21%   34-38, 40-180, 196-204, 216-219, 233-284, 296-318
src/routes/entities.py         64     35    45%   28-44, 60-61, 71-74, 85-99, 112-118
src/routes/units.py            32     11    66%   21-22, 35-39, 52-55
src/schemas.py                 90      5    94%   50, 76, 109, 114, 116
src/services.py                39     23    41%   34-115, 124, 140-149
---------------------------------------------------------
TOTAL                         480    200    58%

=========================== short test summary info ============================
FAILED tests/test_connection_regression.py::TestConnectionRegression::test_update_connection_no_greenlet_error
FAILED tests/test_integration.py::TestConnectionIntegration::test_connection_with_inverse_creation
FAILED tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation
FAILED tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_decimal_rounding_in_inverse
FAILED tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_connection_deletion_removes_inverse
======================== 5 failed, 120 passed in 19.61s ========================
