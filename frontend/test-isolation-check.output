🔍 E2E Test Isolation Verification

This will help determine if failures are TEST issues or SYSTEM issues.

📋 Test 1: Running tests SEQUENTIALLY (workers=1)...


📋 Test 3: Database State Check

Run this command to check for leftover test data:
docker exec -it simile-db psql -U postgres -d simile -c "SELECT name FROM entities WHERE name LIKE '%Test%' OR name LIKE '%TQYO%' OR name LIKE '%TSZI%';"

If you see test entities remaining, it indicates cleanup issues.


Running 2 tests using 1 worker

🧹 Starting pre-test cleanup...
  Found 5 total entities in database (Worker 0)
  Identified 0 test entities to delete for Worker 0
🧹 Pre-test cleanup complete in 25ms (Worker 0):
  • Initial entities: 5
  • Entities deleted: 0
  • Failed deletions: 0
  • Final entity count: 5
  • Net reduction: 0
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human MDXHA
Creating entity for complex test: Human MDXHA
Creating entity: Human MDXHA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human MDXHA" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Human MDXHA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human MDXHA" confirmed visible
Entity creation completed for "Human MDXHA" in 1195ms
  ✅ Complex test entity created: Human MDXHA (ID: temp-1751678010458-n06xuuhz5) in 1416ms
✅ Comparison test entity 1 created: Human MDXHA
Creating comparison test entity 2/6: Ball MDXHB
Creating entity for complex test: Ball MDXHB
Creating entity: Ball MDXHB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball MDXHB" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Ball MDXHB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball MDXHB" confirmed visible
Entity creation completed for "Ball MDXHB" in 1136ms
  ✅ Complex test entity created: Ball MDXHB (ID: temp-1751678012022-kysv22ut0) in 1358ms
✅ Comparison test entity 2 created: Ball MDXHB
Creating comparison test entity 3/6: Build MDXHC
Creating entity for complex test: Build MDXHC
Creating entity: Build MDXHC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build MDXHC" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Build MDXHC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build MDXHC" confirmed visible
Entity creation completed for "Build MDXHC" in 1158ms
  ✅ Complex test entity created: Build MDXHC (ID: temp-1751678013597-gcu26359d) in 1372ms
✅ Comparison test entity 3 created: Build MDXHC
Creating comparison test entity 4/6: Car MDXHD
Creating entity for complex test: Car MDXHD
Creating entity: Car MDXHD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car MDXHD" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Car MDXHD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car MDXHD" confirmed visible
Entity creation completed for "Car MDXHD" in 1126ms
  ✅ Complex test entity created: Car MDXHD (ID: temp-1751678015150-0ao1rrova) in 1346ms
✅ Comparison test entity 4 created: Car MDXHD
Creating comparison test entity 5/6: Eleph MDXHE
Creating entity for complex test: Eleph MDXHE
Creating entity: Eleph MDXHE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph MDXHE" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Eleph MDXHE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph MDXHE" confirmed visible
Entity creation completed for "Eleph MDXHE" in 1153ms
  ✅ Complex test entity created: Eleph MDXHE (ID: temp-1751678016732-ifrqfz215) in 1377ms
✅ Comparison test entity 5 created: Eleph MDXHE
Creating comparison test entity 6/6: Mouse MDXHF
Creating entity for complex test: Mouse MDXHF
Creating entity: Mouse MDXHF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse MDXHF" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Mouse MDXHF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse MDXHF" confirmed visible
Entity creation completed for "Mouse MDXHF" in 1162ms
  ✅ Complex test entity created: Mouse MDXHF (ID: temp-1751678018317-5r5iqg599) in 1381ms
✅ Comparison test entity 6 created: Mouse MDXHF
Creating test connections...
Creating connection: Human MDXHA → Ball MDXHB (10.0x)
Selecting AutoComplete option: Human MDXHA
Using autocomplete prefix: "Human" for entity: Human MDXHA
Found autocomplete option: "Human MDXHAID: 837"
Successfully selected: "Human MDXHA" using dropdown autocomplete
Selecting AutoComplete option: Ball MDXHB
Using autocomplete prefix: "Ball" for entity: Ball MDXHB
Found autocomplete option: "Ball MDXHBID: 838"
Successfully selected: "Ball MDXHB" using dropdown autocomplete
Connection creation completed for "Human MDXHA → Ball MDXHB" in 2643ms
  ℹ️  Tracking connection dependency: temp-1751678010458-n06xuuhz5 → temp-1751678012022-kysv22ut0 (auto-deleted via CASCADE)
Connection found: Human MDXHA → Ball MDXHB in Length (any multiplier)
Creating connection: Ball MDXHB → Build MDXHC (50.0x)
Selecting AutoComplete option: Ball MDXHB
Using autocomplete prefix: "Ball" for entity: Ball MDXHB
Found autocomplete option: "Ball MDXHBID: 838"
Successfully selected: "Ball MDXHB" using dropdown autocomplete
Selecting AutoComplete option: Build MDXHC
Using autocomplete prefix: "Build" for entity: Build MDXHC
Found autocomplete option: "Build MDXHCID: 839"
Successfully selected: "Build MDXHC" using dropdown autocomplete
Connection creation completed for "Ball MDXHB → Build MDXHC" in 2622ms
  ℹ️  Tracking connection dependency: temp-1751678012022-kysv22ut0 → temp-1751678013597-gcu26359d (auto-deleted via CASCADE)
Connection found: Ball MDXHB → Build MDXHC in Length (any multiplier)
Creating connection: Build MDXHC → Mouse MDXHF (0.1x)
Selecting AutoComplete option: Build MDXHC
Using autocomplete prefix: "Build" for entity: Build MDXHC
Found autocomplete option: "Build MDXHCID: 839"
Successfully selected: "Build MDXHC" using dropdown autocomplete
Selecting AutoComplete option: Mouse MDXHF
Using autocomplete prefix: "Mouse" for entity: Mouse MDXHF
Found autocomplete option: "Mouse MDXHFID: 842"
Successfully selected: "Mouse MDXHF" using dropdown autocomplete
Connection creation completed for "Build MDXHC → Mouse MDXHF" in 2703ms
  ℹ️  Tracking connection dependency: temp-1751678013597-gcu26359d → temp-1751678018317-5r5iqg599 (auto-deleted via CASCADE)
Connection found: Build MDXHC → Mouse MDXHF in Length (any multiplier)
Creating connection: Car MDXHD → Eleph MDXHE (0.3x)
Selecting AutoComplete option: Car MDXHD
Using autocomplete prefix: "Car" for entity: Car MDXHD
Found autocomplete option: "Car MDXHDID: 840"
Successfully selected: "Car MDXHD" using dropdown autocomplete
Selecting AutoComplete option: Eleph MDXHE
Using autocomplete prefix: "Eleph" for entity: Eleph MDXHE
Found autocomplete option: "Eleph MDXHEID: 841"
Successfully selected: "Eleph MDXHE" using dropdown autocomplete
Connection creation completed for "Car MDXHD → Eleph MDXHE" in 2589ms
  ℹ️  Tracking connection dependency: temp-1751678015150-0ao1rrova → temp-1751678016732-ifrqfz215 (auto-deleted via CASCADE)
Connection found: Car MDXHD → Eleph MDXHE in Mass (any multiplier)
Comparing: Human MDXHA to Ball MDXHB (count: 1, unit: Length)
Selecting comparison entity for from field: Human MDXHA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human MDXHA"
Comparison from entity completed: "Human MDXHA" (expected: "Human MDXHA")
Selecting comparison entity for to field: Ball MDXHB
Using prefix "Ball" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Ball MDXHB"
Comparison to entity completed: "Ball MDXHB" (expected: "Ball MDXHB")
Path API Response: http://localhost:8000/api/v1/compare/?from=837&to=838&unit=1 - Status: 200
Entity names not found in result, but calculation successful: Did you know thatis asmeasurebigtallheavylongvoluminousas10.0?
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Human MDXHA (ID: 837)
  ✓ Fallback deleted entity: Ball MDXHB (ID: 838)
  ✓ Fallback deleted entity: Build MDXHC (ID: 839)
  ✓ Fallback deleted entity: Car MDXHD (ID: 840)
  ✓ Fallback deleted entity: Eleph MDXHE (ID: 841)
  ✓ Fallback deleted entity: Mouse MDXHF (ID: 842)
  ℹ️  Fallback cleanup handled 6 additional entities
🧹 Post-test cleanup complete in 33ms (Worker 0):
  • Entities deleted: 6 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 0
  • Test efficiency: 100% (33ms cleanup / 47333ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 0 (3ms)
  ✓  1 [chromium] › e2e/tests/comparisons.spec.ts:145:7 › Entity Comparisons and Pathfinding › should calculate direct relationships (47.6s)
🧹 Starting pre-test cleanup...
  Found 5 total entities in database (Worker 0)
  Identified 0 test entities to delete for Worker 0
🧹 Pre-test cleanup complete in 13ms (Worker 0):
  • Initial entities: 5
  • Entities deleted: 0
  • Failed deletions: 0
  • Final entity count: 5
  • Net reduction: 0
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human WBCVA
Creating entity for complex test: Human WBCVA
Creating entity: Human WBCVA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human WBCVA" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Human WBCVA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human WBCVA" confirmed visible
Entity creation completed for "Human WBCVA" in 1153ms
  ✅ Complex test entity created: Human WBCVA (ID: temp-1751678057981-ri0kqohwl) in 1372ms
✅ Comparison test entity 1 created: Human WBCVA
Creating comparison test entity 2/6: Ball WBCVB
Creating entity for complex test: Ball WBCVB
Creating entity: Ball WBCVB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball WBCVB" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Ball WBCVB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball WBCVB" confirmed visible
Entity creation completed for "Ball WBCVB" in 1124ms
  ✅ Complex test entity created: Ball WBCVB (ID: temp-1751678059530-lnzv91zjz) in 1345ms
✅ Comparison test entity 2 created: Ball WBCVB
Creating comparison test entity 3/6: Build WBCVC
Creating entity for complex test: Build WBCVC
Creating entity: Build WBCVC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build WBCVC" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Build WBCVC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build WBCVC" confirmed visible
Entity creation completed for "Build WBCVC" in 1144ms
  ✅ Complex test entity created: Build WBCVC (ID: temp-1751678061095-yt1u5mkv4) in 1362ms
✅ Comparison test entity 3 created: Build WBCVC
Creating comparison test entity 4/6: Car WBCVD
Creating entity for complex test: Car WBCVD
Creating entity: Car WBCVD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car WBCVD" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Car WBCVD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car WBCVD" confirmed visible
Entity creation completed for "Car WBCVD" in 1108ms
  ✅ Complex test entity created: Car WBCVD (ID: temp-1751678062627-ft4tqqq8h) in 1327ms
✅ Comparison test entity 4 created: Car WBCVD
Creating comparison test entity 5/6: Eleph WBCVE
Creating entity for complex test: Eleph WBCVE
Creating entity: Eleph WBCVE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph WBCVE" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Eleph WBCVE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph WBCVE" confirmed visible
Entity creation completed for "Eleph WBCVE" in 1166ms
  ✅ Complex test entity created: Eleph WBCVE (ID: temp-1751678064218-z9kzolq1b) in 1388ms
✅ Comparison test entity 5 created: Eleph WBCVE
Creating comparison test entity 6/6: Mouse WBCVF
Creating entity for complex test: Mouse WBCVF
Creating entity: Mouse WBCVF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse WBCVF" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Mouse WBCVF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse WBCVF" confirmed visible
Entity creation completed for "Mouse WBCVF" in 1189ms
  ✅ Complex test entity created: Mouse WBCVF (ID: temp-1751678065842-kmdkklow9) in 1416ms
✅ Comparison test entity 6 created: Mouse WBCVF
Creating test connections...
Creating connection: Human WBCVA → Ball WBCVB (10.0x)
Selecting AutoComplete option: Human WBCVA
Using autocomplete prefix: "Human" for entity: Human WBCVA
Found autocomplete option: "Human WBCVAID: 843"
Successfully selected: "Human WBCVA" using dropdown autocomplete
Selecting AutoComplete option: Ball WBCVB
Using autocomplete prefix: "Ball" for entity: Ball WBCVB
Found autocomplete option: "Ball WBCVBID: 844"
Successfully selected: "Ball WBCVB" using dropdown autocomplete
Connection creation completed for "Human WBCVA → Ball WBCVB" in 2649ms
  ℹ️  Tracking connection dependency: temp-1751678057981-ri0kqohwl → temp-1751678059530-lnzv91zjz (auto-deleted via CASCADE)
Connection found: Human WBCVA → Ball WBCVB in Length (any multiplier)
Creating connection: Ball WBCVB → Build WBCVC (50.0x)
Selecting AutoComplete option: Ball WBCVB
Using autocomplete prefix: "Ball" for entity: Ball WBCVB
Found autocomplete option: "Ball WBCVBID: 844"
Successfully selected: "Ball WBCVB" using dropdown autocomplete
Selecting AutoComplete option: Build WBCVC
Using autocomplete prefix: "Build" for entity: Build WBCVC
Found autocomplete option: "Build WBCVCID: 845"
Successfully selected: "Build WBCVC" using dropdown autocomplete
Connection creation completed for "Ball WBCVB → Build WBCVC" in 2661ms
  ℹ️  Tracking connection dependency: temp-1751678059530-lnzv91zjz → temp-1751678061095-yt1u5mkv4 (auto-deleted via CASCADE)
Connection found: Ball WBCVB → Build WBCVC in Length (any multiplier)
Creating connection: Build WBCVC → Mouse WBCVF (0.1x)
Selecting AutoComplete option: Build WBCVC
Using autocomplete prefix: "Build" for entity: Build WBCVC
Found autocomplete option: "Build WBCVCID: 845"
Successfully selected: "Build WBCVC" using dropdown autocomplete
Selecting AutoComplete option: Mouse WBCVF
Using autocomplete prefix: "Mouse" for entity: Mouse WBCVF
Found autocomplete option: "Mouse WBCVFID: 848"
Successfully selected: "Mouse WBCVF" using dropdown autocomplete
Connection creation completed for "Build WBCVC → Mouse WBCVF" in 2709ms
  ℹ️  Tracking connection dependency: temp-1751678061095-yt1u5mkv4 → temp-1751678065842-kmdkklow9 (auto-deleted via CASCADE)
Connection found: Build WBCVC → Mouse WBCVF in Length (any multiplier)
Creating connection: Car WBCVD → Eleph WBCVE (0.3x)
Selecting AutoComplete option: Car WBCVD
Using autocomplete prefix: "Car" for entity: Car WBCVD
Found autocomplete option: "Car WBCVDID: 846"
Successfully selected: "Car WBCVD" using dropdown autocomplete
Selecting AutoComplete option: Eleph WBCVE
Using autocomplete prefix: "Eleph" for entity: Eleph WBCVE
Found autocomplete option: "Eleph WBCVEID: 847"
Successfully selected: "Eleph WBCVE" using dropdown autocomplete
Connection creation completed for "Car WBCVD → Eleph WBCVE" in 2596ms
  ℹ️  Tracking connection dependency: temp-1751678062627-ft4tqqq8h → temp-1751678064218-z9kzolq1b (auto-deleted via CASCADE)
Connection found: Car WBCVD → Eleph WBCVE in Mass (any multiplier)
Comparing: Human WBCVA to Build WBCVC (count: 1, unit: Length)
Selecting comparison entity for from field: Human WBCVA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human WBCVA"
Comparison from entity completed: "Human WBCVA" (expected: "Human WBCVA")
Selecting comparison entity for to field: Build WBCVC
Using prefix "Build" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Build WBCVC"
Comparison to entity completed: "Build WBCVC" (expected: "Build WBCVC")
Path API Response: http://localhost:8000/api/v1/compare/?from=843&to=845&unit=1 - Status: 200
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Human WBCVA (ID: 843)
  ✓ Fallback deleted entity: Ball WBCVB (ID: 844)
  ✓ Fallback deleted entity: Build WBCVC (ID: 845)
  ✓ Fallback deleted entity: Car WBCVD (ID: 846)
  ✓ Fallback deleted entity: Eleph WBCVE (ID: 847)
  ✓ Fallback deleted entity: Mouse WBCVF (ID: 848)
  ℹ️  Fallback cleanup handled 6 additional entities
🧹 Post-test cleanup complete in 32ms (Worker 0):
  • Entities deleted: 6 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 0
  • Test efficiency: 100% (32ms cleanup / 37400ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 0 (3ms)
  ✘  2 [chromium] › e2e/tests/comparisons.spec.ts:165:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships (37.7s)
🧹 Starting pre-test cleanup...
  Found 5 total entities in database (Worker 1)
  Identified 0 test entities to delete for Worker 1
🧹 Pre-test cleanup complete in 25ms (Worker 1):
  • Initial entities: 5
  • Entities deleted: 0
  • Failed deletions: 0
  • Final entity count: 5
  • Net reduction: 0
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human FOVJA
Creating entity for complex test: Human FOVJA
Creating entity: Human FOVJA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human FOVJA" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Human FOVJA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human FOVJA" confirmed visible
Entity creation completed for "Human FOVJA" in 1249ms
  ✅ Complex test entity created: Human FOVJA (ID: temp-1751678096565-2y3uxk3m3) in 1474ms
✅ Comparison test entity 1 created: Human FOVJA
Creating comparison test entity 2/6: Ball FOVJB
Creating entity for complex test: Ball FOVJB
Creating entity: Ball FOVJB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball FOVJB" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Ball FOVJB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball FOVJB" confirmed visible
Entity creation completed for "Ball FOVJB" in 1151ms
  ✅ Complex test entity created: Ball FOVJB (ID: temp-1751678098147-az3gzxd8r) in 1375ms
✅ Comparison test entity 2 created: Ball FOVJB
Creating comparison test entity 3/6: Build FOVJC
Creating entity for complex test: Build FOVJC
Creating entity: Build FOVJC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build FOVJC" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Build FOVJC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build FOVJC" confirmed visible
Entity creation completed for "Build FOVJC" in 1176ms
  ✅ Complex test entity created: Build FOVJC (ID: temp-1751678099747-9rp9ptofb) in 1394ms
✅ Comparison test entity 3 created: Build FOVJC
Creating comparison test entity 4/6: Car FOVJD
Creating entity for complex test: Car FOVJD
Creating entity: Car FOVJD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car FOVJD" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Car FOVJD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car FOVJD" confirmed visible
Entity creation completed for "Car FOVJD" in 1120ms
  ✅ Complex test entity created: Car FOVJD (ID: temp-1751678101294-nkuawsump) in 1341ms
✅ Comparison test entity 4 created: Car FOVJD
Creating comparison test entity 5/6: Eleph FOVJE
Creating entity for complex test: Eleph FOVJE
Creating entity: Eleph FOVJE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph FOVJE" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Eleph FOVJE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph FOVJE" confirmed visible
Entity creation completed for "Eleph FOVJE" in 1142ms
  ✅ Complex test entity created: Eleph FOVJE (ID: temp-1751678102859-voyusdwv1) in 1360ms
✅ Comparison test entity 5 created: Eleph FOVJE
Creating comparison test entity 6/6: Mouse FOVJF
Creating entity for complex test: Mouse FOVJF
Creating entity: Mouse FOVJF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse FOVJF" in UI with browser-specific timeout: 2000ms
  ✓ Entity "Mouse FOVJF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse FOVJF" confirmed visible
Entity creation completed for "Mouse FOVJF" in 1161ms
  ✅ Complex test entity created: Mouse FOVJF (ID: temp-1751678104444-lvdyips5v) in 1380ms
✅ Comparison test entity 6 created: Mouse FOVJF
Creating test connections...
Creating connection: Human FOVJA → Ball FOVJB (10.0x)
Selecting AutoComplete option: Human FOVJA
Using autocomplete prefix: "Human" for entity: Human FOVJA
Found autocomplete option: "Human FOVJAID: 849"
Successfully selected: "Human FOVJA" using dropdown autocomplete
Selecting AutoComplete option: Ball FOVJB
Using autocomplete prefix: "Ball" for entity: Ball FOVJB
Found autocomplete option: "Ball FOVJBID: 850"
Successfully selected: "Ball FOVJB" using dropdown autocomplete
Connection creation completed for "Human FOVJA → Ball FOVJB" in 2651ms
  ℹ️  Tracking connection dependency: temp-1751678096565-2y3uxk3m3 → temp-1751678098147-az3gzxd8r (auto-deleted via CASCADE)
Connection found: Human FOVJA → Ball FOVJB in Length (any multiplier)
Creating connection: Ball FOVJB → Build FOVJC (50.0x)
Selecting AutoComplete option: Ball FOVJB
Using autocomplete prefix: "Ball" for entity: Ball FOVJB
Found autocomplete option: "Ball FOVJBID: 850"
Successfully selected: "Ball FOVJB" using dropdown autocomplete
Selecting AutoComplete option: Build FOVJC
Using autocomplete prefix: "Build" for entity: Build FOVJC
Found autocomplete option: "Build FOVJCID: 851"
Successfully selected: "Build FOVJC" using dropdown autocomplete
Connection creation completed for "Ball FOVJB → Build FOVJC" in 2646ms
  ℹ️  Tracking connection dependency: temp-1751678098147-az3gzxd8r → temp-1751678099747-9rp9ptofb (auto-deleted via CASCADE)
Connection found: Ball FOVJB → Build FOVJC in Length (any multiplier)
Creating connection: Build FOVJC → Mouse FOVJF (0.1x)
Selecting AutoComplete option: Build FOVJC
Using autocomplete prefix: "Build" for entity: Build FOVJC
Found autocomplete option: "Build FOVJCID: 851"
Successfully selected: "Build FOVJC" using dropdown autocomplete
Selecting AutoComplete option: Mouse FOVJF
Using autocomplete prefix: "Mouse" for entity: Mouse FOVJF
Found autocomplete option: "Mouse FOVJFID: 854"
Successfully selected: "Mouse FOVJF" using dropdown autocomplete
Connection creation completed for "Build FOVJC → Mouse FOVJF" in 2665ms
  ℹ️  Tracking connection dependency: temp-1751678099747-9rp9ptofb → temp-1751678104444-lvdyips5v (auto-deleted via CASCADE)
Connection found: Build FOVJC → Mouse FOVJF in Length (any multiplier)
Creating connection: Car FOVJD → Eleph FOVJE (0.3x)
Selecting AutoComplete option: Car FOVJD
Using autocomplete prefix: "Car" for entity: Car FOVJD
Found autocomplete option: "Car FOVJDID: 852"
Successfully selected: "Car FOVJD" using dropdown autocomplete
Selecting AutoComplete option: Eleph FOVJE
Using autocomplete prefix: "Eleph" for entity: Eleph FOVJE
Found autocomplete option: "Eleph FOVJEID: 853"
Successfully selected: "Eleph FOVJE" using dropdown autocomplete
Connection creation completed for "Car FOVJD → Eleph FOVJE" in 2578ms
  ℹ️  Tracking connection dependency: temp-1751678101294-nkuawsump → temp-1751678102859-voyusdwv1 (auto-deleted via CASCADE)
Connection found: Car FOVJD → Eleph FOVJE in Mass (any multiplier)
Comparing: Human FOVJA to Build FOVJC (count: 1, unit: Length)
Selecting comparison entity for from field: Human FOVJA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human FOVJA"
Comparison from entity completed: "Human FOVJA" (expected: "Human FOVJA")
Selecting comparison entity for to field: Build FOVJC
Using prefix "Build" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Build FOVJC"
Comparison to entity completed: "Build FOVJC" (expected: "Build FOVJC")
Path API Response: http://localhost:8000/api/v1/compare/?from=849&to=851&unit=1 - Status: 200
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Human FOVJA (ID: 849)
  ✓ Fallback deleted entity: Ball FOVJB (ID: 850)
  ✓ Fallback deleted entity: Build FOVJC (ID: 851)
  ✓ Fallback deleted entity: Car FOVJD (ID: 852)
  ✓ Fallback deleted entity: Eleph FOVJE (ID: 853)
  ✓ Fallback deleted entity: Mouse FOVJF (ID: 854)
  ℹ️  Fallback cleanup handled 6 additional entities
🧹 Post-test cleanup complete in 41ms (Worker 1):
  • Entities deleted: 6 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 1
  • Test efficiency: 100% (41ms cleanup / 37470ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 1 (4ms)
  ✘  3 [chromium] › e2e/tests/comparisons.spec.ts:165:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships (retry #1) (37.8s)


  1) [chromium] › e2e/tests/comparisons.spec.ts:165:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships 

    TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.

       at ../fixtures/page-objects.ts:1503

      1501 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1502 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1503 |     await this.page.waitForFunction(() => {
           |                     ^
      1504 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1505 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1506 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1503:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:169:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.

       at ../fixtures/page-objects.ts:1503

      1501 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1502 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1503 |     await this.page.waitForFunction(() => {
           |                     ^
      1504 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1505 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1506 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1503:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:169:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

  1 failed
    [chromium] › e2e/tests/comparisons.spec.ts:165:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships 
  1 passed (2.1m)

📊 Sequential test result: ❌ FAILED

❌ SYSTEM ISSUE SUSPECTED: Tests fail even when run sequentially
   → Problem: The application may have actual bugs
   → Action: Manually verify the failing scenarios

