
> simile-frontend@0.1.0 test:e2e
> playwright test --grep connection --reporter=json

{
  "config": {
    "configFile": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/playwright.config.ts",
    "rootDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
    "forbidOnly": false,
    "fullyParallel": true,
    "globalSetup": null,
    "globalTeardown": null,
    "globalTimeout": 7200000,
    "grep": {},
    "grepInvert": null,
    "maxFailures": 10,
    "metadata": {
      "actualWorkers": 3
    },
    "preserveOutput": "always",
    "reporter": [
      [
        "json"
      ]
    ],
    "reportSlowTests": {
      "max": 5,
      "threshold": 300000
    },
    "quiet": false,
    "projects": [
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {
          "actualWorkers": 3
        },
        "id": "chromium",
        "name": "chromium",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {
          "actualWorkers": 3
        },
        "id": "firefox",
        "name": "firefox",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {
          "actualWorkers": 3
        },
        "id": "webkit",
        "name": "webkit",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      }
    ],
    "shard": null,
    "updateSnapshots": "missing",
    "updateSourceMethod": "patch",
    "version": "1.53.1",
    "workers": 3,
    "webServer": {
      "command": "npm start",
      "url": "http://localhost:3000",
      "reuseExistingServer": true,
      "timeout": 120000
    }
  },
  "suites": [
    {
      "title": "comparisons.spec.ts",
      "file": "comparisons.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Entity Comparisons and Pathfinding",
          "file": "comparisons.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should handle different unit entities with no connection",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 35962,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at ConnectionManagerPage.createConnection (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:420:21)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:87:9",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 420
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:420\n\n  418 |     \n  419 |     // Wait for submit button to be enabled - restore working timeout\n> 420 |     await this.page.waitForFunction(() => {\n      |                     ^\n  421 |       const submitBtn = document.querySelector('[data-testid=\"connection-submit-button\"]') as HTMLButtonElement;\n  422 |       return submitBtn && !submitBtn.disabled;\n  423 |     }, { timeout: 10000 }); // Restore working timeout"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 420
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:420\n\n  418 |     \n  419 |     // Wait for submit button to be enabled - restore working timeout\n> 420 |     await this.page.waitForFunction(() => {\n      |                     ^\n  421 |       const submitBtn = document.querySelector('[data-testid=\"connection-submit-button\"]') as HTMLButtonElement;\n  422 |       return submitBtn && !submitBtn.disabled;\n  423 |     }, { timeout: 10000 }); // Restore working timeout\n    at ConnectionManagerPage.createConnection (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:420:21)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:87:9"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 11 total entities in database (Worker 0)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 0\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms (Worker 0):\n"
                        },
                        {
                          "text": "  • Initial entities: 11\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating entity: Human NYGNA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human NYGNA\" in 2207ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human NYGNA (ID: temp-1751212493467-9561g0bnn)\n"
                        },
                        {
                          "text": "Creating entity: Ball NYGNB\n"
                        },
                        {
                          "text": "Entity creation completed for \"Ball NYGNB\" in 2028ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Ball NYGNB (ID: temp-1751212495998-c0h32klln)\n"
                        },
                        {
                          "text": "Creating entity: Build NYGNC\n"
                        },
                        {
                          "text": "Entity creation completed for \"Build NYGNC\" in 2132ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Build NYGNC (ID: temp-1751212498635-yvhq602a3)\n"
                        },
                        {
                          "text": "Creating entity: Car NYGND\n"
                        },
                        {
                          "text": "Entity creation completed for \"Car NYGND\" in 1911ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Car NYGND (ID: temp-1751212501048-rupt6f6ef)\n"
                        },
                        {
                          "text": "Creating entity: Eleph NYGNE\n"
                        },
                        {
                          "text": "Entity creation completed for \"Eleph NYGNE\" in 2116ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Eleph NYGNE (ID: temp-1751212503666-uokwzc9ta)\n"
                        },
                        {
                          "text": "Creating entity: Mouse NYGNF\n"
                        },
                        {
                          "text": "Entity creation completed for \"Mouse NYGNF\" in 2110ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Mouse NYGNF (ID: temp-1751212506280-c4h39jyqt)\n"
                        },
                        {
                          "text": "Creating test connections...\n"
                        },
                        {
                          "text": "Creating connection: Human NYGNA → Ball NYGNB (10.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Human NYGNA\n"
                        },
                        {
                          "text": "Dropdown failed, using direct input for: Human NYGNA\n"
                        },
                        {
                          "text": "Successfully set input value: Human NYGNA\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball NYGNB\n"
                        },
                        {
                          "text": "Dropdown failed, using direct input for: Ball NYGNB\n"
                        },
                        {
                          "text": "Successfully set input value: Ball NYGNB\n"
                        },
                        {
                          "text": "Connection creation failed for Human NYGNA → Ball NYGNB, checking if it exists...\n"
                        },
                        {
                          "text": "Connection not found: Human NYGNA → Ball NYGNB (10.0x) in Length\n"
                        },
                        {
                          "text": "Connection list content: Connections (8)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Uni...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 6 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 15ms (Worker 0):\n"
                        },
                        {
                          "text": "  • Entities deleted: 0 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 0\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (15ms cleanup / 35727ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 0 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:54:50.411Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 420
                      }
                    },
                    {
                      "workerIndex": 9,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 35945,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at ConnectionManagerPage.createConnection (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:420:21)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:87:9",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 420
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:420\n\n  418 |     \n  419 |     // Wait for submit button to be enabled - restore working timeout\n> 420 |     await this.page.waitForFunction(() => {\n      |                     ^\n  421 |       const submitBtn = document.querySelector('[data-testid=\"connection-submit-button\"]') as HTMLButtonElement;\n  422 |       return submitBtn && !submitBtn.disabled;\n  423 |     }, { timeout: 10000 }); // Restore working timeout"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 420
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:420\n\n  418 |     \n  419 |     // Wait for submit button to be enabled - restore working timeout\n> 420 |     await this.page.waitForFunction(() => {\n      |                     ^\n  421 |       const submitBtn = document.querySelector('[data-testid=\"connection-submit-button\"]') as HTMLButtonElement;\n  422 |       return submitBtn && !submitBtn.disabled;\n  423 |     }, { timeout: 10000 }); // Restore working timeout\n    at ConnectionManagerPage.createConnection (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:420:21)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:87:9"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 11 total entities in database (Worker 9)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 9\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 22ms (Worker 9):\n"
                        },
                        {
                          "text": "  • Initial entities: 11\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating entity: Human EEFDA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human EEFDA\" in 2158ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human EEFDA (ID: temp-1751212529798-4ru704whh)\n"
                        },
                        {
                          "text": "Creating entity: Ball EEFDB\n"
                        },
                        {
                          "text": "Entity creation completed for \"Ball EEFDB\" in 2018ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Ball EEFDB (ID: temp-1751212532320-l85ru1i1y)\n"
                        },
                        {
                          "text": "Creating entity: Build EEFDC\n"
                        },
                        {
                          "text": "Entity creation completed for \"Build EEFDC\" in 2133ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Build EEFDC (ID: temp-1751212534966-5qjf11ziy)\n"
                        },
                        {
                          "text": "Creating entity: Car EEFDD\n"
                        },
                        {
                          "text": "Entity creation completed for \"Car EEFDD\" in 1911ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Car EEFDD (ID: temp-1751212537381-e2a10fv6v)\n"
                        },
                        {
                          "text": "Creating entity: Eleph EEFDE\n"
                        },
                        {
                          "text": "Entity creation completed for \"Eleph EEFDE\" in 2112ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Eleph EEFDE (ID: temp-1751212539995-8r392dw7n)\n"
                        },
                        {
                          "text": "Creating entity: Mouse EEFDF\n"
                        },
                        {
                          "text": "Entity creation completed for \"Mouse EEFDF\" in 2139ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Mouse EEFDF (ID: temp-1751212542638-r969bxob6)\n"
                        },
                        {
                          "text": "Creating test connections...\n"
                        },
                        {
                          "text": "Creating connection: Human EEFDA → Ball EEFDB (10.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Human EEFDA\n"
                        },
                        {
                          "text": "Dropdown failed, using direct input for: Human EEFDA\n"
                        },
                        {
                          "text": "Successfully set input value: Human EEFDA\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball EEFDB\n"
                        },
                        {
                          "text": "Dropdown failed, using direct input for: Ball EEFDB\n"
                        },
                        {
                          "text": "Successfully set input value: Ball EEFDB\n"
                        },
                        {
                          "text": "Connection creation failed for Human EEFDA → Ball EEFDB, checking if it exists...\n"
                        },
                        {
                          "text": "Connection not found: Human EEFDA → Ball EEFDB (10.0x) in Length\n"
                        },
                        {
                          "text": "Connection list content: Connections (8)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Uni...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 6 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 12ms (Worker 9):\n"
                        },
                        {
                          "text": "  • Entities deleted: 0 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 9\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (12ms cleanup / 35729ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 9 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:55:26.852Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 420
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-8dd14ae43fbe55b3930e",
              "file": "comparisons.spec.ts",
              "line": 196,
              "column": 7
            },
            {
              "title": "should handle different unit entities with no connection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-4cf38eb6479501bb3f92",
              "file": "comparisons.spec.ts",
              "line": 196,
              "column": 7
            },
            {
              "title": "should handle different unit entities with no connection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-3a8a0803f811373c8495",
              "file": "comparisons.spec.ts",
              "line": 196,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "connections.spec.ts",
      "file": "connections.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Connection Management",
          "file": "connections.spec.ts",
          "line": 6,
          "column": 6,
          "specs": [
            {
              "title": "should display connection management page correctly",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 1,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 11131,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 11 total entities in database (Worker 1)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 1\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 26ms (Worker 1):\n"
                        },
                        {
                          "text": "  • Initial entities: 11\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating entity: Human Test BXDHR\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test BXDHR\" in 2725ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test BXDHR (ID: temp-1751212493985-6vklr9rkx)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  BXQPM\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test BXDHR (ID: 225)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 21ms (Worker 1):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 1\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (21ms cleanup / 10985ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 1 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:54:50.410Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-833d1-n-management-page-correctly-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-833d1-n-management-page-correctly-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-833d1-n-management-page-correctly-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-833d1-n-management-page-correctly-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    },
                    {
                      "workerIndex": 3,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 11099,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 15 total entities in database (Worker 3)\n"
                        },
                        {
                          "text": "  Identified 4 test entities to delete for Worker 3\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Build NYGNC (ID: 227)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Car NYGND (ID: 228)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Ball NYGNB (ID: 226)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human NYGNA (ID: 223)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 59ms (Worker 3):\n"
                        },
                        {
                          "text": "  • Initial entities: 15\n"
                        },
                        {
                          "text": "  • Entities deleted: 4\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 4\n"
                        },
                        {
                          "text": "Creating entity: Human Test DXFWY\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test DXFWY\" in 2684ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test DXFWY (ID: temp-1751212505486-dlply0t6h)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  DZDJQ\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test DXFWY (ID: 230)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 20ms (Worker 3):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 3\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (20ms cleanup / 10946ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 3 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:55:01.951Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-833d1-n-management-page-correctly-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-833d1-n-management-page-correctly-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-833d1-n-management-page-correctly-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-833d1-n-management-page-correctly-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "b748afb17871306c20bc-89cf8d0005d7ae37c3c7",
              "file": "connections.spec.ts",
              "line": 44,
              "column": 7
            },
            {
              "title": "should show and hide connection form correctly",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 2,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 11131,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 11 total entities in database (Worker 2)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 2\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 26ms (Worker 2):\n"
                        },
                        {
                          "text": "  • Initial entities: 11\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating entity: Human Test CHBBF\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test CHBBF\" in 2727ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test CHBBF (ID: temp-1751212493985-crkd6vie3)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  COQAE\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test CHBBF (ID: 224)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 21ms (Worker 2):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 2\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (21ms cleanup / 10985ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 2 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:54:50.410Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-e7ca1-e-connection-form-correctly-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-e7ca1-e-connection-form-correctly-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-e7ca1-e-connection-form-correctly-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-e7ca1-e-connection-form-correctly-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    },
                    {
                      "workerIndex": 4,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 11099,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 15 total entities in database (Worker 4)\n"
                        },
                        {
                          "text": "  Identified 4 test entities to delete for Worker 4\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Car NYGND (ID: 228)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Build NYGNC (ID: 227)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Ball NYGNB (ID: 226)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human NYGNA (ID: 223)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 59ms (Worker 4):\n"
                        },
                        {
                          "text": "  • Initial entities: 15\n"
                        },
                        {
                          "text": "  • Entities deleted: 4\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 4\n"
                        },
                        {
                          "text": "Creating entity: Human Test EVLGW\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test EVLGW\" in 2683ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test EVLGW (ID: temp-1751212505486-0z39uvkpz)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  EQCZH\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test EVLGW (ID: 231)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 20ms (Worker 4):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 4\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (20ms cleanup / 10946ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 4 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:55:01.951Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-e7ca1-e-connection-form-correctly-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-e7ca1-e-connection-form-correctly-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-e7ca1-e-connection-form-correctly-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-e7ca1-e-connection-form-correctly-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "b748afb17871306c20bc-1f5e0a82fd166256a222",
              "file": "connections.spec.ts",
              "line": 50,
              "column": 7
            },
            {
              "title": "should create bidirectional connections",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 11142,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 13 total entities in database (Worker 6)\n"
                        },
                        {
                          "text": "  Identified 2 test entities to delete for Worker 6\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Mouse NYGNF (ID: 232)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Eleph NYGNE (ID: 229)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 34ms (Worker 6):\n"
                        },
                        {
                          "text": "  • Initial entities: 13\n"
                        },
                        {
                          "text": "  • Entities deleted: 2\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 2\n"
                        },
                        {
                          "text": "Creating entity: Human Test GVOBW\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test GVOBW\" in 2731ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test GVOBW (ID: temp-1751212517020-ie2haybbd)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  GRBMO\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test GVOBW (ID: 233)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 20ms (Worker 6):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 6\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (20ms cleanup / 11017ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 6 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:55:13.455Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-37156-e-bidirectional-connections-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-37156-e-bidirectional-connections-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-37156-e-bidirectional-connections-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-37156-e-bidirectional-connections-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    },
                    {
                      "workerIndex": 7,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 11074,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 11 total entities in database (Worker 7)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 7\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 26ms (Worker 7):\n"
                        },
                        {
                          "text": "  • Initial entities: 11\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating entity: Human Test HNUAH\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test HNUAH\" in 2689ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test HNUAH (ID: temp-1751212528485-kovspnumf)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  HRHDQ\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test HNUAH (ID: 236)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 21ms (Worker 7):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 7\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (21ms cleanup / 10948ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 7 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:55:24.977Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-37156-e-bidirectional-connections-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-37156-e-bidirectional-connections-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-37156-e-bidirectional-connections-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-37156-e-bidirectional-connections-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "b748afb17871306c20bc-7147252b7e2bb76da71a",
              "file": "connections.spec.ts",
              "line": 66,
              "column": 7
            },
            {
              "title": "should validate positive relationship values with real-time validation",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 5,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 11140,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 13 total entities in database (Worker 5)\n"
                        },
                        {
                          "text": "  Identified 2 test entities to delete for Worker 5\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Eleph NYGNE (ID: 229)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Mouse NYGNF (ID: 232)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 34ms (Worker 5):\n"
                        },
                        {
                          "text": "  • Initial entities: 13\n"
                        },
                        {
                          "text": "  • Entities deleted: 2\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 2\n"
                        },
                        {
                          "text": "Creating entity: Human Test FICXL\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test FICXL\" in 2731ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test FICXL (ID: temp-1751212517020-frtawwlv9)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  FHUWV\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test FICXL (ID: 234)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 19ms (Worker 5):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 5\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (19ms cleanup / 11016ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 5 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:55:13.455Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-d1327-s-with-real-time-validation-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-d1327-s-with-real-time-validation-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-d1327-s-with-real-time-validation-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-d1327-s-with-real-time-validation-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    },
                    {
                      "workerIndex": 8,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 11074,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 11 total entities in database (Worker 8)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 8\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 25ms (Worker 8):\n"
                        },
                        {
                          "text": "  • Initial entities: 11\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating entity: Human Test IBVOK\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test IBVOK\" in 2691ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test IBVOK (ID: temp-1751212528485-4jt6re317)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  IQGIK\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test IBVOK (ID: 235)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 20ms (Worker 8):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 8\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (20ms cleanup / 10948ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 8 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:55:24.977Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-d1327-s-with-real-time-validation-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-d1327-s-with-real-time-validation-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-d1327-s-with-real-time-validation-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-d1327-s-with-real-time-validation-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "b748afb17871306c20bc-4389931d41d61e987072",
              "file": "connections.spec.ts",
              "line": 95,
              "column": 7
            },
            {
              "title": "should validate decimal precision with real-time validation",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 10,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 11101,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 14 total entities in database (Worker 10)\n"
                        },
                        {
                          "text": "  Identified 3 test entities to delete for Worker 10\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Ball EEFDB (ID: 238)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human EEFDA (ID: 237)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Build EEFDC (ID: 239)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 35ms (Worker 10):\n"
                        },
                        {
                          "text": "  • Initial entities: 14\n"
                        },
                        {
                          "text": "  • Entities deleted: 3\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 3\n"
                        },
                        {
                          "text": "Creating entity: Human Test KHGCC\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test KHGCC\" in 2700ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test KHGCC (ID: temp-1751212539963-v45dcnuk2)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  KPULR\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test KHGCC (ID: 242)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 20ms (Worker 10):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 10\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (20ms cleanup / 10977ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 10 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:55:36.439Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-564e3-n-with-real-time-validation-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-564e3-n-with-real-time-validation-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-564e3-n-with-real-time-validation-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-564e3-n-with-real-time-validation-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    },
                    {
                      "workerIndex": 12,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 11067,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 14 total entities in database (Worker 12)\n"
                        },
                        {
                          "text": "  Identified 3 test entities to delete for Worker 12\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Car EEFDD (ID: 240)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Mouse EEFDF (ID: 244)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Eleph EEFDE (ID: 243)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 35ms (Worker 12):\n"
                        },
                        {
                          "text": "  • Initial entities: 14\n"
                        },
                        {
                          "text": "  • Entities deleted: 3\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 3\n"
                        },
                        {
                          "text": "Creating entity: Human Test MZXCC\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test MZXCC\" in 2678ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test MZXCC (ID: temp-1751212551417-w2xcaganz)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  MDZRY\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test MZXCC (ID: 245)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 20ms (Worker 12):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 12\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (20ms cleanup / 10951ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 12 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:55:47.926Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-564e3-n-with-real-time-validation-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-564e3-n-with-real-time-validation-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-564e3-n-with-real-time-validation-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-564e3-n-with-real-time-validation-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "b748afb17871306c20bc-10850e19363b0ff9346c",
              "file": "connections.spec.ts",
              "line": 139,
              "column": 7
            },
            {
              "title": "should prevent zero multiplier values",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 11,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 11103,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 14 total entities in database (Worker 11)\n"
                        },
                        {
                          "text": "  Identified 3 test entities to delete for Worker 11\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Ball EEFDB (ID: 238)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Build EEFDC (ID: 239)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human EEFDA (ID: 237)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 35ms (Worker 11):\n"
                        },
                        {
                          "text": "  • Initial entities: 14\n"
                        },
                        {
                          "text": "  • Entities deleted: 3\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 3\n"
                        },
                        {
                          "text": "Creating entity: Human Test LBFUO\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test LBFUO\" in 2701ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test LBFUO (ID: temp-1751212539964-8i3eutvdl)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  LLIHK\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test LBFUO (ID: 241)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 20ms (Worker 11):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 11\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (20ms cleanup / 10977ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 11 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:55:36.439Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-f861d-vent-zero-multiplier-values-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-f861d-vent-zero-multiplier-values-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-f861d-vent-zero-multiplier-values-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-f861d-vent-zero-multiplier-values-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    },
                    {
                      "workerIndex": 13,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 11067,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 14 total entities in database (Worker 13)\n"
                        },
                        {
                          "text": "  Identified 3 test entities to delete for Worker 13\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Car EEFDD (ID: 240)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Eleph EEFDE (ID: 243)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Mouse EEFDF (ID: 244)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 35ms (Worker 13):\n"
                        },
                        {
                          "text": "  • Initial entities: 14\n"
                        },
                        {
                          "text": "  • Entities deleted: 3\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 3\n"
                        },
                        {
                          "text": "Creating entity: Human Test NHEFY\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test NHEFY\" in 2674ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test NHEFY (ID: temp-1751212551417-kt10c7vmu)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  NTDHM\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test NHEFY (ID: 246)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 20ms (Worker 13):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 13\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (20ms cleanup / 10951ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 13 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:55:47.926Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-f861d-vent-zero-multiplier-values-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-f861d-vent-zero-multiplier-values-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-f861d-vent-zero-multiplier-values-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-f861d-vent-zero-multiplier-values-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "b748afb17871306c20bc-3625df6327a9fcf271ac",
              "file": "connections.spec.ts",
              "line": 181,
              "column": 7
            },
            {
              "title": "should validate same-unit connections only",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 14,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 11052,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 11 total entities in database (Worker 14)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 14\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms (Worker 14):\n"
                        },
                        {
                          "text": "  • Initial entities: 11\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating entity: Human Test OBJRF\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test OBJRF\" in 2712ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test OBJRF (ID: temp-1751212562894-omw7g1stq)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  OBKRQ\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 40ms (Worker 14):\n"
                        },
                        {
                          "text": "  • Entities deleted: 0 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 14\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (40ms cleanup / 10929ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 14 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:55:59.371Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-a6ace--same-unit-connections-only-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-a6ace--same-unit-connections-only-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-a6ace--same-unit-connections-only-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-a6ace--same-unit-connections-only-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    },
                    {
                      "workerIndex": 17,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 11089,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 12 total entities in database (Worker 17)\n"
                        },
                        {
                          "text": "  Identified 1 test entities to delete for Worker 17\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human Test QWZKD (ID: 249)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 33ms (Worker 17):\n"
                        },
                        {
                          "text": "  • Initial entities: 12\n"
                        },
                        {
                          "text": "  • Entities deleted: 1\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 1\n"
                        },
                        {
                          "text": "Creating entity: Human Test RPDLS\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test RPDLS\" in 2713ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test RPDLS (ID: temp-1751212574346-jl448b8xd)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  RTGJJ\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 15ms (Worker 17):\n"
                        },
                        {
                          "text": "  • Entities deleted: 0 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 17\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (15ms cleanup / 10964ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 17 (6ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:56:10.813Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-a6ace--same-unit-connections-only-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-a6ace--same-unit-connections-only-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-a6ace--same-unit-connections-only-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-a6ace--same-unit-connections-only-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "b748afb17871306c20bc-8c3f4a4f0186bb588024",
              "file": "connections.spec.ts",
              "line": 213,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity selection",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 15,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 11050,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 11 total entities in database (Worker 15)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 15\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms (Worker 15):\n"
                        },
                        {
                          "text": "  • Initial entities: 11\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating entity: Human Test POPID\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test POPID\" in 2697ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test POPID (ID: temp-1751212562877-pjuka21gn)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  PTOOE\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 40ms (Worker 15):\n"
                        },
                        {
                          "text": "  • Entities deleted: 0 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 15\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (40ms cleanup / 10929ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 15 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:55:59.371Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-528fb-omplete-in-entity-selection-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-528fb-omplete-in-entity-selection-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-528fb-omplete-in-entity-selection-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-528fb-omplete-in-entity-selection-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    },
                    {
                      "workerIndex": 18,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 11089,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 12 total entities in database (Worker 18)\n"
                        },
                        {
                          "text": "  Identified 1 test entities to delete for Worker 18\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human Test QWZKD (ID: 249)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 33ms (Worker 18):\n"
                        },
                        {
                          "text": "  • Initial entities: 12\n"
                        },
                        {
                          "text": "  • Entities deleted: 1\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 1\n"
                        },
                        {
                          "text": "Creating entity: Human Test SZLMC\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test SZLMC\" in 2713ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test SZLMC (ID: temp-1751212574346-rtzx7eo61)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  SRBPD\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 15ms (Worker 18):\n"
                        },
                        {
                          "text": "  • Entities deleted: 0 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 18\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (15ms cleanup / 10964ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 18 (7ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:56:10.813Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-528fb-omplete-in-entity-selection-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-528fb-omplete-in-entity-selection-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-528fb-omplete-in-entity-selection-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-528fb-omplete-in-entity-selection-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "b748afb17871306c20bc-c4355c1749789a1f565a",
              "file": "connections.spec.ts",
              "line": 234,
              "column": 7
            },
            {
              "title": "should delete a connection successfully",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 16,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 11077,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 13 total entities in database (Worker 16)\n"
                        },
                        {
                          "text": "  Identified 2 test entities to delete for Worker 16\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human Test OBJRF (ID: 248)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human Test POPID (ID: 247)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 30ms (Worker 16):\n"
                        },
                        {
                          "text": "  • Initial entities: 13\n"
                        },
                        {
                          "text": "  • Entities deleted: 2\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 2\n"
                        },
                        {
                          "text": "Creating entity: Human Test QWZKD\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test QWZKD\" in 2705ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test QWZKD (ID: temp-1751212566762-0y0ee0g1m)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  QESGF\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 15ms (Worker 16):\n"
                        },
                        {
                          "text": "  • Entities deleted: 0 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 16\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (15ms cleanup / 10967ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 16 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:56:03.266Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5d9d4-e-a-connection-successfully-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5d9d4-e-a-connection-successfully-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5d9d4-e-a-connection-successfully-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5d9d4-e-a-connection-successfully-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    },
                    {
                      "workerIndex": 19,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 11031,
                      "error": {
                        "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.",
                        "stack": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 21,
                          "line": 80
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 21,
                            "line": 80
                          },
                          "message": "TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.\n\n   at ../fixtures/page-objects.ts:80\n\n  78 |     \n  79 |     // Wait for button to be enabled with more robust checking\n> 80 |     await this.page.waitForFunction(() => {\n     |                     ^\n  81 |       const button = document.querySelector('[data-testid=\"entity-submit-button\"]');\n  82 |       return button && !button.disabled;\n  83 |     }, { timeout: 5000 });\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:80:21)\n    at TestHelpers.createAndTrackEntityWithId (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:772:5)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/connections.spec.ts:29:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 13 total entities in database (Worker 19)\n"
                        },
                        {
                          "text": "  Identified 2 test entities to delete for Worker 19\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human Test SZLMC (ID: 250)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human Test RPDLS (ID: 251)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 30ms (Worker 19):\n"
                        },
                        {
                          "text": "  • Initial entities: 13\n"
                        },
                        {
                          "text": "  • Entities deleted: 2\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 2\n"
                        },
                        {
                          "text": "Creating entity: Human Test TKWGV\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test TKWGV\" in 2680ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test TKWGV (ID: temp-1751212578215-jb0z1fxw3)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  TCLNY\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 14ms (Worker 19):\n"
                        },
                        {
                          "text": "  • Entities deleted: 0 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 19\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (14ms cleanup / 10927ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 19 (5ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-06-29T15:56:14.734Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5d9d4-e-a-connection-successfully-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5d9d4-e-a-connection-successfully-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5d9d4-e-a-connection-successfully-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5d9d4-e-a-connection-successfully-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 21,
                        "line": 80
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "b748afb17871306c20bc-5641ac6865251a11ba7f",
              "file": "connections.spec.ts",
              "line": 279,
              "column": 7
            },
            {
              "title": "should handle connection form validation for required fields with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 20,
                      "parallelIndex": 1,
                      "status": "interrupted",
                      "duration": 3645,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 12 total entities in database (Worker 20)\n"
                        },
                        {
                          "text": "  Identified 1 test entities to delete for Worker 20\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human Test TKWGV (ID: 252)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 31ms (Worker 20):\n"
                        },
                        {
                          "text": "  • Initial entities: 12\n"
                        },
                        {
                          "text": "  • Entities deleted: 1\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 1\n"
                        },
                        {
                          "text": "Creating entity: Human Test URLRR\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test URLRR\" in 2732ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test URLRR (ID: temp-1751212585834-uu5yefalb)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  UVGSJ\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test URLRR (ID: 253)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 20ms (Worker 20):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 20\n"
                        },
                        {
                          "text": "  • Test efficiency: 99% (20ms cleanup / 3538ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 20 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:56:22.270Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5f615-lds-with-real-time-feedback-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5f615-lds-with-real-time-feedback-chromium/video.webm"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-5f615-lds-with-real-time-feedback-chromium/trace.zip"
                        }
                      ]
                    }
                  ],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-b09906742cede2456c84",
              "file": "connections.spec.ts",
              "line": 305,
              "column": 7
            },
            {
              "title": "should show real-time validation states across all connection form fields",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 21,
                      "parallelIndex": 2,
                      "status": "interrupted",
                      "duration": 3647,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 12 total entities in database (Worker 21)\n"
                        },
                        {
                          "text": "  Identified 1 test entities to delete for Worker 21\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Human Test TKWGV (ID: 252)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 30ms (Worker 21):\n"
                        },
                        {
                          "text": "  • Initial entities: 12\n"
                        },
                        {
                          "text": "  • Entities deleted: 1\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 11\n"
                        },
                        {
                          "text": "  • Net reduction: 1\n"
                        },
                        {
                          "text": "Creating entity: Human Test VMWWG\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human Test VMWWG\" in 2728ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human Test VMWWG (ID: temp-1751212585831-21py172bb)\n"
                        },
                        {
                          "text": "Creating entity: Basketball  VQHGI\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human Test VMWWG (ID: 254)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 19ms (Worker 21):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 21\n"
                        },
                        {
                          "text": "  • Test efficiency: 99% (19ms cleanup / 3537ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 21 (4ms)\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-29T15:56:22.270Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-72bb4--all-connection-form-fields-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-72bb4--all-connection-form-fields-chromium/video.webm"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/connections-Connection-Man-72bb4--all-connection-form-fields-chromium/trace.zip"
                        }
                      ]
                    }
                  ],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-9522dbd52555b4fec117",
              "file": "connections.spec.ts",
              "line": 359,
              "column": 7
            },
            {
              "title": "should prevent duplicate connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-a2d4c3e92f2a9e66de0f",
              "file": "connections.spec.ts",
              "line": 428,
              "column": 7
            },
            {
              "title": "should handle rapid connection creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-3d7c0be7bf1892ba5445",
              "file": "connections.spec.ts",
              "line": 451,
              "column": 7
            },
            {
              "title": "should maintain connection list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-8ef74e2d22ee76d96ebc",
              "file": "connections.spec.ts",
              "line": 473,
              "column": 7
            },
            {
              "title": "should display connection management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-b6c5a9e69b8e3a64823c",
              "file": "connections.spec.ts",
              "line": 44,
              "column": 7
            },
            {
              "title": "should show and hide connection form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-9354c5ece45689452f22",
              "file": "connections.spec.ts",
              "line": 50,
              "column": 7
            },
            {
              "title": "should create bidirectional connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-9f5a2be5c109466e3caf",
              "file": "connections.spec.ts",
              "line": 66,
              "column": 7
            },
            {
              "title": "should validate positive relationship values with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-49e49d5270b2c898833a",
              "file": "connections.spec.ts",
              "line": 95,
              "column": 7
            },
            {
              "title": "should validate decimal precision with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d2e12139574eef4fb70c",
              "file": "connections.spec.ts",
              "line": 139,
              "column": 7
            },
            {
              "title": "should prevent zero multiplier values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-6312f2558cdbf8c285ad",
              "file": "connections.spec.ts",
              "line": 181,
              "column": 7
            },
            {
              "title": "should validate same-unit connections only",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-23188e2309ede443f729",
              "file": "connections.spec.ts",
              "line": 213,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d222a1bb2b75cb8fa3c4",
              "file": "connections.spec.ts",
              "line": 234,
              "column": 7
            },
            {
              "title": "should delete a connection successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-6108fff9474bb4422d2b",
              "file": "connections.spec.ts",
              "line": 279,
              "column": 7
            },
            {
              "title": "should handle connection form validation for required fields with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-e723355a0daa8c0870de",
              "file": "connections.spec.ts",
              "line": 305,
              "column": 7
            },
            {
              "title": "should show real-time validation states across all connection form fields",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-58e91db3df14f9685e4b",
              "file": "connections.spec.ts",
              "line": 359,
              "column": 7
            },
            {
              "title": "should prevent duplicate connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d8dbe01708fa4d161591",
              "file": "connections.spec.ts",
              "line": 428,
              "column": 7
            },
            {
              "title": "should handle rapid connection creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-12cbcef4239b401ae054",
              "file": "connections.spec.ts",
              "line": 451,
              "column": 7
            },
            {
              "title": "should maintain connection list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-04fa295f72d5f01599f9",
              "file": "connections.spec.ts",
              "line": 473,
              "column": 7
            },
            {
              "title": "should display connection management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-3a83219f65d5c160e751",
              "file": "connections.spec.ts",
              "line": 44,
              "column": 7
            },
            {
              "title": "should show and hide connection form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-0eb551b4b22c895004ab",
              "file": "connections.spec.ts",
              "line": 50,
              "column": 7
            },
            {
              "title": "should create bidirectional connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-2bdc409db41585270be9",
              "file": "connections.spec.ts",
              "line": 66,
              "column": 7
            },
            {
              "title": "should validate positive relationship values with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-2e14dc6f0c685d8908af",
              "file": "connections.spec.ts",
              "line": 95,
              "column": 7
            },
            {
              "title": "should validate decimal precision with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-68b874b02eebf5411f39",
              "file": "connections.spec.ts",
              "line": 139,
              "column": 7
            },
            {
              "title": "should prevent zero multiplier values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-62bf02938e24c360b22c",
              "file": "connections.spec.ts",
              "line": 181,
              "column": 7
            },
            {
              "title": "should validate same-unit connections only",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-06fe7ade318cd6c1d1e7",
              "file": "connections.spec.ts",
              "line": 213,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d3e881b0728a64594ac0",
              "file": "connections.spec.ts",
              "line": 234,
              "column": 7
            },
            {
              "title": "should delete a connection successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-5eef9928df27181c2779",
              "file": "connections.spec.ts",
              "line": 279,
              "column": 7
            },
            {
              "title": "should handle connection form validation for required fields with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-8cab69022afb1c838d6c",
              "file": "connections.spec.ts",
              "line": 305,
              "column": 7
            },
            {
              "title": "should show real-time validation states across all connection form fields",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-a45806e7499f53dc3fbe",
              "file": "connections.spec.ts",
              "line": 359,
              "column": 7
            },
            {
              "title": "should prevent duplicate connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-6989eaec183fff268824",
              "file": "connections.spec.ts",
              "line": 428,
              "column": 7
            },
            {
              "title": "should handle rapid connection creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-2ca10f4cc13fa70b66a8",
              "file": "connections.spec.ts",
              "line": 451,
              "column": 7
            },
            {
              "title": "should maintain connection list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-82bed2f3416f50137897",
              "file": "connections.spec.ts",
              "line": 473,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "error-handling.spec.ts",
      "file": "error-handling.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Error Handling and Edge Cases",
          "file": "error-handling.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should handle extremely large numbers in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-3d554af004bd5a9751e0",
              "file": "error-handling.spec.ts",
              "line": 289,
              "column": 7
            },
            {
              "title": "should handle scientific notation in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ca613b308615c4034ba0",
              "file": "error-handling.spec.ts",
              "line": 311,
              "column": 7
            },
            {
              "title": "should handle extremely large numbers in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-4b9de9ff0248d911f70d",
              "file": "error-handling.spec.ts",
              "line": 289,
              "column": 7
            },
            {
              "title": "should handle scientific notation in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-d94114d7ae42fec859d4",
              "file": "error-handling.spec.ts",
              "line": 311,
              "column": 7
            },
            {
              "title": "should handle extremely large numbers in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-63a565e819ec1f488c6f",
              "file": "error-handling.spec.ts",
              "line": 289,
              "column": 7
            },
            {
              "title": "should handle scientific notation in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-2d6b4b2cca2e87610cae",
              "file": "error-handling.spec.ts",
              "line": 311,
              "column": 7
            }
          ]
        }
      ]
    }
  ],
  "errors": [
    {
      "message": "\u001b[31mTesting stopped early after 10 maximum allowed failures.\u001b[39m"
    }
  ],
  "stats": {
    "startTime": "2025-06-29T15:54:50.115Z",
    "duration": 95919.493,
    "expected": 0,
    "skipped": 41,
    "unexpected": 10,
    "flaky": 0
  }
}
