<testsuites id="" name="" tests="294" failures="10" skipped="266" errors="0" time="66.796453">
<testsuite name="backend-connectivity-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="10" failures="2" skipped="0" time="4.929" errors="0">
<testcase name="Backend Connectivity Validation › should verify backend API endpoints are accessible" classname="backend-connectivity-validation.spec.ts" time="0.177">
<system-out>
<![CDATA[🔍 Testing backend API accessibility...
🔍 Testing backend connectivity...
  ✅ units: 200
  ✅ entities: 200
  ✅ connections: 200
  ✅ health: 200
🔍 Backend connectivity: HEALTHY
✅ All backend endpoints are accessible
]]>
</system-out>
</testcase>
<testcase name="Backend Connectivity Validation › should validate API stability and response times" classname="backend-connectivity-validation.spec.ts" time="1.227">
<system-out>
<![CDATA[🔍 Testing API stability...
🔍 Testing API stability...
🔍 API stability: STABLE (0/10 errors, avg 6ms)
✅ API is stable with 6ms average response time
]]>
</system-out>
</testcase>
<testcase name="Backend Connectivity Validation › should create entities without duplicate name conflicts" classname="backend-connectivity-validation.spec.ts" time="0.764">
<system-out>
<![CDATA[🔍 Testing entity creation without conflicts...
Generated unique entity name: Confli W A AAB O (from prefix: ConflictTest0, counter: 1)
Generated unique entity name: Confli W A AAC G (from prefix: ConflictTest1, counter: 2)
Generated unique entity name: Confli W A AAD S (from prefix: ConflictTest2, counter: 3)
Generated unique entity name: Confli W A AAE H (from prefix: ConflictTest3, counter: 4)
Generated unique entity name: Confli W A AAF M (from prefix: ConflictTest4, counter: 5)
🚀 Creating 5 entities sequentially...
Creating entity: Confli W A AAB O
  ❌ Entity creation error: Error: apiRequestContext.post: Test ended.

[[ATTACHMENT|test-results/backend-connectivity-valid-1d16a-ut-duplicate-name-conflicts-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/backend-connectivity-valid-1d16a-ut-duplicate-name-conflicts-chromium/video.webm]]

[[ATTACHMENT|test-results/backend-connectivity-valid-1d16a-ut-duplicate-name-conflicts-chromium/trace.zip]]
🔍 Testing entity creation without conflicts...
Generated unique entity name: Confli W C AAB R (from prefix: ConflictTest0, counter: 1)
Generated unique entity name: Confli W C AAC F (from prefix: ConflictTest1, counter: 2)
Generated unique entity name: Confli W C AAD I (from prefix: ConflictTest2, counter: 3)
Generated unique entity name: Confli W C AAE K (from prefix: ConflictTest3, counter: 4)
Generated unique entity name: Confli W C AAF W (from prefix: ConflictTest4, counter: 5)
🚀 Creating 5 entities sequentially...
Creating entity: Confli W C AAB R
  ✅ Entity created: Confli W C AAB R (ID: 239)
Creating entity: Confli W C AAC F
  ✅ Entity created: Confli W C AAC F (ID: 240)
Creating entity: Confli W C AAD I
  ✅ Entity created: Confli W C AAD I (ID: 241)
Creating entity: Confli W C AAE K
  ✅ Entity created: Confli W C AAE K (ID: 242)
Creating entity: Confli W C AAF W
  ✅ Entity created: Confli W C AAF W (ID: 243)
🚀 Sequential creation complete: 5 successful, 0 failed in 561ms
✅ Created 5 entities without conflicts
Deleting entity ID: 239
  ✅ Entity deleted: 239
Deleting entity ID: 240
  ✅ Entity deleted: 240
Deleting entity ID: 241
  ✅ Entity deleted: 241
Deleting entity ID: 242
  ✅ Entity deleted: 242
Deleting entity ID: 243
  ✅ Entity deleted: 243
]]>
</system-out>
</testcase>
<testcase name="Backend Connectivity Validation › should perform database cleanup effectively" classname="backend-connectivity-validation.spec.ts" time="0.561">
<failure message="backend-connectivity-validation.spec.ts:76:7 should perform database cleanup effectively" type="FAILURE">
<![CDATA[  [chromium] › backend-connectivity-validation.spec.ts:76:7 › Backend Connectivity Validation › should perform database cleanup effectively 

    Error: page.waitForTimeout: Target page, context or browser has been closed

       at ../utils/backend-connectivity-fix.ts:278

      276 |       
      277 |       // Brief pause between creations to avoid race conditions
    > 278 |       await this.page.waitForTimeout(100);
          |                       ^
      279 |     }
      280 |     
      281 |     const duration = Date.now() - startTime;
        at BackendConnectivityFix.createEntitiesSequentially (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/backend-connectivity-fix.ts:278:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/backend-connectivity-validation.spec.ts:84:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: expect(received).toBeGreaterThanOrEqual(expected)

    Expected: >= 3
    Received:    0

      89 |     
      90 |     // Cleanup should succeed
    > 91 |     expect(cleanupResult.entitiesDeleted).toBeGreaterThanOrEqual(testNames.length);
         |                                           ^
      92 |     expect(cleanupResult.errors).toHaveLength(0);
      93 |     expect(cleanupResult.duration).toBeLessThan(10000); // Under 10 seconds
      94 |     
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/backend-connectivity-validation.spec.ts:91:43

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🔍 Testing database cleanup effectiveness...
Generated unique entity name: Cleanu W B AAB X (from prefix: CleanupTest0, counter: 1)
Generated unique entity name: Cleanu W B AAC I (from prefix: CleanupTest1, counter: 2)
Generated unique entity name: Cleanu W B AAD X (from prefix: CleanupTest2, counter: 3)
🚀 Creating 3 entities sequentially...
Creating entity: Cleanu W B AAB X
  ❌ Entity creation error: Error: apiRequestContext.post: Test ended.

[[ATTACHMENT|test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium/video.webm]]

[[ATTACHMENT|test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium/trace.zip]]
🔍 Testing database cleanup effectiveness...
Generated unique entity name: Cleanu W D AAB D (from prefix: CleanupTest0, counter: 1)
Generated unique entity name: Cleanu W D AAC Z (from prefix: CleanupTest1, counter: 2)
Generated unique entity name: Cleanu W D AAD L (from prefix: CleanupTest2, counter: 3)
🚀 Creating 3 entities sequentially...
Creating entity: Cleanu W D AAB D
  ✅ Entity created: Cleanu W D AAB D (ID: 244)
Creating entity: Cleanu W D AAC Z
  ✅ Entity created: Cleanu W D AAC Z (ID: 245)
Creating entity: Cleanu W D AAD L
  ✅ Entity created: Cleanu W D AAD L (ID: 246)
🚀 Sequential creation complete: 3 successful, 0 failed in 342ms
🧹 Starting enhanced database cleanup...
  Found 6 total entities
  Identified 0 test entities to delete
🧹 Enhanced cleanup complete: 0 entities deleted in 5ms

[[ATTACHMENT|test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/backend-connectivity-valid-68de8-atabase-cleanup-effectively-chromium-retry1/trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Backend Connectivity Validation › should handle API errors gracefully" classname="backend-connectivity-validation.spec.ts" time="0.065">
<system-out>
<![CDATA[🔍 Testing API error handling...
Creating entity: Invalid-Name-With-Dashes!
  ❌ Entity creation error: Error: apiRequestContext.post: Test ended.
✅ API errors handled gracefully: Error: apiRequestContext.post: Test ended.
]]>
</system-out>
</testcase>
<testcase name="Backend Connectivity Validation › should validate network timeout handling" classname="backend-connectivity-validation.spec.ts" time="0.208">
<system-out>
<![CDATA[🔍 Testing network timeout handling...
🔍 Testing backend connectivity...
  ❌ units: Error: apiRequestContext.get: Test ended.
  ❌ entities: Error: apiRequestContext.get: Test ended.
  ❌ connections: Error: apiRequestContext.get: Test ended.
  ❌ health: Error: apiRequestContext.get: Test ended.
🔍 Backend connectivity: ISSUES FOUND

[[ATTACHMENT|test-results/backend-connectivity-valid-50740-te-network-timeout-handling-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/backend-connectivity-valid-50740-te-network-timeout-handling-chromium/video.webm]]

[[ATTACHMENT|test-results/backend-connectivity-valid-50740-te-network-timeout-handling-chromium/trace.zip]]
🔍 Testing network timeout handling...
🔍 Testing backend connectivity...
  ✅ units: 200
  ✅ entities: 200
  ✅ connections: 200
  ✅ health: 200
🔍 Backend connectivity: HEALTHY
✅ Connectivity test completed in 27ms
]]>
</system-out>
</testcase>
<testcase name="Backend Connectivity Validation › should perform comprehensive health check" classname="backend-connectivity-validation.spec.ts" time="1.301">
<system-out>
<![CDATA[🔍 Performing comprehensive backend health check...
🏥 Performing comprehensive backend health check...
🔍 Testing backend connectivity...
  ❌ units: Error: apiRequestContext.get: Test ended.
  ❌ entities: Error: apiRequestContext.get: Test ended.
  ❌ connections: Error: apiRequestContext.get: Test ended.
  ❌ health: Error: apiRequestContext.get: Test ended.
🔍 Backend connectivity: ISSUES FOUND
🔍 Testing API stability...

[[ATTACHMENT|test-results/backend-connectivity-valid-c6323--comprehensive-health-check-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/backend-connectivity-valid-c6323--comprehensive-health-check-chromium/video.webm]]

[[ATTACHMENT|test-results/backend-connectivity-valid-c6323--comprehensive-health-check-chromium/trace.zip]]
🔍 Performing comprehensive backend health check...
🏥 Performing comprehensive backend health check...
🔍 Testing backend connectivity...
  ✅ units: 200
  ✅ entities: 200
  ✅ connections: 200
  ✅ health: 200
🔍 Backend connectivity: HEALTHY
🔍 Testing API stability...
🔍 API stability: STABLE (0/10 errors, avg 5ms)
🧹 Starting enhanced database cleanup...
  Found 6 total entities
  Identified 0 test entities to delete
🧹 Enhanced cleanup complete: 0 entities deleted in 6ms
🏥 Backend health check: HEALTHY
✅ Comprehensive health check passed
]]>
</system-out>
</testcase>
<testcase name="Backend Connectivity Validation › should validate worker isolation prevents conflicts" classname="backend-connectivity-validation.spec.ts" time="0.273">
<failure message="backend-connectivity-validation.spec.ts:142:7 should validate worker isolation prevents conflicts" type="FAILURE">
<![CDATA[  [chromium] › backend-connectivity-validation.spec.ts:142:7 › Backend Connectivity Validation › should validate worker isolation prevents conflicts 

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "W6"
    Received string:    "Worker W G AAB H"

      153 |     // All names should include worker identifier
      154 |     workerNames.forEach(name => {
    > 155 |       expect(name).toContain(`W${workerId}`);
          |                    ^
      156 |     });
      157 |     
      158 |     // Create entities
        at forEach (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/backend-connectivity-validation.spec.ts:155:20)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/backend-connectivity-validation.spec.ts:154:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "W7"
    Received string:    "Worker W H AAB A"

      153 |     // All names should include worker identifier
      154 |     workerNames.forEach(name => {
    > 155 |       expect(name).toContain(`W${workerId}`);
          |                    ^
      156 |     });
      157 |     
      158 |     // Create entities
        at forEach (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/backend-connectivity-validation.spec.ts:155:20)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/backend-connectivity-validation.spec.ts:154:17

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🔍 Testing worker isolation...
Running as worker: 6
Generated unique entity name: Worker W G AAB H (from prefix: Worker6Test0, counter: 1)
Generated unique entity name: Worker W G AAC G (from prefix: Worker6Test1, counter: 2)
Generated unique entity name: Worker W G AAD J (from prefix: Worker6Test2, counter: 3)

[[ATTACHMENT|test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium/video.webm]]

[[ATTACHMENT|test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium/trace.zip]]
🔍 Testing worker isolation...
Running as worker: 7
Generated unique entity name: Worker W H AAB A (from prefix: Worker7Test0, counter: 1)
Generated unique entity name: Worker W H AAC U (from prefix: Worker7Test1, counter: 2)
Generated unique entity name: Worker W H AAD U (from prefix: Worker7Test2, counter: 3)

[[ATTACHMENT|test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/backend-connectivity-valid-ebd7b-solation-prevents-conflicts-chromium-retry1/trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Backend Connectivity Validation › should validate CORS and network policy compliance" classname="backend-connectivity-validation.spec.ts" time="0.115">
<system-out>
<![CDATA[🔍 Testing CORS and network policies...
CORS header: http://localhost:3000
✅ CORS and network policies are properly configured
]]>
</system-out>
</testcase>
<testcase name="Backend Connectivity Validation › should validate authentication handling" classname="backend-connectivity-validation.spec.ts" time="0.238">
<system-out>
<![CDATA[🔍 Testing authentication handling...
🔍 Testing backend connectivity...
  ❌ units: Error: apiRequestContext.get: Test ended.
  ❌ entities: Error: apiRequestContext.get: Test ended.
  ❌ connections: Error: apiRequestContext.get: Test ended.
  ❌ health: Error: apiRequestContext.get: Test ended.
🔍 Backend connectivity: ISSUES FOUND

[[ATTACHMENT|test-results/backend-connectivity-valid-cce9a-ate-authentication-handling-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/backend-connectivity-valid-cce9a-ate-authentication-handling-chromium/video.webm]]

[[ATTACHMENT|test-results/backend-connectivity-valid-cce9a-ate-authentication-handling-chromium/trace.zip]]
🔍 Testing authentication handling...
🔍 Testing backend connectivity...
  ✅ units: 200
  ✅ entities: 200
  ✅ connections: 200
  ✅ health: 200
🔍 Backend connectivity: HEALTHY
Generated unique entity name: AuthTe W J AAB F (from prefix: AuthTest, counter: 1)
Creating entity: AuthTe W J AAB F
  ✅ Entity created: AuthTe W J AAB F (ID: 262)
Deleting entity ID: 262
  ✅ Entity deleted: 262
✅ Authentication handling validated (no auth required for MVP)
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="cleanup-performance-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="7" failures="6" skipped="0" time="2.073" errors="0">
<testcase name="Cleanup Performance Validation › should demonstrate 80% performance improvement in cleanup operations" classname="cleanup-performance-validation.spec.ts" time="0.298">
<failure message="cleanup-performance-validation.spec.ts:40:7 should demonstrate 80% performance improvement in cleanup operations" type="FAILURE">
<![CDATA[  [chromium] › cleanup-performance-validation.spec.ts:40:7 › Cleanup Performance Validation › should demonstrate 80% performance improvement in cleanup operations 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  OLK PerfTe' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:52:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  Z PerfTest' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:52:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[✅ Backend healthy: API healthy (200) (9ms latency)
🏁 Performance Validation: Testing cleanup of 15 entities
Measuring enhanced cleanup performance vs baseline...

=== Phase 1: Creating Test Data ===
🚀 Batch creating 15 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 21ms for 15 items (1.4ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 5)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 73ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 21ms total, 1.4ms/item (15 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium/trace.zip]]
✅ Backend healthy: API healthy (200) (19ms latency)
🏁 Performance Validation: Testing cleanup of 15 entities
Measuring enhanced cleanup performance vs baseline...

=== Phase 1: Creating Test Data ===
🚀 Batch creating 15 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 28ms for 15 items (1.9ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 10)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 88ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 28ms total, 1.9ms/item (15 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-0632a-ement-in-cleanup-operations-chromium-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Batch entity creation failed after 21ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  OLK PerfTe' already exists"}
❌ Batch entity creation failed after 28ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  Z PerfTest' already exists"}
]]>
</system-err>
</testcase>
<testcase name="Cleanup Performance Validation › should validate batch cleanup operations efficiency" classname="cleanup-performance-validation.spec.ts" time="0.298">
<failure message="cleanup-performance-validation.spec.ts:100:7 should validate batch cleanup operations efficiency" type="FAILURE">
<![CDATA[  [chromium] › cleanup-performance-validation.spec.ts:100:7 › Cleanup Performance Validation › should validate batch cleanup operations efficiency 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  HEK BatchT' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:106:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  LKM BatchT' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:106:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[✅ Backend healthy: API healthy (200) (7ms latency)
🚀 Batch Cleanup Validation: Testing 10 entities
🚀 Batch creating 10 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 17ms for 10 items (1.7ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 9)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 77ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 17ms total, 1.7ms/item (10 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium/trace.zip]]
✅ Backend healthy: API healthy (200) (20ms latency)
🚀 Batch Cleanup Validation: Testing 10 entities
🚀 Batch creating 10 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 28ms for 10 items (2.8ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 12)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 81ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 28ms total, 2.8ms/item (10 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-97ec1-eanup-operations-efficiency-chromium-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Batch entity creation failed after 17ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  HEK BatchT' already exists"}
❌ Batch entity creation failed after 28ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  LKM BatchT' already exists"}
]]>
</system-err>
</testcase>
<testcase name="Cleanup Performance Validation › should validate worker isolation in parallel test environment" classname="cleanup-performance-validation.spec.ts" time="0.363">
<failure message="cleanup-performance-validation.spec.ts:135:7 should validate worker isolation in parallel test environment" type="FAILURE">
<![CDATA[  [chromium] › cleanup-performance-validation.spec.ts:135:7 › Cleanup Performance Validation › should validate worker isolation in parallel test environment 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  CH WorkerE' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:142:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  OCD Worker' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:142:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[✅ Backend healthy: API healthy (200) (20ms latency)
👥 Worker Isolation Test: Worker 11 creating 8 entities
🚀 Batch creating 8 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 25ms for 8 items (3.1ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 11)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 82ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 25ms total, 3.1ms/item (8 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium/trace.zip]]
✅ Backend healthy: API healthy (200) (17ms latency)
👥 Worker Isolation Test: Worker 14 creating 8 entities
🚀 Batch creating 8 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 20ms for 8 items (2.5ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 14)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 92ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 20ms total, 2.5ms/item (8 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-1df3e-n-parallel-test-environment-chromium-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Batch entity creation failed after 25ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  CH WorkerE' already exists"}
❌ Batch entity creation failed after 20ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  OCD Worker' already exists"}
]]>
</system-err>
</testcase>
<testcase name="Cleanup Performance Validation › should validate error handling and recovery in cleanup operations" classname="cleanup-performance-validation.spec.ts" time="0.343">
<failure message="cleanup-performance-validation.spec.ts:171:7 should validate error handling and recovery in cleanup operations" type="FAILURE">
<![CDATA[  [chromium] › cleanup-performance-validation.spec.ts:171:7 › Cleanup Performance Validation › should validate error handling and recovery in cleanup operations 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  XOK ErrorT' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:175:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  QLS ErrorT' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:175:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[✅ Backend healthy: API healthy (200) (17ms latency)
🛡️  Error Handling Validation: Testing cleanup resilience
🚀 Batch creating 2 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 12ms for 2 items (6.0ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 13)
Performance: post-test-cleanup completed in 1ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 1ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 1ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 76ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 1ms (min: 1ms, max: 1ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 12ms total, 6.0ms/item (2 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium/trace.zip]]
✅ Backend healthy: API healthy (200) (17ms latency)
🛡️  Error Handling Validation: Testing cleanup resilience
🚀 Batch creating 2 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 13ms for 2 items (6.5ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 15)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 76ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 13ms total, 6.5ms/item (2 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-810fa-overy-in-cleanup-operations-chromium-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Batch entity creation failed after 12ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  XOK ErrorT' already exists"}
❌ Batch entity creation failed after 13ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  QLS ErrorT' already exists"}
]]>
</system-err>
</testcase>
<testcase name="Cleanup Performance Validation › should validate cleanup performance monitoring and reporting" classname="cleanup-performance-validation.spec.ts" time="0.35">
<failure message="cleanup-performance-validation.spec.ts:209:7 should validate cleanup performance monitoring and reporting" type="FAILURE">
<![CDATA[  [chromium] › cleanup-performance-validation.spec.ts:209:7 › Cleanup Performance Validation › should validate cleanup performance monitoring and reporting 

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  XO Monitor' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:213:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp  UEO Monito' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:213:22

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[✅ Backend healthy: API healthy (200) (18ms latency)
📊 Performance Monitoring Validation
🚀 Batch creating 3 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 13ms for 3 items (4.3ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 16)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 76ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 13ms total, 4.3ms/item (3 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium/trace.zip]]
✅ Backend healthy: API healthy (200) (18ms latency)
📊 Performance Monitoring Validation
🚀 Batch creating 3 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 33ms for 3 items (11.0ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 18)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 78ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 33ms total, 11.0ms/item (3 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-29f49-ce-monitoring-and-reporting-chromium-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Batch entity creation failed after 13ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  XO Monitor' already exists"}
❌ Batch entity creation failed after 33ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp  UEO Monito' already exists"}
]]>
</system-err>
</testcase>
<testcase name="Cleanup Performance Validation › should validate health check and system status monitoring" classname="cleanup-performance-validation.spec.ts" time="0.172">
<system-out>
<![CDATA[✅ Backend healthy: API healthy (200) (18ms latency)
🏥 Health Check Validation
📊 Health Check Results:
  • Healthy: true
  • Response: API healthy (200)
  • Latency: 31ms
✅ Health check validated
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 17)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 53ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

]]>
</system-out>
</testcase>
<testcase name="Cleanup Performance Validation › should demonstrate overall performance gains across all operations" classname="cleanup-performance-validation.spec.ts" time="0.249">
<failure message="cleanup-performance-validation.spec.ts:257:7 should demonstrate overall performance gains across all operations" type="FAILURE">
<![CDATA[  [chromium] › cleanup-performance-validation.spec.ts:257:7 › Cleanup Performance Validation › should demonstrate overall performance gains across all operations 

    Error: Backend health check failed: Network error: Error: apiRequestContext.get: Test ended. (0ms latency)
    Please ensure services are running: docker-compose -f docker-compose.dev.yml up -d

       at ../utils/enhanced-helpers.ts:298

      296 |     
      297 |     if (!healthCheck.healthy) {
    > 298 |       throw new Error(
          |             ^
      299 |         `Backend health check failed: ${healthCheck.response} (${healthCheck.latency}ms latency)\n` +
      300 |         'Please ensure services are running: docker-compose -f docker-compose.dev.yml up -d'
      301 |       );
        at EnhancedTestHelpers.ensureBackendHealthy (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:298:13)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:24:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: API returned status 400: {"detail":"Entity 'TEST Wwp   Overall' already exists"}

       at ../utils/enhanced-helpers.ts:200

      198 |         
      199 |         if (!response.ok()) {
    > 200 |           throw new Error(`API returned status ${response.status()}: ${await response.text()}`);
          |                 ^
      201 |         }
      202 |         
      203 |         const entity = await response.json();
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:200:17
        at EnhancedTestHelpers.batchCreateEntitiesWithTracking (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/enhanced-helpers.ts:207:23)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/cleanup-performance-validation.spec.ts:274:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 17)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 48ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 2)


[[ATTACHMENT|test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium/trace.zip]]
✅ Backend healthy: API healthy (200) (17ms latency)
🎯 Overall Performance Validation
  Testing 5 operations with 4 entities each
  Operation 1/5
🚀 Batch creating 4 entities with optimized tracking...
🚀 Parallel batch-entity-creation: 15ms for 4 items (3.8ms/item)
🚀 Fast post-test cleanup starting...
🧹 Starting optimized post-test cleanup (Worker 20)
Performance: post-test-cleanup completed in 0ms
🧹 Post-test cleanup complete: 0 tracked entities to clean in 0ms
🚀 Fast post-test cleanup complete: 0 entities deleted in 0ms

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms


📊 Enhanced Test Performance Report:
  • Test duration: 66ms
  • Entities created: 0
  • Connections created: 0
  • Test efficiency: 100%

🧹 Cleanup Performance Report:
  • Total cleanup operations: 0
  • Total cleanup time: 0ms

📊 Enhanced Performance Report:

🔹 General Operations:
  post-test-cleanup: avg 0ms (min: 0ms, max: 0ms, samples: 1)

🚀 Parallel Operations:
  batch-entity-creation: avg 15ms total, 3.8ms/item (4 items, 1 samples)


[[ATTACHMENT|test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/cleanup-performance-valida-9865f-gains-across-all-operations-chromium-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Batch entity creation failed after 15ms: Error: API returned status 400: {"detail":"Entity 'TEST Wwp   Overall' already exists"}
]]>
</system-err>
</testcase>
</testsuite>
<testsuite name="cleanup-system-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="8" failures="0" skipped="0" time="1.62" errors="0">
<testcase name="Cleanup System Validation › should perform unified database cleanup correctly" classname="cleanup-system-validation.spec.ts" time="0.158">
<system-out>
<![CDATA[🧪 Testing unified database cleanup system
  ✓ Health check passed: All systems operational
🧹 Starting complete database cleanup (Worker 19)
🔗 Starting connection cleanup (Worker 19)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 19)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 19)
Performance: complete-database-cleanup completed in 25ms
🧹 Complete cleanup completed: 0 items deleted in 25ms
  ✓ Complete cleanup successful: 0 entities, 0 connections in 25ms
✅ Database clean state verified (Worker 19)
  ✓ Database clean state verified
  ✓ Cleanup metrics tracked: 1 operations
]]>
</system-out>
</testcase>
<testcase name="Cleanup System Validation › should handle test lifecycle cleanup properly" classname="cleanup-system-validation.spec.ts" time="0.309">
<system-out>
<![CDATA[🧪 Testing test lifecycle cleanup system
🚀 Starting pre-test cleanup (Worker 19)
Performance: pre-test-cleanup completed in 1ms

[[ATTACHMENT|test-results/cleanup-system-validation--11c79--lifecycle-cleanup-properly-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-system-validation--11c79--lifecycle-cleanup-properly-chromium/video.webm]]

[[ATTACHMENT|test-results/cleanup-system-validation--11c79--lifecycle-cleanup-properly-chromium/trace.zip]]
🧪 Testing test lifecycle cleanup system
🚀 Starting pre-test cleanup (Worker 22)
  ✓ System health check passed (27ms)
🧹 Starting complete database cleanup (Worker 22)
🔗 Starting connection cleanup (Worker 22)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 22)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 22)
Performance: complete-database-cleanup completed in 23ms
🧹 Complete cleanup completed: 0 items deleted in 23ms
✅ Database clean state verified (Worker 22)
Performance: pre-test-cleanup completed in 58ms
🚀 Pre-test cleanup completed: 0 items cleaned in 58ms
  ✓ Pre-test cleanup successful: 0 items in 58ms
🏁 Starting post-test cleanup (Worker 22)
🎯 Cleaning tracked items (Worker 22): 0 connections, 0 entities
🎯 Tracked cleanup completed: 0 items in 0ms
🧹 Starting complete database cleanup (Worker 22)
🔗 Starting connection cleanup (Worker 22)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 22)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 22)
Performance: complete-database-cleanup completed in 17ms
🧹 Complete cleanup completed: 0 items deleted in 17ms
✅ Database clean state verified (Worker 22)
Performance: post-test-cleanup completed in 23ms
🏁 Post-test cleanup completed: 0 items cleaned in 23ms
  ✓ Post-test cleanup successful: 0 items in 23ms
  ✓ Session metrics tracked: 1 tests, 100% success rate
✅ Database clean state verified (Worker 22)
  ✓ Cleanup health validation passed
]]>
</system-out>
<system-err>
<![CDATA[❌ Pre-test cleanup failed: Error: System health check failed: Health check failed: Error: apiRequestContext.get: Test ended. (1ms)
]]>
</system-err>
</testcase>
<testcase name="Cleanup System Validation › should maintain worker isolation correctly" classname="cleanup-system-validation.spec.ts" time="0.161">
<system-out>
<![CDATA[🧪 Testing worker isolation system
🔒 Worker isolation registered: W21P1 with prefix "TEST W21P1 967016ZLD"
🏷️  Created isolated entity name: "TEST W21P1 967016ZLD" (Worker W21P1)
🏷️  Created isolated entity name: "TEST W21P1 967016ZLD" (Worker W21P1)
  ✓ Isolated entity names created: "TEST W21P1 967016ZLD", "TEST W21P1 967016ZLD"
🔒 Starting isolated cleanup for worker W21P1
  Found 0 entities for this worker, 0 for other workers
  Found 0 connections for this worker, 0 for other workers
🔒 Isolated cleanup completed: 0 entities, 0 connections in 27ms
  Cross-worker items skipped: 0, Violations: 0
  ✓ Isolated cleanup successful: 0 entities, 0 connections
🔍 Verifying isolation integrity for worker W21P1
🔍 Isolation integrity verified for worker W21P1
  ✓ Isolation integrity verified: 0 cross-worker entities
  ✓ Isolation metrics tracked for worker W21P1
🔓 Worker isolation unregistered: W21P1
]]>
</system-out>
</testcase>
<testcase name="Cleanup System Validation › should verify database state accurately" classname="cleanup-system-validation.spec.ts" time="0.096">
<system-out>
<![CDATA[🧪 Testing database state verification system
🔍 Starting quick database state verification
🔍 Database verification passed: 1 issues in 16ms
  ✓ Quick verification completed in 16ms (excellent)
🔍 Starting standard database state verification
🔍 Database verification passed: 1 issues in 9ms
  ✓ Standard verification found 17 entities, 4 connections
🔍 Starting thorough database state verification
🔍 Database verification passed: 12 issues in 11ms
  ✓ Thorough verification completed with 1 recommendations
  ✓ Verification metrics tracked: 3 runs, 12ms average
]]>
</system-out>
</testcase>
<testcase name="Cleanup System Validation › should monitor cleanup performance effectively" classname="cleanup-system-validation.spec.ts" time="0.163">
<system-out>
<![CDATA[🧪 Testing cleanup monitoring system
📊 Monitoring started: test-cleanup (ID: test-cleanup_1751812967185_8qokkr7gz)
  ✓ Started monitoring operation: test-cleanup_1751812967185_8qokkr7gz
Performance: cleanup-test-cleanup completed in 103ms
📊 Monitoring completed: test-cleanup - SUCCESS in 103ms (5 items)
  ✓ Completed monitored operation successfully
  ✓ Performance metrics recorded: 1 operations, 100% success
  ✓ Session summary: 1 operations, healthy health
  ✓ System health check: healthy
  ✓ Monitoring report generated successfully
]]>
</system-out>
</testcase>
<testcase name="Cleanup System Validation › should handle error scenarios gracefully" classname="cleanup-system-validation.spec.ts" time="0.065">
<system-out>
<![CDATA[🧪 Testing error handling in cleanup system
🚨 Emergency cleanup initiated (Worker 21)
🧹 Starting complete database cleanup (Worker 21)
🔗 Starting connection cleanup (Worker 21)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 21)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 21)
Performance: complete-database-cleanup completed in 2ms
🧹 Complete cleanup completed: 0 items deleted in 2ms
🚨 Emergency cleanup complete (Worker 21)
  ✓ Emergency cleanup completed without errors
✅ Database clean state verified (Worker 21)
  ✓ Database state clean after emergency cleanup
💥 Force cleaning database (Worker 21)
🚨 Emergency cleanup initiated (Worker 21)
🧹 Starting complete database cleanup (Worker 21)
🔗 Starting connection cleanup (Worker 21)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 21)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 21)
Performance: complete-database-cleanup completed in 1ms
🧹 Complete cleanup completed: 0 items deleted in 1ms
🚨 Emergency cleanup complete (Worker 21)
🧹 Starting complete database cleanup (Worker 21)
🔗 Starting connection cleanup (Worker 21)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 21)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 21)
Performance: complete-database-cleanup completed in 1ms
🧹 Complete cleanup completed: 0 items deleted in 1ms
✅ Database clean state verified (Worker 21)
💥 Force clean completed in 2ms
  Performed emergency cleanup
  Cleanup pass 1: 0 items cleaned
  Final verification: Clean
  ✓ Force clean database successful: 3 actions taken
🔍 Starting thorough database state verification
🔍 Database verification passed: 0 issues in 1ms
  ✓ Database verification passed after force clean
]]>
</system-out>
<system-err>
<![CDATA[Error fetching connections: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:312:48[90m)[39m
    at UnifiedDatabaseCleanup.cleanupAllTestConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:160:38[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:89:43[90m)[39m
    at UnifiedDatabaseCleanup.emergencyCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:709:18[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:187:19
Error fetching entities: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:296:48[90m)[39m
    at UnifiedDatabaseCleanup.cleanupAllTestEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:233:35[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:95:39[90m)[39m
    at UnifiedDatabaseCleanup.emergencyCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:709:7[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:187:5
Error fetching entities: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:296:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:537:35[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:101:45[90m)[39m
    at UnifiedDatabaseCleanup.emergencyCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:709:7[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:187:5
Error fetching connections: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:312:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:541:38[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:101:34[90m)[39m
    at UnifiedDatabaseCleanup.emergencyCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:709:7[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:187:5
Error fetching entities: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:296:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:537:35[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:191:46
Error fetching connections: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:312:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:541:38[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:191:32
Error fetching connections: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:312:48[90m)[39m
    at UnifiedDatabaseCleanup.cleanupAllTestConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:160:38[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:89:43[90m)[39m
    at UnifiedDatabaseCleanup.emergencyCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:709:18[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:289:32[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:46
Error fetching entities: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:296:48[90m)[39m
    at UnifiedDatabaseCleanup.cleanupAllTestEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:233:35[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:95:39[90m)[39m
    at UnifiedDatabaseCleanup.emergencyCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:709:7[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:289:7[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:30
Error fetching entities: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:296:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:537:35[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:101:45[90m)[39m
    at UnifiedDatabaseCleanup.emergencyCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:709:7[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:289:7[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:30
Error fetching connections: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:312:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:541:38[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:101:34[90m)[39m
    at UnifiedDatabaseCleanup.emergencyCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:709:7[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:289:7[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:30
Error fetching connections: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:312:48[90m)[39m
    at UnifiedDatabaseCleanup.cleanupAllTestConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:160:38[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:89:43[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:293:56[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:30
Error fetching entities: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:296:48[90m)[39m
    at UnifiedDatabaseCleanup.cleanupAllTestEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:233:35[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:95:39[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:293:31[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:30
Error fetching entities: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:296:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:537:35[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:101:45[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:293:31[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:30
Error fetching connections: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:312:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:541:38[90m)[39m
    at UnifiedDatabaseCleanup.performCompleteCleanup [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:101:34[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:293:31[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:30
Error fetching entities: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:296:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:537:35[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:302:59[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:30
Error fetching connections: apiRequestContext.get: Test ended.
    at UnifiedDatabaseCleanup.getAllConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:312:48[90m)[39m
    at UnifiedDatabaseCleanup.verifyDatabaseCleanState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/unified-database-cleanup.ts:541:38[90m)[39m
    at TestLifecycleCleanup.forceCleanDatabase [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/test-lifecycle-cleanup.ts:302:34[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:197:30
Error fetching entities: apiRequestContext.get: Test ended.
    at DatabaseStateVerifier.getAllEntities [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/database-state-verifier.ts:715:48[90m)[39m
    at DatabaseStateVerifier.verifyDatabaseState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/database-state-verifier.ts:90:35[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:203:51
Error fetching connections: apiRequestContext.get: Test ended.
    at DatabaseStateVerifier.getAllConnections [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/database-state-verifier.ts:728:48[90m)[39m
    at DatabaseStateVerifier.verifyDatabaseState [90m(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/utils/database-state-verifier.ts:91:38[90m)[39m
    at [90m/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/[39me2e/tests/cleanup-system-validation.spec.ts:203:36
]]>
</system-err>
</testcase>
<testcase name="Cleanup System Validation › should provide comprehensive cleanup integration" classname="cleanup-system-validation.spec.ts" time="0.298">
<system-out>
<![CDATA[🧪 Testing integrated cleanup system functionality
  🔄 Starting integrated cleanup workflow...
📊 Monitoring started: integrated-test (ID: integrated-test_1751812967297_gxfjy1f7v)
🚀 Starting pre-test cleanup (Worker 21)
Performance: pre-test-cleanup completed in 0ms

[[ATTACHMENT|test-results/cleanup-system-validation--0a6f4-hensive-cleanup-integration-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-system-validation--0a6f4-hensive-cleanup-integration-chromium/video.webm]]

[[ATTACHMENT|test-results/cleanup-system-validation--0a6f4-hensive-cleanup-integration-chromium/trace.zip]]
🧪 Testing integrated cleanup system functionality
  🔄 Starting integrated cleanup workflow...
📊 Monitoring started: integrated-test (ID: integrated-test_1751812967774_o1865ebmi)
🚀 Starting pre-test cleanup (Worker 23)
  ✓ System health check passed (27ms)
🧹 Starting complete database cleanup (Worker 23)
🔗 Starting connection cleanup (Worker 23)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 23)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 23)
Performance: complete-database-cleanup completed in 25ms
🧹 Complete cleanup completed: 0 items deleted in 25ms
✅ Database clean state verified (Worker 23)
Performance: pre-test-cleanup completed in 60ms
🚀 Pre-test cleanup completed: 0 items cleaned in 60ms
🔍 Starting standard database state verification
🔍 Database verification passed: 1 issues in 14ms
🏁 Starting post-test cleanup (Worker 23)
🎯 Cleaning tracked items (Worker 23): 0 connections, 0 entities
🎯 Tracked cleanup completed: 0 items in 0ms
🧹 Starting complete database cleanup (Worker 23)
🔗 Starting connection cleanup (Worker 23)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 23)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 23)
Performance: complete-database-cleanup completed in 18ms
🧹 Complete cleanup completed: 0 items deleted in 18ms
✅ Database clean state verified (Worker 23)
Performance: post-test-cleanup completed in 27ms
🏁 Post-test cleanup completed: 0 items cleaned in 27ms
🔍 Starting quick database state verification
🔍 Database verification passed: 1 issues in 10ms
Performance: cleanup-integrated-test completed in 112ms
📊 Monitoring completed: integrated-test - SUCCESS in 112ms (0 items)
  ✓ Integrated workflow successful: 0 total items cleaned
  ✓ System health after integration test: healthy
  ✓ All system reports generated successfully
  ✓ All metrics collected successfully
    - Cleanup operations: 2
    - Session tests: 1
    - Verifications: 2
    - Monitoring operations: 1
]]>
</system-out>
<system-err>
<![CDATA[❌ Pre-test cleanup failed: Error: System health check failed: Health check failed: Error: apiRequestContext.get: Test ended. (0ms)
]]>
</system-err>
</testcase>
<testcase name="Cleanup System Validation › should demonstrate performance improvements" classname="cleanup-system-validation.spec.ts" time="0.37">
<system-out>
<![CDATA[🧪 Testing cleanup system performance improvements
📊 Cleanup monitoring session reset
🚀 Starting pre-test cleanup (Worker 22)
Performance: pre-test-cleanup completed in 0ms

[[ATTACHMENT|test-results/cleanup-system-validation--ce05d-te-performance-improvements-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/cleanup-system-validation--ce05d-te-performance-improvements-chromium/video.webm]]

[[ATTACHMENT|test-results/cleanup-system-validation--ce05d-te-performance-improvements-chromium/trace.zip]]
🧪 Testing cleanup system performance improvements
📊 Cleanup monitoring session reset
🚀 Starting pre-test cleanup (Worker 24)
  ✓ System health check passed (21ms)
🧹 Starting complete database cleanup (Worker 24)
🔗 Starting connection cleanup (Worker 24)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 24)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 24)
Performance: complete-database-cleanup completed in 21ms
🧹 Complete cleanup completed: 0 items deleted in 21ms
✅ Database clean state verified (Worker 24)
Performance: pre-test-cleanup completed in 50ms
🚀 Pre-test cleanup completed: 0 items cleaned in 50ms
    Operation 1: 51ms (0 items)
🚀 Starting pre-test cleanup (Worker 24)
  ✓ System health check passed (9ms)
🧹 Starting complete database cleanup (Worker 24)
🔗 Starting connection cleanup (Worker 24)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 24)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 24)
Performance: complete-database-cleanup completed in 14ms
🧹 Complete cleanup completed: 0 items deleted in 14ms
✅ Database clean state verified (Worker 24)
Performance: pre-test-cleanup completed in 31ms
🚀 Pre-test cleanup completed: 0 items cleaned in 31ms
    Operation 2: 32ms (0 items)
🚀 Starting pre-test cleanup (Worker 24)
  ✓ System health check passed (6ms)
🧹 Starting complete database cleanup (Worker 24)
🔗 Starting connection cleanup (Worker 24)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 24)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 24)
Performance: complete-database-cleanup completed in 17ms
🧹 Complete cleanup completed: 0 items deleted in 17ms
✅ Database clean state verified (Worker 24)
Performance: pre-test-cleanup completed in 30ms
🚀 Pre-test cleanup completed: 0 items cleaned in 30ms
    Operation 3: 30ms (0 items)
🚀 Starting pre-test cleanup (Worker 24)
  ✓ System health check passed (7ms)
🧹 Starting complete database cleanup (Worker 24)
🔗 Starting connection cleanup (Worker 24)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 24)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 24)
Performance: complete-database-cleanup completed in 18ms
🧹 Complete cleanup completed: 0 items deleted in 18ms
✅ Database clean state verified (Worker 24)
Performance: pre-test-cleanup completed in 33ms
🚀 Pre-test cleanup completed: 0 items cleaned in 33ms
    Operation 4: 33ms (0 items)
🚀 Starting pre-test cleanup (Worker 24)
  ✓ System health check passed (5ms)
🧹 Starting complete database cleanup (Worker 24)
🔗 Starting connection cleanup (Worker 24)
  Found 0 test connections to clean
👤 Starting entity cleanup (Worker 24)
  Found 0 test entities to clean
✅ Database clean state verified (Worker 24)
Performance: complete-database-cleanup completed in 12ms
🧹 Complete cleanup completed: 0 items deleted in 12ms
✅ Database clean state verified (Worker 24)
Performance: pre-test-cleanup completed in 24ms
🚀 Pre-test cleanup completed: 0 items cleaned in 24ms
    Operation 5: 24ms (0 items)
  ✓ Performance analysis complete:
    - Operations: 5
    - Average time: 34ms
    - Min time: 24ms
    - Max time: 51ms
    - Performance consistency: Good
  ✓ System efficiency rating: excellent
]]>
</system-out>
<system-err>
<![CDATA[❌ Pre-test cleanup failed: Error: System health check failed: Health check failed: Error: apiRequestContext.get: Test ended. (0ms)
]]>
</system-err>
</testcase>
</testsuite>
<testsuite name="comparisons.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="15" failures="2" skipped="12" time="114.691" errors="0">
<testcase name="Entity Comparisons and Pathfinding › should display comparison page correctly" classname="comparisons.spec.ts" time="1.092">
<system-out>
<![CDATA[🔒 Worker isolation registered: W23P1 with prefix "TEST W23P1 967957GYU"
🧹 Starting pre-test cleanup...
  Found 17 total entities in database (Worker 23)
  Identified 11 test entities to delete for Worker 23
  ✓ Deleted test entity: TEST Wwp   Overall (ID: 324)
  ✓ Deleted test entity: TEST Wwp  HEK BatchT (ID: 278)
  ✓ Deleted test entity: TEST Wwp  CH WorkerE (ID: 288)
  ✓ Deleted test entity: TEST Wwp  OCD Worker (ID: 308)
  ✓ Deleted test entity: TEST Wwp  LKM BatchT (ID: 289)
  ✓ Deleted test entity: TEST Wwp  OLK PerfTe (ID: 247)
  ✓ Deleted test entity: TEST Wwp  QLS ErrorT (ID: 317)
  ✓ Deleted test entity: TEST Wwp  UEO Monito (ID: 321)
  ✓ Deleted test entity: TEST Wwp  XOK ErrorT (ID: 306)
  ✓ Deleted test entity: TEST Wwp  XO Monitor (ID: 316)
  ✓ Deleted test entity: TEST Wwp  Z PerfTest (ID: 263)
🧹 Pre-test cleanup complete in 132ms (Worker 23):
  • Initial entities: 17
  • Entities deleted: 11
  • Failed deletions: 0
  • Final entity count: 6
  • Net reduction: 11
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [1/6] Created via API: Human ZKDUA (ID: 328)
  ✓ [2/6] Created via API: Ball ZKDUB (ID: 329)
  ✓ [3/6] Created via API: Build ZKDUC (ID: 330)
  ✓ [4/6] Created via API: Car ZKDUD (ID: 331)
  ✓ [5/6] Created via API: Eleph ZKDUE (ID: 332)
  ✓ [6/6] Created via API: Mouse ZKDUF (ID: 333)
🚀 Parallel entity-creation: 13ms for 6 items (2.2ms/item)
🚀 Parallel entity creation complete in 13ms:
  • Created 6 entities concurrently
  • Average time per entity: 2ms
  • Speed improvement vs sequential: ~231x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [2/4] Created connection via API: 329 → 330 (50.0x)
  ℹ️  Tracking connection dependency: 329 → 330 (auto-deleted via CASCADE)
  ✓ [1/4] Created connection via API: 328 → 329 (10.0x)
  ℹ️  Tracking connection dependency: 328 → 329 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 331 → 332 (0.3x)
  ℹ️  Tracking connection dependency: 331 → 332 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 330 → 333 (0.1x)
  ℹ️  Tracking connection dependency: 330 → 333 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 16ms for 4 items (4.0ms/item)
🚀 Parallel connection creation complete in 16ms:
  • Created 4 connections concurrently
  • Average time per connection: 4ms
  • Speed improvement vs sequential: ~250x faster
🚀 Parallel test data creation complete in 29ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~241x faster
🚀 Parallel test data setup complete in 29ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
✅ Comparison test setup complete - all test data ready
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ✓ [1/6] Deleted entity ID: 328 (1 deps) + cascaded connections
  ✓ [2/6] Deleted entity ID: 331 (1 deps) + cascaded connections
  ✓ [3/6] Deleted entity ID: 332 (1 deps) + cascaded connections
  ✓ [4/6] Deleted entity ID: 333 (1 deps) + cascaded connections
  ✓ [5/6] Deleted entity ID: 329 (2 deps) + cascaded connections
  ✓ [6/6] Deleted entity ID: 330 (2 deps) + cascaded connections
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 183ms (Worker 23):
  • Entities deleted: 6 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 23
  • Test efficiency: 82% (183ms cleanup / 1021ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 23 (4ms)
]]>
</system-out>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should calculate direct relationships" classname="comparisons.spec.ts" time="42.298">
<failure message="comparisons.spec.ts:195:7 should calculate direct relationships" type="FAILURE">
<![CDATA[  [chromium] › comparisons.spec.ts:195:7 › Entity Comparisons and Pathfinding › should calculate direct relationships 

    TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.

       at ../fixtures/page-objects.ts:1510

      1508 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1509 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1510 |     await this.page.waitForFunction(() => {
           |                     ^
      1511 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1512 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1513 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1510:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:199:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    TimeoutError: page.waitForFunction: Timeout 8000ms exceeded.

       at ../fixtures/page-objects.ts:1510

      1508 |     // Form auto-calculates when both entities and unit are filled, no button click needed
      1509 |     // Wait for calculation to complete by waiting for the calculated value to change from '?'
    > 1510 |     await this.page.waitForFunction(() => {
           |                     ^
      1511 |       const calculatedValue = document.querySelector('[data-testid="comparison-result"], .template-calculated-value');
      1512 |       return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';
      1513 |     }, { timeout: 10000 });
        at ComparisonManagerPage.compareEntities (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:1510:21)
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:199:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🔒 Worker isolation registered: W24P0 with prefix "TEST W24P0 968087GEW"
🧹 Starting pre-test cleanup...
  Found 6 total entities in database (Worker 24)
  Identified 0 test entities to delete for Worker 24
🧹 Pre-test cleanup complete in 13ms (Worker 24):
  • Initial entities: 6
  • Entities deleted: 0
  • Failed deletions: 0
  • Final entity count: 6
  • Net reduction: 0
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [1/6] Created via API: Human JVJEA (ID: 334)
  ✓ [3/6] Created via API: Build JVJEC (ID: 335)
  ✓ [4/6] Created via API: Car JVJED (ID: 336)
  ✓ [2/6] Created via API: Ball JVJEB (ID: 338)
  ✓ [5/6] Created via API: Eleph JVJEE (ID: 337)
  ✓ [6/6] Created via API: Mouse JVJEF (ID: 339)
🚀 Parallel entity-creation: 19ms for 6 items (3.2ms/item)
🚀 Parallel entity creation complete in 19ms:
  • Created 6 entities concurrently
  • Average time per entity: 3ms
  • Speed improvement vs sequential: ~158x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [1/4] Created connection via API: 334 → 338 (10.0x)
  ℹ️  Tracking connection dependency: 334 → 338 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 335 → 339 (0.1x)
  ℹ️  Tracking connection dependency: 335 → 339 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 338 → 335 (50.0x)
  ℹ️  Tracking connection dependency: 338 → 335 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 336 → 337 (0.3x)
  ℹ️  Tracking connection dependency: 336 → 337 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 12ms for 4 items (3.0ms/item)
🚀 Parallel connection creation complete in 12ms:
  • Created 4 connections concurrently
  • Average time per connection: 3ms
  • Speed improvement vs sequential: ~333x faster
🚀 Parallel test data creation complete in 32ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~219x faster
🚀 Parallel test data setup complete in 32ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
✅ Comparison test setup complete - all test data ready
Comparing: Human JVJEA to Ball JVJEB (count: 1, unit: Length)
Selecting comparison entity for from field: Human JVJEA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human JVJEA"
Comparison from entity completed: "Human JVJEA" (expected: "Human JVJEA")
Selecting comparison entity for to field: Ball JVJEB
Using prefix "Ball" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Ball JVJEB"
Comparison to entity completed: "Ball JVJEB" (expected: "Ball JVJEB")
Path API Response: http://localhost:8000/api/v1/compare/?from=334&to=338&unit=1 - Status: 404
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 334
  ❌ [2/6] Failed to delete entity ID: 336
  ❌ [3/6] Failed to delete entity ID: 337
  ❌ [4/6] Failed to delete entity ID: 339
  ❌ [5/6] Failed to delete entity ID: 335
  ❌ [6/6] Failed to delete entity ID: 338
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 167ms (Worker 24):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 24
  • Test efficiency: 99% (167ms cleanup / 21051ms total)
  ✅ Cleanup verification: No test entities remain from Worker 24 (5ms)

[[ATTACHMENT|test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/video.webm]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/error-context.md]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/trace.zip]]
🔒 Worker isolation registered: W25P0 with prefix "TEST W25P0 989687GH9"
🧹 Starting pre-test cleanup...
  Found 12 total entities in database (Worker 25)
  Identified 6 test entities to delete for Worker 25
  ✓ Deleted test entity: Ball GAVTB (ID: 343)
  ✓ Deleted test entity: Car GAVTD (ID: 344)
  ✓ Deleted test entity: Build GAVTC (ID: 342)
  ✓ Deleted test entity: Eleph GAVTE (ID: 341)
  ✓ Deleted test entity: Mouse GAVTF (ID: 345)
  ✓ Deleted test entity: Human GAVTA (ID: 340)
🧹 Pre-test cleanup complete in 34ms (Worker 25):
  • Initial entities: 12
  • Entities deleted: 6
  • Failed deletions: 0
  • Final entity count: 6
  • Net reduction: 6
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [1/6] Created via API: Human TVGJA (ID: 346)
  ✓ [3/6] Created via API: Build TVGJC (ID: 347)
  ✓ [2/6] Created via API: Ball TVGJB (ID: 348)
  ✓ [4/6] Created via API: Car TVGJD (ID: 349)
  ✓ [6/6] Created via API: Mouse TVGJF (ID: 350)
  ✓ [5/6] Created via API: Eleph TVGJE (ID: 351)
🚀 Parallel entity-creation: 14ms for 6 items (2.3ms/item)
🚀 Parallel entity creation complete in 14ms:
  • Created 6 entities concurrently
  • Average time per entity: 2ms
  • Speed improvement vs sequential: ~214x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [1/4] Created connection via API: 346 → 348 (10.0x)
  ℹ️  Tracking connection dependency: 346 → 348 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 349 → 351 (0.3x)
  ℹ️  Tracking connection dependency: 349 → 351 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 348 → 347 (50.0x)
  ℹ️  Tracking connection dependency: 348 → 347 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 347 → 350 (0.1x)
  ℹ️  Tracking connection dependency: 347 → 350 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 11ms for 4 items (2.8ms/item)
🚀 Parallel connection creation complete in 11ms:
  • Created 4 connections concurrently
  • Average time per connection: 3ms
  • Speed improvement vs sequential: ~364x faster
🚀 Parallel test data creation complete in 25ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~280x faster
🚀 Parallel test data setup complete in 25ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
✅ Comparison test setup complete - all test data ready
Comparing: Human TVGJA to Ball TVGJB (count: 1, unit: Length)
Selecting comparison entity for from field: Human TVGJA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human TVGJA"
Comparison from entity completed: "Human TVGJA" (expected: "Human TVGJA")
Selecting comparison entity for to field: Ball TVGJB
Using prefix "Ball" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Ball TVGJB"
Comparison to entity completed: "Ball TVGJB" (expected: "Ball TVGJB")
Path API Response: http://localhost:8000/api/v1/compare/?from=346&to=348&unit=1 - Status: 404
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 346
  ❌ [2/6] Failed to delete entity ID: 349
  ❌ [3/6] Failed to delete entity ID: 350
  ❌ [4/6] Failed to delete entity ID: 351
  ❌ [5/6] Failed to delete entity ID: 347
  ❌ [6/6] Failed to delete entity ID: 348
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 164ms (Worker 25):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 25
  • Test efficiency: 99% (164ms cleanup / 21066ms total)
  ✅ Cleanup verification: No test entities remain from Worker 25 (3ms)

[[ATTACHMENT|test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/error-context.md]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[⚠️  6 cleanup operations failed - may affect future tests
⚠️  6 cleanup operations failed - may affect future tests
]]>
</system-err>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should calculate transitive relationships" classname="comparisons.spec.ts" time="56.371">
<failure message="comparisons.spec.ts:215:7 should calculate transitive relationships" type="FAILURE">
<![CDATA[  [chromium] › comparisons.spec.ts:215:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships 

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "Human"
    Received string:    "Did you know thatis asmeasurebigtallheavylongvoluminousas500.0?"

      223 |     const result = await comparisonPage.getComparisonResult();
      224 |     expect(result).toContain('500.0');
    > 225 |     expect(result).toContain('Human');
          |                    ^
      226 |     expect(result).toContain('Building');
      227 |   });
      228 |
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:225:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "Human"
    Received string:    "Did you know thatis asmeasurebigtallheavylongvoluminousas500.0?"

      223 |     const result = await comparisonPage.getComparisonResult();
      224 |     expect(result).toContain('500.0');
    > 225 |     expect(result).toContain('Human');
          |                    ^
      226 |     expect(result).toContain('Building');
      227 |   });
      228 |
        at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:225:20

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/trace.zip
    Usage:

        npx playwright show-trace test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🔒 Worker isolation registered: W23P1 with prefix "TEST W23P1 9690511A9"
🧹 Starting pre-test cleanup...
  Found 12 total entities in database (Worker 23)
  Identified 6 test entities to delete for Worker 23
  ✓ Deleted test entity: Ball JVJEB (ID: 338)
  ✓ Deleted test entity: Mouse JVJEF (ID: 339)
  ✓ Deleted test entity: Car JVJED (ID: 336)
  ✓ Deleted test entity: Human JVJEA (ID: 334)
  ✓ Deleted test entity: Build JVJEC (ID: 335)
  ✓ Deleted test entity: Eleph JVJEE (ID: 337)
🧹 Pre-test cleanup complete in 20ms (Worker 23):
  • Initial entities: 12
  • Entities deleted: 6
  • Failed deletions: 0
  • Final entity count: 6
  • Net reduction: 6
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [5/6] Created via API: Eleph GAVTE (ID: 341)
  ✓ [1/6] Created via API: Human GAVTA (ID: 340)
  ✓ [3/6] Created via API: Build GAVTC (ID: 342)
  ✓ [2/6] Created via API: Ball GAVTB (ID: 343)
  ✓ [4/6] Created via API: Car GAVTD (ID: 344)
  ✓ [6/6] Created via API: Mouse GAVTF (ID: 345)
🚀 Parallel entity-creation: 13ms for 6 items (2.2ms/item)
🚀 Parallel entity creation complete in 13ms:
  • Created 6 entities concurrently
  • Average time per entity: 2ms
  • Speed improvement vs sequential: ~231x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [1/4] Created connection via API: 340 → 343 (10.0x)
  ℹ️  Tracking connection dependency: 340 → 343 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 344 → 341 (0.3x)
  ℹ️  Tracking connection dependency: 344 → 341 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 343 → 342 (50.0x)
  ℹ️  Tracking connection dependency: 343 → 342 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 342 → 345 (0.1x)
  ℹ️  Tracking connection dependency: 342 → 345 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 12ms for 4 items (3.0ms/item)
🚀 Parallel connection creation complete in 12ms:
  • Created 4 connections concurrently
  • Average time per connection: 3ms
  • Speed improvement vs sequential: ~333x faster
🚀 Parallel test data creation complete in 25ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~280x faster
🚀 Parallel test data setup complete in 25ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
✅ Comparison test setup complete - all test data ready
Comparing: Human GAVTA to Build GAVTC (count: 1, unit: Length)
Selecting comparison entity for from field: Human GAVTA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human GAVTA"
Comparison from entity completed: "Human GAVTA" (expected: "Human GAVTA")
Selecting comparison entity for to field: Build GAVTC
Using prefix "Build" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Build GAVTC"
Comparison to entity completed: "Build GAVTC" (expected: "Build GAVTC")
Path API Response: http://localhost:8000/api/v1/compare/?from=340&to=342&unit=1 - Status: 200
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 341
  ❌ [2/6] Failed to delete entity ID: 340
  ❌ [3/6] Failed to delete entity ID: 344
  ❌ [4/6] Failed to delete entity ID: 345
  ❌ [5/6] Failed to delete entity ID: 342
  ❌ [6/6] Failed to delete entity ID: 343
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 168ms (Worker 23):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 23
  • Test efficiency: 99% (168ms cleanup / 28069ms total)
  ✅ Cleanup verification: No test entities remain from Worker 23 (5ms)

[[ATTACHMENT|test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/video.webm]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/error-context.md]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/trace.zip]]
🔒 Worker isolation registered: W26P1 with prefix "TEST W26P1 997577Z31"
🧹 Starting pre-test cleanup...
  Found 12 total entities in database (Worker 26)
  Identified 6 test entities to delete for Worker 26
  ✓ Deleted test entity: Ball TVGJB (ID: 348)
  ✓ Deleted test entity: Eleph TVGJE (ID: 351)
  ✓ Deleted test entity: Build TVGJC (ID: 347)
  ✓ Deleted test entity: Human TVGJA (ID: 346)
  ✓ Deleted test entity: Mouse TVGJF (ID: 350)
  ✓ Deleted test entity: Car TVGJD (ID: 349)
🧹 Pre-test cleanup complete in 37ms (Worker 26):
  • Initial entities: 12
  • Entities deleted: 6
  • Failed deletions: 0
  • Final entity count: 6
  • Net reduction: 6
🚀 Setting up test data for comparison tests using parallel creation...
🚀 Creating test data in parallel: 6 entities + 4 connections...
🚀 Creating 6 entities in parallel via API...
  ✓ [1/6] Created via API: Human YSQKA (ID: 352)
  ✓ [4/6] Created via API: Car YSQKD (ID: 353)
  ✓ [3/6] Created via API: Build YSQKC (ID: 354)
  ✓ [5/6] Created via API: Eleph YSQKE (ID: 356)
  ✓ [2/6] Created via API: Ball YSQKB (ID: 355)
  ✓ [6/6] Created via API: Mouse YSQKF (ID: 357)
🚀 Parallel entity-creation: 14ms for 6 items (2.3ms/item)
🚀 Parallel entity creation complete in 14ms:
  • Created 6 entities concurrently
  • Average time per entity: 2ms
  • Speed improvement vs sequential: ~214x faster
🚀 Creating 4 connections in parallel via API...
  ✓ [1/4] Created connection via API: 352 → 355 (10.0x)
  ℹ️  Tracking connection dependency: 352 → 355 (auto-deleted via CASCADE)
  ✓ [4/4] Created connection via API: 353 → 356 (0.3x)
  ℹ️  Tracking connection dependency: 353 → 356 (auto-deleted via CASCADE)
  ✓ [3/4] Created connection via API: 354 → 357 (0.1x)
  ℹ️  Tracking connection dependency: 354 → 357 (auto-deleted via CASCADE)
  ✓ [2/4] Created connection via API: 355 → 354 (50.0x)
  ℹ️  Tracking connection dependency: 355 → 354 (auto-deleted via CASCADE)
🚀 Parallel connection-creation: 8ms for 4 items (2.0ms/item)
🚀 Parallel connection creation complete in 8ms:
  • Created 4 connections concurrently
  • Average time per connection: 2ms
  • Speed improvement vs sequential: ~500x faster
🚀 Parallel test data creation complete in 23ms:
  • Entities: 6 created in parallel
  • Connections: 4 created in parallel
  • Total speedup vs sequential: ~304x faster
🚀 Parallel test data setup complete in 24ms (vs ~7000ms sequential)
✅ Created 6 entities and 4 connections for comparison tests
✅ Comparison test setup complete - all test data ready
Comparing: Human YSQKA to Build YSQKC (count: 1, unit: Length)
Selecting comparison entity for from field: Human YSQKA
Using prefix "Human" for comparison from entity
Dropdown autocomplete failed for comparison from: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected from entity using direct input: "Human YSQKA"
Comparison from entity completed: "Human YSQKA" (expected: "Human YSQKA")
Selecting comparison entity for to field: Build YSQKC
Using prefix "Build" for comparison to entity
Dropdown autocomplete failed for comparison to: locator.waitFor: Timeout 3000ms exceeded.
Call log:
[2m  - waiting for locator('.autocomplete-dropdown').first() to be visible[22m

Selected to entity using direct input: "Build YSQKC"
Comparison to entity completed: "Build YSQKC" (expected: "Build YSQKC")
Path API Response: http://localhost:8000/api/v1/compare/?from=352&to=354&unit=1 - Status: 200
🧹 Starting post-test cleanup...
  Cleaning 6 tracked entity IDs...
  ℹ️  Dependencies: 8 connections, avg 1.3 per entity
  ℹ️  Using optimal cleanup order to minimize CASCADE DELETE overhead
  ❌ [1/6] Failed to delete entity ID: 352
  ❌ [2/6] Failed to delete entity ID: 353
  ❌ [3/6] Failed to delete entity ID: 356
  ❌ [4/6] Failed to delete entity ID: 357
  ❌ [5/6] Failed to delete entity ID: 354
  ❌ [6/6] Failed to delete entity ID: 355
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 171ms (Worker 26):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 6
  • Worker isolation: 26
  • Test efficiency: 99% (171ms cleanup / 28120ms total)
  ✅ Cleanup verification: No test entities remain from Worker 26 (5ms)

[[ATTACHMENT|test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/test-failed-1.png]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/video.webm]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/error-context.md]]

[[ATTACHMENT|test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[⚠️  6 cleanup operations failed - may affect future tests
⚠️  6 cleanup operations failed - may affect future tests
]]>
</system-err>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should calculate complex multi-hop paths" classname="comparisons.spec.ts" time="14.93">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle reverse path calculations" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle different unit entities with no connection" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle same entity comparison" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle custom from count values" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should validate entity selection" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should validate non-existent entities" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle decimal precision in results" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should clear form after successful comparison" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle rapid successive comparisons" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should respect maximum path length limits" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle autocomplete in entity inputs" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="connections.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="Connection Management › should display connection management page correctly" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should show and hide connection form correctly" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should create bidirectional connections" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should validate positive relationship values with real-time validation" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should validate decimal precision with real-time validation" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should prevent zero multiplier values" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should validate same-unit connections only" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should handle autocomplete in entity selection" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should delete a connection successfully" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should handle connection form validation for required fields with real-time feedback" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should show real-time validation states across all connection form fields" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should prevent duplicate connections" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should handle rapid connection creation" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should maintain connection list after page refresh" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="debug-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="1" failures="0" skipped="1" time="0" errors="0">
<testcase name="debug validation timing" classname="debug-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="entities.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Entity Management › should display entity management page correctly" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should create a new entity successfully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should show and hide entity form correctly" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should validate entity name requirements with real-time validation" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should accept valid entity names" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should prevent duplicate entity names" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should delete an entity successfully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should edit an entity successfully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle entity form validation for name length" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle entity form validation for special characters with real-time feedback" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should provide real-time validation feedback during typing" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle empty entity list gracefully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should maintain entity list after page refresh" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle rapid entity creation" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should maintain validation consistency across form interactions" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="error-handling.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="Error Handling and Edge Cases › should handle API error gracefully" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle network timeout gracefully" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle malformed API responses" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle server 500 errors" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle 404 errors for missing entities" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle browser console errors" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle empty/whitespace-only inputs" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle very long entity names" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle special Unicode characters" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle concurrent user actions" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle page refresh during form submission" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle browser back during form editing" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle extremely large numbers in connections" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle scientific notation in connections" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="navigation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Navigation › should load homepage successfully" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should navigate between all pages" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should highlight active navigation item" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should handle browser back/forward navigation" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should maintain navigation state on page refresh" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should handle invalid routes gracefully" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="performance-benchmark.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Performance Benchmark: Parallel vs Sequential › should demonstrate significant performance improvement with parallel entity creation" classname="performance-benchmark.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Performance Benchmark: Parallel vs Sequential › should demonstrate parallel connection creation performance" classname="performance-benchmark.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Performance Benchmark: Parallel vs Sequential › should demonstrate end-to-end test setup optimization" classname="performance-benchmark.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="sequential-entity-creation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="2" failures="0" skipped="2" time="0" errors="0">
<testcase name="Sequential Entity Creation Test › should create multiple entities sequentially without state interference" classname="sequential-entity-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Sequential Entity Creation Test › should handle rapid entity creation (stress test)" classname="sequential-entity-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="setup-verification.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="chromium" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Setup Verification › should verify basic Playwright setup" classname="setup-verification.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Setup Verification › should verify localhost accessibility" classname="setup-verification.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Setup Verification › should verify backend API accessibility" classname="setup-verification.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="backend-connectivity-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Backend Connectivity Validation › should verify backend API endpoints are accessible" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate API stability and response times" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should create entities without duplicate name conflicts" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should perform database cleanup effectively" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should handle API errors gracefully" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate network timeout handling" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should perform comprehensive health check" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate worker isolation prevents conflicts" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate CORS and network policy compliance" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate authentication handling" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="cleanup-performance-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Cleanup Performance Validation › should demonstrate 80% performance improvement in cleanup operations" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate batch cleanup operations efficiency" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate worker isolation in parallel test environment" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate error handling and recovery in cleanup operations" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate cleanup performance monitoring and reporting" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate health check and system status monitoring" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should demonstrate overall performance gains across all operations" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="cleanup-system-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Cleanup System Validation › should perform unified database cleanup correctly" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should handle test lifecycle cleanup properly" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should maintain worker isolation correctly" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should verify database state accurately" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should monitor cleanup performance effectively" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should handle error scenarios gracefully" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should provide comprehensive cleanup integration" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should demonstrate performance improvements" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="comparisons.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Entity Comparisons and Pathfinding › should display comparison page correctly" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should calculate direct relationships" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should calculate transitive relationships" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should calculate complex multi-hop paths" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle reverse path calculations" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle different unit entities with no connection" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle same entity comparison" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle custom from count values" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should validate entity selection" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should validate non-existent entities" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle decimal precision in results" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should clear form after successful comparison" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle rapid successive comparisons" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should respect maximum path length limits" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle autocomplete in entity inputs" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="connections.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="Connection Management › should display connection management page correctly" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should show and hide connection form correctly" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should create bidirectional connections" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should validate positive relationship values with real-time validation" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should validate decimal precision with real-time validation" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should prevent zero multiplier values" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should validate same-unit connections only" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should handle autocomplete in entity selection" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should delete a connection successfully" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should handle connection form validation for required fields with real-time feedback" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should show real-time validation states across all connection form fields" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should prevent duplicate connections" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should handle rapid connection creation" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should maintain connection list after page refresh" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="debug-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="1" failures="0" skipped="1" time="0" errors="0">
<testcase name="debug validation timing" classname="debug-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="entities.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Entity Management › should display entity management page correctly" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should create a new entity successfully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should show and hide entity form correctly" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should validate entity name requirements with real-time validation" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should accept valid entity names" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should prevent duplicate entity names" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should delete an entity successfully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should edit an entity successfully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle entity form validation for name length" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle entity form validation for special characters with real-time feedback" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should provide real-time validation feedback during typing" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle empty entity list gracefully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should maintain entity list after page refresh" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle rapid entity creation" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should maintain validation consistency across form interactions" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="error-handling.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="Error Handling and Edge Cases › should handle API error gracefully" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle network timeout gracefully" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle malformed API responses" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle server 500 errors" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle 404 errors for missing entities" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle browser console errors" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle empty/whitespace-only inputs" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle very long entity names" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle special Unicode characters" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle concurrent user actions" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle page refresh during form submission" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle browser back during form editing" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle extremely large numbers in connections" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle scientific notation in connections" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="navigation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Navigation › should load homepage successfully" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should navigate between all pages" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should highlight active navigation item" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should handle browser back/forward navigation" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should maintain navigation state on page refresh" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should handle invalid routes gracefully" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="performance-benchmark.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Performance Benchmark: Parallel vs Sequential › should demonstrate significant performance improvement with parallel entity creation" classname="performance-benchmark.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Performance Benchmark: Parallel vs Sequential › should demonstrate parallel connection creation performance" classname="performance-benchmark.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Performance Benchmark: Parallel vs Sequential › should demonstrate end-to-end test setup optimization" classname="performance-benchmark.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="sequential-entity-creation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="2" failures="0" skipped="2" time="0" errors="0">
<testcase name="Sequential Entity Creation Test › should create multiple entities sequentially without state interference" classname="sequential-entity-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Sequential Entity Creation Test › should handle rapid entity creation (stress test)" classname="sequential-entity-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="setup-verification.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="firefox" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Setup Verification › should verify basic Playwright setup" classname="setup-verification.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Setup Verification › should verify localhost accessibility" classname="setup-verification.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Setup Verification › should verify backend API accessibility" classname="setup-verification.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="backend-connectivity-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Backend Connectivity Validation › should verify backend API endpoints are accessible" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate API stability and response times" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should create entities without duplicate name conflicts" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should perform database cleanup effectively" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should handle API errors gracefully" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate network timeout handling" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should perform comprehensive health check" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate worker isolation prevents conflicts" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate CORS and network policy compliance" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Backend Connectivity Validation › should validate authentication handling" classname="backend-connectivity-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="cleanup-performance-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Cleanup Performance Validation › should demonstrate 80% performance improvement in cleanup operations" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate batch cleanup operations efficiency" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate worker isolation in parallel test environment" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate error handling and recovery in cleanup operations" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate cleanup performance monitoring and reporting" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should validate health check and system status monitoring" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup Performance Validation › should demonstrate overall performance gains across all operations" classname="cleanup-performance-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="cleanup-system-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Cleanup System Validation › should perform unified database cleanup correctly" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should handle test lifecycle cleanup properly" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should maintain worker isolation correctly" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should verify database state accurately" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should monitor cleanup performance effectively" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should handle error scenarios gracefully" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should provide comprehensive cleanup integration" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Cleanup System Validation › should demonstrate performance improvements" classname="cleanup-system-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="comparisons.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Entity Comparisons and Pathfinding › should display comparison page correctly" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should calculate direct relationships" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should calculate transitive relationships" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should calculate complex multi-hop paths" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle reverse path calculations" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle different unit entities with no connection" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle same entity comparison" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle custom from count values" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should validate entity selection" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should validate non-existent entities" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle decimal precision in results" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should clear form after successful comparison" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle rapid successive comparisons" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should respect maximum path length limits" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Comparisons and Pathfinding › should handle autocomplete in entity inputs" classname="comparisons.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="connections.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="Connection Management › should display connection management page correctly" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should show and hide connection form correctly" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should create bidirectional connections" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should validate positive relationship values with real-time validation" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should validate decimal precision with real-time validation" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should prevent zero multiplier values" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should validate same-unit connections only" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should handle autocomplete in entity selection" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should delete a connection successfully" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should handle connection form validation for required fields with real-time feedback" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should show real-time validation states across all connection form fields" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should prevent duplicate connections" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should handle rapid connection creation" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Connection Management › should maintain connection list after page refresh" classname="connections.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="debug-validation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="1" failures="0" skipped="1" time="0" errors="0">
<testcase name="debug validation timing" classname="debug-validation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="entities.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Entity Management › should display entity management page correctly" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should create a new entity successfully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should show and hide entity form correctly" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should validate entity name requirements with real-time validation" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should accept valid entity names" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should prevent duplicate entity names" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should delete an entity successfully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should edit an entity successfully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle entity form validation for name length" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle entity form validation for special characters with real-time feedback" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should provide real-time validation feedback during typing" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle empty entity list gracefully" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should maintain entity list after page refresh" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should handle rapid entity creation" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Entity Management › should maintain validation consistency across form interactions" classname="entities.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="error-handling.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="14" failures="0" skipped="14" time="0" errors="0">
<testcase name="Error Handling and Edge Cases › should handle API error gracefully" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle network timeout gracefully" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle malformed API responses" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle server 500 errors" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle 404 errors for missing entities" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle browser console errors" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle empty/whitespace-only inputs" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle very long entity names" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle special Unicode characters" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle concurrent user actions" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle page refresh during form submission" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle browser back during form editing" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle extremely large numbers in connections" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Error Handling and Edge Cases › should handle scientific notation in connections" classname="error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="navigation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Navigation › should load homepage successfully" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should navigate between all pages" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should highlight active navigation item" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should handle browser back/forward navigation" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should maintain navigation state on page refresh" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Navigation › should handle invalid routes gracefully" classname="navigation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="performance-benchmark.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Performance Benchmark: Parallel vs Sequential › should demonstrate significant performance improvement with parallel entity creation" classname="performance-benchmark.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Performance Benchmark: Parallel vs Sequential › should demonstrate parallel connection creation performance" classname="performance-benchmark.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Performance Benchmark: Parallel vs Sequential › should demonstrate end-to-end test setup optimization" classname="performance-benchmark.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="sequential-entity-creation.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="2" failures="0" skipped="2" time="0" errors="0">
<testcase name="Sequential Entity Creation Test › should create multiple entities sequentially without state interference" classname="sequential-entity-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Sequential Entity Creation Test › should handle rapid entity creation (stress test)" classname="sequential-entity-creation.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="setup-verification.spec.ts" timestamp="2025-07-06T14:42:39.417Z" hostname="webkit" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="Setup Verification › should verify basic Playwright setup" classname="setup-verification.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Setup Verification › should verify localhost accessibility" classname="setup-verification.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Setup Verification › should verify backend API accessibility" classname="setup-verification.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>