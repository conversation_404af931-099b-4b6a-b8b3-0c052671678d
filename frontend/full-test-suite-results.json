
> simile-frontend@0.1.0 test:e2e
> playwright test --reporter=json

{
  "config": {
    "configFile": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/playwright.config.ts",
    "rootDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
    "forbidOnly": false,
    "fullyParallel": true,
    "globalSetup": null,
    "globalTeardown": null,
    "globalTimeout": 7200000,
    "grep": {},
    "grepInvert": null,
    "maxFailures": 10,
    "metadata": {
      "actualWorkers": 3
    },
    "preserveOutput": "always",
    "reporter": [
      [
        "json"
      ]
    ],
    "reportSlowTests": {
      "max": 5,
      "threshold": 300000
    },
    "quiet": false,
    "projects": [
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {
          "actualWorkers": 3
        },
        "id": "chromium",
        "name": "chromium",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {
          "actualWorkers": 3
        },
        "id": "firefox",
        "name": "firefox",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 1,
        "metadata": {
          "actualWorkers": 3
        },
        "id": "webkit",
        "name": "webkit",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      }
    ],
    "shard": null,
    "updateSnapshots": "missing",
    "updateSourceMethod": "patch",
    "version": "1.53.1",
    "workers": 3,
    "webServer": {
      "command": "npm start",
      "url": "http://localhost:3000",
      "reuseExistingServer": true,
      "timeout": 120000
    }
  },
  "suites": [
    {
      "title": "comparisons.spec.ts",
      "file": "comparisons.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Entity Comparisons and Pathfinding",
          "file": "comparisons.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should display comparison page correctly",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 16477,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball YUKTB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball YUKTB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball YUKTB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball YUKTB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball YUKTB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball YUKTB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball YUKTB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball YUKTB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball YUKTB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 12 total entities in database (Worker 0)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 0\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 107ms (Worker 0):\n"
                        },
                        {
                          "text": "  • Initial entities: 12\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 12\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human YUKTA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human YUKTA\n"
                        },
                        {
                          "text": "Creating entity: Human YUKTA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human YUKTA\" in 2527ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human YUKTA (ID: temp-1751224768520-e8v6mv5zj) in 3049ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human YUKTA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball YUKTB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball YUKTB\n"
                        },
                        {
                          "text": "Creating entity: Ball YUKTB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball YUKTB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human YUKTA (ID: 402)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 27ms (Worker 0):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 0\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (27ms cleanup / 16193ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 0 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball YUKTB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball YUKTB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball YUKTB after 11609ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball YUKTB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball YUKTB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball YUKTB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:19:24.234Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 3,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 16291,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball MWHLB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWHLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWHLB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball MWHLB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWHLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWHLB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball MWHLB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWHLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWHLB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 15 total entities in database (Worker 3)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 3\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 28ms (Worker 3):\n"
                        },
                        {
                          "text": "  • Initial entities: 15\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 15\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human MWHLA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human MWHLA\n"
                        },
                        {
                          "text": "Creating entity: Human MWHLA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human MWHLA\" in 2556ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human MWHLA (ID: temp-1751224785285-c3wfob3ei) in 3086ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human MWHLA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball MWHLB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball MWHLB\n"
                        },
                        {
                          "text": "Creating entity: Ball MWHLB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball MWHLB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human MWHLA (ID: 407)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 28ms (Worker 3):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 3\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (28ms cleanup / 16096ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 3 (6ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWHLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWHLB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball MWHLB after 11614ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball MWHLB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWHLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWHLB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:19:41.322Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-ec5b6735c4e594d4a630",
              "file": "comparisons.spec.ts",
              "line": 136,
              "column": 7
            },
            {
              "title": "should calculate direct relationships",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 1,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 16485,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball EAXAB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball EAXAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball EAXAB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball EAXAB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball EAXAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball EAXAB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball EAXAB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball EAXAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball EAXAB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 12 total entities in database (Worker 1)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 1\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 98ms (Worker 1):\n"
                        },
                        {
                          "text": "  • Initial entities: 12\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 12\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human EAXAA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human EAXAA\n"
                        },
                        {
                          "text": "Creating entity: Human EAXAA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human EAXAA\" in 2529ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human EAXAA (ID: temp-1751224768523-j5947vpt7) in 3052ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human EAXAA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball EAXAB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball EAXAB\n"
                        },
                        {
                          "text": "Creating entity: Ball EAXAB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball EAXAB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human EAXAA (ID: 403)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 27ms (Worker 1):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 1\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (27ms cleanup / 16193ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 1 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball EAXAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball EAXAB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball EAXAB after 11606ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball EAXAB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball EAXAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball EAXAB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:19:24.236Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 5,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 16290,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball RTTEB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball RTTEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball RTTEB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball RTTEB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball RTTEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball RTTEB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball RTTEB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball RTTEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball RTTEB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 15 total entities in database (Worker 5)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 5\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 28ms (Worker 5):\n"
                        },
                        {
                          "text": "  • Initial entities: 15\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 15\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human RTTEA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human RTTEA\n"
                        },
                        {
                          "text": "Creating entity: Human RTTEA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human RTTEA\" in 2557ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human RTTEA (ID: temp-1751224785286-9tg1x7f74) in 3083ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human RTTEA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball RTTEB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball RTTEB\n"
                        },
                        {
                          "text": "Creating entity: Ball RTTEB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball RTTEB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human RTTEA (ID: 408)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 28ms (Worker 5):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 5\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (28ms cleanup / 16096ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 5 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball RTTEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball RTTEB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball RTTEB after 11613ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball RTTEB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball RTTEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball RTTEB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:19:41.322Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-f2bf549b20a73c7f70d0",
              "file": "comparisons.spec.ts",
              "line": 144,
              "column": 7
            },
            {
              "title": "should calculate transitive relationships",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 2,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 16481,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball PHPLB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PHPLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PHPLB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball PHPLB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PHPLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PHPLB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball PHPLB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PHPLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PHPLB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 12 total entities in database (Worker 2)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 2\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 98ms (Worker 2):\n"
                        },
                        {
                          "text": "  • Initial entities: 12\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 12\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human PHPLA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human PHPLA\n"
                        },
                        {
                          "text": "Creating entity: Human PHPLA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human PHPLA\" in 2526ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human PHPLA (ID: temp-1751224768519-6aba9cndi) in 3048ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human PHPLA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball PHPLB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball PHPLB\n"
                        },
                        {
                          "text": "Creating entity: Ball PHPLB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball PHPLB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human PHPLA (ID: 401)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 27ms (Worker 2):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 2\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (27ms cleanup / 16193ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 2 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PHPLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PHPLB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball PHPLB after 11612ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball PHPLB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PHPLB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PHPLB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:19:24.235Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 4,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 16291,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball QXRJB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXRJB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXRJB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball QXRJB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXRJB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXRJB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball QXRJB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXRJB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXRJB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 15 total entities in database (Worker 4)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 4\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 28ms (Worker 4):\n"
                        },
                        {
                          "text": "  • Initial entities: 15\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 15\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human QXRJA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human QXRJA\n"
                        },
                        {
                          "text": "Creating entity: Human QXRJA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human QXRJA\" in 2556ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human QXRJA (ID: temp-1751224785288-n1d0pduuh) in 3087ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human QXRJA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball QXRJB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball QXRJB\n"
                        },
                        {
                          "text": "Creating entity: Ball QXRJB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball QXRJB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human QXRJA (ID: 409)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 28ms (Worker 4):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 4\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (28ms cleanup / 16097ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 4 (6ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXRJB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXRJB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball QXRJB after 11612ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball QXRJB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXRJB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXRJB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:19:41.322Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-06fb9657b4a5cf096857",
              "file": "comparisons.spec.ts",
              "line": 158,
              "column": 7
            },
            {
              "title": "should calculate complex multi-hop paths",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 16184,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball ESBEB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ESBEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ESBEB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball ESBEB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ESBEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ESBEB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball ESBEB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ESBEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ESBEB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 18 total entities in database (Worker 6)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 6\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 25ms (Worker 6):\n"
                        },
                        {
                          "text": "  • Initial entities: 18\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 18\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human ESBEA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human ESBEA\n"
                        },
                        {
                          "text": "Creating entity: Human ESBEA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human ESBEA\" in 2507ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human ESBEA (ID: temp-1751224801966-uuifkwokh) in 3033ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human ESBEA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball ESBEB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball ESBEB\n"
                        },
                        {
                          "text": "Creating entity: Ball ESBEB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball ESBEB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human ESBEA (ID: 415)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 22ms (Worker 6):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 6\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (22ms cleanup / 15991ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 6 (6ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ESBEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ESBEB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball ESBEB after 11595ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball ESBEB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ESBEB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ESBEB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:19:58.079Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 9,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 16279,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball PIPFB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PIPFB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PIPFB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball PIPFB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PIPFB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PIPFB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball PIPFB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PIPFB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PIPFB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 21 total entities in database (Worker 9)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 9\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 28ms (Worker 9):\n"
                        },
                        {
                          "text": "  • Initial entities: 21\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 21\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human PIPFA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human PIPFA\n"
                        },
                        {
                          "text": "Creating entity: Human PIPFA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human PIPFA\" in 2561ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human PIPFA (ID: temp-1751224818614-p8mmhq5tb) in 3088ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human PIPFA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball PIPFB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball PIPFB\n"
                        },
                        {
                          "text": "Creating entity: Ball PIPFB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball PIPFB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human PIPFA (ID: 420)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 24ms (Worker 9):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 9\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (24ms cleanup / 16093ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 9 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PIPFB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PIPFB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball PIPFB after 11620ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball PIPFB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball PIPFB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball PIPFB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:20:14.664Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-75303118dff4d7b236bf",
              "file": "comparisons.spec.ts",
              "line": 172,
              "column": 7
            },
            {
              "title": "should handle reverse path calculations",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 7,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 16184,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball QXWBB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXWBB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXWBB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball QXWBB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXWBB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXWBB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball QXWBB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXWBB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXWBB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 18 total entities in database (Worker 7)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 7\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 25ms (Worker 7):\n"
                        },
                        {
                          "text": "  • Initial entities: 18\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 18\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human QXWBA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human QXWBA\n"
                        },
                        {
                          "text": "Creating entity: Human QXWBA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human QXWBA\" in 2498ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human QXWBA (ID: temp-1751224801957-fofr7cird) in 3024ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human QXWBA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball QXWBB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball QXWBB\n"
                        },
                        {
                          "text": "Creating entity: Ball QXWBB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball QXWBB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human QXWBA (ID: 414)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 22ms (Worker 7):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 7\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (22ms cleanup / 15991ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 7 (6ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXWBB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXWBB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball QXWBB after 11599ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball QXWBB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball QXWBB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball QXWBB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:19:58.080Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 10,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 16279,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball VDJXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball VDJXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball VDJXB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball VDJXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball VDJXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball VDJXB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball VDJXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball VDJXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball VDJXB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 21 total entities in database (Worker 10)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 10\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms (Worker 10):\n"
                        },
                        {
                          "text": "  • Initial entities: 21\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 21\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human VDJXA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human VDJXA\n"
                        },
                        {
                          "text": "Creating entity: Human VDJXA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human VDJXA\" in 2559ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human VDJXA (ID: temp-1751224818612-dkmm3qeiu) in 3089ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human VDJXA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball VDJXB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball VDJXB\n"
                        },
                        {
                          "text": "Creating entity: Ball VDJXB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball VDJXB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human VDJXA (ID: 419)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 23ms (Worker 10):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 10\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (23ms cleanup / 16093ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 10 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball VDJXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball VDJXB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball VDJXB after 11620ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball VDJXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball VDJXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball VDJXB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:20:14.666Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-983edd654911dead2884",
              "file": "comparisons.spec.ts",
              "line": 187,
              "column": 7
            },
            {
              "title": "should handle different unit entities with no connection",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 8,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 16189,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball AYNIB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AYNIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AYNIB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball AYNIB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AYNIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AYNIB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball AYNIB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AYNIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AYNIB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 18 total entities in database (Worker 8)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 8\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 25ms (Worker 8):\n"
                        },
                        {
                          "text": "  • Initial entities: 18\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 18\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human AYNIA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human AYNIA\n"
                        },
                        {
                          "text": "Creating entity: Human AYNIA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human AYNIA\" in 2498ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human AYNIA (ID: temp-1751224801962-wkgxf841f) in 3030ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human AYNIA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball AYNIB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball AYNIB\n"
                        },
                        {
                          "text": "Creating entity: Ball AYNIB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball AYNIB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human AYNIA (ID: 413)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 15ms (Worker 8):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 8\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (15ms cleanup / 15999ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 8 (4ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AYNIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AYNIB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball AYNIB after 11605ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball AYNIB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AYNIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AYNIB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:19:58.080Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 11,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 16279,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball OGIXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball OGIXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball OGIXB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball OGIXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball OGIXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball OGIXB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball OGIXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball OGIXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball OGIXB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 21 total entities in database (Worker 11)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 11\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 28ms (Worker 11):\n"
                        },
                        {
                          "text": "  • Initial entities: 21\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 21\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human OGIXA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human OGIXA\n"
                        },
                        {
                          "text": "Creating entity: Human OGIXA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human OGIXA\" in 2560ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human OGIXA (ID: temp-1751224818612-kav7s2rc2) in 3087ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human OGIXA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball OGIXB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball OGIXB\n"
                        },
                        {
                          "text": "Creating entity: Ball OGIXB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball OGIXB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human OGIXA (ID: 421)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 24ms (Worker 11):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 11\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (24ms cleanup / 16093ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 11 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball OGIXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball OGIXB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball OGIXB after 11622ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball OGIXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball OGIXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball OGIXB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:20:14.687Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39c74-entities-with-no-connection-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-8dd14ae43fbe55b3930e",
              "file": "comparisons.spec.ts",
              "line": 201,
              "column": 7
            },
            {
              "title": "should handle same entity comparison",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 12,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 16302,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball WLDCB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball WLDCB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball WLDCB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball WLDCB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball WLDCB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball WLDCB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball WLDCB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball WLDCB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball WLDCB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 24 total entities in database (Worker 12)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 12\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms (Worker 12):\n"
                        },
                        {
                          "text": "  • Initial entities: 24\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 24\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human WLDCA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human WLDCA\n"
                        },
                        {
                          "text": "Creating entity: Human WLDCA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human WLDCA\" in 2564ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human WLDCA (ID: temp-1751224835317-13wbtol29) in 3103ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human WLDCA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball WLDCB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball WLDCB\n"
                        },
                        {
                          "text": "Creating entity: Ball WLDCB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball WLDCB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human WLDCA (ID: 426)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 22ms (Worker 12):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 12\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (22ms cleanup / 16094ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 12 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball WLDCB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball WLDCB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball WLDCB after 11624ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball WLDCB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball WLDCB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball WLDCB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:20:31.369Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 17,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 16228,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball HTVWB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball HTVWB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball HTVWB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball HTVWB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball HTVWB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball HTVWB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball HTVWB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball HTVWB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball HTVWB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 27 total entities in database (Worker 17)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 17\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 29ms (Worker 17):\n"
                        },
                        {
                          "text": "  • Initial entities: 27\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 27\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human HTVWA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human HTVWA\n"
                        },
                        {
                          "text": "Creating entity: Human HTVWA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human HTVWA\" in 2526ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human HTVWA (ID: temp-1751224851957-v8jc6ldb6) in 3058ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human HTVWA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball HTVWB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball HTVWB\n"
                        },
                        {
                          "text": "Creating entity: Ball HTVWB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball HTVWB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human HTVWA (ID: 431)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 24ms (Worker 17):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 17\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (24ms cleanup / 16041ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 17 (6ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball HTVWB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball HTVWB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball HTVWB after 11592ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball HTVWB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball HTVWB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball HTVWB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:20:48.053Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-39729-ndle-same-entity-comparison-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-b8244a8bfedfe296979d",
              "file": "comparisons.spec.ts",
              "line": 217,
              "column": 7
            },
            {
              "title": "should handle custom from count values",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 13,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 16286,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball BHZDB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball BHZDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball BHZDB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball BHZDB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball BHZDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball BHZDB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball BHZDB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball BHZDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball BHZDB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 24 total entities in database (Worker 13)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 13\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms (Worker 13):\n"
                        },
                        {
                          "text": "  • Initial entities: 24\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 24\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human BHZDA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human BHZDA\n"
                        },
                        {
                          "text": "Creating entity: Human BHZDA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human BHZDA\" in 2575ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human BHZDA (ID: temp-1751224835318-g9v6d2953) in 3103ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human BHZDA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball BHZDB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball BHZDB\n"
                        },
                        {
                          "text": "Creating entity: Ball BHZDB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball BHZDB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human BHZDA (ID: 427)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 21ms (Worker 13):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 13\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (21ms cleanup / 16094ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 13 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball BHZDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball BHZDB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball BHZDB after 11619ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball BHZDB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball BHZDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball BHZDB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:20:31.368Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-d15ba-le-custom-from-count-values-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-d15ba-le-custom-from-count-values-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-d15ba-le-custom-from-count-values-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-d15ba-le-custom-from-count-values-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 15,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 16228,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball ZMCKB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ZMCKB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ZMCKB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball ZMCKB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ZMCKB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ZMCKB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball ZMCKB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ZMCKB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ZMCKB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 27 total entities in database (Worker 15)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 15\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 29ms (Worker 15):\n"
                        },
                        {
                          "text": "  • Initial entities: 27\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 27\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human ZMCKA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human ZMCKA\n"
                        },
                        {
                          "text": "Creating entity: Human ZMCKA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human ZMCKA\" in 2525ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human ZMCKA (ID: temp-1751224851956-gorrqiqz6) in 3058ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human ZMCKA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball ZMCKB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball ZMCKB\n"
                        },
                        {
                          "text": "Creating entity: Ball ZMCKB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball ZMCKB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human ZMCKA (ID: 433)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 24ms (Worker 15):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 15\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (24ms cleanup / 16041ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 15 (6ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ZMCKB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ZMCKB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball ZMCKB after 11592ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball ZMCKB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ZMCKB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ZMCKB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:20:48.039Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-d15ba-le-custom-from-count-values-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-d15ba-le-custom-from-count-values-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-d15ba-le-custom-from-count-values-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-d15ba-le-custom-from-count-values-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-4cbcfdb664b6aa97687a",
              "file": "comparisons.spec.ts",
              "line": 228,
              "column": 7
            },
            {
              "title": "should validate entity selection",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 14,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 16290,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball MWTPB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWTPB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWTPB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball MWTPB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWTPB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWTPB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball MWTPB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWTPB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWTPB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 24 total entities in database (Worker 14)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 14\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms (Worker 14):\n"
                        },
                        {
                          "text": "  • Initial entities: 24\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 24\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human MWTPA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human MWTPA\n"
                        },
                        {
                          "text": "Creating entity: Human MWTPA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human MWTPA\" in 2564ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human MWTPA (ID: temp-1751224835315-ixhve6cta) in 3101ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human MWTPA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball MWTPB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball MWTPB\n"
                        },
                        {
                          "text": "Creating entity: Ball MWTPB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball MWTPB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human MWTPA (ID: 425)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 22ms (Worker 14):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 14\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (22ms cleanup / 16094ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 14 (7ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWTPB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWTPB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball MWTPB after 11627ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball MWTPB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball MWTPB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball MWTPB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:20:31.368Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-e872f-d-validate-entity-selection-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-e872f-d-validate-entity-selection-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-e872f-d-validate-entity-selection-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-e872f-d-validate-entity-selection-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 16,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 16228,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball XIJDB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball XIJDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball XIJDB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball XIJDB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball XIJDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball XIJDB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball XIJDB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball XIJDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball XIJDB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 27 total entities in database (Worker 16)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 16\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 29ms (Worker 16):\n"
                        },
                        {
                          "text": "  • Initial entities: 27\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 27\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human XIJDA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human XIJDA\n"
                        },
                        {
                          "text": "Creating entity: Human XIJDA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human XIJDA\" in 2527ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human XIJDA (ID: temp-1751224851957-58knip9dt) in 3058ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human XIJDA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball XIJDB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball XIJDB\n"
                        },
                        {
                          "text": "Creating entity: Ball XIJDB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball XIJDB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human XIJDA (ID: 432)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 24ms (Worker 16):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 16\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (24ms cleanup / 16041ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 16 (6ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball XIJDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball XIJDB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball XIJDB after 11593ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball XIJDB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball XIJDB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball XIJDB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:20:48.048Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-e872f-d-validate-entity-selection-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-e872f-d-validate-entity-selection-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-e872f-d-validate-entity-selection-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-e872f-d-validate-entity-selection-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-d27a3ee7a10378599fab",
              "file": "comparisons.spec.ts",
              "line": 242,
              "column": 7
            },
            {
              "title": "should validate non-existent entities",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 19,
                      "parallelIndex": 2,
                      "status": "failed",
                      "duration": 16161,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball NJNNB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball NJNNB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball NJNNB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball NJNNB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball NJNNB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball NJNNB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball NJNNB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball NJNNB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball NJNNB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 30 total entities in database (Worker 19)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 19\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 24ms (Worker 19):\n"
                        },
                        {
                          "text": "  • Initial entities: 30\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 30\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human NJNNA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human NJNNA\n"
                        },
                        {
                          "text": "Creating entity: Human NJNNA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human NJNNA\" in 2506ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human NJNNA (ID: temp-1751224868679-spvwk1a7w) in 3030ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human NJNNA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball NJNNB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball NJNNB\n"
                        },
                        {
                          "text": "Creating entity: Ball NJNNB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball NJNNB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human NJNNA (ID: 439)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 28ms (Worker 19):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 19\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (28ms cleanup / 15954ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 19 (8ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball NJNNB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball NJNNB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball NJNNB after 11548ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball NJNNB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball NJNNB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball NJNNB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:21:04.751Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b101e-idate-non-existent-entities-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b101e-idate-non-existent-entities-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b101e-idate-non-existent-entities-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b101e-idate-non-existent-entities-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 22,
                      "parallelIndex": 2,
                      "status": "interrupted",
                      "duration": 16262,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 33 total entities in database (Worker 22)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 22\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 29ms (Worker 22):\n"
                        },
                        {
                          "text": "  • Initial entities: 33\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 33\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human ZQYHA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human ZQYHA\n"
                        },
                        {
                          "text": "Creating entity: Human ZQYHA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human ZQYHA\" in 2521ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human ZQYHA (ID: temp-1751224885284-9bwatnr2b) in 3049ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human ZQYHA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball ZQYHB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball ZQYHB\n"
                        },
                        {
                          "text": "Creating entity: Ball ZQYHB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball ZQYHB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human ZQYHA (ID: 444)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 23ms (Worker 22):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 22\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (23ms cleanup / 16066ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 22 (9ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ZQYHB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ZQYHB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball ZQYHB after 11624ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball ZQYHB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball ZQYHB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball ZQYHB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:21:21.356Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b101e-idate-non-existent-entities-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b101e-idate-non-existent-entities-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b101e-idate-non-existent-entities-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b101e-idate-non-existent-entities-chromium-retry1/trace.zip"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-dde12c529cc2fa44585a",
              "file": "comparisons.spec.ts",
              "line": 250,
              "column": 7
            },
            {
              "title": "should handle decimal precision in results",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 18,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 16158,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball CXIAB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball CXIAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball CXIAB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball CXIAB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball CXIAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball CXIAB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball CXIAB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball CXIAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball CXIAB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 30 total entities in database (Worker 18)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 18\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 24ms (Worker 18):\n"
                        },
                        {
                          "text": "  • Initial entities: 30\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 30\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human CXIAA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human CXIAA\n"
                        },
                        {
                          "text": "Creating entity: Human CXIAA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human CXIAA\" in 2489ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human CXIAA (ID: temp-1751224868663-43ry32r4x) in 3015ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human CXIAA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball CXIAB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball CXIAB\n"
                        },
                        {
                          "text": "Creating entity: Ball CXIAB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball CXIAB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human CXIAA (ID: 438)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 28ms (Worker 18):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 18\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (28ms cleanup / 15954ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 18 (8ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball CXIAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball CXIAB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball CXIAB after 11565ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball CXIAB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball CXIAB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball CXIAB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:21:04.751Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-379c6-ecimal-precision-in-results-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-379c6-ecimal-precision-in-results-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-379c6-ecimal-precision-in-results-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-379c6-ecimal-precision-in-results-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 21,
                      "parallelIndex": 1,
                      "status": "failed",
                      "duration": 16261,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball LFAXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball LFAXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball LFAXB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball LFAXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball LFAXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball LFAXB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball LFAXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball LFAXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball LFAXB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 33 total entities in database (Worker 21)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 21\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 28ms (Worker 21):\n"
                        },
                        {
                          "text": "  • Initial entities: 33\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 33\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human LFAXA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human LFAXA\n"
                        },
                        {
                          "text": "Creating entity: Human LFAXA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human LFAXA\" in 2523ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human LFAXA (ID: temp-1751224885285-07ev3q379) in 3051ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human LFAXA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball LFAXB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball LFAXB\n"
                        },
                        {
                          "text": "Creating entity: Ball LFAXB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball LFAXB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human LFAXA (ID: 443)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 23ms (Worker 21):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 21\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (23ms cleanup / 16066ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 21 (6ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball LFAXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball LFAXB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball LFAXB after 11624ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball LFAXB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball LFAXB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball LFAXB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:21:21.355Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-379c6-ecimal-precision-in-results-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-379c6-ecimal-precision-in-results-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-379c6-ecimal-precision-in-results-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-379c6-ecimal-precision-in-results-chromium-retry1/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-adbe99fcb7a761f29e9b",
              "file": "comparisons.spec.ts",
              "line": 259,
              "column": 7
            },
            {
              "title": "should clear form after successful comparison",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 20,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 16164,
                      "error": {
                        "message": "Error: Entity creation failed for \"Ball AJSIB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AJSIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AJSIB\")')\u001b[22m\n",
                        "stack": "Error: Entity creation failed for \"Ball AJSIB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AJSIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AJSIB\")')\u001b[22m\n\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                          "column": 13,
                          "line": 143
                        },
                        "snippet": "\u001b[90m   at \u001b[39m../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                            "column": 13,
                            "line": 143
                          },
                          "message": "Error: Entity creation failed for \"Ball AJSIB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AJSIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AJSIB\")')\u001b[22m\n\n\n   at ../fixtures/page-objects.ts:143\n\n  141 |       // Minimal error reporting for performance\n  142 |       console.error(`Entity creation error: ${error.message}`);\n> 143 |       throw new Error(`Entity creation failed for \"${name}\": ${error.message}`);\n      |             ^\n  144 |     }\n  145 |   }\n  146 |\n    at EntityManagerPage.createEntity (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts:143:13)\n    at TestHelpers.createEntityForComplexTest (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/utils/helpers.ts:852:7)\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:59:24"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 30 total entities in database (Worker 20)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 20\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 24ms (Worker 20):\n"
                        },
                        {
                          "text": "  • Initial entities: 30\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 30\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human AJSIA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human AJSIA\n"
                        },
                        {
                          "text": "Creating entity: Human AJSIA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human AJSIA\" in 2477ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human AJSIA (ID: temp-1751224868651-sl2w3dytl) in 3003ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human AJSIA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball AJSIB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball AJSIB\n"
                        },
                        {
                          "text": "Creating entity: Ball AJSIB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball AJSIB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human AJSIA (ID: 437)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 28ms (Worker 20):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 20\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (28ms cleanup / 15954ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 20 (8ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AJSIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AJSIB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball AJSIB after 11571ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball AJSIB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball AJSIB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball AJSIB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 0,
                      "startTime": "2025-06-29T19:21:04.751Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-8e0d0-after-successful-comparison-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-8e0d0-after-successful-comparison-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-8e0d0-after-successful-comparison-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-8e0d0-after-successful-comparison-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/fixtures/page-objects.ts",
                        "column": 13,
                        "line": 143
                      }
                    },
                    {
                      "workerIndex": 23,
                      "parallelIndex": 0,
                      "status": "interrupted",
                      "duration": 16264,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 33 total entities in database (Worker 23)\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete for Worker 23\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 28ms (Worker 23):\n"
                        },
                        {
                          "text": "  • Initial entities: 33\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 33\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating comparison test entity 1/6: Human YTFBA\n"
                        },
                        {
                          "text": "Creating entity for complex test: Human YTFBA\n"
                        },
                        {
                          "text": "Creating entity: Human YTFBA\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human YTFBA\" in 2534ms\n"
                        },
                        {
                          "text": "  ✅ Complex test entity created: Human YTFBA (ID: temp-1751224885296-8hkfk7b6z) in 3062ms\n"
                        },
                        {
                          "text": "✅ Comparison test entity 1 created: Human YTFBA\n"
                        },
                        {
                          "text": "Creating comparison test entity 2/6: Ball YTFBB\n"
                        },
                        {
                          "text": "Creating entity for complex test: Ball YTFBB\n"
                        },
                        {
                          "text": "Creating entity: Ball YTFBB\n"
                        },
                        {
                          "text": "First attempt to find entity \"Ball YTFBB\" failed, trying enhanced refresh...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Fallback cleanup for 1 named entities...\n"
                        },
                        {
                          "text": "  ✓ Fallback deleted entity: Human YTFBA (ID: 445)\n"
                        },
                        {
                          "text": "  ℹ️  Fallback cleanup handled 1 additional entities\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 23ms (Worker 23):\n"
                        },
                        {
                          "text": "  • Entities deleted: 1 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  • Worker isolation: 23\n"
                        },
                        {
                          "text": "  • Test efficiency: 100% (23ms cleanup / 16066ms total)\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        },
                        {
                          "text": "  ✅ Cleanup verification: No test entities remain from Worker 23 (6ms)\n"
                        }
                      ],
                      "stderr": [
                        {
                          "text": "Entity creation error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball YTFBB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball YTFBB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "  ❌ Complex test entity creation failed: Ball YTFBB after 11614ms\n"
                        },
                        {
                          "text": "     Error: Entity creation failed for \"Ball YTFBB\": \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Ball YTFBB\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Ball YTFBB\")')\u001b[22m\n\n"
                        },
                        {
                          "text": "     Current URL: http://localhost:3000/entities\n"
                        },
                        {
                          "text": "     Page title: SIMILE\n"
                        }
                      ],
                      "retry": 1,
                      "startTime": "2025-06-29T19:21:21.355Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-8e0d0-after-successful-comparison-chromium-retry1/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-8e0d0-after-successful-comparison-chromium-retry1/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-8e0d0-after-successful-comparison-chromium-retry1/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-8e0d0-after-successful-comparison-chromium-retry1/trace.zip"
                        }
                      ]
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-4469f619db661f2ed843",
              "file": "comparisons.spec.ts",
              "line": 280,
              "column": 7
            },
            {
              "title": "should handle rapid successive comparisons",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-eb74e6b2805b080a35a6",
              "file": "comparisons.spec.ts",
              "line": 297,
              "column": 7
            },
            {
              "title": "should respect maximum path length limits",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-32c9662514bb00c6ac2a",
              "file": "comparisons.spec.ts",
              "line": 314,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-996a59fe8054d81075b7",
              "file": "comparisons.spec.ts",
              "line": 351,
              "column": 7
            },
            {
              "title": "should display comparison page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-09f169bb4fbfca7ebd21",
              "file": "comparisons.spec.ts",
              "line": 136,
              "column": 7
            },
            {
              "title": "should calculate direct relationships",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-ddc570b641e608d31e0d",
              "file": "comparisons.spec.ts",
              "line": 144,
              "column": 7
            },
            {
              "title": "should calculate transitive relationships",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-5489436aa17a4b7fbc85",
              "file": "comparisons.spec.ts",
              "line": 158,
              "column": 7
            },
            {
              "title": "should calculate complex multi-hop paths",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-ee0d07af83af076fd0a1",
              "file": "comparisons.spec.ts",
              "line": 172,
              "column": 7
            },
            {
              "title": "should handle reverse path calculations",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-72fe5a43e7281b7fa1e5",
              "file": "comparisons.spec.ts",
              "line": 187,
              "column": 7
            },
            {
              "title": "should handle different unit entities with no connection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-4cf38eb6479501bb3f92",
              "file": "comparisons.spec.ts",
              "line": 201,
              "column": 7
            },
            {
              "title": "should handle same entity comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-ad84851910c7df27242b",
              "file": "comparisons.spec.ts",
              "line": 217,
              "column": 7
            },
            {
              "title": "should handle custom from count values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-3fc0c039e1b8229d21c4",
              "file": "comparisons.spec.ts",
              "line": 228,
              "column": 7
            },
            {
              "title": "should validate entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-e46039d79939d723b870",
              "file": "comparisons.spec.ts",
              "line": 242,
              "column": 7
            },
            {
              "title": "should validate non-existent entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-e489e6fe49d68af04ab1",
              "file": "comparisons.spec.ts",
              "line": 250,
              "column": 7
            },
            {
              "title": "should handle decimal precision in results",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-7dc564482b2588ac1d04",
              "file": "comparisons.spec.ts",
              "line": 259,
              "column": 7
            },
            {
              "title": "should clear form after successful comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-09181f1aa7ef65aaf6ce",
              "file": "comparisons.spec.ts",
              "line": 280,
              "column": 7
            },
            {
              "title": "should handle rapid successive comparisons",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-130953dd609a31d64aa8",
              "file": "comparisons.spec.ts",
              "line": 297,
              "column": 7
            },
            {
              "title": "should respect maximum path length limits",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-849363998e7d171ea9db",
              "file": "comparisons.spec.ts",
              "line": 314,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-fa8c5bf7bc2e7f79b791",
              "file": "comparisons.spec.ts",
              "line": 351,
              "column": 7
            },
            {
              "title": "should display comparison page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-cc7d6aa7748091b4da50",
              "file": "comparisons.spec.ts",
              "line": 136,
              "column": 7
            },
            {
              "title": "should calculate direct relationships",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-98396ab11a92e1da98a3",
              "file": "comparisons.spec.ts",
              "line": 144,
              "column": 7
            },
            {
              "title": "should calculate transitive relationships",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-74838f8e5f8f53560cbc",
              "file": "comparisons.spec.ts",
              "line": 158,
              "column": 7
            },
            {
              "title": "should calculate complex multi-hop paths",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-e9dcd07c7ea93c2cd475",
              "file": "comparisons.spec.ts",
              "line": 172,
              "column": 7
            },
            {
              "title": "should handle reverse path calculations",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-0d3bf65d2af148c70122",
              "file": "comparisons.spec.ts",
              "line": 187,
              "column": 7
            },
            {
              "title": "should handle different unit entities with no connection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-3a8a0803f811373c8495",
              "file": "comparisons.spec.ts",
              "line": 201,
              "column": 7
            },
            {
              "title": "should handle same entity comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-91df89957049a9b6d444",
              "file": "comparisons.spec.ts",
              "line": 217,
              "column": 7
            },
            {
              "title": "should handle custom from count values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-8f788e0136057c58b206",
              "file": "comparisons.spec.ts",
              "line": 228,
              "column": 7
            },
            {
              "title": "should validate entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-274a366a2aed2e4b66ac",
              "file": "comparisons.spec.ts",
              "line": 242,
              "column": 7
            },
            {
              "title": "should validate non-existent entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-6cce352380dc802e92a5",
              "file": "comparisons.spec.ts",
              "line": 250,
              "column": 7
            },
            {
              "title": "should handle decimal precision in results",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-9eefbfe3b6e95db8e363",
              "file": "comparisons.spec.ts",
              "line": 259,
              "column": 7
            },
            {
              "title": "should clear form after successful comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-91753e5ae820da7e1a61",
              "file": "comparisons.spec.ts",
              "line": 280,
              "column": 7
            },
            {
              "title": "should handle rapid successive comparisons",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-944e794bf780c819258f",
              "file": "comparisons.spec.ts",
              "line": 297,
              "column": 7
            },
            {
              "title": "should respect maximum path length limits",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-bf85fff8e956886ac07c",
              "file": "comparisons.spec.ts",
              "line": 314,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-c35bbd3c255c6e77f874",
              "file": "comparisons.spec.ts",
              "line": 351,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "connections.spec.ts",
      "file": "connections.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Connection Management",
          "file": "connections.spec.ts",
          "line": 6,
          "column": 6,
          "specs": [
            {
              "title": "should display connection management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-89cf8d0005d7ae37c3c7",
              "file": "connections.spec.ts",
              "line": 68,
              "column": 7
            },
            {
              "title": "should show and hide connection form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-1f5e0a82fd166256a222",
              "file": "connections.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should create bidirectional connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-7147252b7e2bb76da71a",
              "file": "connections.spec.ts",
              "line": 90,
              "column": 7
            },
            {
              "title": "should validate positive relationship values with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-4389931d41d61e987072",
              "file": "connections.spec.ts",
              "line": 119,
              "column": 7
            },
            {
              "title": "should validate decimal precision with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-10850e19363b0ff9346c",
              "file": "connections.spec.ts",
              "line": 163,
              "column": 7
            },
            {
              "title": "should prevent zero multiplier values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-3625df6327a9fcf271ac",
              "file": "connections.spec.ts",
              "line": 205,
              "column": 7
            },
            {
              "title": "should validate same-unit connections only",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-8c3f4a4f0186bb588024",
              "file": "connections.spec.ts",
              "line": 237,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-c4355c1749789a1f565a",
              "file": "connections.spec.ts",
              "line": 258,
              "column": 7
            },
            {
              "title": "should delete a connection successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-5641ac6865251a11ba7f",
              "file": "connections.spec.ts",
              "line": 303,
              "column": 7
            },
            {
              "title": "should handle connection form validation for required fields with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-b09906742cede2456c84",
              "file": "connections.spec.ts",
              "line": 329,
              "column": 7
            },
            {
              "title": "should show real-time validation states across all connection form fields",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-9522dbd52555b4fec117",
              "file": "connections.spec.ts",
              "line": 383,
              "column": 7
            },
            {
              "title": "should prevent duplicate connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-a2d4c3e92f2a9e66de0f",
              "file": "connections.spec.ts",
              "line": 452,
              "column": 7
            },
            {
              "title": "should handle rapid connection creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-3d7c0be7bf1892ba5445",
              "file": "connections.spec.ts",
              "line": 475,
              "column": 7
            },
            {
              "title": "should maintain connection list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-8ef74e2d22ee76d96ebc",
              "file": "connections.spec.ts",
              "line": 497,
              "column": 7
            },
            {
              "title": "should display connection management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-b6c5a9e69b8e3a64823c",
              "file": "connections.spec.ts",
              "line": 68,
              "column": 7
            },
            {
              "title": "should show and hide connection form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-9354c5ece45689452f22",
              "file": "connections.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should create bidirectional connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-9f5a2be5c109466e3caf",
              "file": "connections.spec.ts",
              "line": 90,
              "column": 7
            },
            {
              "title": "should validate positive relationship values with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-49e49d5270b2c898833a",
              "file": "connections.spec.ts",
              "line": 119,
              "column": 7
            },
            {
              "title": "should validate decimal precision with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d2e12139574eef4fb70c",
              "file": "connections.spec.ts",
              "line": 163,
              "column": 7
            },
            {
              "title": "should prevent zero multiplier values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-6312f2558cdbf8c285ad",
              "file": "connections.spec.ts",
              "line": 205,
              "column": 7
            },
            {
              "title": "should validate same-unit connections only",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-23188e2309ede443f729",
              "file": "connections.spec.ts",
              "line": 237,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d222a1bb2b75cb8fa3c4",
              "file": "connections.spec.ts",
              "line": 258,
              "column": 7
            },
            {
              "title": "should delete a connection successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-6108fff9474bb4422d2b",
              "file": "connections.spec.ts",
              "line": 303,
              "column": 7
            },
            {
              "title": "should handle connection form validation for required fields with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-e723355a0daa8c0870de",
              "file": "connections.spec.ts",
              "line": 329,
              "column": 7
            },
            {
              "title": "should show real-time validation states across all connection form fields",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-58e91db3df14f9685e4b",
              "file": "connections.spec.ts",
              "line": 383,
              "column": 7
            },
            {
              "title": "should prevent duplicate connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d8dbe01708fa4d161591",
              "file": "connections.spec.ts",
              "line": 452,
              "column": 7
            },
            {
              "title": "should handle rapid connection creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-12cbcef4239b401ae054",
              "file": "connections.spec.ts",
              "line": 475,
              "column": 7
            },
            {
              "title": "should maintain connection list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-04fa295f72d5f01599f9",
              "file": "connections.spec.ts",
              "line": 497,
              "column": 7
            },
            {
              "title": "should display connection management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-3a83219f65d5c160e751",
              "file": "connections.spec.ts",
              "line": 68,
              "column": 7
            },
            {
              "title": "should show and hide connection form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-0eb551b4b22c895004ab",
              "file": "connections.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should create bidirectional connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-2bdc409db41585270be9",
              "file": "connections.spec.ts",
              "line": 90,
              "column": 7
            },
            {
              "title": "should validate positive relationship values with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-2e14dc6f0c685d8908af",
              "file": "connections.spec.ts",
              "line": 119,
              "column": 7
            },
            {
              "title": "should validate decimal precision with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-68b874b02eebf5411f39",
              "file": "connections.spec.ts",
              "line": 163,
              "column": 7
            },
            {
              "title": "should prevent zero multiplier values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-62bf02938e24c360b22c",
              "file": "connections.spec.ts",
              "line": 205,
              "column": 7
            },
            {
              "title": "should validate same-unit connections only",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-06fe7ade318cd6c1d1e7",
              "file": "connections.spec.ts",
              "line": 237,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d3e881b0728a64594ac0",
              "file": "connections.spec.ts",
              "line": 258,
              "column": 7
            },
            {
              "title": "should delete a connection successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-5eef9928df27181c2779",
              "file": "connections.spec.ts",
              "line": 303,
              "column": 7
            },
            {
              "title": "should handle connection form validation for required fields with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-8cab69022afb1c838d6c",
              "file": "connections.spec.ts",
              "line": 329,
              "column": 7
            },
            {
              "title": "should show real-time validation states across all connection form fields",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-a45806e7499f53dc3fbe",
              "file": "connections.spec.ts",
              "line": 383,
              "column": 7
            },
            {
              "title": "should prevent duplicate connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-6989eaec183fff268824",
              "file": "connections.spec.ts",
              "line": 452,
              "column": 7
            },
            {
              "title": "should handle rapid connection creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-2ca10f4cc13fa70b66a8",
              "file": "connections.spec.ts",
              "line": 475,
              "column": 7
            },
            {
              "title": "should maintain connection list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-82bed2f3416f50137897",
              "file": "connections.spec.ts",
              "line": 497,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "entities.spec.ts",
      "file": "entities.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Entity Management",
          "file": "entities.spec.ts",
          "line": 6,
          "column": 6,
          "specs": [
            {
              "title": "should display entity management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-7301148abc4cbc2e0449",
              "file": "entities.spec.ts",
              "line": 41,
              "column": 7
            },
            {
              "title": "should create a new entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-f9b8fa840e3bf5b25b15",
              "file": "entities.spec.ts",
              "line": 47,
              "column": 7
            },
            {
              "title": "should show and hide entity form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-2897fc9b86cd3e336219",
              "file": "entities.spec.ts",
              "line": 59,
              "column": 7
            },
            {
              "title": "should validate entity name requirements with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-78165309abd2f93ac6d9",
              "file": "entities.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should accept valid entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-25e1ba607c2e7a8df80d",
              "file": "entities.spec.ts",
              "line": 142,
              "column": 7
            },
            {
              "title": "should prevent duplicate entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-6d5aceca1e1c0a9efeae",
              "file": "entities.spec.ts",
              "line": 154,
              "column": 7
            },
            {
              "title": "should delete an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-f56026af2309c45f89b4",
              "file": "entities.spec.ts",
              "line": 172,
              "column": 7
            },
            {
              "title": "should edit an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-e2fd5bf243ca7cc9093b",
              "file": "entities.spec.ts",
              "line": 189,
              "column": 7
            },
            {
              "title": "should handle entity form validation for name length",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-f1b1f32fd78b4bd1eb82",
              "file": "entities.spec.ts",
              "line": 205,
              "column": 7
            },
            {
              "title": "should handle entity form validation for special characters with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-d9306e4ad89f23e0ead1",
              "file": "entities.spec.ts",
              "line": 228,
              "column": 7
            },
            {
              "title": "should provide real-time validation feedback during typing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-e5f2472ce680390f0777",
              "file": "entities.spec.ts",
              "line": 252,
              "column": 7
            },
            {
              "title": "should handle empty entity list gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-6c3de81d6fdd3a5a4406",
              "file": "entities.spec.ts",
              "line": 285,
              "column": 7
            },
            {
              "title": "should maintain entity list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-767a128aa307272989da",
              "file": "entities.spec.ts",
              "line": 294,
              "column": 7
            },
            {
              "title": "should handle rapid entity creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-8f71abff0626d5153d1d",
              "file": "entities.spec.ts",
              "line": 309,
              "column": 7
            },
            {
              "title": "should maintain validation consistency across form interactions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-001fc6127f420c5826f7",
              "file": "entities.spec.ts",
              "line": 330,
              "column": 7
            },
            {
              "title": "should display entity management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-45e5f5d8b6d8d2e33d7e",
              "file": "entities.spec.ts",
              "line": 41,
              "column": 7
            },
            {
              "title": "should create a new entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-59035f7914db90cddccd",
              "file": "entities.spec.ts",
              "line": 47,
              "column": 7
            },
            {
              "title": "should show and hide entity form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b623675f4b5bfbae7a2c",
              "file": "entities.spec.ts",
              "line": 59,
              "column": 7
            },
            {
              "title": "should validate entity name requirements with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-f904ff9354448a431bcd",
              "file": "entities.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should accept valid entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b3390d70a987084c18d9",
              "file": "entities.spec.ts",
              "line": 142,
              "column": 7
            },
            {
              "title": "should prevent duplicate entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b62f88db87af155669b1",
              "file": "entities.spec.ts",
              "line": 154,
              "column": 7
            },
            {
              "title": "should delete an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b127091ea7dd91083349",
              "file": "entities.spec.ts",
              "line": 172,
              "column": 7
            },
            {
              "title": "should edit an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-a1c01dc30b2439fd20ec",
              "file": "entities.spec.ts",
              "line": 189,
              "column": 7
            },
            {
              "title": "should handle entity form validation for name length",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-5aea8dad5724970fe76c",
              "file": "entities.spec.ts",
              "line": 205,
              "column": 7
            },
            {
              "title": "should handle entity form validation for special characters with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-83138fd69bcb8a3070e8",
              "file": "entities.spec.ts",
              "line": 228,
              "column": 7
            },
            {
              "title": "should provide real-time validation feedback during typing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-d08c863f6f75ebb386b0",
              "file": "entities.spec.ts",
              "line": 252,
              "column": 7
            },
            {
              "title": "should handle empty entity list gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-8ea30d7d05f742f449cd",
              "file": "entities.spec.ts",
              "line": 285,
              "column": 7
            },
            {
              "title": "should maintain entity list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-1d37a4693a7af3e54cf6",
              "file": "entities.spec.ts",
              "line": 294,
              "column": 7
            },
            {
              "title": "should handle rapid entity creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-aea741ce072c4c79a9de",
              "file": "entities.spec.ts",
              "line": 309,
              "column": 7
            },
            {
              "title": "should maintain validation consistency across form interactions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-76b61ac1cbf9eb271752",
              "file": "entities.spec.ts",
              "line": 330,
              "column": 7
            },
            {
              "title": "should display entity management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-955506e5559a9041e0bf",
              "file": "entities.spec.ts",
              "line": 41,
              "column": 7
            },
            {
              "title": "should create a new entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-08b525741cd9913c0b9d",
              "file": "entities.spec.ts",
              "line": 47,
              "column": 7
            },
            {
              "title": "should show and hide entity form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-2e8e8f5e83966aad66c7",
              "file": "entities.spec.ts",
              "line": 59,
              "column": 7
            },
            {
              "title": "should validate entity name requirements with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-1dfc9184050068d58f3a",
              "file": "entities.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should accept valid entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-c3070039b12a40938799",
              "file": "entities.spec.ts",
              "line": 142,
              "column": 7
            },
            {
              "title": "should prevent duplicate entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-1c8636db88eae63bf945",
              "file": "entities.spec.ts",
              "line": 154,
              "column": 7
            },
            {
              "title": "should delete an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-270d2c1473495aa2ed92",
              "file": "entities.spec.ts",
              "line": 172,
              "column": 7
            },
            {
              "title": "should edit an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b7042503d6c2e9d07a69",
              "file": "entities.spec.ts",
              "line": 189,
              "column": 7
            },
            {
              "title": "should handle entity form validation for name length",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b3ed2c4c7c31efcd7c7d",
              "file": "entities.spec.ts",
              "line": 205,
              "column": 7
            },
            {
              "title": "should handle entity form validation for special characters with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-805807b72833b49cf022",
              "file": "entities.spec.ts",
              "line": 228,
              "column": 7
            },
            {
              "title": "should provide real-time validation feedback during typing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-52f9f01956f38bbb02c7",
              "file": "entities.spec.ts",
              "line": 252,
              "column": 7
            },
            {
              "title": "should handle empty entity list gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-003d4deecefc521b3a75",
              "file": "entities.spec.ts",
              "line": 285,
              "column": 7
            },
            {
              "title": "should maintain entity list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-65e1763ab1da1ca3f61a",
              "file": "entities.spec.ts",
              "line": 294,
              "column": 7
            },
            {
              "title": "should handle rapid entity creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-9cc958cba5f4ce175ba0",
              "file": "entities.spec.ts",
              "line": 309,
              "column": 7
            },
            {
              "title": "should maintain validation consistency across form interactions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-6c7d978fdd239fed0157",
              "file": "entities.spec.ts",
              "line": 330,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "error-handling.spec.ts",
      "file": "error-handling.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Error Handling and Edge Cases",
          "file": "error-handling.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should handle API error gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ca859f9949a8c2a31afc",
              "file": "error-handling.spec.ts",
              "line": 27,
              "column": 7
            },
            {
              "title": "should handle network timeout gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-e1f054bd740f09a6af63",
              "file": "error-handling.spec.ts",
              "line": 49,
              "column": 7
            },
            {
              "title": "should handle malformed API responses",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-cea6557acb8ba78fd76d",
              "file": "error-handling.spec.ts",
              "line": 73,
              "column": 7
            },
            {
              "title": "should handle server 500 errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-7a6c9fe05e80529e9036",
              "file": "error-handling.spec.ts",
              "line": 90,
              "column": 7
            },
            {
              "title": "should handle 404 errors for missing entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-8522d838261a58188975",
              "file": "error-handling.spec.ts",
              "line": 113,
              "column": 7
            },
            {
              "title": "should handle browser console errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-95d03b3f00a52226f367",
              "file": "error-handling.spec.ts",
              "line": 135,
              "column": 7
            },
            {
              "title": "should handle empty/whitespace-only inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ce960f9efe78d30397ad",
              "file": "error-handling.spec.ts",
              "line": 164,
              "column": 7
            },
            {
              "title": "should handle very long entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-527a9644caab136ce976",
              "file": "error-handling.spec.ts",
              "line": 188,
              "column": 7
            },
            {
              "title": "should handle special Unicode characters",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-f587986b86018e1ce827",
              "file": "error-handling.spec.ts",
              "line": 201,
              "column": 7
            },
            {
              "title": "should handle concurrent user actions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ea8c11647c1abe3494aa",
              "file": "error-handling.spec.ts",
              "line": 232,
              "column": 7
            },
            {
              "title": "should handle page refresh during form submission",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-cd1f0c5d813ff8d2d537",
              "file": "error-handling.spec.ts",
              "line": 257,
              "column": 7
            },
            {
              "title": "should handle browser back during form editing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-3f9babbfa1fe18458083",
              "file": "error-handling.spec.ts",
              "line": 273,
              "column": 7
            },
            {
              "title": "should handle extremely large numbers in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-3d554af004bd5a9751e0",
              "file": "error-handling.spec.ts",
              "line": 289,
              "column": 7
            },
            {
              "title": "should handle scientific notation in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ca613b308615c4034ba0",
              "file": "error-handling.spec.ts",
              "line": 311,
              "column": 7
            },
            {
              "title": "should handle API error gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-38aae48e3c532d330abc",
              "file": "error-handling.spec.ts",
              "line": 27,
              "column": 7
            },
            {
              "title": "should handle network timeout gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-c7fa5cc7ea6fd919aa76",
              "file": "error-handling.spec.ts",
              "line": 49,
              "column": 7
            },
            {
              "title": "should handle malformed API responses",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-2aaab293510cc3086b91",
              "file": "error-handling.spec.ts",
              "line": 73,
              "column": 7
            },
            {
              "title": "should handle server 500 errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-a83d139b173e43665506",
              "file": "error-handling.spec.ts",
              "line": 90,
              "column": 7
            },
            {
              "title": "should handle 404 errors for missing entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-238d4b3a44d5f7591bca",
              "file": "error-handling.spec.ts",
              "line": 113,
              "column": 7
            },
            {
              "title": "should handle browser console errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-95762cca6409391a2152",
              "file": "error-handling.spec.ts",
              "line": 135,
              "column": 7
            },
            {
              "title": "should handle empty/whitespace-only inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-eef9f59e04e027c4a69c",
              "file": "error-handling.spec.ts",
              "line": 164,
              "column": 7
            },
            {
              "title": "should handle very long entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-fe8b417e6dc573df16d5",
              "file": "error-handling.spec.ts",
              "line": 188,
              "column": 7
            },
            {
              "title": "should handle special Unicode characters",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-45b1968fe759452e44d0",
              "file": "error-handling.spec.ts",
              "line": 201,
              "column": 7
            },
            {
              "title": "should handle concurrent user actions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-c96368441054b965c34a",
              "file": "error-handling.spec.ts",
              "line": 232,
              "column": 7
            },
            {
              "title": "should handle page refresh during form submission",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ee06fa93285bbb47b3f0",
              "file": "error-handling.spec.ts",
              "line": 257,
              "column": 7
            },
            {
              "title": "should handle browser back during form editing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-bc01480e1dfa8f2dda53",
              "file": "error-handling.spec.ts",
              "line": 273,
              "column": 7
            },
            {
              "title": "should handle extremely large numbers in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-4b9de9ff0248d911f70d",
              "file": "error-handling.spec.ts",
              "line": 289,
              "column": 7
            },
            {
              "title": "should handle scientific notation in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-d94114d7ae42fec859d4",
              "file": "error-handling.spec.ts",
              "line": 311,
              "column": 7
            },
            {
              "title": "should handle API error gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-36fb5c7303aaf4f2b6ae",
              "file": "error-handling.spec.ts",
              "line": 27,
              "column": 7
            },
            {
              "title": "should handle network timeout gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-a5708fd52186e4719d78",
              "file": "error-handling.spec.ts",
              "line": 49,
              "column": 7
            },
            {
              "title": "should handle malformed API responses",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ddf0c5e4dcaf69938e54",
              "file": "error-handling.spec.ts",
              "line": 73,
              "column": 7
            },
            {
              "title": "should handle server 500 errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-044c88f51fb0692ea3ca",
              "file": "error-handling.spec.ts",
              "line": 90,
              "column": 7
            },
            {
              "title": "should handle 404 errors for missing entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-69a5a5106f067c7f6c02",
              "file": "error-handling.spec.ts",
              "line": 113,
              "column": 7
            },
            {
              "title": "should handle browser console errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-0c9cd87c9c27888c6ef9",
              "file": "error-handling.spec.ts",
              "line": 135,
              "column": 7
            },
            {
              "title": "should handle empty/whitespace-only inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-026d7a5a972aa2aa9647",
              "file": "error-handling.spec.ts",
              "line": 164,
              "column": 7
            },
            {
              "title": "should handle very long entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-d47d27f6b6bcac66daff",
              "file": "error-handling.spec.ts",
              "line": 188,
              "column": 7
            },
            {
              "title": "should handle special Unicode characters",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-faeaaec4aaab3976ae38",
              "file": "error-handling.spec.ts",
              "line": 201,
              "column": 7
            },
            {
              "title": "should handle concurrent user actions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-e50e79fe6ec0faa42dff",
              "file": "error-handling.spec.ts",
              "line": 232,
              "column": 7
            },
            {
              "title": "should handle page refresh during form submission",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-17732d458766d4c28db1",
              "file": "error-handling.spec.ts",
              "line": 257,
              "column": 7
            },
            {
              "title": "should handle browser back during form editing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-2cb99ec1402289383fe6",
              "file": "error-handling.spec.ts",
              "line": 273,
              "column": 7
            },
            {
              "title": "should handle extremely large numbers in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-63a565e819ec1f488c6f",
              "file": "error-handling.spec.ts",
              "line": 289,
              "column": 7
            },
            {
              "title": "should handle scientific notation in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-2d6b4b2cca2e87610cae",
              "file": "error-handling.spec.ts",
              "line": 311,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "navigation.spec.ts",
      "file": "navigation.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Navigation",
          "file": "navigation.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should load homepage successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-7c8d07ae606044b23924",
              "file": "navigation.spec.ts",
              "line": 15,
              "column": 7
            },
            {
              "title": "should navigate between all pages",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-1ae7e8272e895bd23cd5",
              "file": "navigation.spec.ts",
              "line": 20,
              "column": 7
            },
            {
              "title": "should highlight active navigation item",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-8c32442308196962cf34",
              "file": "navigation.spec.ts",
              "line": 39,
              "column": 7
            },
            {
              "title": "should handle browser back/forward navigation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-4d3dad8fe99836d99504",
              "file": "navigation.spec.ts",
              "line": 58,
              "column": 7
            },
            {
              "title": "should maintain navigation state on page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-821abdd12a244527b08d",
              "file": "navigation.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should handle invalid routes gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-2bcb424aa225584d405e",
              "file": "navigation.spec.ts",
              "line": 87,
              "column": 7
            },
            {
              "title": "should load homepage successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-06c2ff18bde51594b953",
              "file": "navigation.spec.ts",
              "line": 15,
              "column": 7
            },
            {
              "title": "should navigate between all pages",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-58112e7c8bbad3ae8e79",
              "file": "navigation.spec.ts",
              "line": 20,
              "column": 7
            },
            {
              "title": "should highlight active navigation item",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-2fa59172e6173ea03053",
              "file": "navigation.spec.ts",
              "line": 39,
              "column": 7
            },
            {
              "title": "should handle browser back/forward navigation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-6fa7834a643219c7a58b",
              "file": "navigation.spec.ts",
              "line": 58,
              "column": 7
            },
            {
              "title": "should maintain navigation state on page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-a82860f00d65937995ae",
              "file": "navigation.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should handle invalid routes gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-ccc1b38004a43af3a3d7",
              "file": "navigation.spec.ts",
              "line": 87,
              "column": 7
            },
            {
              "title": "should load homepage successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-2b1ef374bcd2b82f07c3",
              "file": "navigation.spec.ts",
              "line": 15,
              "column": 7
            },
            {
              "title": "should navigate between all pages",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-944f429d2e01bda3386f",
              "file": "navigation.spec.ts",
              "line": 20,
              "column": 7
            },
            {
              "title": "should highlight active navigation item",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-25afbb7005f1925d43a2",
              "file": "navigation.spec.ts",
              "line": 39,
              "column": 7
            },
            {
              "title": "should handle browser back/forward navigation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-e0cc1f592dc5c5f852be",
              "file": "navigation.spec.ts",
              "line": 58,
              "column": 7
            },
            {
              "title": "should maintain navigation state on page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-364b7c19ddfb350b4e7b",
              "file": "navigation.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should handle invalid routes gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-5e3949d649b3832680ed",
              "file": "navigation.spec.ts",
              "line": 87,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "sequential-entity-creation.spec.ts",
      "file": "sequential-entity-creation.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Sequential Entity Creation Test",
          "file": "sequential-entity-creation.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should create multiple entities sequentially without state interference",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "8fbe495bc0383df4e88b-9352dfb8ef65c43d4733",
              "file": "sequential-entity-creation.spec.ts",
              "line": 23,
              "column": 7
            },
            {
              "title": "should handle rapid entity creation (stress test)",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "8fbe495bc0383df4e88b-8fbb946f19376f0de19b",
              "file": "sequential-entity-creation.spec.ts",
              "line": 66,
              "column": 7
            },
            {
              "title": "should create multiple entities sequentially without state interference",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "8fbe495bc0383df4e88b-abc847f2d7ab11a073f6",
              "file": "sequential-entity-creation.spec.ts",
              "line": 23,
              "column": 7
            },
            {
              "title": "should handle rapid entity creation (stress test)",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "8fbe495bc0383df4e88b-87b234accfddbb009590",
              "file": "sequential-entity-creation.spec.ts",
              "line": 66,
              "column": 7
            },
            {
              "title": "should create multiple entities sequentially without state interference",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "8fbe495bc0383df4e88b-087b0dbde861c35d24c3",
              "file": "sequential-entity-creation.spec.ts",
              "line": 23,
              "column": 7
            },
            {
              "title": "should handle rapid entity creation (stress test)",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "8fbe495bc0383df4e88b-9367c110815436881790",
              "file": "sequential-entity-creation.spec.ts",
              "line": 66,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "setup-verification.spec.ts",
      "file": "setup-verification.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Setup Verification",
          "file": "setup-verification.spec.ts",
          "line": 3,
          "column": 6,
          "specs": [
            {
              "title": "should verify basic Playwright setup",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-20c0d3fde9535b962ab2",
              "file": "setup-verification.spec.ts",
              "line": 4,
              "column": 7
            },
            {
              "title": "should verify localhost accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-8dd9f647ec1f7b0617eb",
              "file": "setup-verification.spec.ts",
              "line": 10,
              "column": 7
            },
            {
              "title": "should verify backend API accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-35dadd570e478a551ba8",
              "file": "setup-verification.spec.ts",
              "line": 22,
              "column": 7
            },
            {
              "title": "should verify basic Playwright setup",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-f694b85f133ed63960e0",
              "file": "setup-verification.spec.ts",
              "line": 4,
              "column": 7
            },
            {
              "title": "should verify localhost accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-fdb354fa095a51eb2506",
              "file": "setup-verification.spec.ts",
              "line": 10,
              "column": 7
            },
            {
              "title": "should verify backend API accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-853196aad21c5a0c5b1b",
              "file": "setup-verification.spec.ts",
              "line": 22,
              "column": 7
            },
            {
              "title": "should verify basic Playwright setup",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-dac09f63f06fcdcd0e16",
              "file": "setup-verification.spec.ts",
              "line": 4,
              "column": 7
            },
            {
              "title": "should verify localhost accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-7e2ef5d5b8013d8b70d7",
              "file": "setup-verification.spec.ts",
              "line": 10,
              "column": 7
            },
            {
              "title": "should verify backend API accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-93b27af1fc161ea7ebf4",
              "file": "setup-verification.spec.ts",
              "line": 22,
              "column": 7
            }
          ]
        }
      ]
    }
  ],
  "errors": [
    {
      "message": "\u001b[31mTesting stopped early after 10 maximum allowed failures.\u001b[39m"
    }
  ],
  "stats": {
    "startTime": "2025-06-29T19:19:23.933Z",
    "duration": 133839.299,
    "expected": 0,
    "skipped": 195,
    "unexpected": 12,
    "flaky": 0
  }
}
