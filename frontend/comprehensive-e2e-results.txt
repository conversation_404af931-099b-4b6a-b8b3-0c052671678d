
> simile-frontend@0.1.0 test:e2e
> playwright test

================================================================================
🚀 Starting E2E test suite with 210 tests
🔧 Running with 3 workers
================================================================================


Running 210 tests using 3 workers

[1/210] 🏃 Starting: should display comparison page correctly
[1/210] 🏃 Starting: should calculate transitive relationships
[1/210] 🏃 Starting: should calculate direct relationships
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 95 total entities in database (Worker 2)
  Identified 15 test entities to delete for Worker 2
  Found 95 total entities in database (Worker 1)
  Identified 15 test entities to delete for Worker 1
  Found 95 total entities in database (Worker 0)
  Identified 15 test entities to delete for Worker 0
  ✓ Deleted test entity: Ball PSOUB (ID: 2493)
  ✓ Deleted test entity: Ball YXGTB (ID: 2492)
  ✓ Deleted test entity: Build PSOUC (ID: 2496)
  ✓ Deleted test entity: Ball PSOUB (ID: 2493)
  ✓ Deleted test entity: Ball YXGTB (ID: 2492)
  ✓ Deleted test entity: Build PSOUC (ID: 2496)
  ✓ Deleted test entity: Ball PSOUB (ID: 2493)
  ❌ Failed to delete entity: Build PSOUC (ID: 2496)
  ❌ Failed to delete entity: Ball YXGTB (ID: 2492)
  ✓ Deleted test entity: Build YXGTC (ID: 2495)
  ✓ Deleted test entity: Build YXGTC (ID: 2495)
  ❌ Failed to delete entity: Build YXGTC (ID: 2495)
  ✓ Deleted test entity: Car PSOUD (ID: 2498)
  ✓ Deleted test entity: Car JNBGD (ID: 2488)
  ✓ Deleted test entity: Car JNBGD (ID: 2488)
  ✓ Deleted test entity: Car JNBGD (ID: 2488)
  ❌ Failed to delete entity: Car PSOUD (ID: 2498)
  ✓ Deleted test entity: Car YXGTD (ID: 2497)
  ✓ Deleted test entity: Eleph PSOUE (ID: 2500)
  ✓ Deleted test entity: Car YXGTD (ID: 2497)
  ✓ Deleted test entity: Car YXGTD (ID: 2497)
  ✓ Deleted test entity: Eleph PSOUE (ID: 2500)
  ✓ Deleted test entity: Eleph JNBGE (ID: 2491)
  ❌ Failed to delete entity: Car PSOUD (ID: 2498)
  ✓ Deleted test entity: Eleph JNBGE (ID: 2491)
  ✓ Deleted test entity: Eleph YXGTE (ID: 2499)
  ❌ Failed to delete entity: Eleph JNBGE (ID: 2491)
  ✓ Deleted test entity: Eleph YXGTE (ID: 2499)
  ❌ Failed to delete entity: Eleph PSOUE (ID: 2500)
  ✓ Deleted test entity: Eleph YXGTE (ID: 2499)
  ✓ Deleted test entity: Human YXGTA (ID: 2490)
  ✓ Deleted test entity: Human PSOUA (ID: 2489)
  ✓ Deleted test entity: Mouse YXGTF (ID: 2501)
  ✓ Deleted test entity: Mouse JNBGF (ID: 2494)
  ✓ Deleted test entity: Mouse PSOUF (ID: 2502)
  ✓ Deleted test entity: Human YXGTA (ID: 2490)
  ✓ Deleted test entity: Human PSOUA (ID: 2489)
  ✓ Deleted test entity: Human PSOUA (ID: 2489)
  ✓ Deleted test entity: Human YXGTA (ID: 2490)
  ✓ Deleted test entity: Mouse JNBGF (ID: 2494)
  ✓ Deleted test entity: Mouse JNBGF (ID: 2494)
  ✓ Deleted test entity: Mouse YXGTF (ID: 2501)
  ✓ Deleted test entity: Mouse PSOUF (ID: 2502)
  ✓ Deleted test entity: Mouse YXGTF (ID: 2501)
  ✓ Deleted test entity: Mouse PSOUF (ID: 2502)
🧹 Pre-test cleanup complete in 180ms (Worker 2):
  • Initial entities: 95
  • Entities deleted: 13
  • Failed deletions: 2
  • Final entity count: 80
  • Net reduction: 15
⚠️  2 entity deletions failed - may cause test conflicts
🧹 Pre-test cleanup complete in 180ms (Worker 1):
  • Initial entities: 95
  • Entities deleted: 13
  • Failed deletions: 2
  • Final entity count: 80
  • Net reduction: 15
⚠️  2 entity deletions failed - may cause test conflicts
🧹 Pre-test cleanup complete in 180ms (Worker 0):
  • Initial entities: 95
  • Entities deleted: 12
  • Failed deletions: 3
  • Final entity count: 80
  • Net reduction: 15
⚠️  3 entity deletions failed - may cause test conflicts
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human CURKA
Creating entity for complex test: Human CURKA
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human UIQSA
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human JXVHA
Creating entity for complex test: Human JXVHA
Creating entity for complex test: Human UIQSA
Creating entity: Human CURKA (expectFailure: false)
Creating entity: Human JXVHA (expectFailure: false)
Creating entity: Human UIQSA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human UIQSA" in UI with browser-specific timeout: 5000ms
Verifying entity "Human CURKA" in UI with browser-specific timeout: 5000ms
Verifying entity "Human JXVHA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human UIQSA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Human CURKA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Human JXVHA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human CURKA" confirmed visible
Entity creation completed for "Human CURKA" in 2016ms
    ✓ UI refresh complete and entity "Human UIQSA" confirmed visible
Entity creation completed for "Human UIQSA" in 2016ms
    ✓ UI refresh complete and entity "Human JXVHA" confirmed visible
Entity creation completed for "Human JXVHA" in 2018ms
  ✅ Complex test entity created: Human UIQSA (ID: temp-1751670215875-91a6m054g) in 2547ms
✅ Comparison test entity 1 created: Human UIQSA
  ✅ Complex test entity created: Human CURKA (ID: temp-1751670215875-gf8g96wcs) in 2549ms
✅ Comparison test entity 1 created: Human CURKA
  ✅ Complex test entity created: Human JXVHA (ID: temp-1751670215876-6jvhyy4na) in 2548ms
✅ Comparison test entity 1 created: Human JXVHA
Creating comparison test entity 2/6: Ball JXVHB
Creating entity for complex test: Ball JXVHB
Creating comparison test entity 2/6: Ball CURKB
Creating entity for complex test: Ball CURKB
Creating comparison test entity 2/6: Ball UIQSB
Creating entity for complex test: Ball UIQSB
Creating entity: Ball JXVHB (expectFailure: false)
Creating entity: Ball CURKB (expectFailure: false)
Creating entity: Ball UIQSB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball JXVHB" in UI with browser-specific timeout: 5000ms
Verifying entity "Ball CURKB" in UI with browser-specific timeout: 5000ms
Verifying entity "Ball UIQSB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball JXVHB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Ball CURKB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Ball UIQSB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball JXVHB" confirmed visible
Entity creation completed for "Ball JXVHB" in 1895ms
    ✓ UI refresh complete and entity "Ball CURKB" confirmed visible
Entity creation completed for "Ball CURKB" in 1894ms
    ✓ UI refresh complete and entity "Ball UIQSB" confirmed visible
Entity creation completed for "Ball UIQSB" in 1897ms
  ✅ Complex test entity created: Ball UIQSB (ID: temp-1751670218917-ti0wcj5t5) in 2427ms
✅ Comparison test entity 2 created: Ball UIQSB
  ✅ Complex test entity created: Ball CURKB (ID: temp-1751670218918-sz4pez5nb) in 2429ms
  ✅ Complex test entity created: Ball JXVHB (ID: temp-1751670218918-fuwmb9nj7) in 2435ms
✅ Comparison test entity 2 created: Ball JXVHB
✅ Comparison test entity 2 created: Ball CURKB
Creating comparison test entity 3/6: Build UIQSC
Creating entity for complex test: Build UIQSC
Creating comparison test entity 3/6: Build JXVHC
Creating entity for complex test: Build JXVHC
Creating comparison test entity 3/6: Build CURKC
Creating entity for complex test: Build CURKC
Creating entity: Build JXVHC (expectFailure: false)
Creating entity: Build CURKC (expectFailure: false)
Creating entity: Build UIQSC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build JXVHC" in UI with browser-specific timeout: 5000ms
Verifying entity "Build UIQSC" in UI with browser-specific timeout: 5000ms
Verifying entity "Build CURKC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build JXVHC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Build CURKC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Build UIQSC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build CURKC" confirmed visible
Entity creation completed for "Build CURKC" in 1956ms
    ✓ UI refresh complete and entity "Build JXVHC" confirmed visible
Entity creation completed for "Build JXVHC" in 1956ms
    ✓ UI refresh complete and entity "Build UIQSC" confirmed visible
Entity creation completed for "Build UIQSC" in 1955ms
  ✅ Complex test entity created: Build CURKC (ID: temp-1751670222008-xqasfxymn) in 2485ms
✅ Comparison test entity 3 created: Build CURKC
  ✅ Complex test entity created: Build UIQSC (ID: temp-1751670222008-9xh7i4wvr) in 2486ms
✅ Comparison test entity 3 created: Build UIQSC
  ✅ Complex test entity created: Build JXVHC (ID: temp-1751670222009-ffw4sye0k) in 2487ms
✅ Comparison test entity 3 created: Build JXVHC
Creating comparison test entity 4/6: Car UIQSD
Creating entity for complex test: Car UIQSD
Creating comparison test entity 4/6: Car CURKD
Creating entity for complex test: Car CURKD
Creating comparison test entity 4/6: Car JXVHD
Creating entity for complex test: Car JXVHD
Creating entity: Car JXVHD (expectFailure: false)
Creating entity: Car UIQSD (expectFailure: false)
Creating entity: Car CURKD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car CURKD" in UI with browser-specific timeout: 5000ms
Verifying entity "Car JXVHD" in UI with browser-specific timeout: 5000ms
Verifying entity "Car UIQSD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car JXVHD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Car UIQSD" verified on first attempt
  ✓ Entity "Car CURKD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car JXVHD" confirmed visible
Entity creation completed for "Car JXVHD" in 1848ms
    ✓ UI refresh complete and entity "Car CURKD" confirmed visible
Entity creation completed for "Car CURKD" in 1847ms
    ✓ UI refresh complete and entity "Car UIQSD" confirmed visible
Entity creation completed for "Car UIQSD" in 1849ms
  ✅ Complex test entity created: Car CURKD (ID: temp-1751670224998-o74egdjtp) in 2384ms
✅ Comparison test entity 4 created: Car CURKD
  ✅ Complex test entity created: Car JXVHD (ID: temp-1751670224999-dw36ajmk7) in 2384ms
✅ Comparison test entity 4 created: Car JXVHD
  ✅ Complex test entity created: Car UIQSD (ID: temp-1751670225000-6q1uyhi0a) in 2386ms
✅ Comparison test entity 4 created: Car UIQSD
Creating comparison test entity 5/6: Eleph CURKE
Creating entity for complex test: Eleph CURKE
Creating comparison test entity 5/6: Eleph JXVHE
Creating entity for complex test: Eleph JXVHE
Creating comparison test entity 5/6: Eleph UIQSE
Creating entity for complex test: Eleph UIQSE
Creating entity: Eleph UIQSE (expectFailure: false)
Creating entity: Eleph JXVHE (expectFailure: false)
Creating entity: Eleph CURKE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph UIQSE" in UI with browser-specific timeout: 5000ms
Verifying entity "Eleph CURKE" in UI with browser-specific timeout: 5000ms
Verifying entity "Eleph JXVHE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph UIQSE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Eleph CURKE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Eleph JXVHE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph UIQSE" confirmed visible
Entity creation completed for "Eleph UIQSE" in 1926ms
    ✓ UI refresh complete and entity "Eleph CURKE" confirmed visible
Entity creation completed for "Eleph CURKE" in 1924ms
    ✓ UI refresh complete and entity "Eleph JXVHE" confirmed visible
Entity creation completed for "Eleph JXVHE" in 1925ms
  ✅ Complex test entity created: Eleph JXVHE (ID: temp-1751670228070-555i2s281) in 2462ms
✅ Comparison test entity 5 created: Eleph JXVHE
  ✅ Complex test entity created: Eleph CURKE (ID: temp-1751670228071-l9wlwubbg) in 2463ms
✅ Comparison test entity 5 created: Eleph CURKE
  ✅ Complex test entity created: Eleph UIQSE (ID: temp-1751670228071-0ih0a9nc7) in 2463ms
✅ Comparison test entity 5 created: Eleph UIQSE
Creating comparison test entity 6/6: Mouse JXVHF
Creating entity for complex test: Mouse JXVHF
Creating comparison test entity 6/6: Mouse CURKF
Creating entity for complex test: Mouse CURKF
Creating comparison test entity 6/6: Mouse UIQSF
Creating entity for complex test: Mouse UIQSF
Creating entity: Mouse UIQSF (expectFailure: false)
Creating entity: Mouse JXVHF (expectFailure: false)
Creating entity: Mouse CURKF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse UIQSF" in UI with browser-specific timeout: 5000ms
Verifying entity "Mouse JXVHF" in UI with browser-specific timeout: 5000ms
Verifying entity "Mouse CURKF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse UIQSF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Mouse JXVHF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Mouse CURKF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse CURKF" confirmed visible
Entity creation completed for "Mouse CURKF" in 1956ms
    ✓ UI refresh complete and entity "Mouse JXVHF" confirmed visible
Entity creation completed for "Mouse JXVHF" in 1957ms
    ✓ UI refresh complete and entity "Mouse UIQSF" confirmed visible
Entity creation completed for "Mouse UIQSF" in 1957ms
  ✅ Complex test entity created: Mouse JXVHF (ID: temp-1751670231177-2ftynksxt) in 2497ms
✅ Comparison test entity 6 created: Mouse JXVHF
  ✅ Complex test entity created: Mouse CURKF (ID: temp-1751670231178-c2jho1kx7) in 2498ms
✅ Comparison test entity 6 created: Mouse CURKF
  ✅ Complex test entity created: Mouse UIQSF (ID: temp-1751670231179-xhdwhyv00) in 2498ms
✅ Comparison test entity 6 created: Mouse UIQSF
Creating test connections...
Creating test connections...
Creating connection: Human CURKA → Ball CURKB (10.0x)
Creating connection: Human JXVHA → Ball JXVHB (10.0x)
Creating test connections...
Creating connection: Human UIQSA → Ball UIQSB (10.0x)
Selecting AutoComplete option: Human JXVHA
Selecting AutoComplete option: Human CURKA
Selecting AutoComplete option: Human UIQSA
Successfully selected: Human JXVHA using dropdown
Successfully selected: Human CURKA using dropdown
Successfully selected: Human UIQSA using dropdown
Successfully set input value: Human JXVHA
Successfully set input value: Human CURKA
Successfully set input value: Human UIQSA
Selecting AutoComplete option: Ball JXVHB
Selecting AutoComplete option: Ball CURKB
Selecting AutoComplete option: Ball UIQSB
Successfully selected: Ball CURKB using dropdown
Successfully selected: Ball JXVHB using dropdown
Successfully selected: Ball UIQSB using dropdown
Successfully set input value: Ball JXVHB
Successfully set input value: Ball CURKB
Successfully set input value: Ball UIQSB
Connection creation completed for "Human CURKA → Ball CURKB" in 2593ms
  ℹ️  Tracking connection dependency: temp-1751670215875-gf8g96wcs → temp-1751670218918-sz4pez5nb (auto-deleted via CASCADE)
Connection creation completed for "Human JXVHA → Ball JXVHB" in 2593ms
  ℹ️  Tracking connection dependency: temp-1751670215876-6jvhyy4na → temp-1751670218918-fuwmb9nj7 (auto-deleted via CASCADE)
Connection creation completed for "Human UIQSA → Ball UIQSB" in 2612ms
  ℹ️  Tracking connection dependency: temp-1751670215875-91a6m054g → temp-1751670218917-ti0wcj5t5 (auto-deleted via CASCADE)
Connection found: Human CURKA → Ball CURKB in Length (any multiplier)
Connection found: Human JXVHA → Ball JXVHB in Length (any multiplier)
Connection found: Human UIQSA → Ball UIQSB in Length (any multiplier)
Creating connection: Ball CURKB → Build CURKC (50.0x)
Creating connection: Ball JXVHB → Build JXVHC (50.0x)
Creating connection: Ball UIQSB → Build UIQSC (50.0x)
Selecting AutoComplete option: Ball JXVHB
Selecting AutoComplete option: Ball CURKB
Selecting AutoComplete option: Ball UIQSB
Successfully selected: Ball UIQSB using dropdown
Successfully selected: Ball CURKB using dropdown
Successfully selected: Ball JXVHB using dropdown
Successfully set input value: Ball CURKB
Successfully set input value: Ball JXVHB
Successfully set input value: Ball UIQSB
Selecting AutoComplete option: Build JXVHC
Selecting AutoComplete option: Build CURKC
Selecting AutoComplete option: Build UIQSC
Successfully selected: Build CURKC using dropdown
Successfully selected: Build UIQSC using dropdown
Successfully selected: Build JXVHC using dropdown
Successfully set input value: Build JXVHC
Successfully set input value: Build CURKC
Successfully set input value: Build UIQSC
Connection creation completed for "Ball JXVHB → Build JXVHC" in 2630ms
  ℹ️  Tracking connection dependency: temp-1751670218918-fuwmb9nj7 → temp-1751670222009-ffw4sye0k (auto-deleted via CASCADE)
Connection creation completed for "Ball UIQSB → Build UIQSC" in 2590ms
  ℹ️  Tracking connection dependency: temp-1751670218917-ti0wcj5t5 → temp-1751670222008-9xh7i4wvr (auto-deleted via CASCADE)
Connection creation completed for "Ball CURKB → Build CURKC" in 2631ms
  ℹ️  Tracking connection dependency: temp-1751670218918-sz4pez5nb → temp-1751670222008-xqasfxymn (auto-deleted via CASCADE)
Connection found: Ball JXVHB → Build JXVHC in Length (any multiplier)
Connection found: Ball UIQSB → Build UIQSC in Length (any multiplier)
Connection found: Ball CURKB → Build CURKC in Length (any multiplier)
Creating connection: Build CURKC → Mouse CURKF (0.1x)
Creating connection: Build JXVHC → Mouse JXVHF (0.1x)
Creating connection: Build UIQSC → Mouse UIQSF (0.1x)
Selecting AutoComplete option: Build JXVHC
Selecting AutoComplete option: Build CURKC
Selecting AutoComplete option: Build UIQSC
Successfully selected: Build JXVHC using dropdown
Successfully selected: Build UIQSC using dropdown
Successfully selected: Build CURKC using dropdown
Successfully set input value: Build UIQSC
Successfully set input value: Build JXVHC
Successfully set input value: Build CURKC
Selecting AutoComplete option: Mouse UIQSF
Selecting AutoComplete option: Mouse CURKF
Selecting AutoComplete option: Mouse JXVHF
Successfully selected: Mouse JXVHF using dropdown
Successfully selected: Mouse CURKF using dropdown
Successfully selected: Mouse UIQSF using dropdown
Successfully set input value: Mouse UIQSF
Successfully set input value: Mouse CURKF
Successfully set input value: Mouse JXVHF
Connection creation completed for "Build CURKC → Mouse CURKF" in 2629ms
Connection creation completed for "Build JXVHC → Mouse JXVHF" in 2628ms
  ℹ️  Tracking connection dependency: temp-1751670222009-ffw4sye0k → temp-1751670231177-2ftynksxt (auto-deleted via CASCADE)
  ℹ️  Tracking connection dependency: temp-1751670222008-xqasfxymn → temp-1751670231178-c2jho1kx7 (auto-deleted via CASCADE)
Connection creation completed for "Build UIQSC → Mouse UIQSF" in 2629ms
  ℹ️  Tracking connection dependency: temp-1751670222008-9xh7i4wvr → temp-1751670231179-xhdwhyv00 (auto-deleted via CASCADE)
Connection found: Build JXVHC → Mouse JXVHF in Length (any multiplier)
Connection found: Build CURKC → Mouse CURKF in Length (any multiplier)
Connection found: Build UIQSC → Mouse UIQSF in Length (any multiplier)
Creating connection: Car CURKD → Eleph CURKE (0.3x)
Creating connection: Car JXVHD → Eleph JXVHE (0.3x)
Creating connection: Car UIQSD → Eleph UIQSE (0.3x)
Selecting AutoComplete option: Car CURKD
Selecting AutoComplete option: Car JXVHD
Selecting AutoComplete option: Car UIQSD
Successfully selected: Car CURKD using dropdown
Successfully selected: Car JXVHD using dropdown
Successfully selected: Car UIQSD using dropdown
Successfully set input value: Car CURKD
Successfully set input value: Car JXVHD
Successfully set input value: Car UIQSD
Selecting AutoComplete option: Eleph UIQSE
Selecting AutoComplete option: Eleph JXVHE
Selecting AutoComplete option: Eleph CURKE
Successfully selected: Eleph UIQSE using dropdown
Successfully selected: Eleph JXVHE using dropdown
Successfully selected: Eleph CURKE using dropdown
Successfully set input value: Eleph UIQSE
Successfully set input value: Eleph CURKE
Successfully set input value: Eleph JXVHE
Connection creation completed for "Car UIQSD → Eleph UIQSE" in 2590ms
  ℹ️  Tracking connection dependency: temp-1751670225000-6q1uyhi0a → temp-1751670228071-0ih0a9nc7 (auto-deleted via CASCADE)
Connection creation completed for "Car CURKD → Eleph CURKE" in 2590ms
  ℹ️  Tracking connection dependency: temp-1751670224998-o74egdjtp → temp-1751670228071-l9wlwubbg (auto-deleted via CASCADE)
Connection creation completed for "Car JXVHD → Eleph JXVHE" in 2591ms
  ℹ️  Tracking connection dependency: temp-1751670224999-dw36ajmk7 → temp-1751670228070-555i2s281 (auto-deleted via CASCADE)
Connection found: Car CURKD → Eleph CURKE in Mass (any multiplier)
Connection found: Car UIQSD → Eleph UIQSE in Mass (any multiplier)
Connection found: Car JXVHD → Eleph JXVHE in Mass (any multiplier)
Comparing: Human UIQSA to Ball UIQSB (count: 1, unit: Length)
Comparing: Human JXVHA to Build JXVHC (count: 1, unit: Length)
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Human CURKA (ID: 2505)
  ✓ Fallback deleted entity: Ball CURKB (ID: 2506)
  ✓ Fallback deleted entity: Build CURKC (ID: 2509)
  ✓ Fallback deleted entity: Car CURKD (ID: 2514)
  ✓ Fallback deleted entity: Eleph CURKE (ID: 2515)
  ✓ Fallback deleted entity: Mouse CURKF (ID: 2519)
  ℹ️  Fallback cleanup handled 6 additional entities
🧹 Post-test cleanup complete in 38ms (Worker 0):
  • Entities deleted: 6 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 0
  • Test efficiency: 100% (38ms cleanup / 35101ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 0 (4ms)
[1/210] ✅ should display comparison page correctly (35.2s)
  ✓  1 [chromium] › e2e/tests/comparisons.spec.ts:136:7 › Entity Comparisons and Pathfinding › should display comparison page correctly (35.2s)
[2/210] 🏃 Starting: should calculate complex multi-hop paths
🧹 Starting pre-test cleanup...
  Found 92 total entities in database (Worker 0)
  Identified 12 test entities to delete for Worker 0
  ✓ Deleted test entity: Ball JXVHB (ID: 2508)
  ✓ Deleted test entity: Build JXVHC (ID: 2511)
  ✓ Deleted test entity: Ball UIQSB (ID: 2507)
  ✓ Deleted test entity: Car JXVHD (ID: 2512)
  ✓ Deleted test entity: Build UIQSC (ID: 2510)
  ✓ Deleted test entity: Eleph JXVHE (ID: 2516)
  ✓ Deleted test entity: Human JXVHA (ID: 2504)
  ✓ Deleted test entity: Human UIQSA (ID: 2503)
  ❌ Failed to delete entity: Car UIQSD (ID: 2513)
  ✓ Deleted test entity: Eleph UIQSE (ID: 2517)
  ✓ Deleted test entity: Mouse UIQSF (ID: 2518)
  ✓ Deleted test entity: Mouse JXVHF (ID: 2520)
🧹 Pre-test cleanup complete in 1159ms (Worker 0):
  • Initial entities: 92
  • Entities deleted: 11
  • Failed deletions: 1
  • Final entity count: 81
  • Net reduction: 11
⚠️  1 entity deletions failed - may cause test conflicts
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human PYAKA
Creating entity for complex test: Human PYAKA
Creating entity: Human PYAKA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human PYAKA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human PYAKA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human PYAKA" confirmed visible
Entity creation completed for "Human PYAKA" in 1968ms
  ✅ Complex test entity created: Human PYAKA (ID: temp-1751670251944-my859u8w5) in 2493ms
✅ Comparison test entity 1 created: Human PYAKA
Creating comparison test entity 2/6: Ball PYAKB
Creating entity for complex test: Ball PYAKB
Creating entity: Ball PYAKB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball PYAKB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball PYAKB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball PYAKB" confirmed visible
Entity creation completed for "Ball PYAKB" in 1890ms
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Car UIQSD (ID: 2513)
  ℹ️  Fallback cleanup handled 1 additional entities
🧹 Post-test cleanup complete in 21ms (Worker 1):
  • Entities deleted: 1 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 1
  • Test efficiency: 100% (21ms cleanup / 42257ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 1 (5ms)
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 11ms (Worker 2):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 2
  • Test efficiency: 100% (11ms cleanup / 42276ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 2 (4ms)
  ✅ Complex test entity created: Ball PYAKB (ID: temp-1751670254968-c67qgvgyx) in 2414ms
✅ Comparison test entity 2 created: Ball PYAKB
[2/210] ❌ should calculate direct relationships (42.7s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.
  ✘  3 [chromium] › e2e/tests/comparisons.spec.ts:145:7 › Entity Comparisons and Pathfinding › should calculate direct relationships (42.7s)
[3/210] ❌ should calculate transitive relationships (42.7s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.
  ✘  2 [chromium] › e2e/tests/comparisons.spec.ts:165:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships (42.7s)
Creating comparison test entity 3/6: Build PYAKC
Creating entity for complex test: Build PYAKC
[4/210] 🏃 Starting: should calculate direct relationships
[4/210] 🏃 Starting: should calculate transitive relationships
Creating entity: Build PYAKC (expectFailure: false)
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 82 total entities in database (Worker 4)
  Found 82 total entities in database (Worker 3)
  Identified 2 test entities to delete for Worker 3
  Identified 2 test entities to delete for Worker 4
  ✓ Deleted test entity: Ball PYAKB (ID: 2522)
  ✓ Deleted test entity: Human PYAKA (ID: 2521)
  ✓ Deleted test entity: Human PYAKA (ID: 2521)
  ✓ Deleted test entity: Ball PYAKB (ID: 2522)
🧹 Pre-test cleanup complete in 36ms (Worker 4):
  • Initial entities: 82
  • Entities deleted: 2
  • Failed deletions: 0
🧹 Pre-test cleanup complete in 37ms (Worker 3):
  • Initial entities: 82
  • Entities deleted: 2
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 2
  • Final entity count: 80
  • Net reduction: 2
Create button clicked successfully with standard click (attempt 1)
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human JRDXA
Creating entity for complex test: Human JRDXA
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human XXUCA
Creating entity for complex test: Human XXUCA
Creating entity: Human JRDXA (expectFailure: false)
Creating entity: Human XXUCA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build PYAKC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build PYAKC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build PYAKC" confirmed visible
Entity creation completed for "Build PYAKC" in 1906ms
  ✅ Complex test entity created: Build PYAKC (ID: temp-1751670257992-ycr7ehlzn) in 2420ms
✅ Comparison test entity 3 created: Build PYAKC
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human JRDXA" in UI with browser-specific timeout: 5000ms
Verifying entity "Human XXUCA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human JRDXA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Human XXUCA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating comparison test entity 4/6: Car PYAKD
Creating entity for complex test: Car PYAKD
    ✓ UI refresh complete and entity "Human JRDXA" confirmed visible
Entity creation completed for "Human JRDXA" in 1986ms
    ✓ UI refresh complete and entity "Human XXUCA" confirmed visible
Entity creation completed for "Human XXUCA" in 1981ms
Creating entity: Car PYAKD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Human JRDXA (ID: temp-1751670259070-2r4mvkr4m) in 2510ms
✅ Comparison test entity 1 created: Human JRDXA
  ✅ Complex test entity created: Human XXUCA (ID: temp-1751670259072-uqn4re50u) in 2504ms
✅ Comparison test entity 1 created: Human XXUCA
Creating comparison test entity 2/6: Ball JRDXB
Creating entity for complex test: Ball JRDXB
Creating comparison test entity 2/6: Ball XXUCB
Creating entity for complex test: Ball XXUCB
Creating entity: Ball JRDXB (expectFailure: false)
Creating entity: Ball XXUCB (expectFailure: false)
Submit button is ready for interaction (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car PYAKD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car PYAKD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car PYAKD" confirmed visible
Entity creation completed for "Car PYAKD" in 1815ms
  ✅ Complex test entity created: Car PYAKD (ID: temp-1751670260931-hdd2hawcz) in 2335ms
✅ Comparison test entity 4 created: Car PYAKD
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating comparison test entity 5/6: Eleph PYAKE
Creating entity for complex test: Eleph PYAKE
Verifying entity "Ball JRDXB" in UI with browser-specific timeout: 5000ms
Verifying entity "Ball XXUCB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball JRDXB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Ball XXUCB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating entity: Eleph PYAKE (expectFailure: false)
    ✓ UI refresh complete and entity "Ball JRDXB" confirmed visible
    ✓ UI refresh complete and entity "Ball XXUCB" confirmed visible
Entity creation completed for "Ball JRDXB" in 1891ms
Entity creation completed for "Ball XXUCB" in 1889ms
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Ball JRDXB (ID: temp-1751670262090-8nj054471) in 2411ms
✅ Comparison test entity 2 created: Ball JRDXB
  ✅ Complex test entity created: Ball XXUCB (ID: temp-1751670262090-imzvzzgvu) in 2410ms
✅ Comparison test entity 2 created: Ball XXUCB
Creating comparison test entity 3/6: Build XXUCC
Creating entity for complex test: Build XXUCC
Creating comparison test entity 3/6: Build JRDXC
Creating entity for complex test: Build JRDXC
Creating entity: Build XXUCC (expectFailure: false)
Creating entity: Build JRDXC (expectFailure: false)
Submit button is ready for interaction (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph PYAKE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph PYAKE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph PYAKE" confirmed visible
Entity creation completed for "Eleph PYAKE" in 1916ms
  ✅ Complex test entity created: Eleph PYAKE (ID: temp-1751670263982-iypjxyom0) in 2443ms
✅ Comparison test entity 5 created: Eleph PYAKE
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating comparison test entity 6/6: Mouse PYAKF
Creating entity for complex test: Mouse PYAKF
Verifying entity "Build XXUCC" in UI with browser-specific timeout: 5000ms
Verifying entity "Build JRDXC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build JRDXC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Build XXUCC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating entity: Mouse PYAKF (expectFailure: false)
    ✓ UI refresh complete and entity "Build XXUCC" confirmed visible
    ✓ UI refresh complete and entity "Build JRDXC" confirmed visible
Entity creation completed for "Build JRDXC" in 1937ms
Entity creation completed for "Build XXUCC" in 1940ms
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Build JRDXC (ID: temp-1751670265158-70ukmdy2v) in 2463ms
✅ Comparison test entity 3 created: Build JRDXC
  ✅ Complex test entity created: Build XXUCC (ID: temp-1751670265158-vfbekudxh) in 2463ms
✅ Comparison test entity 3 created: Build XXUCC
Creating comparison test entity 4/6: Car JRDXD
Creating entity for complex test: Car JRDXD
Creating comparison test entity 4/6: Car XXUCD
Creating entity for complex test: Car XXUCD
Creating entity: Car XXUCD (expectFailure: false)
Creating entity: Car JRDXD (expectFailure: false)
Submit button is ready for interaction (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse PYAKF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse PYAKF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse PYAKF" confirmed visible
Entity creation completed for "Mouse PYAKF" in 1925ms
  ✅ Complex test entity created: Mouse PYAKF (ID: temp-1751670267042-0lns98mvb) in 2452ms
✅ Comparison test entity 6 created: Mouse PYAKF
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car JRDXD" in UI with browser-specific timeout: 5000ms
Verifying entity "Car XXUCD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car JRDXD" verified on first attempt
  ✓ Entity "Car XXUCD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car XXUCD" confirmed visible
Entity creation completed for "Car XXUCD" in 1819ms
    ✓ UI refresh complete and entity "Car JRDXD" confirmed visible
Entity creation completed for "Car JRDXD" in 1819ms
  ✅ Complex test entity created: Car XXUCD (ID: temp-1751670268110-gkz4nhniy) in 2346ms
✅ Comparison test entity 4 created: Car XXUCD
  ✅ Complex test entity created: Car JRDXD (ID: temp-1751670268111-1d34v7d66) in 2347ms
✅ Comparison test entity 4 created: Car JRDXD
Creating test connections...
Creating connection: Human PYAKA → Ball PYAKB (10.0x)
Selecting AutoComplete option: Human PYAKA
Creating comparison test entity 5/6: Eleph XXUCE
Creating entity for complex test: Eleph XXUCE
Creating comparison test entity 5/6: Eleph JRDXE
Creating entity for complex test: Eleph JRDXE
Creating entity: Eleph XXUCE (expectFailure: false)
Creating entity: Eleph JRDXE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph XXUCE" in UI with browser-specific timeout: 5000ms
Verifying entity "Eleph JRDXE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph JRDXE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Eleph XXUCE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph XXUCE" confirmed visible
Entity creation completed for "Eleph XXUCE" in 1938ms
    ✓ UI refresh complete and entity "Eleph JRDXE" confirmed visible
Entity creation completed for "Eleph JRDXE" in 1938ms
Dropdown failed, using direct input for: Human PYAKA
Successfully set input value: Human PYAKA
  ✅ Complex test entity created: Eleph XXUCE (ID: temp-1751670271178-h51yxjd7t) in 2461ms
✅ Comparison test entity 5 created: Eleph XXUCE
  ✅ Complex test entity created: Eleph JRDXE (ID: temp-1751670271178-3gr7byjjd) in 2461ms
✅ Comparison test entity 5 created: Eleph JRDXE
Selecting AutoComplete option: Ball PYAKB
Creating comparison test entity 6/6: Mouse XXUCF
Creating entity for complex test: Mouse XXUCF
Creating comparison test entity 6/6: Mouse JRDXF
Creating entity for complex test: Mouse JRDXF
Creating entity: Mouse XXUCF (expectFailure: false)
Creating entity: Mouse JRDXF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse JRDXF" in UI with browser-specific timeout: 5000ms
Verifying entity "Mouse XXUCF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse JRDXF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Mouse XXUCF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Dropdown failed, using direct input for: Ball PYAKB
    ✓ UI refresh complete and entity "Mouse XXUCF" confirmed visible
Entity creation completed for "Mouse XXUCF" in 1930ms
    ✓ UI refresh complete and entity "Mouse JRDXF" confirmed visible
Entity creation completed for "Mouse JRDXF" in 1930ms
Successfully set input value: Ball PYAKB
  ✅ Complex test entity created: Mouse XXUCF (ID: temp-1751670274239-19ku87bo6) in 2455ms
✅ Comparison test entity 6 created: Mouse XXUCF
  ✅ Complex test entity created: Mouse JRDXF (ID: temp-1751670274240-riali7mus) in 2456ms
✅ Comparison test entity 6 created: Mouse JRDXF
Creating test connections...
Creating test connections...
Creating connection: Human XXUCA → Ball XXUCB (10.0x)
Creating connection: Human JRDXA → Ball JRDXB (10.0x)
Selecting AutoComplete option: Human XXUCA
Selecting AutoComplete option: Human JRDXA
Successfully selected: Human JRDXA using dropdown
Successfully selected: Human XXUCA using dropdown
Successfully set input value: Human XXUCA
Successfully set input value: Human JRDXA
Selecting AutoComplete option: Ball JRDXB
Selecting AutoComplete option: Ball XXUCB
Successfully selected: Ball JRDXB using dropdown
Successfully selected: Ball XXUCB using dropdown
Successfully set input value: Ball XXUCB
Successfully set input value: Ball JRDXB
Connection creation completed for "Human XXUCA → Ball XXUCB" in 2664ms
Connection creation completed for "Human JRDXA → Ball JRDXB" in 2664ms
  ℹ️  Tracking connection dependency: temp-1751670259072-uqn4re50u → temp-1751670262090-imzvzzgvu (auto-deleted via CASCADE)
  ℹ️  Tracking connection dependency: temp-1751670259070-2r4mvkr4m → temp-1751670262090-8nj054471 (auto-deleted via CASCADE)
Connection found: Human JRDXA → Ball JRDXB in Length (any multiplier)
Connection found: Human XXUCA → Ball XXUCB in Length (any multiplier)
Creating connection: Ball JRDXB → Build JRDXC (50.0x)
Creating connection: Ball XXUCB → Build XXUCC (50.0x)
Selecting AutoComplete option: Ball XXUCB
Selecting AutoComplete option: Ball JRDXB
Connection creation failed for Human PYAKA → Ball PYAKB, checking if it exists...
Successfully selected: Ball XXUCB using dropdown
Successfully selected: Ball JRDXB using dropdown
Successfully set input value: Ball JRDXB
Successfully set input value: Ball XXUCB
Selecting AutoComplete option: Build XXUCC
Selecting AutoComplete option: Build JRDXC
Successfully selected: Build JRDXC using dropdown
Successfully selected: Build XXUCC using dropdown
Successfully set input value: Build XXUCC
Successfully set input value: Build JRDXC
Connection creation completed for "Ball XXUCB → Build XXUCC" in 2601ms
  ℹ️  Tracking connection dependency: temp-1751670262090-imzvzzgvu → temp-1751670265158-vfbekudxh (auto-deleted via CASCADE)
Connection creation completed for "Ball JRDXB → Build JRDXC" in 2602ms
  ℹ️  Tracking connection dependency: temp-1751670262090-8nj054471 → temp-1751670265158-70ukmdy2v (auto-deleted via CASCADE)
Connection found: Ball XXUCB → Build XXUCC in Length (any multiplier)
Connection found: Ball JRDXB → Build JRDXC in Length (any multiplier)
Creating connection: Build XXUCC → Mouse XXUCF (0.1x)
Creating connection: Build JRDXC → Mouse JRDXF (0.1x)
Selecting AutoComplete option: Build XXUCC
Selecting AutoComplete option: Build JRDXC
Successfully selected: Build XXUCC using dropdown
Successfully selected: Build JRDXC using dropdown
Successfully set input value: Build JRDXC
Successfully set input value: Build XXUCC
Selecting AutoComplete option: Mouse JRDXF
Selecting AutoComplete option: Mouse XXUCF
Successfully selected: Mouse JRDXF using dropdown
Successfully selected: Mouse XXUCF using dropdown
Successfully set input value: Mouse JRDXF
Successfully set input value: Mouse XXUCF
Connection creation completed for "Build JRDXC → Mouse JRDXF" in 2629ms
  ℹ️  Tracking connection dependency: temp-1751670265158-70ukmdy2v → temp-1751670274240-riali7mus (auto-deleted via CASCADE)
Connection creation completed for "Build XXUCC → Mouse XXUCF" in 2646ms
  ℹ️  Tracking connection dependency: temp-1751670265158-vfbekudxh → temp-1751670274239-19ku87bo6 (auto-deleted via CASCADE)
Connection found: Build JRDXC → Mouse JRDXF in Length (any multiplier)
Connection found: Build XXUCC → Mouse XXUCF in Length (any multiplier)
Creating connection: Car JRDXD → Eleph JRDXE (0.3x)
Creating connection: Car XXUCD → Eleph XXUCE (0.3x)
Selecting AutoComplete option: Car JRDXD
Selecting AutoComplete option: Car XXUCD
Successfully selected: Car XXUCD using dropdown
Successfully selected: Car JRDXD using dropdown
Connection not found: Human PYAKA → Ball PYAKB (10.0x) in Length
Connection list content: Connections (18)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Build PYAKC (ID: 2523)
  ✓ Fallback deleted entity: Car PYAKD (ID: 2526)
  ✓ Fallback deleted entity: Eleph PYAKE (ID: 2529)
  ✓ Fallback deleted entity: Mouse PYAKF (ID: 2532)
  ℹ️  Fallback cleanup handled 4 additional entities
🧹 Post-test cleanup complete in 26ms (Worker 0):
  • Entities deleted: 4 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 0
  • Test efficiency: 100% (26ms cleanup / 39563ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 0 (5ms)
Successfully set input value: Car XXUCD
Successfully set input value: Car JRDXD
Selecting AutoComplete option: Eleph JRDXE
Selecting AutoComplete option: Eleph XXUCE
[4/210] ❌ should calculate complex multi-hop paths (39.8s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.
  ✘  4 [chromium] › e2e/tests/comparisons.spec.ts:179:7 › Entity Comparisons and Pathfinding › should calculate complex multi-hop paths (39.8s)
[5/210] 🏃 Starting: should calculate complex multi-hop paths
Successfully selected: Eleph JRDXE using dropdown
Successfully selected: Eleph XXUCE using dropdown
🧹 Starting pre-test cleanup...
  Found 92 total entities in database (Worker 5)
  Identified 12 test entities to delete for Worker 5
  ✓ Deleted test entity: Ball JRDXB (ID: 2527)
  ✓ Deleted test entity: Car JRDXD (ID: 2533)
  ✓ Deleted test entity: Build XXUCC (ID: 2531)
  ✓ Deleted test entity: Build JRDXC (ID: 2530)
  ✓ Deleted test entity: Ball XXUCB (ID: 2528)
  ✓ Deleted test entity: Car XXUCD (ID: 2534)
  ✓ Deleted test entity: Eleph XXUCE (ID: 2536)
  ✓ Deleted test entity: Human JRDXA (ID: 2524)
  ✓ Deleted test entity: Eleph JRDXE (ID: 2535)
  ✓ Deleted test entity: Human XXUCA (ID: 2525)
Successfully set input value: Eleph XXUCE
Successfully set input value: Eleph JRDXE
  ✓ Deleted test entity: Mouse XXUCF (ID: 2538)
  ✓ Deleted test entity: Mouse JRDXF (ID: 2537)
🧹 Pre-test cleanup complete in 162ms (Worker 5):
  • Initial entities: 92
  • Entities deleted: 12
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 12
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Car XXUCD → Eleph XXUCE, checking if it exists...
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Car JRDXD → Eleph JRDXE, checking if it exists...
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human ILKXA
Creating entity for complex test: Human ILKXA
Creating entity: Human ILKXA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human ILKXA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human ILKXA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human ILKXA" confirmed visible
Entity creation completed for "Human ILKXA" in 2023ms
  ✅ Complex test entity created: Human ILKXA (ID: temp-1751670291342-f8m32wksq) in 2549ms
✅ Comparison test entity 1 created: Human ILKXA
Creating comparison test entity 2/6: Ball ILKXB
Creating entity for complex test: Ball ILKXB
Creating entity: Ball ILKXB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball ILKXB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball ILKXB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball ILKXB" confirmed visible
Entity creation completed for "Ball ILKXB" in 1901ms
  ✅ Complex test entity created: Ball ILKXB (ID: temp-1751670294394-e2h4llmll) in 2435ms
✅ Comparison test entity 2 created: Ball ILKXB
Creating comparison test entity 3/6: Build ILKXC
Creating entity for complex test: Build ILKXC
Creating entity: Build ILKXC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Connection not found: Car XXUCD → Eleph XXUCE (0.3x) in Mass
Connection not found: Car JRDXD → Eleph JRDXE (0.3x) in Mass
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
🧹 Starting post-test cleanup...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 18ms (Worker 3):
🧹 Post-test cleanup complete in 18ms (Worker 4):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 4
  • Test efficiency: 100% (18ms cleanup / 40525ms total)
  ✅ All cleanup operations successful
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 3
  • Test efficiency: 100% (18ms cleanup / 40526ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 3 (6ms)
  ✅ Cleanup verification: No test entities remain from Worker 4 (6ms)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build ILKXC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build ILKXC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
[5/210] ❌ should calculate transitive relationships (40.9s)
   └─ Error: Error: Connection creation failed for "Car XXUCD → Eleph XXUCE": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  6 [chromium] › e2e/tests/comparisons.spec.ts:165:7 › Entity Comparisons and Pathfinding › should calculate transitive relationships (retry #1) (40.9s)
[6/210] ❌ should calculate direct relationships (40.9s)
   └─ Error: Error: Connection creation failed for "Car JRDXD → Eleph JRDXE": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  5 [chromium] › e2e/tests/comparisons.spec.ts:145:7 › Entity Comparisons and Pathfinding › should calculate direct relationships (retry #1) (40.9s)
    ✓ UI refresh complete and entity "Build ILKXC" confirmed visible
Entity creation completed for "Build ILKXC" in 1938ms
[7/210] 🏃 Starting: should handle reverse path calculations
[7/210] 🏃 Starting: should handle different unit entities with no connection
  ✅ Complex test entity created: Build ILKXC (ID: temp-1751670297470-w27kixrhn) in 2466ms
✅ Comparison test entity 3 created: Build ILKXC
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 83 total entities in database (Worker 6)
  Found 83 total entities in database (Worker 7)
  Identified 3 test entities to delete for Worker 6
  Identified 3 test entities to delete for Worker 7
  ✓ Deleted test entity: Ball ILKXB (ID: 2540)
  ✓ Deleted test entity: Human ILKXA (ID: 2539)
  ✓ Deleted test entity: Build ILKXC (ID: 2541)
  ✓ Deleted test entity: Ball ILKXB (ID: 2540)
  ✓ Deleted test entity: Human ILKXA (ID: 2539)
  ✓ Deleted test entity: Build ILKXC (ID: 2541)
🧹 Pre-test cleanup complete in 36ms (Worker 7):
  • Initial entities: 83
  • Entities deleted: 3
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 3
🧹 Pre-test cleanup complete in 36ms (Worker 6):
  • Initial entities: 83
  • Entities deleted: 3
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 3
Creating comparison test entity 4/6: Car ILKXD
Creating entity for complex test: Car ILKXD
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human DUNMA
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human XOEPA
Creating entity for complex test: Human DUNMA
Creating entity for complex test: Human XOEPA
Creating entity: Car ILKXD (expectFailure: false)
Creating entity: Human XOEPA (expectFailure: false)
Creating entity: Human DUNMA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car ILKXD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car ILKXD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car ILKXD" confirmed visible
Entity creation completed for "Car ILKXD" in 1784ms
Verifying entity "Human DUNMA" in UI with browser-specific timeout: 5000ms
Verifying entity "Human XOEPA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human DUNMA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Human XOEPA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human DUNMA" confirmed visible
Entity creation completed for "Human DUNMA" in 1937ms
    ✓ UI refresh complete and entity "Human XOEPA" confirmed visible
Entity creation completed for "Human XOEPA" in 1938ms
  ✅ Complex test entity created: Car ILKXD (ID: temp-1751670300378-xmpntchw3) in 2301ms
✅ Comparison test entity 4 created: Car ILKXD
  ✅ Complex test entity created: Human DUNMA (ID: temp-1751670300625-hasx0leyb) in 2453ms
✅ Comparison test entity 1 created: Human DUNMA
  ✅ Complex test entity created: Human XOEPA (ID: temp-1751670300628-nnw9d920z) in 2455ms
✅ Comparison test entity 1 created: Human XOEPA
Creating comparison test entity 5/6: Eleph ILKXE
Creating entity for complex test: Eleph ILKXE
Creating entity: Eleph ILKXE (expectFailure: false)
Creating comparison test entity 2/6: Ball DUNMB
Creating entity for complex test: Ball DUNMB
Creating comparison test entity 2/6: Ball XOEPB
Creating entity for complex test: Ball XOEPB
Create button clicked successfully with standard click (attempt 1)
Creating entity: Ball DUNMB (expectFailure: false)
Creating entity: Ball XOEPB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph ILKXE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph ILKXE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Verifying entity "Ball DUNMB" in UI with browser-specific timeout: 5000ms
Verifying entity "Ball XOEPB" in UI with browser-specific timeout: 5000ms
    ✓ UI refresh complete and entity "Eleph ILKXE" confirmed visible
Entity creation completed for "Eleph ILKXE" in 1908ms
  ✓ Entity "Ball XOEPB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Ball DUNMB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball DUNMB" confirmed visible
Entity creation completed for "Ball DUNMB" in 1885ms
    ✓ UI refresh complete and entity "Ball XOEPB" confirmed visible
Entity creation completed for "Ball XOEPB" in 1883ms
  ✅ Complex test entity created: Eleph ILKXE (ID: temp-1751670303422-q5zgo37ol) in 2438ms
✅ Comparison test entity 5 created: Eleph ILKXE
  ✅ Complex test entity created: Ball DUNMB (ID: temp-1751670303645-zdzxmcmgk) in 2414ms
✅ Comparison test entity 2 created: Ball DUNMB
  ✅ Complex test entity created: Ball XOEPB (ID: temp-1751670303646-r1oi5pgzc) in 2411ms
✅ Comparison test entity 2 created: Ball XOEPB
Creating comparison test entity 6/6: Mouse ILKXF
Creating entity for complex test: Mouse ILKXF
Creating comparison test entity 3/6: Build XOEPC
Creating entity for complex test: Build XOEPC
Creating entity: Mouse ILKXF (expectFailure: false)
Creating comparison test entity 3/6: Build DUNMC
Creating entity for complex test: Build DUNMC
Create button clicked successfully with standard click (attempt 1)
Creating entity: Build DUNMC (expectFailure: false)
Creating entity: Build XOEPC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse ILKXF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse ILKXF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse ILKXF" confirmed visible
Entity creation completed for "Mouse ILKXF" in 1923ms
Verifying entity "Build XOEPC" in UI with browser-specific timeout: 5000ms
Verifying entity "Build DUNMC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build XOEPC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Build DUNMC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build DUNMC" confirmed visible
Entity creation completed for "Build DUNMC" in 1932ms
    ✓ UI refresh complete and entity "Build XOEPC" confirmed visible
Entity creation completed for "Build XOEPC" in 1930ms
  ✅ Complex test entity created: Mouse ILKXF (ID: temp-1751670306486-ne1mmqixr) in 2452ms
✅ Comparison test entity 6 created: Mouse ILKXF
  ✅ Complex test entity created: Build XOEPC (ID: temp-1751670306707-n99c07yel) in 2454ms
✅ Comparison test entity 3 created: Build XOEPC
  ✅ Complex test entity created: Build DUNMC (ID: temp-1751670306709-cd2vr5mt9) in 2452ms
✅ Comparison test entity 3 created: Build DUNMC
Creating comparison test entity 4/6: Car XOEPD
Creating entity for complex test: Car XOEPD
Creating comparison test entity 4/6: Car DUNMD
Creating entity for complex test: Car DUNMD
Creating entity: Car XOEPD (expectFailure: false)
Creating entity: Car DUNMD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Creating test connections...
Creating connection: Human ILKXA → Ball ILKXB (10.0x)
Selecting AutoComplete option: Human ILKXA
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car DUNMD" in UI with browser-specific timeout: 5000ms
Verifying entity "Car XOEPD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car DUNMD" verified on first attempt
  ✓ Entity "Car XOEPD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car DUNMD" confirmed visible
Entity creation completed for "Car DUNMD" in 1801ms
    ✓ UI refresh complete and entity "Car XOEPD" confirmed visible
Entity creation completed for "Car XOEPD" in 1803ms
  ✅ Complex test entity created: Car DUNMD (ID: temp-1751670309642-630o1uav9) in 2327ms
✅ Comparison test entity 4 created: Car DUNMD
  ✅ Complex test entity created: Car XOEPD (ID: temp-1751670309642-i7sacxrqh) in 2328ms
✅ Comparison test entity 4 created: Car XOEPD
Creating comparison test entity 5/6: Eleph DUNME
Creating entity for complex test: Eleph DUNME
Creating comparison test entity 5/6: Eleph XOEPE
Creating entity for complex test: Eleph XOEPE
Dropdown failed, using direct input for: Human ILKXA
Creating entity: Eleph XOEPE (expectFailure: false)
Creating entity: Eleph DUNME (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Successfully set input value: Human ILKXA
Selecting AutoComplete option: Ball ILKXB
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph XOEPE" in UI with browser-specific timeout: 5000ms
Verifying entity "Eleph DUNME" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph XOEPE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Eleph DUNME" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph DUNME" confirmed visible
Entity creation completed for "Eleph DUNME" in 1902ms
    ✓ UI refresh complete and entity "Eleph XOEPE" confirmed visible
Entity creation completed for "Eleph XOEPE" in 1902ms
  ✅ Complex test entity created: Eleph DUNME (ID: temp-1751670312674-pmt8241ev) in 2425ms
✅ Comparison test entity 5 created: Eleph DUNME
  ✅ Complex test entity created: Eleph XOEPE (ID: temp-1751670312675-qe6sfx1z7) in 2426ms
✅ Comparison test entity 5 created: Eleph XOEPE
Dropdown failed, using direct input for: Ball ILKXB
Creating comparison test entity 6/6: Mouse DUNMF
Creating entity for complex test: Mouse DUNMF
Creating comparison test entity 6/6: Mouse XOEPF
Creating entity for complex test: Mouse XOEPF
Successfully set input value: Ball ILKXB
Creating entity: Mouse XOEPF (expectFailure: false)
Creating entity: Mouse DUNMF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse DUNMF" in UI with browser-specific timeout: 5000ms
Verifying entity "Mouse XOEPF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse DUNMF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Mouse XOEPF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse DUNMF" confirmed visible
Entity creation completed for "Mouse DUNMF" in 1911ms
    ✓ UI refresh complete and entity "Mouse XOEPF" confirmed visible
Entity creation completed for "Mouse XOEPF" in 1912ms
  ✅ Complex test entity created: Mouse DUNMF (ID: temp-1751670315708-lpb2v5p11) in 2430ms
✅ Comparison test entity 6 created: Mouse DUNMF
  ✅ Complex test entity created: Mouse XOEPF (ID: temp-1751670315709-o3rs5rhxv) in 2431ms
✅ Comparison test entity 6 created: Mouse XOEPF
Creating test connections...
Creating test connections...
Creating connection: Human DUNMA → Ball DUNMB (10.0x)
Creating connection: Human XOEPA → Ball XOEPB (10.0x)
Selecting AutoComplete option: Human XOEPA
Selecting AutoComplete option: Human DUNMA
Successfully selected: Human DUNMA using dropdown
Successfully selected: Human XOEPA using dropdown
Successfully set input value: Human DUNMA
Successfully set input value: Human XOEPA
Selecting AutoComplete option: Ball XOEPB
Selecting AutoComplete option: Ball DUNMB
Successfully selected: Ball DUNMB using dropdown
Successfully selected: Ball XOEPB using dropdown
Successfully set input value: Ball XOEPB
Successfully set input value: Ball DUNMB
Connection creation failed for Human ILKXA → Ball ILKXB, checking if it exists...
Connection creation completed for "Human DUNMA → Ball DUNMB" in 2624ms
Connection creation completed for "Human XOEPA → Ball XOEPB" in 2624ms
  ℹ️  Tracking connection dependency: temp-1751670300628-nnw9d920z → temp-1751670303646-r1oi5pgzc (auto-deleted via CASCADE)
  ℹ️  Tracking connection dependency: temp-1751670300625-hasx0leyb → temp-1751670303645-zdzxmcmgk (auto-deleted via CASCADE)
Connection found: Human DUNMA → Ball DUNMB in Length (any multiplier)
Connection found: Human XOEPA → Ball XOEPB in Length (any multiplier)
Creating connection: Ball XOEPB → Build XOEPC (50.0x)
Creating connection: Ball DUNMB → Build DUNMC (50.0x)
Selecting AutoComplete option: Ball DUNMB
Selecting AutoComplete option: Ball XOEPB
Successfully selected: Ball XOEPB using dropdown
Successfully selected: Ball DUNMB using dropdown
Successfully set input value: Ball XOEPB
Successfully set input value: Ball DUNMB
Selecting AutoComplete option: Build XOEPC
Selecting AutoComplete option: Build DUNMC
Successfully selected: Build DUNMC using dropdown
Successfully selected: Build XOEPC using dropdown
Successfully set input value: Build XOEPC
Successfully set input value: Build DUNMC
Connection creation completed for "Ball XOEPB → Build XOEPC" in 2615ms
  ℹ️  Tracking connection dependency: temp-1751670303646-r1oi5pgzc → temp-1751670306707-n99c07yel (auto-deleted via CASCADE)
Connection creation completed for "Ball DUNMB → Build DUNMC" in 2614ms
  ℹ️  Tracking connection dependency: temp-1751670303645-zdzxmcmgk → temp-1751670306709-cd2vr5mt9 (auto-deleted via CASCADE)
Connection found: Ball DUNMB → Build DUNMC in Length (any multiplier)
Connection found: Ball XOEPB → Build XOEPC in Length (any multiplier)
Creating connection: Build XOEPC → Mouse XOEPF (0.1x)
Creating connection: Build DUNMC → Mouse DUNMF (0.1x)
Selecting AutoComplete option: Build DUNMC
Selecting AutoComplete option: Build XOEPC
Successfully selected: Build DUNMC using dropdown
Successfully selected: Build XOEPC using dropdown
Successfully set input value: Build DUNMC
Successfully set input value: Build XOEPC
Selecting AutoComplete option: Mouse XOEPF
Selecting AutoComplete option: Mouse DUNMF
Successfully selected: Mouse XOEPF using dropdown
Successfully selected: Mouse DUNMF using dropdown
Successfully set input value: Mouse DUNMF
Successfully set input value: Mouse XOEPF
Connection not found: Human ILKXA → Ball ILKXB (10.0x) in Length
Connection list content: Connections (18)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Car ILKXD (ID: 2542)
  ✓ Fallback deleted entity: Eleph ILKXE (ID: 2545)
  ✓ Fallback deleted entity: Mouse ILKXF (ID: 2548)
  ℹ️  Fallback cleanup handled 3 additional entities
🧹 Post-test cleanup complete in 21ms (Worker 5):
  • Entities deleted: 3 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 5
  • Test efficiency: 100% (21ms cleanup / 38662ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 5 (3ms)
Connection creation completed for "Build XOEPC → Mouse XOEPF" in 2617ms
  ℹ️  Tracking connection dependency: temp-1751670306707-n99c07yel → temp-1751670315709-o3rs5rhxv (auto-deleted via CASCADE)
Connection creation completed for "Build DUNMC → Mouse DUNMF" in 2617ms
  ℹ️  Tracking connection dependency: temp-1751670306709-cd2vr5mt9 → temp-1751670315708-lpb2v5p11 (auto-deleted via CASCADE)
[7/210] ❌ should calculate complex multi-hop paths (38.9s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.
  ✘  7 [chromium] › e2e/tests/comparisons.spec.ts:179:7 › Entity Comparisons and Pathfinding › should calculate complex multi-hop paths (retry #1) (38.9s)
Connection found: Build DUNMC → Mouse DUNMF in Length (any multiplier)
Connection found: Build XOEPC → Mouse XOEPF in Length (any multiplier)
[8/210] 🏃 Starting: should handle same entity comparison
🧹 Starting pre-test cleanup...
  Found 92 total entities in database (Worker 8)
  Identified 12 test entities to delete for Worker 8
  ✓ Deleted test entity: Ball DUNMB (ID: 2547)
  ✓ Deleted test entity: Ball XOEPB (ID: 2546)
  ✓ Deleted test entity: Car XOEPD (ID: 2551)
  ✓ Deleted test entity: Build XOEPC (ID: 2550)
  ✓ Deleted test entity: Car DUNMD (ID: 2552)
  ✓ Deleted test entity: Build DUNMC (ID: 2549)
  ✓ Deleted test entity: Eleph XOEPE (ID: 2554)
  ✓ Deleted test entity: Eleph DUNME (ID: 2553)
  ✓ Deleted test entity: Human XOEPA (ID: 2543)
  ✓ Deleted test entity: Human DUNMA (ID: 2544)
  ✓ Deleted test entity: Mouse DUNMF (ID: 2556)
  ✓ Deleted test entity: Mouse XOEPF (ID: 2555)
🧹 Pre-test cleanup complete in 150ms (Worker 8):
  • Initial entities: 92
  • Entities deleted: 12
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 12
Creating connection: Car XOEPD → Eleph XOEPE (0.3x)
Creating connection: Car DUNMD → Eleph DUNME (0.3x)
Selecting AutoComplete option: Car DUNMD
Selecting AutoComplete option: Car XOEPD
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human JZEOA
Creating entity for complex test: Human JZEOA
Creating entity: Human JZEOA (expectFailure: false)
Successfully selected: Car DUNMD using dropdown
Successfully selected: Car XOEPD using dropdown
Create button clicked successfully with standard click (attempt 1)
Successfully set input value: Car XOEPD
Successfully set input value: Car DUNMD
Selecting AutoComplete option: Eleph XOEPE
Selecting AutoComplete option: Eleph DUNME
Successfully selected: Eleph DUNME using dropdown
Successfully selected: Eleph XOEPE using dropdown
Successfully set input value: Eleph DUNME
Successfully set input value: Eleph XOEPE
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Car DUNMD → Eleph DUNME, checking if it exists...
Connection creation failed for Car XOEPD → Eleph XOEPE, checking if it exists...
Verifying entity "Human JZEOA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human JZEOA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human JZEOA" confirmed visible
Entity creation completed for "Human JZEOA" in 1943ms
  ✅ Complex test entity created: Human JZEOA (ID: temp-1751670330669-byjciu9i2) in 2467ms
✅ Comparison test entity 1 created: Human JZEOA
Creating comparison test entity 2/6: Ball JZEOB
Creating entity for complex test: Ball JZEOB
Creating entity: Ball JZEOB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball JZEOB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball JZEOB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball JZEOB" confirmed visible
Entity creation completed for "Ball JZEOB" in 1886ms
  ✅ Complex test entity created: Ball JZEOB (ID: temp-1751670333694-qy0pd8xvl) in 2418ms
✅ Comparison test entity 2 created: Ball JZEOB
Creating comparison test entity 3/6: Build JZEOC
Creating entity for complex test: Build JZEOC
Creating entity: Build JZEOC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build JZEOC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build JZEOC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build JZEOC" confirmed visible
Entity creation completed for "Build JZEOC" in 1967ms
  ✅ Complex test entity created: Build JZEOC (ID: temp-1751670336787-29pjmshj7) in 2489ms
✅ Comparison test entity 3 created: Build JZEOC
Creating comparison test entity 4/6: Car JZEOD
Creating entity for complex test: Car JZEOD
Creating entity: Car JZEOD (expectFailure: false)
Connection not found: Car DUNMD → Eleph DUNME (0.3x) in Mass
Connection not found: Car XOEPD → Eleph XOEPE (0.3x) in Mass
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
Create button clicked successfully with standard click (attempt 1)
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 21ms (Worker 7):
🧹 Post-test cleanup complete in 21ms (Worker 6):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 6
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 7
  • Test efficiency: 100% (21ms cleanup / 40321ms total)
  ✅ All cleanup operations successful
  • Test efficiency: 100% (21ms cleanup / 40321ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 6 (6ms)
  ✅ Cleanup verification: No test entities remain from Worker 7 (6ms)
[8/210] ❌ should handle reverse path calculations (40.8s)
   └─ Error: Error: Connection creation failed for "Car DUNMD → Eleph DUNME": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  8 [chromium] › e2e/tests/comparisons.spec.ts:194:7 › Entity Comparisons and Pathfinding › should handle reverse path calculations (40.8s)
[9/210] ❌ should handle different unit entities with no connection (40.8s)
   └─ Error: Error: Connection creation failed for "Car XOEPD → Eleph XOEPE": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  9 [chromium] › e2e/tests/comparisons.spec.ts:208:7 › Entity Comparisons and Pathfinding › should handle different unit entities with no connection (40.8s)
Submit button is ready for interaction (attempt 1)
[10/210] 🏃 Starting: should handle different unit entities with no connection
[10/210] 🏃 Starting: should handle reverse path calculations
Submit button clicked successfully with standard click (attempt 1)
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 84 total entities in database (Worker 9)
  Found 84 total entities in database (Worker 10)
  Identified 4 test entities to delete for Worker 9
  Identified 4 test entities to delete for Worker 10
  ✓ Deleted test entity: Ball JZEOB (ID: 2558)
  ✓ Deleted test entity: Ball JZEOB (ID: 2558)
  ✓ Deleted test entity: Build JZEOC (ID: 2559)
  ✓ Deleted test entity: Car JZEOD (ID: 2560)
  ✓ Deleted test entity: Build JZEOC (ID: 2559)
  ✓ Deleted test entity: Car JZEOD (ID: 2560)
  ✓ Deleted test entity: Human JZEOA (ID: 2557)
  ✓ Deleted test entity: Human JZEOA (ID: 2557)
🧹 Pre-test cleanup complete in 39ms (Worker 9):
  • Initial entities: 84
  • Entities deleted: 4
  • Failed deletions: 0
  • Final entity count: 80
🧹 Pre-test cleanup complete in 39ms (Worker 10):
  • Initial entities: 84
  • Net reduction: 4
  • Entities deleted: 4
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 4
Verifying entity "Car JZEOD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car JZEOD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car JZEOD" confirmed visible
Entity creation completed for "Car JZEOD" in 1795ms
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human GHSGA
Creating entity for complex test: Human GHSGA
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human FBGLA
Creating entity for complex test: Human FBGLA
  ✅ Complex test entity created: Car JZEOD (ID: temp-1751670339721-la4k434ke) in 2322ms
✅ Comparison test entity 4 created: Car JZEOD
Creating entity: Human GHSGA (expectFailure: false)
Creating entity: Human FBGLA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Creating comparison test entity 5/6: Eleph JZEOE
Creating entity for complex test: Eleph JZEOE
Creating entity: Eleph JZEOE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human FBGLA" in UI with browser-specific timeout: 5000ms
Verifying entity "Human GHSGA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human GHSGA" verified on first attempt
  ✓ Entity "Human FBGLA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
    ✓ UI refresh complete and entity "Human FBGLA" confirmed visible
Entity creation completed for "Human FBGLA" in 1993ms
    ✓ UI refresh complete and entity "Human GHSGA" confirmed visible
Entity creation completed for "Human GHSGA" in 1993ms
  ✅ Complex test entity created: Human FBGLA (ID: temp-1751670342220-s6ll0quif) in 2517ms
✅ Comparison test entity 1 created: Human FBGLA
  ✅ Complex test entity created: Human GHSGA (ID: temp-1751670342220-zkpzla8xc) in 2517ms
✅ Comparison test entity 1 created: Human GHSGA
Verifying entity "Eleph JZEOE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph JZEOE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph JZEOE" confirmed visible
Entity creation completed for "Eleph JZEOE" in 1917ms
  ✅ Complex test entity created: Eleph JZEOE (ID: temp-1751670342759-y62mivl7g) in 2434ms
✅ Comparison test entity 5 created: Eleph JZEOE
Creating comparison test entity 2/6: Ball FBGLB
Creating entity for complex test: Ball FBGLB
Creating comparison test entity 2/6: Ball GHSGB
Creating entity for complex test: Ball GHSGB
Creating entity: Ball FBGLB (expectFailure: false)
Creating entity: Ball GHSGB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Creating comparison test entity 6/6: Mouse JZEOF
Creating entity for complex test: Mouse JZEOF
Creating entity: Mouse JZEOF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball FBGLB" in UI with browser-specific timeout: 5000ms
Verifying entity "Ball GHSGB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball GHSGB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Ball FBGLB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Submit button is ready for interaction (attempt 1)
    ✓ UI refresh complete and entity "Ball GHSGB" confirmed visible
Entity creation completed for "Ball GHSGB" in 1871ms
    ✓ UI refresh complete and entity "Ball FBGLB" confirmed visible
Entity creation completed for "Ball FBGLB" in 1872ms
Submit button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Ball GHSGB (ID: temp-1751670345224-377o9l1h9) in 2398ms
✅ Comparison test entity 2 created: Ball GHSGB
  ✅ Complex test entity created: Ball FBGLB (ID: temp-1751670345224-40tq0n57j) in 2398ms
✅ Comparison test entity 2 created: Ball FBGLB
Verifying entity "Mouse JZEOF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse JZEOF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse JZEOF" confirmed visible
Entity creation completed for "Mouse JZEOF" in 1930ms
  ✅ Complex test entity created: Mouse JZEOF (ID: temp-1751670345817-67mcxi7zz) in 2454ms
✅ Comparison test entity 6 created: Mouse JZEOF
Creating comparison test entity 3/6: Build GHSGC
Creating entity for complex test: Build GHSGC
Creating comparison test entity 3/6: Build FBGLC
Creating entity for complex test: Build FBGLC
Creating entity: Build GHSGC (expectFailure: false)
Creating entity: Build FBGLC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Creating test connections...
Creating connection: Human JZEOA → Ball JZEOB (10.0x)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Selecting AutoComplete option: Human JZEOA
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build FBGLC" in UI with browser-specific timeout: 5000ms
Verifying entity "Build GHSGC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build FBGLC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Build GHSGC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build FBGLC" confirmed visible
Entity creation completed for "Build FBGLC" in 1926ms
    ✓ UI refresh complete and entity "Build GHSGC" confirmed visible
Entity creation completed for "Build GHSGC" in 1928ms
  ✅ Complex test entity created: Build FBGLC (ID: temp-1751670348283-2s2o1boeq) in 2450ms
✅ Comparison test entity 3 created: Build FBGLC
  ✅ Complex test entity created: Build GHSGC (ID: temp-1751670348285-2k18bmdch) in 2453ms
✅ Comparison test entity 3 created: Build GHSGC
Creating comparison test entity 4/6: Car FBGLD
Creating entity for complex test: Car FBGLD
Creating comparison test entity 4/6: Car GHSGD
Creating entity for complex test: Car GHSGD
Creating entity: Car FBGLD (expectFailure: false)
Creating entity: Car GHSGD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Dropdown failed, using direct input for: Human JZEOA
Successfully set input value: Human JZEOA
Selecting AutoComplete option: Ball JZEOB
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car GHSGD" in UI with browser-specific timeout: 5000ms
Verifying entity "Car FBGLD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car GHSGD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Car FBGLD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car GHSGD" confirmed visible
    ✓ UI refresh complete and entity "Car FBGLD" confirmed visible
Entity creation completed for "Car FBGLD" in 1828ms
Entity creation completed for "Car GHSGD" in 1828ms
  ✅ Complex test entity created: Car GHSGD (ID: temp-1751670351258-j6ro84zfh) in 2364ms
✅ Comparison test entity 4 created: Car GHSGD
  ✅ Complex test entity created: Car FBGLD (ID: temp-1751670351259-wsu3fvw9f) in 2367ms
✅ Comparison test entity 4 created: Car FBGLD
Creating comparison test entity 5/6: Eleph GHSGE
Creating entity for complex test: Eleph GHSGE
Creating comparison test entity 5/6: Eleph FBGLE
Creating entity for complex test: Eleph FBGLE
Creating entity: Eleph FBGLE (expectFailure: false)
Creating entity: Eleph GHSGE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Dropdown failed, using direct input for: Ball JZEOB
Successfully set input value: Ball JZEOB
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph FBGLE" in UI with browser-specific timeout: 5000ms
Verifying entity "Eleph GHSGE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph FBGLE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Eleph GHSGE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph GHSGE" confirmed visible
    ✓ UI refresh complete and entity "Eleph FBGLE" confirmed visible
Entity creation completed for "Eleph FBGLE" in 1922ms
Entity creation completed for "Eleph GHSGE" in 1922ms
  ✅ Complex test entity created: Eleph FBGLE (ID: temp-1751670354325-qifrzfiz0) in 2455ms
✅ Comparison test entity 5 created: Eleph FBGLE
  ✅ Complex test entity created: Eleph GHSGE (ID: temp-1751670354325-t5p4n182h) in 2455ms
✅ Comparison test entity 5 created: Eleph GHSGE
Creating comparison test entity 6/6: Mouse GHSGF
Creating entity for complex test: Mouse GHSGF
Creating comparison test entity 6/6: Mouse FBGLF
Creating entity for complex test: Mouse FBGLF
Creating entity: Mouse GHSGF (expectFailure: false)
Creating entity: Mouse FBGLF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse GHSGF" in UI with browser-specific timeout: 5000ms
Verifying entity "Mouse FBGLF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse FBGLF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Mouse GHSGF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse FBGLF" confirmed visible
Entity creation completed for "Mouse FBGLF" in 1967ms
    ✓ UI refresh complete and entity "Mouse GHSGF" confirmed visible
Entity creation completed for "Mouse GHSGF" in 1968ms
  ✅ Complex test entity created: Mouse FBGLF (ID: temp-1751670357440-osdlzrtbp) in 2505ms
✅ Comparison test entity 6 created: Mouse FBGLF
  ✅ Complex test entity created: Mouse GHSGF (ID: temp-1751670357440-6mxgbx08k) in 2505ms
✅ Comparison test entity 6 created: Mouse GHSGF
Connection creation failed for Human JZEOA → Ball JZEOB, checking if it exists...
Creating test connections...
Creating connection: Human FBGLA → Ball FBGLB (10.0x)
Creating test connections...
Creating connection: Human GHSGA → Ball GHSGB (10.0x)
Selecting AutoComplete option: Human FBGLA
Selecting AutoComplete option: Human GHSGA
Successfully selected: Human GHSGA using dropdown
Successfully selected: Human FBGLA using dropdown
Successfully set input value: Human FBGLA
Successfully set input value: Human GHSGA
Selecting AutoComplete option: Ball FBGLB
Selecting AutoComplete option: Ball GHSGB
Successfully selected: Ball FBGLB using dropdown
Successfully selected: Ball GHSGB using dropdown
Successfully set input value: Ball FBGLB
Successfully set input value: Ball GHSGB
Connection creation completed for "Human GHSGA → Ball GHSGB" in 2633ms
  ℹ️  Tracking connection dependency: temp-1751670342220-zkpzla8xc → temp-1751670345224-377o9l1h9 (auto-deleted via CASCADE)
Connection creation completed for "Human FBGLA → Ball FBGLB" in 2634ms
  ℹ️  Tracking connection dependency: temp-1751670342220-s6ll0quif → temp-1751670345224-40tq0n57j (auto-deleted via CASCADE)
Connection found: Human FBGLA → Ball FBGLB in Length (any multiplier)
Connection found: Human GHSGA → Ball GHSGB in Length (any multiplier)
Creating connection: Ball FBGLB → Build FBGLC (50.0x)
Creating connection: Ball GHSGB → Build GHSGC (50.0x)
Selecting AutoComplete option: Ball FBGLB
Selecting AutoComplete option: Ball GHSGB
Successfully selected: Ball GHSGB using dropdown
Successfully selected: Ball FBGLB using dropdown
Successfully set input value: Ball GHSGB
Successfully set input value: Ball FBGLB
Selecting AutoComplete option: Build GHSGC
Selecting AutoComplete option: Build FBGLC
Successfully selected: Build FBGLC using dropdown
Successfully selected: Build GHSGC using dropdown
Successfully set input value: Build FBGLC
Successfully set input value: Build GHSGC
Connection creation completed for "Ball GHSGB → Build GHSGC" in 2591ms
  ℹ️  Tracking connection dependency: temp-1751670345224-377o9l1h9 → temp-1751670348285-2k18bmdch (auto-deleted via CASCADE)
Connection creation completed for "Ball FBGLB → Build FBGLC" in 2592ms
  ℹ️  Tracking connection dependency: temp-1751670345224-40tq0n57j → temp-1751670348283-2s2o1boeq (auto-deleted via CASCADE)
Connection found: Ball GHSGB → Build GHSGC in Length (any multiplier)
Connection found: Ball FBGLB → Build FBGLC in Length (any multiplier)
Connection not found: Human JZEOA → Ball JZEOB (10.0x) in Length
Connection list content: Connections (14)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
Creating connection: Build GHSGC → Mouse GHSGF (0.1x)
Creating connection: Build FBGLC → Mouse FBGLF (0.1x)
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Eleph JZEOE (ID: 2563)
  ✓ Fallback deleted entity: Mouse JZEOF (ID: 2566)
  ℹ️  Fallback cleanup handled 2 additional entities
🧹 Post-test cleanup complete in 24ms (Worker 8):
  • Entities deleted: 2 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 8
  • Test efficiency: 100% (24ms cleanup / 38550ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 8 (4ms)
Selecting AutoComplete option: Build GHSGC
Selecting AutoComplete option: Build FBGLC
[10/210] ❌ should handle same entity comparison (38.8s)

📈 Progress: 10/210 (5%)
   ✅ 1 passed | ❌ 9 failed | ⏭️ 0 skipped
   ⏱️  Elapsed: 2.6min | Remaining: ~133.8min

   └─ Error: TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.
  ✘  10 [chromium] › e2e/tests/comparisons.spec.ts:225:7 › Entity Comparisons and Pathfinding › should handle same entity comparison (38.8s)
[11/210] 🏃 Starting: should handle same entity comparison
Successfully selected: Build GHSGC using dropdown
Successfully selected: Build FBGLC using dropdown
🧹 Starting pre-test cleanup...
  Found 92 total entities in database (Worker 11)
  Identified 12 test entities to delete for Worker 11
  ✓ Deleted test entity: Ball FBGLB (ID: 2564)
  ✓ Deleted test entity: Build GHSGC (ID: 2568)
  ✓ Deleted test entity: Car FBGLD (ID: 2569)
  ✓ Deleted test entity: Ball GHSGB (ID: 2565)
  ✓ Deleted test entity: Car GHSGD (ID: 2570)
  ✓ Deleted test entity: Build FBGLC (ID: 2567)
  ✓ Deleted test entity: Eleph FBGLE (ID: 2571)
  ✓ Deleted test entity: Eleph GHSGE (ID: 2572)
  ✓ Deleted test entity: Human FBGLA (ID: 2561)
  ✓ Deleted test entity: Human GHSGA (ID: 2562)
Successfully set input value: Build FBGLC
Successfully set input value: Build GHSGC
  ✓ Deleted test entity: Mouse GHSGF (ID: 2573)
  ✓ Deleted test entity: Mouse FBGLF (ID: 2574)
🧹 Pre-test cleanup complete in 162ms (Worker 11):
  • Initial entities: 92
  • Entities deleted: 12
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 12
Selecting AutoComplete option: Mouse FBGLF
Selecting AutoComplete option: Mouse GHSGF
Successfully selected: Mouse FBGLF using dropdown
Successfully selected: Mouse GHSGF using dropdown
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human CKSAA
Creating entity for complex test: Human CKSAA
Successfully set input value: Mouse FBGLF
Successfully set input value: Mouse GHSGF
Creating entity: Human CKSAA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Build GHSGC → Mouse GHSGF, checking if it exists...
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Build FBGLC → Mouse FBGLF, checking if it exists...
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human CKSAA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human CKSAA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human CKSAA" confirmed visible
Entity creation completed for "Human CKSAA" in 1985ms
  ✅ Complex test entity created: Human CKSAA (ID: temp-1751670370056-7yzcc75xi) in 2508ms
✅ Comparison test entity 1 created: Human CKSAA
Creating comparison test entity 2/6: Ball CKSAB
Creating entity for complex test: Ball CKSAB
Creating entity: Ball CKSAB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball CKSAB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball CKSAB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball CKSAB" confirmed visible
Entity creation completed for "Ball CKSAB" in 1889ms
  ✅ Complex test entity created: Ball CKSAB (ID: temp-1751670373095-k0xbs1iy2) in 2421ms
✅ Comparison test entity 2 created: Ball CKSAB
Creating comparison test entity 3/6: Build CKSAC
Creating entity for complex test: Build CKSAC
Creating entity: Build CKSAC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build CKSAC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build CKSAC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Connection not found: Build GHSGC → Mouse GHSGF (0.1x) in Length
Connection not found: Build FBGLC → Mouse FBGLF (0.1x) in Length
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
    ✓ UI refresh complete and entity "Build CKSAC" confirmed visible
Entity creation completed for "Build CKSAC" in 1936ms
🧹 Starting post-test cleanup...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 19ms (Worker 9):
🧹 Post-test cleanup complete in 19ms (Worker 10):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 10
  • Test efficiency: 100% (19ms cleanup / 36915ms total)
  ✅ All cleanup operations successful
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 9
  • Test efficiency: 100% (19ms cleanup / 36915ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 10 (5ms)
  ✅ Cleanup verification: No test entities remain from Worker 9 (5ms)
  ✅ Complex test entity created: Build CKSAC (ID: temp-1751670376170-6cdmck3k0) in 2464ms
✅ Comparison test entity 3 created: Build CKSAC
[11/210] ❌ should handle different unit entities with no connection (37.3s)
   └─ Error: Error: Connection creation failed for "Build FBGLC → Mouse FBGLF": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  11 [chromium] › e2e/tests/comparisons.spec.ts:208:7 › Entity Comparisons and Pathfinding › should handle different unit entities with no connection (retry #1) (37.3s)
[12/210] ❌ should handle reverse path calculations (37.3s)
   └─ Error: Error: Connection creation failed for "Build GHSGC → Mouse GHSGF": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  12 [chromium] › e2e/tests/comparisons.spec.ts:194:7 › Entity Comparisons and Pathfinding › should handle reverse path calculations (retry #1) (37.3s)
Creating comparison test entity 4/6: Car CKSAD
Creating entity for complex test: Car CKSAD
[13/210] 🏃 Starting: should handle custom from count values
[13/210] 🏃 Starting: should validate entity selection
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 83 total entities in database (Worker 12)
  Found 83 total entities in database (Worker 13)
  Identified 3 test entities to delete for Worker 12
  Identified 3 test entities to delete for Worker 13
  ✓ Deleted test entity: Ball CKSAB (ID: 2576)
  ✓ Deleted test entity: Human CKSAA (ID: 2575)
  ✓ Deleted test entity: Human CKSAA (ID: 2575)
  ✓ Deleted test entity: Ball CKSAB (ID: 2576)
  ✓ Deleted test entity: Build CKSAC (ID: 2577)
  ✓ Deleted test entity: Build CKSAC (ID: 2577)
🧹 Pre-test cleanup complete in 39ms (Worker 12):
  • Initial entities: 83
  • Entities deleted: 3
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 3
🧹 Pre-test cleanup complete in 39ms (Worker 13):
  • Initial entities: 83
  • Entities deleted: 3
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 3
Creating entity: Car CKSAD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human OJMPA
Creating entity for complex test: Human OJMPA
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human BUMDA
Creating entity for complex test: Human BUMDA
Creating entity: Human OJMPA (expectFailure: false)
Creating entity: Human BUMDA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car CKSAD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car CKSAD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car CKSAD" confirmed visible
Entity creation completed for "Car CKSAD" in 1786ms
  ✅ Complex test entity created: Car CKSAD (ID: temp-1751670379075-xmimwofsw) in 2301ms
✅ Comparison test entity 4 created: Car CKSAD
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human OJMPA" in UI with browser-specific timeout: 5000ms
Verifying entity "Human BUMDA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human BUMDA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Human OJMPA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating comparison test entity 5/6: Eleph CKSAE
Creating entity for complex test: Eleph CKSAE
    ✓ UI refresh complete and entity "Human BUMDA" confirmed visible
Entity creation completed for "Human BUMDA" in 2030ms
    ✓ UI refresh complete and entity "Human OJMPA" confirmed visible
Entity creation completed for "Human OJMPA" in 2031ms
Creating entity: Eleph CKSAE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Human BUMDA (ID: temp-1751670380196-tke82g2s4) in 2552ms
✅ Comparison test entity 1 created: Human BUMDA
  ✅ Complex test entity created: Human OJMPA (ID: temp-1751670380196-2y2yotbab) in 2553ms
✅ Comparison test entity 1 created: Human OJMPA
Creating comparison test entity 2/6: Ball BUMDB
Creating entity for complex test: Ball BUMDB
Creating comparison test entity 2/6: Ball OJMPB
Creating entity for complex test: Ball OJMPB
Creating entity: Ball BUMDB (expectFailure: false)
Creating entity: Ball OJMPB (expectFailure: false)
Submit button is ready for interaction (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph CKSAE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph CKSAE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph CKSAE" confirmed visible
Entity creation completed for "Eleph CKSAE" in 1927ms
  ✅ Complex test entity created: Eleph CKSAE (ID: temp-1751670382128-e699zl2tl) in 2449ms
✅ Comparison test entity 5 created: Eleph CKSAE
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball OJMPB" in UI with browser-specific timeout: 5000ms
Verifying entity "Ball BUMDB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball BUMDB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Ball OJMPB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating comparison test entity 6/6: Mouse CKSAF
Creating entity for complex test: Mouse CKSAF
    ✓ UI refresh complete and entity "Ball OJMPB" confirmed visible
Entity creation completed for "Ball OJMPB" in 1890ms
    ✓ UI refresh complete and entity "Ball BUMDB" confirmed visible
Entity creation completed for "Ball BUMDB" in 1892ms
Creating entity: Mouse CKSAF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Ball OJMPB (ID: temp-1751670383216-fis188roc) in 2412ms
✅ Comparison test entity 2 created: Ball OJMPB
  ✅ Complex test entity created: Ball BUMDB (ID: temp-1751670383217-sx84hbt7w) in 2414ms
✅ Comparison test entity 2 created: Ball BUMDB
Creating comparison test entity 3/6: Build OJMPC
Creating entity for complex test: Build OJMPC
Creating comparison test entity 3/6: Build BUMDC
Creating entity for complex test: Build BUMDC
Creating entity: Build OJMPC (expectFailure: false)
Creating entity: Build BUMDC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse CKSAF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse CKSAF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse CKSAF" confirmed visible
Entity creation completed for "Mouse CKSAF" in 1923ms
  ✅ Complex test entity created: Mouse CKSAF (ID: temp-1751670385176-y2hha7qk0) in 2441ms
✅ Comparison test entity 6 created: Mouse CKSAF
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build BUMDC" in UI with browser-specific timeout: 5000ms
Verifying entity "Build OJMPC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build OJMPC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Build BUMDC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build OJMPC" confirmed visible
Entity creation completed for "Build OJMPC" in 1924ms
    ✓ UI refresh complete and entity "Build BUMDC" confirmed visible
Entity creation completed for "Build BUMDC" in 1926ms
  ✅ Complex test entity created: Build OJMPC (ID: temp-1751670386269-nrnuvixfy) in 2447ms
✅ Comparison test entity 3 created: Build OJMPC
  ✅ Complex test entity created: Build BUMDC (ID: temp-1751670386270-jwnuhadsh) in 2448ms
✅ Comparison test entity 3 created: Build BUMDC
Creating test connections...
Creating connection: Human CKSAA → Ball CKSAB (10.0x)
Selecting AutoComplete option: Human CKSAA
Creating comparison test entity 4/6: Car BUMDD
Creating entity for complex test: Car BUMDD
Creating comparison test entity 4/6: Car OJMPD
Creating entity for complex test: Car OJMPD
Creating entity: Car BUMDD (expectFailure: false)
Creating entity: Car OJMPD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car BUMDD" in UI with browser-specific timeout: 5000ms
Verifying entity "Car OJMPD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car BUMDD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Car OJMPD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car OJMPD" confirmed visible
Entity creation completed for "Car OJMPD" in 1809ms
    ✓ UI refresh complete and entity "Car BUMDD" confirmed visible
Entity creation completed for "Car BUMDD" in 1811ms
Dropdown failed, using direct input for: Human CKSAA
  ✅ Complex test entity created: Car OJMPD (ID: temp-1751670389207-0tz1d4rf6) in 2333ms
✅ Comparison test entity 4 created: Car OJMPD
  ✅ Complex test entity created: Car BUMDD (ID: temp-1751670389207-je6mpohm2) in 2333ms
✅ Comparison test entity 4 created: Car BUMDD
Successfully set input value: Human CKSAA
Selecting AutoComplete option: Ball CKSAB
Creating comparison test entity 5/6: Eleph BUMDE
Creating entity for complex test: Eleph BUMDE
Creating comparison test entity 5/6: Eleph OJMPE
Creating entity for complex test: Eleph OJMPE
Creating entity: Eleph BUMDE (expectFailure: false)
Creating entity: Eleph OJMPE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph OJMPE" in UI with browser-specific timeout: 5000ms
Verifying entity "Eleph BUMDE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph BUMDE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Eleph OJMPE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Dropdown failed, using direct input for: Ball CKSAB
    ✓ UI refresh complete and entity "Eleph OJMPE" confirmed visible
Entity creation completed for "Eleph OJMPE" in 1912ms
    ✓ UI refresh complete and entity "Eleph BUMDE" confirmed visible
Entity creation completed for "Eleph BUMDE" in 1913ms
Successfully set input value: Ball CKSAB
  ✅ Complex test entity created: Eleph OJMPE (ID: temp-1751670392241-wbwr6dkpg) in 2428ms
✅ Comparison test entity 5 created: Eleph OJMPE
  ✅ Complex test entity created: Eleph BUMDE (ID: temp-1751670392243-1w4yltfn0) in 2430ms
✅ Comparison test entity 5 created: Eleph BUMDE
Creating comparison test entity 6/6: Mouse OJMPF
Creating entity for complex test: Mouse OJMPF
Creating comparison test entity 6/6: Mouse BUMDF
Creating entity for complex test: Mouse BUMDF
Creating entity: Mouse OJMPF (expectFailure: false)
Creating entity: Mouse BUMDF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse BUMDF" in UI with browser-specific timeout: 5000ms
Verifying entity "Mouse OJMPF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse BUMDF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Mouse OJMPF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse OJMPF" confirmed visible
Entity creation completed for "Mouse OJMPF" in 1951ms
    ✓ UI refresh complete and entity "Mouse BUMDF" confirmed visible
Entity creation completed for "Mouse BUMDF" in 1951ms
  ✅ Complex test entity created: Mouse BUMDF (ID: temp-1751670395326-yq16wc7do) in 2479ms
✅ Comparison test entity 6 created: Mouse BUMDF
  ✅ Complex test entity created: Mouse OJMPF (ID: temp-1751670395329-oacibgppr) in 2484ms
✅ Comparison test entity 6 created: Mouse OJMPF
Creating test connections...
Creating connection: Human OJMPA → Ball OJMPB (10.0x)
Creating test connections...
Creating connection: Human BUMDA → Ball BUMDB (10.0x)
Selecting AutoComplete option: Human OJMPA
Selecting AutoComplete option: Human BUMDA
Successfully selected: Human OJMPA using dropdown
Successfully selected: Human BUMDA using dropdown
Successfully set input value: Human OJMPA
Successfully set input value: Human BUMDA
Connection creation failed for Human CKSAA → Ball CKSAB, checking if it exists...
Selecting AutoComplete option: Ball OJMPB
Selecting AutoComplete option: Ball BUMDB
Successfully selected: Ball OJMPB using dropdown
Successfully selected: Ball BUMDB using dropdown
Successfully set input value: Ball OJMPB
Successfully set input value: Ball BUMDB
Connection creation completed for "Human OJMPA → Ball OJMPB" in 2582ms
  ℹ️  Tracking connection dependency: temp-1751670380196-2y2yotbab → temp-1751670383216-fis188roc (auto-deleted via CASCADE)
Connection creation completed for "Human BUMDA → Ball BUMDB" in 2583ms
  ℹ️  Tracking connection dependency: temp-1751670380196-tke82g2s4 → temp-1751670383217-sx84hbt7w (auto-deleted via CASCADE)
Connection found: Human OJMPA → Ball OJMPB in Length (any multiplier)
Connection found: Human BUMDA → Ball BUMDB in Length (any multiplier)
Creating connection: Ball OJMPB → Build OJMPC (50.0x)
Creating connection: Ball BUMDB → Build BUMDC (50.0x)
Selecting AutoComplete option: Ball OJMPB
Selecting AutoComplete option: Ball BUMDB
Successfully selected: Ball OJMPB using dropdown
Successfully selected: Ball BUMDB using dropdown
Successfully set input value: Ball OJMPB
Successfully set input value: Ball BUMDB
Selecting AutoComplete option: Build OJMPC
Selecting AutoComplete option: Build BUMDC
Successfully selected: Build OJMPC using dropdown
Successfully selected: Build BUMDC using dropdown
Successfully set input value: Build OJMPC
Successfully set input value: Build BUMDC
Connection creation completed for "Ball OJMPB → Build OJMPC" in 2598ms
  ℹ️  Tracking connection dependency: temp-1751670383216-fis188roc → temp-1751670386269-nrnuvixfy (auto-deleted via CASCADE)
Connection creation completed for "Ball BUMDB → Build BUMDC" in 2568ms
  ℹ️  Tracking connection dependency: temp-1751670383217-sx84hbt7w → temp-1751670386270-jwnuhadsh (auto-deleted via CASCADE)
Connection found: Ball OJMPB → Build OJMPC in Length (any multiplier)
Connection found: Ball BUMDB → Build BUMDC in Length (any multiplier)
Creating connection: Build OJMPC → Mouse OJMPF (0.1x)
Creating connection: Build BUMDC → Mouse BUMDF (0.1x)
Selecting AutoComplete option: Build BUMDC
Selecting AutoComplete option: Build OJMPC
Successfully selected: Build OJMPC using dropdown
Successfully selected: Build BUMDC using dropdown
Successfully set input value: Build OJMPC
Successfully set input value: Build BUMDC
Selecting AutoComplete option: Mouse BUMDF
Selecting AutoComplete option: Mouse OJMPF
Connection not found: Human CKSAA → Ball CKSAB (10.0x) in Length
Connection list content: Connections (14)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Car CKSAD (ID: 2578)
  ✓ Fallback deleted entity: Eleph CKSAE (ID: 2581)
  ✓ Fallback deleted entity: Mouse CKSAF (ID: 2584)
  ℹ️  Fallback cleanup handled 3 additional entities
🧹 Post-test cleanup complete in 25ms (Worker 11):
  • Entities deleted: 3 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 11
  • Test efficiency: 100% (25ms cleanup / 38547ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 11 (4ms)
Successfully selected: Mouse OJMPF using dropdown
Successfully selected: Mouse BUMDF using dropdown
Successfully set input value: Mouse OJMPF
Successfully set input value: Mouse BUMDF
[13/210] ❌ should handle same entity comparison (38.8s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.
  ✘  13 [chromium] › e2e/tests/comparisons.spec.ts:225:7 › Entity Comparisons and Pathfinding › should handle same entity comparison (retry #1) (38.8s)
[14/210] 🏃 Starting: should validate non-existent entities
🧹 Starting pre-test cleanup...
  Found 92 total entities in database (Worker 14)
  Identified 12 test entities to delete for Worker 14
  ✓ Deleted test entity: Ball BUMDB (ID: 2583)
  ✓ Deleted test entity: Build BUMDC (ID: 2585)
  ✓ Deleted test entity: Build OJMPC (ID: 2586)
  ✓ Deleted test entity: Eleph BUMDE (ID: 2589)
  ✓ Deleted test entity: Ball OJMPB (ID: 2582)
  ✓ Deleted test entity: Car BUMDD (ID: 2587)
  ✓ Deleted test entity: Eleph OJMPE (ID: 2590)
  ✓ Deleted test entity: Car OJMPD (ID: 2588)
  ✓ Deleted test entity: Human BUMDA (ID: 2579)
  ✓ Deleted test entity: Human OJMPA (ID: 2580)
  ✓ Deleted test entity: Mouse OJMPF (ID: 2592)
  ✓ Deleted test entity: Mouse BUMDF (ID: 2591)
🧹 Pre-test cleanup complete in 157ms (Worker 14):
  • Initial entities: 92
  • Entities deleted: 12
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 12
Connection creation completed for "Build BUMDC → Mouse BUMDF" in 2618ms
  ℹ️  Tracking connection dependency: temp-1751670386270-jwnuhadsh → temp-1751670395326-yq16wc7do (auto-deleted via CASCADE)
Connection creation completed for "Build OJMPC → Mouse OJMPF" in 2628ms
  ℹ️  Tracking connection dependency: temp-1751670386269-nrnuvixfy → temp-1751670395329-oacibgppr (auto-deleted via CASCADE)
Connection found: Build BUMDC → Mouse BUMDF in Length (any multiplier)
Connection found: Build OJMPC → Mouse OJMPF in Length (any multiplier)
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human VZATA
Creating entity for complex test: Human VZATA
Creating entity: Human VZATA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Creating connection: Car BUMDD → Eleph BUMDE (0.3x)
Creating connection: Car OJMPD → Eleph OJMPE (0.3x)
Selecting AutoComplete option: Car BUMDD
Selecting AutoComplete option: Car OJMPD
Successfully selected: Car OJMPD using dropdown
Successfully selected: Car BUMDD using dropdown
Successfully set input value: Car BUMDD
Successfully set input value: Car OJMPD
Selecting AutoComplete option: Eleph OJMPE
Selecting AutoComplete option: Eleph BUMDE
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human VZATA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human VZATA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Successfully selected: Eleph BUMDE using dropdown
Successfully selected: Eleph OJMPE using dropdown
Successfully set input value: Eleph OJMPE
Successfully set input value: Eleph BUMDE
    ✓ UI refresh complete and entity "Human VZATA" confirmed visible
Entity creation completed for "Human VZATA" in 1935ms
  ✅ Complex test entity created: Human VZATA (ID: temp-1751670409441-f87wr9h3y) in 2453ms
✅ Comparison test entity 1 created: Human VZATA
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Car BUMDD → Eleph BUMDE, checking if it exists...
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Car OJMPD → Eleph OJMPE, checking if it exists...
Creating comparison test entity 2/6: Ball VZATB
Creating entity for complex test: Ball VZATB
Creating entity: Ball VZATB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball VZATB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball VZATB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball VZATB" confirmed visible
Entity creation completed for "Ball VZATB" in 1923ms
  ✅ Complex test entity created: Ball VZATB (ID: temp-1751670412511-y6y3j3y0c) in 2461ms
✅ Comparison test entity 2 created: Ball VZATB
Creating comparison test entity 3/6: Build VZATC
Creating entity for complex test: Build VZATC
Creating entity: Build VZATC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build VZATC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build VZATC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build VZATC" confirmed visible
Entity creation completed for "Build VZATC" in 1968ms
  ✅ Complex test entity created: Build VZATC (ID: temp-1751670415624-ombseooab) in 2504ms
✅ Comparison test entity 3 created: Build VZATC
Creating comparison test entity 4/6: Car VZATD
Creating entity for complex test: Car VZATD
Creating entity: Car VZATD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Connection not found: Car OJMPD → Eleph OJMPE (0.3x) in Mass
Connection not found: Car BUMDD → Eleph BUMDE (0.3x) in Mass
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
🧹 Starting post-test cleanup...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 19ms (Worker 13):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 13
  • Test efficiency: 100% (19ms cleanup / 40444ms total)
  ✅ All cleanup operations successful
🧹 Post-test cleanup complete in 19ms (Worker 12):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 12
  • Test efficiency: 100% (19ms cleanup / 40444ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 12 (6ms)
  ✅ Cleanup verification: No test entities remain from Worker 13 (6ms)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car VZATD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car VZATD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
[14/210] ❌ should handle custom from count values (40.9s)
   └─ Error: Error: Connection creation failed for "Car BUMDD → Eleph BUMDE": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  14 [chromium] › e2e/tests/comparisons.spec.ts:236:7 › Entity Comparisons and Pathfinding › should handle custom from count values (40.9s)
[15/210] ❌ should validate entity selection (40.9s)
   └─ Error: Error: Connection creation failed for "Car OJMPD → Eleph OJMPE": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  15 [chromium] › e2e/tests/comparisons.spec.ts:250:7 › Entity Comparisons and Pathfinding › should validate entity selection (40.9s)
    ✓ UI refresh complete and entity "Car VZATD" confirmed visible
Entity creation completed for "Car VZATD" in 1808ms
[16/210] 🏃 Starting: should validate entity selection
[16/210] 🏃 Starting: should handle custom from count values
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  ✅ Complex test entity created: Car VZATD (ID: temp-1751670418568-mkin9zvcu) in 2334ms
✅ Comparison test entity 4 created: Car VZATD
  Found 84 total entities in database (Worker 16)
  Found 84 total entities in database (Worker 15)
  Identified 4 test entities to delete for Worker 16
  Identified 4 test entities to delete for Worker 15
  ✓ Deleted test entity: Ball VZATB (ID: 2594)
  ✓ Deleted test entity: Ball VZATB (ID: 2594)
  ✓ Deleted test entity: Car VZATD (ID: 2596)
  ✓ Deleted test entity: Build VZATC (ID: 2595)
  ✓ Deleted test entity: Build VZATC (ID: 2595)
  ✓ Deleted test entity: Human VZATA (ID: 2593)
  ✓ Deleted test entity: Car VZATD (ID: 2596)
  ✓ Deleted test entity: Human VZATA (ID: 2593)
🧹 Pre-test cleanup complete in 43ms (Worker 15):
  • Initial entities: 84
  • Entities deleted: 4
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 4
🧹 Pre-test cleanup complete in 43ms (Worker 16):
  • Initial entities: 84
  • Entities deleted: 4
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 4
Creating comparison test entity 5/6: Eleph VZATE
Creating entity for complex test: Eleph VZATE
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human HPBKA
Creating entity for complex test: Human HPBKA
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human KSVMA
Creating entity for complex test: Human KSVMA
Creating entity: Eleph VZATE (expectFailure: false)
Creating entity: Human HPBKA (expectFailure: false)
Creating entity: Human KSVMA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph VZATE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph VZATE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Verifying entity "Human HPBKA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human HPBKA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Verifying entity "Human KSVMA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human KSVMA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph VZATE" confirmed visible
Entity creation completed for "Eleph VZATE" in 1913ms
    ✓ UI refresh complete and entity "Human HPBKA" confirmed visible
Entity creation completed for "Human HPBKA" in 1927ms
    ✓ UI refresh complete and entity "Human KSVMA" confirmed visible
Entity creation completed for "Human KSVMA" in 1957ms
  ✅ Complex test entity created: Eleph VZATE (ID: temp-1751670421609-uk9rgqni7) in 2437ms
✅ Comparison test entity 5 created: Eleph VZATE
  ✅ Complex test entity created: Human HPBKA (ID: temp-1751670421699-ecogygxs4) in 2448ms
✅ Comparison test entity 1 created: Human HPBKA
  ✅ Complex test entity created: Human KSVMA (ID: temp-1751670421737-j5y4c3tx0) in 2479ms
✅ Comparison test entity 1 created: Human KSVMA
Creating comparison test entity 6/6: Mouse VZATF
Creating entity for complex test: Mouse VZATF
Creating comparison test entity 2/6: Ball HPBKB
Creating entity for complex test: Ball HPBKB
Creating comparison test entity 2/6: Ball KSVMB
Creating entity for complex test: Ball KSVMB
Creating entity: Mouse VZATF (expectFailure: false)
Creating entity: Ball HPBKB (expectFailure: false)
Creating entity: Ball KSVMB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse VZATF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse VZATF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Verifying entity "Ball HPBKB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball HPBKB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Verifying entity "Ball KSVMB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball KSVMB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse VZATF" confirmed visible
Entity creation completed for "Mouse VZATF" in 1924ms
    ✓ UI refresh complete and entity "Ball HPBKB" confirmed visible
Entity creation completed for "Ball HPBKB" in 1876ms
    ✓ UI refresh complete and entity "Ball KSVMB" confirmed visible
Entity creation completed for "Ball KSVMB" in 1855ms
  ✅ Complex test entity created: Mouse VZATF (ID: temp-1751670424680-n8sjqi7s4) in 2461ms
✅ Comparison test entity 6 created: Mouse VZATF
  ✅ Complex test entity created: Ball HPBKB (ID: temp-1751670424723-jaicbbb6g) in 2412ms
✅ Comparison test entity 2 created: Ball HPBKB
  ✅ Complex test entity created: Ball KSVMB (ID: temp-1751670424729-vgt1rswj5) in 2379ms
✅ Comparison test entity 2 created: Ball KSVMB
Creating comparison test entity 3/6: Build HPBKC
Creating entity for complex test: Build HPBKC
Creating comparison test entity 3/6: Build KSVMC
Creating entity for complex test: Build KSVMC
Creating entity: Build HPBKC (expectFailure: false)
Creating entity: Build KSVMC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Creating test connections...
Creating connection: Human VZATA → Ball VZATB (10.0x)
Selecting AutoComplete option: Human VZATA
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build HPBKC" in UI with browser-specific timeout: 5000ms
Verifying entity "Build KSVMC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build HPBKC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Build KSVMC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build KSVMC" confirmed visible
Entity creation completed for "Build KSVMC" in 1953ms
    ✓ UI refresh complete and entity "Build HPBKC" confirmed visible
Entity creation completed for "Build HPBKC" in 1955ms
  ✅ Complex test entity created: Build HPBKC (ID: temp-1751670427813-0lh8zakb5) in 2482ms
✅ Comparison test entity 3 created: Build HPBKC
  ✅ Complex test entity created: Build KSVMC (ID: temp-1751670427814-9040s531z) in 2480ms
✅ Comparison test entity 3 created: Build KSVMC
Creating comparison test entity 4/6: Car HPBKD
Creating entity for complex test: Car HPBKD
Creating comparison test entity 4/6: Car KSVMD
Creating entity for complex test: Car KSVMD
Creating entity: Car HPBKD (expectFailure: false)
Creating entity: Car KSVMD (expectFailure: false)
Dropdown failed, using direct input for: Human VZATA
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Successfully set input value: Human VZATA
Selecting AutoComplete option: Ball VZATB
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Car HPBKD" in UI with browser-specific timeout: 5000ms
Verifying entity "Car KSVMD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car HPBKD" verified on first attempt
  ✓ Entity "Car KSVMD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car KSVMD" confirmed visible
Entity creation completed for "Car KSVMD" in 1844ms
    ✓ UI refresh complete and entity "Car HPBKD" confirmed visible
Entity creation completed for "Car HPBKD" in 1844ms
  ✅ Complex test entity created: Car HPBKD (ID: temp-1751670430799-tj6hye8r3) in 2379ms
✅ Comparison test entity 4 created: Car HPBKD
  ✅ Complex test entity created: Car KSVMD (ID: temp-1751670430801-ojc88p2ci) in 2381ms
✅ Comparison test entity 4 created: Car KSVMD
Creating comparison test entity 5/6: Eleph HPBKE
Creating entity for complex test: Eleph HPBKE
Creating comparison test entity 5/6: Eleph KSVME
Creating entity for complex test: Eleph KSVME
Dropdown failed, using direct input for: Ball VZATB
Successfully set input value: Ball VZATB
Creating entity: Eleph HPBKE (expectFailure: false)
Creating entity: Eleph KSVME (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph HPBKE" in UI with browser-specific timeout: 5000ms
Verifying entity "Eleph KSVME" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph KSVME" verified on first attempt
  ✓ Entity "Eleph HPBKE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph HPBKE" confirmed visible
    ✓ UI refresh complete and entity "Eleph KSVME" confirmed visible
Entity creation completed for "Eleph KSVME" in 1946ms
Entity creation completed for "Eleph HPBKE" in 1947ms
  ✅ Complex test entity created: Eleph KSVME (ID: temp-1751670433884-55jps1bad) in 2472ms
✅ Comparison test entity 5 created: Eleph KSVME
  ✅ Complex test entity created: Eleph HPBKE (ID: temp-1751670433884-1gn74gms4) in 2472ms
✅ Comparison test entity 5 created: Eleph HPBKE
Creating comparison test entity 6/6: Mouse KSVMF
Creating entity for complex test: Mouse KSVMF
Creating comparison test entity 6/6: Mouse HPBKF
Creating entity for complex test: Mouse HPBKF
Creating entity: Mouse KSVMF (expectFailure: false)
Creating entity: Mouse HPBKF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse KSVMF" in UI with browser-specific timeout: 5000ms
Verifying entity "Mouse HPBKF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse KSVMF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Mouse HPBKF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse HPBKF" confirmed visible
Entity creation completed for "Mouse HPBKF" in 1950ms
    ✓ UI refresh complete and entity "Mouse KSVMF" confirmed visible
Entity creation completed for "Mouse KSVMF" in 1953ms
  ✅ Complex test entity created: Mouse KSVMF (ID: temp-1751670436968-nqxo3yfjb) in 2477ms
✅ Comparison test entity 6 created: Mouse KSVMF
  ✅ Complex test entity created: Mouse HPBKF (ID: temp-1751670436968-z4dx7sb6i) in 2477ms
✅ Comparison test entity 6 created: Mouse HPBKF
Connection creation failed for Human VZATA → Ball VZATB, checking if it exists...
Creating test connections...
Creating test connections...
Creating connection: Human HPBKA → Ball HPBKB (10.0x)
Creating connection: Human KSVMA → Ball KSVMB (10.0x)
Selecting AutoComplete option: Human KSVMA
Selecting AutoComplete option: Human HPBKA
Successfully selected: Human HPBKA using dropdown
Successfully selected: Human KSVMA using dropdown
Successfully set input value: Human HPBKA
Successfully set input value: Human KSVMA
Selecting AutoComplete option: Ball HPBKB
Selecting AutoComplete option: Ball KSVMB
Successfully selected: Ball KSVMB using dropdown
Successfully selected: Ball HPBKB using dropdown
Successfully set input value: Ball HPBKB
Successfully set input value: Ball KSVMB
Connection creation completed for "Human KSVMA → Ball KSVMB" in 2617ms
  ℹ️  Tracking connection dependency: temp-1751670421737-j5y4c3tx0 → temp-1751670424729-vgt1rswj5 (auto-deleted via CASCADE)
Connection creation completed for "Human HPBKA → Ball HPBKB" in 2619ms
  ℹ️  Tracking connection dependency: temp-1751670421699-ecogygxs4 → temp-1751670424723-jaicbbb6g (auto-deleted via CASCADE)
Connection found: Human KSVMA → Ball KSVMB in Length (any multiplier)
Connection found: Human HPBKA → Ball HPBKB in Length (any multiplier)
Creating connection: Ball KSVMB → Build KSVMC (50.0x)
Creating connection: Ball HPBKB → Build HPBKC (50.0x)
Selecting AutoComplete option: Ball KSVMB
Selecting AutoComplete option: Ball HPBKB
Successfully selected: Ball KSVMB using dropdown
Successfully selected: Ball HPBKB using dropdown
Successfully set input value: Ball HPBKB
Successfully set input value: Ball KSVMB
Selecting AutoComplete option: Build KSVMC
Selecting AutoComplete option: Build HPBKC
Successfully selected: Build HPBKC using dropdown
Successfully selected: Build KSVMC using dropdown
Successfully set input value: Build HPBKC
Successfully set input value: Build KSVMC
Connection creation completed for "Ball KSVMB → Build KSVMC" in 2637ms
  ℹ️  Tracking connection dependency: temp-1751670424729-vgt1rswj5 → temp-1751670427814-9040s531z (auto-deleted via CASCADE)
Connection creation completed for "Ball HPBKB → Build HPBKC" in 2636ms
  ℹ️  Tracking connection dependency: temp-1751670424723-jaicbbb6g → temp-1751670427813-0lh8zakb5 (auto-deleted via CASCADE)
Connection not found: Human VZATA → Ball VZATB (10.0x) in Length
Connection list content: Connections (14)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Eleph VZATE (ID: 2597)
  ✓ Fallback deleted entity: Mouse VZATF (ID: 2600)
  ℹ️  Fallback cleanup handled 2 additional entities
🧹 Post-test cleanup complete in 23ms (Worker 14):
  • Entities deleted: 2 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 14
  • Test efficiency: 100% (23ms cleanup / 38746ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 14 (5ms)
Connection found: Ball KSVMB → Build KSVMC in Length (any multiplier)
Connection found: Ball HPBKB → Build HPBKC in Length (any multiplier)
[16/210] ❌ should validate non-existent entities (39.0s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.
  ✘  16 [chromium] › e2e/tests/comparisons.spec.ts:258:7 › Entity Comparisons and Pathfinding › should validate non-existent entities (39.0s)
Creating connection: Build KSVMC → Mouse KSVMF (0.1x)
Creating connection: Build HPBKC → Mouse HPBKF (0.1x)
[17/210] 🏃 Starting: should validate non-existent entities
🧹 Starting pre-test cleanup...
  Found 92 total entities in database (Worker 17)
  Identified 12 test entities to delete for Worker 17
  ✓ Deleted test entity: Ball HPBKB (ID: 2601)
  ✓ Deleted test entity: Human KSVMA (ID: 2599)
  ✓ Deleted test entity: Ball KSVMB (ID: 2602)
  ✓ Deleted test entity: Human HPBKA (ID: 2598)
  ✓ Deleted test entity: Build HPBKC (ID: 2603)
  ✓ Deleted test entity: Car KSVMD (ID: 2605)
  ✓ Deleted test entity: Eleph KSVME (ID: 2607)
  ✓ Deleted test entity: Build KSVMC (ID: 2604)
  ✓ Deleted test entity: Car HPBKD (ID: 2606)
  ✓ Deleted test entity: Eleph HPBKE (ID: 2608)
Selecting AutoComplete option: Build HPBKC
Selecting AutoComplete option: Build KSVMC
  ✓ Deleted test entity: Mouse KSVMF (ID: 2609)
  ✓ Deleted test entity: Mouse HPBKF (ID: 2610)
🧹 Pre-test cleanup complete in 162ms (Worker 17):
  • Initial entities: 92
  • Entities deleted: 12
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 12
Successfully selected: Build HPBKC using dropdown
Successfully selected: Build KSVMC using dropdown
Successfully set input value: Build KSVMC
Successfully set input value: Build HPBKC
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human TWSEA
Creating entity for complex test: Human TWSEA
Selecting AutoComplete option: Mouse KSVMF
Selecting AutoComplete option: Mouse HPBKF
Creating entity: Human TWSEA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Successfully selected: Mouse KSVMF using dropdown
Successfully selected: Mouse HPBKF using dropdown
Successfully set input value: Mouse KSVMF
Successfully set input value: Mouse HPBKF
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Build KSVMC → Mouse KSVMF, checking if it exists...
Connection creation failed for Build HPBKC → Mouse HPBKF, checking if it exists...
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human TWSEA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human TWSEA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human TWSEA" confirmed visible
Entity creation completed for "Human TWSEA" in 1988ms
  ✅ Complex test entity created: Human TWSEA (ID: temp-1751670448964-gsszyhwkz) in 2512ms
✅ Comparison test entity 1 created: Human TWSEA
Creating comparison test entity 2/6: Ball TWSEB
Creating entity for complex test: Ball TWSEB
Creating entity: Ball TWSEB (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball TWSEB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball TWSEB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball TWSEB" confirmed visible
Entity creation completed for "Ball TWSEB" in 1897ms
  ✅ Complex test entity created: Ball TWSEB (ID: temp-1751670452010-yrevziob2) in 2436ms
✅ Comparison test entity 2 created: Ball TWSEB
Creating comparison test entity 3/6: Build TWSEC
Creating entity for complex test: Build TWSEC
Creating entity: Build TWSEC (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build TWSEC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build TWSEC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build TWSEC" confirmed visible
Entity creation completed for "Build TWSEC" in 1937ms
  ✅ Complex test entity created: Build TWSEC (ID: temp-1751670455075-5td05t5dg) in 2460ms
✅ Comparison test entity 3 created: Build TWSEC
Connection not found: Build HPBKC → Mouse HPBKF (0.1x) in Length
Connection not found: Build KSVMC → Mouse KSVMF (0.1x) in Length
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 20ms (Worker 16):
  • Entities deleted: 0 (+ cascaded connections)
🧹 Post-test cleanup complete in 20ms (Worker 15):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 15
  • Test efficiency: 100% (20ms cleanup / 36985ms total)
  ✅ All cleanup operations successful
  • Failed operations: 0
  • Worker isolation: 16
  • Test efficiency: 100% (20ms cleanup / 36985ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 15 (7ms)
  ✅ Cleanup verification: No test entities remain from Worker 16 (7ms)
Creating comparison test entity 4/6: Car TWSED
Creating entity for complex test: Car TWSED
Creating entity: Car TWSED (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
[17/210] ❌ should handle custom from count values (37.3s)
   └─ Error: Error: Connection creation failed for "Build HPBKC → Mouse HPBKF": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  18 [chromium] › e2e/tests/comparisons.spec.ts:236:7 › Entity Comparisons and Pathfinding › should handle custom from count values (retry #1) (37.3s)
[18/210] ❌ should validate entity selection (37.3s)
   └─ Error: Error: Connection creation failed for "Build KSVMC → Mouse KSVMF": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  17 [chromium] › e2e/tests/comparisons.spec.ts:250:7 › Entity Comparisons and Pathfinding › should validate entity selection (retry #1) (37.3s)
[19/210] 🏃 Starting: should handle decimal precision in results
[19/210] 🏃 Starting: should clear form after successful comparison
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 83 total entities in database (Worker 18)
  Identified 3 test entities to delete for Worker 18
  Found 83 total entities in database (Worker 19)
  Identified 3 test entities to delete for Worker 19
  ✓ Deleted test entity: Ball TWSEB (ID: 2612)
  ✓ Deleted test entity: Ball TWSEB (ID: 2612)
  ✓ Deleted test entity: Build TWSEC (ID: 2613)
  ✓ Deleted test entity: Human TWSEA (ID: 2611)
  ✓ Deleted test entity: Build TWSEC (ID: 2613)
  ✓ Deleted test entity: Human TWSEA (ID: 2611)
🧹 Pre-test cleanup complete in 36ms (Worker 19):
  • Initial entities: 83
  • Entities deleted: 3
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 3
🧹 Pre-test cleanup complete in 37ms (Worker 18):
  • Initial entities: 83
  • Entities deleted: 3
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 3
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human BRXQA
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human IBPTA
Creating entity for complex test: Human BRXQA
Creating entity for complex test: Human IBPTA
Verifying entity "Car TWSED" in UI with browser-specific timeout: 5000ms
Creating entity: Human IBPTA (expectFailure: false)
Creating entity: Human BRXQA (expectFailure: false)
  ✓ Entity "Car TWSED" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
    ✓ UI refresh complete and entity "Car TWSED" confirmed visible
Entity creation completed for "Car TWSED" in 1810ms
  ✅ Complex test entity created: Car TWSED (ID: temp-1751670458004-78vkptmv9) in 2324ms
✅ Comparison test entity 4 created: Car TWSED
Creating comparison test entity 5/6: Eleph TWSEE
Creating entity for complex test: Eleph TWSEE
Creating entity: Eleph TWSEE (expectFailure: false)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human BRXQA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human BRXQA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Verifying entity "Human IBPTA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human IBPTA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human BRXQA" confirmed visible
Entity creation completed for "Human BRXQA" in 2014ms
    ✓ UI refresh complete and entity "Human IBPTA" confirmed visible
Entity creation completed for "Human IBPTA" in 2024ms
  ✅ Complex test entity created: Human BRXQA (ID: temp-1751670459813-f9wth4ipi) in 2540ms
✅ Comparison test entity 1 created: Human BRXQA
  ✅ Complex test entity created: Human IBPTA (ID: temp-1751670459820-rz0jod7fy) in 2547ms
✅ Comparison test entity 1 created: Human IBPTA
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating comparison test entity 2/6: Ball BRXQB
Creating entity for complex test: Ball BRXQB
Creating comparison test entity 2/6: Ball IBPTB
Creating entity for complex test: Ball IBPTB
Verifying entity "Eleph TWSEE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph TWSEE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating entity: Ball BRXQB (expectFailure: false)
Creating entity: Ball IBPTB (expectFailure: false)
    ✓ UI refresh complete and entity "Eleph TWSEE" confirmed visible
Entity creation completed for "Eleph TWSEE" in 1935ms
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Eleph TWSEE (ID: temp-1751670461063-hfcmw4yut) in 2454ms
✅ Comparison test entity 5 created: Eleph TWSEE
Creating comparison test entity 6/6: Mouse TWSEF
Creating entity for complex test: Mouse TWSEF
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Creating entity: Mouse TWSEF (expectFailure: false)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Verifying entity "Ball IBPTB" in UI with browser-specific timeout: 5000ms
Verifying entity "Ball BRXQB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball IBPTB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Ball BRXQB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball IBPTB" confirmed visible
Entity creation completed for "Ball IBPTB" in 1876ms
    ✓ UI refresh complete and entity "Ball BRXQB" confirmed visible
Entity creation completed for "Ball BRXQB" in 1881ms
  ✅ Complex test entity created: Ball IBPTB (ID: temp-1751670462828-j02gne1oz) in 2398ms
✅ Comparison test entity 2 created: Ball IBPTB
  ✅ Complex test entity created: Ball BRXQB (ID: temp-1751670462831-ra87kqptq) in 2406ms
✅ Comparison test entity 2 created: Ball BRXQB
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating comparison test entity 3/6: Build BRXQC
Creating entity for complex test: Build BRXQC
Creating comparison test entity 3/6: Build IBPTC
Creating entity for complex test: Build IBPTC
Verifying entity "Mouse TWSEF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse TWSEF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating entity: Build BRXQC (expectFailure: false)
Creating entity: Build IBPTC (expectFailure: false)
    ✓ UI refresh complete and entity "Mouse TWSEF" confirmed visible
Entity creation completed for "Mouse TWSEF" in 1912ms
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Mouse TWSEF (ID: temp-1751670464095-nrm5gbgh5) in 2429ms
✅ Comparison test entity 6 created: Mouse TWSEF
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating test connections...
Creating connection: Human TWSEA → Ball TWSEB (10.0x)
Verifying entity "Build BRXQC" in UI with browser-specific timeout: 5000ms
Verifying entity "Build IBPTC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build IBPTC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Build BRXQC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build BRXQC" confirmed visible
Entity creation completed for "Build BRXQC" in 1941ms
    ✓ UI refresh complete and entity "Build IBPTC" confirmed visible
Entity creation completed for "Build IBPTC" in 1939ms
Selecting AutoComplete option: Human TWSEA
  ✅ Complex test entity created: Build BRXQC (ID: temp-1751670465907-j5wxaqbzp) in 2466ms
✅ Comparison test entity 3 created: Build BRXQC
  ✅ Complex test entity created: Build IBPTC (ID: temp-1751670465907-a5cs99yoy) in 2463ms
✅ Comparison test entity 3 created: Build IBPTC
Creating comparison test entity 4/6: Car BRXQD
Creating entity for complex test: Car BRXQD
Creating comparison test entity 4/6: Car IBPTD
Creating entity for complex test: Car IBPTD
Creating entity: Car BRXQD (expectFailure: false)
Creating entity: Car IBPTD (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Dropdown failed, using direct input for: Human TWSEA
Successfully set input value: Human TWSEA
Verifying entity "Car IBPTD" in UI with browser-specific timeout: 5000ms
Verifying entity "Car BRXQD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car BRXQD" verified on first attempt
  ✓ Entity "Car IBPTD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Selecting AutoComplete option: Ball TWSEB
    ✓ UI refresh complete and entity "Car IBPTD" confirmed visible
Entity creation completed for "Car IBPTD" in 1803ms
    ✓ UI refresh complete and entity "Car BRXQD" confirmed visible
Entity creation completed for "Car BRXQD" in 1805ms
  ✅ Complex test entity created: Car IBPTD (ID: temp-1751670468839-6bz8f3y87) in 2328ms
✅ Comparison test entity 4 created: Car IBPTD
  ✅ Complex test entity created: Car BRXQD (ID: temp-1751670468842-i1ug97vnp) in 2331ms
✅ Comparison test entity 4 created: Car BRXQD
Creating comparison test entity 5/6: Eleph IBPTE
Creating entity for complex test: Eleph IBPTE
Creating comparison test entity 5/6: Eleph BRXQE
Creating entity for complex test: Eleph BRXQE
Creating entity: Eleph IBPTE (expectFailure: false)
Creating entity: Eleph BRXQE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Dropdown failed, using direct input for: Ball TWSEB
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Successfully set input value: Ball TWSEB
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph IBPTE" in UI with browser-specific timeout: 5000ms
Verifying entity "Eleph BRXQE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph IBPTE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Eleph BRXQE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph IBPTE" confirmed visible
Entity creation completed for "Eleph IBPTE" in 1934ms
    ✓ UI refresh complete and entity "Eleph BRXQE" confirmed visible
Entity creation completed for "Eleph BRXQE" in 1935ms
  ✅ Complex test entity created: Eleph IBPTE (ID: temp-1751670471909-5zgz0ttij) in 2460ms
✅ Comparison test entity 5 created: Eleph IBPTE
  ✅ Complex test entity created: Eleph BRXQE (ID: temp-1751670471910-n6tnmcivr) in 2459ms
✅ Comparison test entity 5 created: Eleph BRXQE
Creating comparison test entity 6/6: Mouse BRXQF
Creating entity for complex test: Mouse BRXQF
Creating comparison test entity 6/6: Mouse IBPTF
Creating entity for complex test: Mouse IBPTF
Creating entity: Mouse BRXQF (expectFailure: false)
Creating entity: Mouse IBPTF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse BRXQF" in UI with browser-specific timeout: 5000ms
Verifying entity "Mouse IBPTF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse IBPTF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Mouse BRXQF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse BRXQF" confirmed visible
Entity creation completed for "Mouse BRXQF" in 1906ms
    ✓ UI refresh complete and entity "Mouse IBPTF" confirmed visible
Entity creation completed for "Mouse IBPTF" in 1905ms
  ✅ Complex test entity created: Mouse BRXQF (ID: temp-1751670474946-seh7yygdw) in 2429ms
✅ Comparison test entity 6 created: Mouse BRXQF
  ✅ Complex test entity created: Mouse IBPTF (ID: temp-1751670474946-f59aaxevd) in 2428ms
✅ Comparison test entity 6 created: Mouse IBPTF
Creating test connections...
Creating connection: Human IBPTA → Ball IBPTB (10.0x)
Creating test connections...
Creating connection: Human BRXQA → Ball BRXQB (10.0x)
Selecting AutoComplete option: Human IBPTA
Selecting AutoComplete option: Human BRXQA
Connection creation failed for Human TWSEA → Ball TWSEB, checking if it exists...
Successfully selected: Human IBPTA using dropdown
Successfully selected: Human BRXQA using dropdown
Successfully set input value: Human IBPTA
Successfully set input value: Human BRXQA
Selecting AutoComplete option: Ball IBPTB
Selecting AutoComplete option: Ball BRXQB
Successfully selected: Ball IBPTB using dropdown
Successfully selected: Ball BRXQB using dropdown
Successfully set input value: Ball IBPTB
Successfully set input value: Ball BRXQB
Connection creation completed for "Human IBPTA → Ball IBPTB" in 2610ms
  ℹ️  Tracking connection dependency: temp-1751670459820-rz0jod7fy → temp-1751670462828-j02gne1oz (auto-deleted via CASCADE)
Connection creation completed for "Human BRXQA → Ball BRXQB" in 2594ms
  ℹ️  Tracking connection dependency: temp-1751670459813-f9wth4ipi → temp-1751670462831-ra87kqptq (auto-deleted via CASCADE)
Connection found: Human IBPTA → Ball IBPTB in Length (any multiplier)
Connection found: Human BRXQA → Ball BRXQB in Length (any multiplier)
Creating connection: Ball IBPTB → Build IBPTC (50.0x)
Creating connection: Ball BRXQB → Build BRXQC (50.0x)
Selecting AutoComplete option: Ball IBPTB
Selecting AutoComplete option: Ball BRXQB
Successfully selected: Ball BRXQB using dropdown
Successfully selected: Ball IBPTB using dropdown
Successfully set input value: Ball BRXQB
Successfully set input value: Ball IBPTB
Selecting AutoComplete option: Build BRXQC
Selecting AutoComplete option: Build IBPTC
Successfully selected: Build BRXQC using dropdown
Successfully selected: Build IBPTC using dropdown
Successfully set input value: Build BRXQC
Successfully set input value: Build IBPTC
Connection creation completed for "Ball BRXQB → Build BRXQC" in 2556ms
  ℹ️  Tracking connection dependency: temp-1751670462831-ra87kqptq → temp-1751670465907-j5wxaqbzp (auto-deleted via CASCADE)
Connection creation completed for "Ball IBPTB → Build IBPTC" in 2602ms
  ℹ️  Tracking connection dependency: temp-1751670462828-j02gne1oz → temp-1751670465907-a5cs99yoy (auto-deleted via CASCADE)
Connection found: Ball BRXQB → Build BRXQC in Length (any multiplier)
Connection found: Ball IBPTB → Build IBPTC in Length (any multiplier)
Creating connection: Build BRXQC → Mouse BRXQF (0.1x)
Creating connection: Build IBPTC → Mouse IBPTF (0.1x)
Selecting AutoComplete option: Build BRXQC
Selecting AutoComplete option: Build IBPTC
Successfully selected: Build IBPTC using dropdown
Successfully selected: Build BRXQC using dropdown
Connection not found: Human TWSEA → Ball TWSEB (10.0x) in Length
Connection list content: Connections (14)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
  ✓ Fallback deleted entity: Car TWSED (ID: 2614)
  ✓ Fallback deleted entity: Eleph TWSEE (ID: 2617)
  ✓ Fallback deleted entity: Mouse TWSEF (ID: 2620)
  ℹ️  Fallback cleanup handled 3 additional entities
🧹 Post-test cleanup complete in 26ms (Worker 17):
  • Entities deleted: 3 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 17
  • Test efficiency: 100% (26ms cleanup / 38625ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 17 (7ms)
Successfully set input value: Build IBPTC
Successfully set input value: Build BRXQC
Selecting AutoComplete option: Mouse BRXQF
Selecting AutoComplete option: Mouse IBPTF
[19/210] ❌ should validate non-existent entities (38.9s)
   └─ Error: TimeoutError: page.waitForFunction: Timeout 5000ms exceeded.
  ✘  19 [chromium] › e2e/tests/comparisons.spec.ts:258:7 › Entity Comparisons and Pathfinding › should validate non-existent entities (retry #1) (38.9s)
Successfully selected: Mouse BRXQF using dropdown
Successfully selected: Mouse IBPTF using dropdown
[20/210] 🏃 Starting: should handle rapid successive comparisons
Successfully set input value: Mouse BRXQF
Successfully set input value: Mouse IBPTF
🧹 Starting pre-test cleanup...
  Found 92 total entities in database (Worker 20)
  Identified 12 test entities to delete for Worker 20
  ✓ Deleted test entity: Ball BRXQB (ID: 2618)
  ✓ Deleted test entity: Eleph IBPTE (ID: 2626)
  ✓ Deleted test entity: Car BRXQD (ID: 2623)
  ✓ Deleted test entity: Human IBPTA (ID: 2615)
  ✓ Deleted test entity: Eleph BRXQE (ID: 2625)
  ✓ Deleted test entity: Build BRXQC (ID: 2621)
  ✓ Deleted test entity: Ball IBPTB (ID: 2619)
  ✓ Deleted test entity: Car IBPTD (ID: 2624)
  ✓ Deleted test entity: Build IBPTC (ID: 2622)
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Build BRXQC → Mouse BRXQF, checking if it exists...
Connection creation error: Connection creation API failed: 404 - {"detail":"From entity not found"}
Connection creation failed for Build IBPTC → Mouse IBPTF, checking if it exists...
  ✓ Deleted test entity: Human BRXQA (ID: 2616)
  ✓ Deleted test entity: Mouse BRXQF (ID: 2627)
  ✓ Deleted test entity: Mouse IBPTF (ID: 2628)
🧹 Pre-test cleanup complete in 5184ms (Worker 20):
  • Initial entities: 92
  • Entities deleted: 12
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 12
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human XPKWA
Creating entity for complex test: Human XPKWA
Creating entity: Human XPKWA (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Human XPKWA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human XPKWA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Human XPKWA" confirmed visible
Entity creation completed for "Human XPKWA" in 2000ms
Connection not found: Build IBPTC → Mouse IBPTF (0.1x) in Length
Connection not found: Build BRXQC → Mouse BRXQF (0.1x) in Length
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
Connection list content: Connections (10)Ais10.0xBin CountID: 37Unit: #EditDeleteBis0.1xAin CountID: 38Unit: #EditDeleteAis8.5xBin LengthID: 39Unit: mEditDeleteBis0.1xAin LengthID: 40Unit: mEditDeleteAis5.0xBin VolumeID: 41Un...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
🧹 Starting post-test cleanup...
  Fallback cleanup for 6 named entities...
🧹 Post-test cleanup complete in 19ms (Worker 19):
🧹 Post-test cleanup complete in 18ms (Worker 18):
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 18
  • Test efficiency: 100% (18ms cleanup / 36911ms total)
  ✅ All cleanup operations successful
  • Entities deleted: 0 (+ cascaded connections)
  • Failed operations: 0
  • Worker isolation: 19
  • Test efficiency: 100% (19ms cleanup / 36911ms total)
  ✅ All cleanup operations successful
  ✅ Cleanup verification: No test entities remain from Worker 19 (5ms)
  ✅ Cleanup verification: No test entities remain from Worker 18 (5ms)
  ✅ Complex test entity created: Human XPKWA (ID: temp-1751670493510-7votbmw8y) in 2527ms
✅ Comparison test entity 1 created: Human XPKWA
[20/210] ❌ should clear form after successful comparison (37.3s)

📈 Progress: 20/210 (10%)
   ✅ 1 passed | ❌ 19 failed | ⏭️ 0 skipped
   ⏱️  Elapsed: 4.7min | Remaining: ~124.5min

   └─ Error: Error: Connection creation failed for "Build IBPTC → Mouse IBPTF": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  21 [chromium] › e2e/tests/comparisons.spec.ts:288:7 › Entity Comparisons and Pathfinding › should clear form after successful comparison (37.3s)
[21/210] ❌ should handle decimal precision in results (37.3s)
   └─ Error: Error: Connection creation failed for "Build BRXQC → Mouse BRXQF": Connection creation API failed: 404 - {"detail":"From entity not found"}
  ✘  20 [chromium] › e2e/tests/comparisons.spec.ts:267:7 › Entity Comparisons and Pathfinding › should handle decimal precision in results (37.3s)
Creating comparison test entity 2/6: Ball XPKWB
Creating entity for complex test: Ball XPKWB
Creating entity: Ball XPKWB (expectFailure: false)
[22/210] 🏃 Starting: should clear form after successful comparison
[22/210] 🏃 Starting: should handle decimal precision in results
Create button clicked successfully with standard click (attempt 1)
🧹 Starting pre-test cleanup...
🧹 Starting pre-test cleanup...
  Found 81 total entities in database (Worker 22)
  Identified 1 test entities to delete for Worker 22
  Found 81 total entities in database (Worker 21)
  Identified 1 test entities to delete for Worker 21
  ✓ Deleted test entity: Human XPKWA (ID: 2629)
  ✓ Deleted test entity: Human XPKWA (ID: 2629)
🧹 Pre-test cleanup complete in 37ms (Worker 22):
  • Initial entities: 81
  • Entities deleted: 1
  • Failed deletions: 0
  • Final entity count: 80
🧹 Pre-test cleanup complete in 37ms (Worker 21):
  • Initial entities: 81
  • Entities deleted: 1
  • Failed deletions: 0
  • Final entity count: 80
  • Net reduction: 1
  • Net reduction: 1
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human FKDVA
Creating entity for complex test: Human FKDVA
Creating test entities for comparison tests...
Creating comparison test entity 1/6: Human BSVEA
Creating entity for complex test: Human BSVEA
Creating entity: Human FKDVA (expectFailure: false)
Creating entity: Human BSVEA (expectFailure: false)
Submit button is ready for interaction (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Ball XPKWB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball XPKWB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Ball XPKWB" confirmed visible
Entity creation completed for "Ball XPKWB" in 1854ms
  ✅ Complex test entity created: Ball XPKWB (ID: temp-1751670496482-3ovz5er44) in 2368ms
✅ Comparison test entity 2 created: Ball XPKWB
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating comparison test entity 3/6: Build XPKWC
Creating entity for complex test: Build XPKWC
Verifying entity "Human BSVEA" in UI with browser-specific timeout: 5000ms
Verifying entity "Human FKDVA" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Human BSVEA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Human FKDVA" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating entity: Build XPKWC (expectFailure: false)
    ✓ UI refresh complete and entity "Human BSVEA" confirmed visible
Entity creation completed for "Human BSVEA" in 1979ms
    ✓ UI refresh complete and entity "Human FKDVA" confirmed visible
Entity creation completed for "Human FKDVA" in 1985ms
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Human BSVEA (ID: temp-1751670497713-7r7gg0ae0) in 2498ms
✅ Comparison test entity 1 created: Human BSVEA
  ✅ Complex test entity created: Human FKDVA (ID: temp-1751670497718-mhpnf85i7) in 2503ms
✅ Comparison test entity 1 created: Human FKDVA
Creating comparison test entity 2/6: Ball BSVEB
Creating entity for complex test: Ball BSVEB
Creating comparison test entity 2/6: Ball FKDVB
Creating entity for complex test: Ball FKDVB
Creating entity: Ball BSVEB (expectFailure: false)
Creating entity: Ball FKDVB (expectFailure: false)
Submit button is ready for interaction (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Build XPKWC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build XPKWC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Build XPKWC" confirmed visible
Entity creation completed for "Build XPKWC" in 1917ms
  ✅ Complex test entity created: Build XPKWC (ID: temp-1751670499529-ggarg4m4e) in 2442ms
✅ Comparison test entity 3 created: Build XPKWC
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating comparison test entity 4/6: Car XPKWD
Creating entity for complex test: Car XPKWD
Verifying entity "Ball FKDVB" in UI with browser-specific timeout: 5000ms
Verifying entity "Ball BSVEB" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Ball FKDVB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Ball BSVEB" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating entity: Car XPKWD (expectFailure: false)
    ✓ UI refresh complete and entity "Ball FKDVB" confirmed visible
Entity creation completed for "Ball FKDVB" in 1884ms
    ✓ UI refresh complete and entity "Ball BSVEB" confirmed visible
Entity creation completed for "Ball BSVEB" in 1889ms
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Ball FKDVB (ID: temp-1751670500731-p08ns83ja) in 2408ms
✅ Comparison test entity 2 created: Ball FKDVB
  ✅ Complex test entity created: Ball BSVEB (ID: temp-1751670500733-h9mmdsse4) in 2414ms
✅ Comparison test entity 2 created: Ball BSVEB
Creating comparison test entity 3/6: Build FKDVC
Creating entity for complex test: Build FKDVC
Creating comparison test entity 3/6: Build BSVEC
Creating entity for complex test: Build BSVEC
Submit button is ready for interaction (attempt 1)
Creating entity: Build BSVEC (expectFailure: false)
Creating entity: Build FKDVC (expectFailure: false)
Submit button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Verifying entity "Car XPKWD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car XPKWD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Car XPKWD" confirmed visible
Entity creation completed for "Car XPKWD" in 1827ms
  ✅ Complex test entity created: Car XPKWD (ID: temp-1751670502489-yaoeaax2y) in 2355ms
✅ Comparison test entity 4 created: Car XPKWD
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating comparison test entity 5/6: Eleph XPKWE
Creating entity for complex test: Eleph XPKWE
Verifying entity "Build FKDVC" in UI with browser-specific timeout: 5000ms
Verifying entity "Build BSVEC" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Build FKDVC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Build BSVEC" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating entity: Eleph XPKWE (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
    ✓ UI refresh complete and entity "Build FKDVC" confirmed visible
Entity creation completed for "Build FKDVC" in 1938ms
    ✓ UI refresh complete and entity "Build BSVEC" confirmed visible
Entity creation completed for "Build BSVEC" in 1942ms
  ✅ Complex test entity created: Build FKDVC (ID: temp-1751670503796-nzcyp9b1z) in 2461ms
✅ Comparison test entity 3 created: Build FKDVC
  ✅ Complex test entity created: Build BSVEC (ID: temp-1751670503796-124rli5id) in 2459ms
✅ Comparison test entity 3 created: Build BSVEC
Creating comparison test entity 4/6: Car BSVED
Creating entity for complex test: Car BSVED
Creating comparison test entity 4/6: Car FKDVD
Creating entity for complex test: Car FKDVD
Submit button is ready for interaction (attempt 1)
Creating entity: Car BSVED (expectFailure: false)
Creating entity: Car FKDVD (expectFailure: false)
Submit button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph XPKWE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph XPKWE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph XPKWE" confirmed visible
Entity creation completed for "Eleph XPKWE" in 1922ms
  ✅ Complex test entity created: Eleph XPKWE (ID: temp-1751670505536-bw4sza8xx) in 2441ms
✅ Comparison test entity 5 created: Eleph XPKWE
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Creating comparison test entity 6/6: Mouse XPKWF
Creating entity for complex test: Mouse XPKWF
Verifying entity "Car BSVED" in UI with browser-specific timeout: 5000ms
Verifying entity "Car FKDVD" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Car BSVED" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Car FKDVD" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
Creating entity: Mouse XPKWF (expectFailure: false)
    ✓ UI refresh complete and entity "Car FKDVD" confirmed visible
Entity creation completed for "Car FKDVD" in 1812ms
    ✓ UI refresh complete and entity "Car BSVED" confirmed visible
Entity creation completed for "Car BSVED" in 1814ms
Create button clicked successfully with standard click (attempt 1)
  ✅ Complex test entity created: Car FKDVD (ID: temp-1751670506733-iestjyni6) in 2334ms
✅ Comparison test entity 4 created: Car FKDVD
  ✅ Complex test entity created: Car BSVED (ID: temp-1751670506735-fy57gb4jz) in 2336ms
✅ Comparison test entity 4 created: Car BSVED
Creating comparison test entity 5/6: Eleph FKDVE
Creating entity for complex test: Eleph FKDVE
Creating comparison test entity 5/6: Eleph BSVEE
Creating entity for complex test: Eleph BSVEE
Creating entity: Eleph FKDVE (expectFailure: false)
Creating entity: Eleph BSVEE (expectFailure: false)
Submit button is ready for interaction (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Mouse XPKWF" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Mouse XPKWF" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Mouse XPKWF" confirmed visible
Entity creation completed for "Mouse XPKWF" in 1909ms
  ✅ Complex test entity created: Mouse XPKWF (ID: temp-1751670508590-tp0zx33sx) in 2445ms
✅ Comparison test entity 6 created: Mouse XPKWF
Submit button is ready for interaction (attempt 1)
Submit button is ready for interaction (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Submit button clicked successfully with standard click (attempt 1)
Verifying entity "Eleph BSVEE" in UI with browser-specific timeout: 5000ms
Verifying entity "Eleph FKDVE" in UI with browser-specific timeout: 5000ms
  ✓ Entity "Eleph FKDVE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
  ✓ Entity "Eleph BSVEE" verified on first attempt
    ⏱️  Ensuring UI refresh complete for chromium (200ms delay)
    ✓ UI refresh complete and entity "Eleph FKDVE" confirmed visible
Entity creation completed for "Eleph FKDVE" in 1931ms
    ✓ UI refresh complete and entity "Eleph BSVEE" confirmed visible
Entity creation completed for "Eleph BSVEE" in 1929ms
Creating test connections...
Creating connection: Human XPKWA → Ball XPKWB (10.0x)
  ✅ Complex test entity created: Eleph FKDVE (ID: temp-1751670509797-27dr9l0u6) in 2458ms
✅ Comparison test entity 5 created: Eleph FKDVE
  ✅ Complex test entity created: Eleph BSVEE (ID: temp-1751670509798-m5oqsavav) in 2457ms
✅ Comparison test entity 5 created: Eleph BSVEE
Selecting AutoComplete option: Human XPKWA
Creating comparison test entity 6/6: Mouse FKDVF
Creating entity for complex test: Mouse FKDVF
Creating comparison test entity 6/6: Mouse BSVEF
Creating entity for complex test: Mouse BSVEF
Creating entity: Mouse FKDVF (expectFailure: false)
Creating entity: Mouse BSVEF (expectFailure: false)
Create button clicked successfully with standard click (attempt 1)
Create button clicked successfully with standard click (attempt 1)
