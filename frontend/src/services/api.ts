import axios, { AxiosResponse } from 'axios';
import {
  Entity,
  Unit,
  Connection,
  ComparisonResult,
  CreateEntityRequest,
  CreateUnitRequest,
  CreateConnectionRequest,
  UpdateConnectionRequest,
  HealthResponse
} from '../types/api';

const BASE_URL = process.env.REACT_APP_API_URL 
  ? `${process.env.REACT_APP_API_URL}/api/v1`
  : '/api/v1';

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.config.method?.toUpperCase(), response.config.url, response.status, response.data);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.config?.method?.toUpperCase(), error.config?.url, error.response?.status, error.response?.data);
    
    // Add network error handling
    if (!error.response && error.code === 'ERR_NETWORK') {
      error.code = 'NETWORK_ERROR';
      error.message = 'Network connection failed';
    }
    
    return Promise.reject(error);
  }
);

const apiService = {
  // Health check
  async health(): Promise<HealthResponse> {
    const response: AxiosResponse<HealthResponse> = await api.get('/health');
    return response.data;
  },

  // Entity operations
  async getEntities(): Promise<Entity[]> {
    const response: AxiosResponse<Entity[]> = await api.get('/entities/');
    return response.data;
  },

  async getEntity(id: number): Promise<Entity> {
    const response: AxiosResponse<Entity> = await api.get(`/entities/${id}`);
    return response.data;
  },

  async createEntity(entity: CreateEntityRequest): Promise<Entity> {
    const response: AxiosResponse<Entity> = await api.post('/entities/', entity);
    return response.data;
  },

  async updateEntity(id: number, entity: CreateEntityRequest): Promise<Entity> {
    const response: AxiosResponse<Entity> = await api.put(`/entities/${id}`, entity);
    return response.data;
  },

  async deleteEntity(id: number): Promise<void> {
    await api.delete(`/entities/${id}`);
  },

  // Unit operations
  async getUnits(): Promise<Unit[]> {
    const response: AxiosResponse<Unit[]> = await api.get('/units/');
    return response.data;
  },

  async getUnit(id: number): Promise<Unit> {
    const response: AxiosResponse<Unit> = await api.get(`/units/${id}`);
    return response.data;
  },

  async createUnit(unit: CreateUnitRequest): Promise<Unit> {
    const response: AxiosResponse<Unit> = await api.post('/units/', unit);
    return response.data;
  },

  // Connection operations
  async getConnections(): Promise<Connection[]> {
    const response: AxiosResponse<Connection[]> = await api.get('/connections/');
    return response.data;
  },

  async getConnection(id: number): Promise<Connection> {
    const response: AxiosResponse<Connection> = await api.get(`/connections/${id}`);
    return response.data;
  },

  async createConnection(connection: CreateConnectionRequest): Promise<Connection> {
    const response: AxiosResponse<Connection> = await api.post('/connections/', connection);
    return response.data;
  },

  async updateConnection(id: number, connection: UpdateConnectionRequest): Promise<Connection> {
    const response: AxiosResponse<Connection> = await api.put(`/connections/${id}`, connection);
    return response.data;
  },

  async deleteConnection(id: number): Promise<void> {
    await api.delete(`/connections/${id}`);
  },

  // Comparison operations
  async compare(fromEntityId: number, toEntityId: number, unitId: number): Promise<ComparisonResult> {
    const response: AxiosResponse<ComparisonResult> = await api.get('/compare/', {
      params: {
        from: fromEntityId,
        to: toEntityId,
        unit: unitId
      }
    });
    return response.data;
  }
};

export default apiService;