/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Navigation */
.navigation {
  background-color: #282c34;
  padding: 1rem 2rem;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-brand .brand-link {
  color: white;
  text-decoration: none;
}

.nav-brand h1 {
  font-size: 1.8rem;
  margin-bottom: 0.2rem;
}

.nav-brand p {
  font-size: 0.9rem;
  color: #61dafb;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  background-color: #61dafb;
  color: #282c34;
}

/* Main content */
.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Manager headers */
.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e0e0e0;
}

.manager-header h2 {
  color: #333;
  font-size: 1.8rem;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-select, .btn-edit, .btn-delete {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-select {
  background-color: #28a745;
  color: white;
}

.btn-edit {
  background-color: #ffc107;
  color: #212529;
}

.btn-delete {
  background-color: #dc3545;
  color: white;
}

.btn-delete:hover:not(:disabled) {
  background-color: #c82333;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Cards and grids */
.entity-grid, .connections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.entity-card, .connection-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.entity-card h4 {
  margin-bottom: 0.5rem;
  color: #333;
}

.entity-id {
  color: #666;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.entity-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.connection-relationship {
  margin-bottom: 1rem;
  font-size: 1.1rem;
  line-height: 1.4;
}

.entity-name {
  font-weight: bold;
  color: #007bff;
}

.multiplier {
  font-weight: bold;
  color: #28a745;
  font-size: 1.2em;
}

.relationship-text, .times-text {
  color: #666;
  margin: 0 0.3rem;
}

.unit-text {
  color: #6c757d;
  font-style: italic;
}

.connection-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: #666;
}

/* Forms */
.entity-form, .connection-form, .comparison-form {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

/* Form validation states */
.form-input {
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input.valid {
  border-color: #28a745;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
}

.form-input.invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.validation-success {
  color: #28a745;
  font-weight: 500;
  margin-left: 0.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input, .form-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus, .form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-help {
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-size: 0.8rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.form-note {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

/* Previews */
.connection-preview, .comparison-preview {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  border-left: 4px solid #28a745;
}

/* Results */
.comparison-result {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
  margin-top: 2rem;
}

.main-result {
  text-align: center;
  margin-bottom: 2rem;
}

.result-statement {
  font-size: 1.5rem;
  line-height: 1.6;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #28a745;
}

/* Template-style result statement */
.result-statement-template {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 3rem;
  font-weight: 300;
  line-height: 1.5;
  padding: 3rem 2rem;
  text-align: center;
  letter-spacing: 0.02em;
  margin-bottom: 2rem;
}

.template-prefix {
  color: #d2691e; /* Orange/brown color */
  margin-right: 0.3rem;
}

.template-from-count {
  color: #333;
  font-weight: 400;
  border-bottom: 2px solid #333;
  padding: 0.1rem 0.3rem;
  margin: 0 0.4rem;
}

.template-from-entity {
  color: #008B8B; /* Teal */
  font-weight: 400;
  border-bottom: 2px solid #333;
  padding: 0.1rem 0.3rem;
  margin: 0 0.4rem;
}

.template-relationship {
  color: #333;
  font-weight: 400;
  margin: 0 0.4rem;
}

.template-multiplier {
  color: #333;
  font-weight: 400;
  border-bottom: 2px solid #333;
  padding: 0.1rem 0.3rem;
  margin: 0 0.4rem;
}

.template-to-entity {
  color: #808080; /* Gray */
  font-weight: 400;
  border-bottom: 2px solid #333;
  padding: 0.1rem 0.3rem;
  margin: 0 0.4rem;
}

.template-suffix {
  color: #D2691E; /* Orange color */
  font-weight: 400;
  font-size: 3.2rem;
  margin-left: 0.4rem;
}

.calculation-path {
  margin-bottom: 2rem;
}

.calculation-path h4 {
  margin-bottom: 1rem;
  color: #333;
}

.path-steps {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.path-step {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.step-number {
  background-color: #007bff;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-arrow {
  text-align: center;
  color: #666;
  margin: 0.5rem 0;
  font-size: 1.2rem;
}

.calculation-formula {
  background-color: white;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.result-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
}

.detail-item {
  color: #666;
}

/* Loading and error states */
.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  margin-bottom: 1rem;
}

.no-data {
  text-align: center;
  padding: 2rem;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .navigation {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-links {
    gap: 1rem;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .manager-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .entity-grid, .connections-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .result-details {
    grid-template-columns: 1fr;
  }
  
  .result-statement-template {
    font-size: 2rem;
    padding: 2rem 1rem;
    line-height: 1.4;
  }
  
  .result-statement-template .template-suffix {
    font-size: 2.2rem;
  }
}

/* Skeleton loading animations */
.skeleton {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite ease-in-out;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-card {
  opacity: 0.7;
}

.skeleton-form {
  opacity: 0.7;
}

.skeleton-form .skeleton-form-title {
  margin-bottom: 1rem;
}

.skeleton-form .skeleton-label {
  margin-bottom: 0.5rem;
}

.skeleton-form .skeleton-input {
  margin-bottom: 1rem;
}

.skeleton-list {
  opacity: 0.7;
}

.skeleton-list .skeleton-list-title {
  margin-bottom: 1rem;
}

.skeleton-list-item {
  margin-bottom: 1rem;
  padding: 0.5rem 0;
}

.skeleton-list-item .skeleton:last-child {
  margin-bottom: 0;
}

/* Loading states */
.loading-overlay {
  position: relative;
}

.loading-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  z-index: 10;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Boundary Styles */
.error-boundary, .api-error-fallback {
  background: white;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 2rem;
  margin: 1rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.error-boundary h2, .api-error-fallback h3 {
  color: #721c24;
  margin-bottom: 1rem;
}

.error-boundary p, .api-error-fallback p {
  color: #721c24;
  margin-bottom: 1rem;
}

.api-error-fallback .error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.api-error-fallback .error-message {
  font-weight: 600;
  font-size: 1.1rem;
}

.api-error-fallback .error-suggestion {
  color: #6c757d;
  font-size: 0.9rem;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1.5rem;
}

.error-details {
  margin-top: 1.5rem;
  text-align: left;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 1rem;
}

.error-details summary {
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.error-details pre {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0.5rem;
  overflow-x: auto;
  font-size: 0.8rem;
  margin: 0.5rem 0;
}

/* Template Form Styles */
.template-form {
  background: white;
  border-radius: 8px;
  padding: 3rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
  margin-top: 1rem;
}

.template-sentence {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 3rem;
  font-weight: 300;
  line-height: 1.5;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.6rem;
  justify-content: center;
  margin-bottom: 2rem;
  letter-spacing: 0.02em;
}

.template-prefix {
  color: #D2691E; /* Orange color to match template */
  font-weight: 400;
}

.template-suffix {
  color: #D2691E; /* Orange color to match template */
  font-weight: 400;
  font-size: 3.2rem;
}

.template-relationship {
  color: #333;
  font-weight: normal;
}

.template-input {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 3rem;
  font-weight: 300;
  border: none !important;
  border-bottom: 2px solid #333 !important;
  border-radius: 0 !important;
  padding: 0.1rem 0.2rem !important;
  text-align: center;
  min-width: 100px;
  background: transparent !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: textfield !important;
  appearance: none !important;
}

.template-input:focus {
  outline: none;
  border-bottom-color: #007bff;
  box-shadow: none;
}

.template-count {
  color: #333;
  font-weight: 300;
  width: 120px;
  border: none !important;
  border-bottom: 2px solid #333 !important;
  background: transparent !important;
  background-color: transparent !important;
  margin: 0 0.1rem;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: textfield !important;
  appearance: none !important;
  padding: 0 0.2rem !important;
  line-height: 1.2 !important;
  vertical-align: baseline !important;
}

.template-measure {
  color: #333;
  font-weight: 300;
  min-width: 150px;
  border: none !important;
  border-bottom: 2px solid #333 !important;
  background: transparent !important;
  background-color: transparent !important;
  margin: 0 0.1rem;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  padding: 0 0.2rem !important;
  line-height: 1.2 !important;
  vertical-align: baseline !important;
}

.template-calculated-value {
  color: #333;
  font-weight: 300;
  border-bottom: 2px solid #333;
  min-width: 100px;
  text-align: center;
  padding: 0.1rem 0.3rem;
  background: transparent;
  display: inline-block;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 3rem;
  margin: 0 0.2rem;
}

.template-dropdown {
  position: relative;
  display: inline-block;
}

/* Seamless entity input styling */
.template-entity-input {
  font-size: 3rem !important;
  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;
  font-weight: 300 !important;
  border: none !important;
  border-bottom: 2px solid #333 !important;
  border-radius: 0 !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  background: transparent !important;
  background-color: transparent !important;
  min-width: 180px;
  text-align: center;
  outline: none !important;
  padding: 0 0.2rem !important;
  margin: 0 0.1rem !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  line-height: 1.2 !important;
  vertical-align: baseline !important;
}

/* Alternative: Contenteditable span approach */
.template-entity-editable {
  font-size: 3rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-weight: 300;
  border-bottom: 2px solid #333;
  background: transparent;
  min-width: 180px;
  text-align: center;
  outline: none;
  padding: 0 0.2rem;
  margin: 0 0.1rem;
  display: inline-block;
  line-height: 1.2;
  vertical-align: baseline;
  cursor: text;
}

.template-entity-editable:focus {
  border-bottom-color: #007bff;
}

.template-entity-editable:empty:before {
  content: attr(data-placeholder);
  color: #ccc;
  font-style: italic;
}

.template-entity-input.from-entity {
  color: #008B8B !important; /* Teal for first entity */
}

.template-entity-input.to-entity {
  color: #808080 !important; /* Gray for second entity */
}

/* Make autocomplete dropdown match the input styling */
.template-entity-input.autocomplete-input {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
  border-bottom: 2px solid #333 !important;
}

.template-entity-input:focus {
  border-bottom-color: #333 !important;
  box-shadow: none !important;
}

/* Style placeholder text to match */
.template-entity-input::placeholder {
  color: #ccc !important;
  font-weight: 300 !important;
  font-style: italic !important;
}

.template-actions {
  text-align: center;
  margin-top: 1rem;
}

/* Additional underline styling */
.template-input:hover {
  border-bottom-color: #666;
}

.template-entity-input:focus {
  border-bottom-color: #007bff !important;
}

.template-relationship {
  color: #333;
  font-weight: 400;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
}

/* Template form responsive design */
@media (max-width: 768px) {
  .template-sentence {
    font-size: 2rem;
    justify-content: flex-start;
    line-height: 1.4;
    gap: 0.4rem;
  }
  
  .template-input {
    font-size: 2rem;
    min-width: 80px;
  }
  
  .template-count {
    width: 90px;
  }
  
  .template-measure {
    min-width: 120px;
  }
  
  .template-entity-input {
    font-size: 2rem !important;
    min-width: 140px;
    font-weight: 300 !important;
    padding: 0.1rem 0.2rem !important;
  }
  
  .template-calculated-value {
    font-size: 2rem;
    font-weight: 300;
    min-width: 80px;
    padding: 0.1rem 0.2rem;
  }
  
  .template-count {
    font-weight: 300;
    width: 100px;
  }
  
  .template-measure {
    font-weight: 300;
    min-width: 120px;
  }
  
  .template-suffix {
    font-size: 2.2rem;
  }
}

/* AutoComplete Styles */
.autocomplete {
  position: relative;
  display: inline-block;
  width: 100%;
}

.autocomplete-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.autocomplete-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Additional AutoComplete styling for template */
.template-dropdown .autocomplete {
  display: inline-block;
  position: relative;
}

.template-dropdown .autocomplete-dropdown {
  font-size: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-weight: 400;
  z-index: 1001;
  min-width: 200px;
  max-width: 300px;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.autocomplete-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.autocomplete-option {
  padding: 0.5rem;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.autocomplete-option:last-child {
  border-bottom: none;
}

.autocomplete-option:hover,
.autocomplete-option.highlighted {
  background-color: #f8f9fa;
}

.autocomplete-option.highlighted {
  background-color: #e3f2fd;
}

.option-name {
  font-weight: 500;
}

.option-id {
  font-size: 0.8rem;
  color: #6c757d;
}

.autocomplete-no-results {
  padding: 0.5rem;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

/* AutoComplete enhanced states */
.autocomplete-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.autocomplete-loading, 
.autocomplete-confirmed {
  position: absolute;
  right: 0.5rem;
  font-size: 0.9rem;
  pointer-events: none;
}

.autocomplete-confirmed {
  color: #28a745;
  font-weight: bold;
}

.autocomplete-loading {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.autocomplete.loading .autocomplete-input {
  background-color: #f8f9fa;
  cursor: wait;
}

.autocomplete.confirmed .autocomplete-input {
  border-color: #28a745;
  background-color: #f8fff8;
}

.autocomplete.error .autocomplete-input {
  border-color: #dc3545;
  background-color: #fff5f5;
}

.autocomplete-error {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  padding-left: 0.5rem;
}

/* EditableSpan color styling */
.template-entity-editable.from-entity {
  color: #008B8B; /* Teal for first entity */
}

.template-entity-editable.to-entity {
  color: #808080; /* Gray for second entity */
}

.template-entity-editable.placeholder {
  color: #ccc;
  font-style: italic;
}

.template-entity-editable:focus {
  border-bottom-color: #333;
  outline: none;
}

/* Remove input number controls */
input[type="number"].template-count::-webkit-outer-spin-button,
input[type="number"].template-count::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"].template-count {
  -moz-appearance: textfield;
}

/* Remove select dropdown styling */
select.template-measure {
  background-image: none;
  padding-right: 0.5rem;
}

select.template-measure::-ms-expand {
  display: none;
}

/* Ultra-seamless entity input styling */
.template-entity-seamless {
  font-size: 3rem !important;
  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;
  font-weight: 300 !important;
  border: none !important;
  border-bottom: 2px solid #333 !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  min-width: 180px;
  text-align: center;
  outline: none !important;
  padding: 0 0.2rem !important;
  margin: 0 0.1rem !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  line-height: 1.2 !important;
  vertical-align: baseline !important;
  border-radius: 0 !important;
}

.template-entity-seamless.autocomplete-input {
  font-size: 3rem !important;
  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;
  font-weight: 300 !important;
  border: none !important;
  border-bottom: 2px solid #333 !important;
  background: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

.template-entity-seamless.from-entity {
  color: #008B8B !important;
}

.template-entity-seamless.to-entity {
  color: #808080 !important;
}

.template-entity-seamless:focus {
  border: none !important;
  border-bottom: 2px solid #333 !important;
  box-shadow: none !important;
  outline: none !important;
}

.template-entity-wrapper {
  display: inline-block;
  position: relative;
}

.template-entity-wrapper .autocomplete {
  display: inline-block;
  width: auto;
}

.template-entity-wrapper .autocomplete-dropdown {
  font-size: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 400;
}

/* Ultra-seamless input styling - complete invisibility except underline */
.template-input-seamless {
  font-size: 3rem !important;
  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;
  font-weight: 300 !important;
  
  /* Completely remove all borders and backgrounds */
  border: none !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  border-bottom: 2px solid #333 !important;
  border-radius: 0 !important;
  
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  
  /* Remove all shadows and outlines */
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  outline: none !important;
  
  /* Remove browser default styling */
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  
  /* Positioning and sizing */
  min-width: 180px;
  max-width: 200px;
  text-align: center;
  padding: 0 0.2rem !important;
  margin: 0 0.1rem !important;
  line-height: 1.2 !important;
  vertical-align: baseline !important;
}

/* Color variations */
.template-input-seamless.from-entity {
  color: #008B8B !important; /* Teal */
}

.template-input-seamless.to-entity {
  color: #808080 !important; /* Gray */
}

/* Focus state - keep the same styling */
.template-input-seamless:focus {
  border: none !important;
  border-bottom: 2px solid #333 !important;
  background: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Placeholder styling */
.template-input-seamless::placeholder {
  color: #ccc !important;
  font-style: italic !important;
  font-weight: 300 !important;
}

/* Remove any autofill styling */
.template-input-seamless:-webkit-autofill,
.template-input-seamless:-webkit-autofill:hover,
.template-input-seamless:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
  -webkit-text-fill-color: inherit !important;
  background-color: transparent !important;
  background: transparent !important;
}