import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navigation from './components/Navigation';
import ComparisonManager from './components/ComparisonManager';
import EntityManager from './components/EntityManager';
import ConnectionManager from './components/ConnectionManager';
import ErrorBoundary from './components/ErrorBoundary';
import ApiErrorFallback from './components/ApiErrorFallback';
import './App.css';

function App() {
  return (
    <ErrorBoundary>
      <Router>
        <div className="App">
          <Navigation />
          <main className="main-content">
            <ErrorBoundary fallback={ApiErrorFallback}>
              <Routes>
                <Route path="/" element={<ComparisonManager />} />
                <Route path="/entities" element={<EntityManager />} />
                <Route path="/connections" element={<ConnectionManager />} />
              </Routes>
            </ErrorBoundary>
          </main>
        </div>
      </Router>
    </ErrorBoundary>
  );
}

export default App;