export interface Entity {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface Unit {
  id: number;
  name: string;
  symbol: string;
  created_at: string;
  updated_at: string;
}

export interface Connection {
  id: number;
  from_entity_id: number;
  to_entity_id: number;
  unit_id: number;
  multiplier: number;
  created_at: string;
  updated_at: string;
}

export interface ComparisonResult {
  from_entity: Entity;
  to_entity: Entity;
  unit: Unit;
  multiplier: number;
  path: Array<{
    from_entity_id: number;
    to_entity_id: number;
    from_entity_name?: string;
    to_entity_name?: string;
    from_entity?: Entity; // Optional for backwards compatibility
    to_entity?: Entity;   // Optional for backwards compatibility
    multiplier: number;
    hop?: number;
  }>;
}

export interface CreateEntityRequest {
  name: string;
}

export interface CreateUnitRequest {
  name: string;
  symbol: string;
}

export interface CreateConnectionRequest {
  from_entity_id: number;
  to_entity_id: number;
  unit_id: number;
  multiplier: number;
}

export interface UpdateConnectionRequest {
  multiplier?: number;
}

export interface HealthResponse {
  status: string;
  service: string;
}

export interface ApiError {
  detail: string;
}