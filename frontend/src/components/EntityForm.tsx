import React, { useState, useEffect } from 'react';
import { Entity, CreateEntityRequest } from '../types/api';
import ApiService from '../services/cachedApi';

interface EntityFormProps {
  entity?: Entity;
  onSuccess: (entity: Entity) => void;
  onCancel: () => void;
}

const EntityForm: React.FC<EntityFormProps> = ({
  entity,
  onSuccess,
  onCancel
}) => {
  // Initialize state with explicit reset values for reliable automated testing
  const [name, setName] = useState(entity?.name || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationState, setValidationState] = useState<'neutral' | 'valid' | 'invalid'>('neutral');
  const [touched, setTouched] = useState(false);
  const [isFormValid, setIsFormValid] = useState(!!entity?.name); // Initialize as valid if editing existing entity
  const [, setRetryAttempts] = useState(0);
  const [showRetry, setShowRetry] = useState(false);

  const isEdit = !!entity;

  // Force complete state reset when component mounts to prevent test interference
  useEffect(() => {
    // Reset all form state to initial values on mount
    setName(entity?.name || '');
    setLoading(false);
    setError(null);
    setValidationState('neutral');
    setTouched(false);
    setIsFormValid(!!entity?.name);
    setRetryAttempts(0);
    setShowRetry(false);
  }, [entity?.name]); // Reset when entity changes (edit vs create)

  // Additional effect to ensure form state is completely clean on mount
  useEffect(() => {
    // This effect runs on every mount to ensure clean state
    const timeout = setTimeout(() => {
      // Double-check form state after a brief delay to ensure React has settled
      if (!entity?.name) {
        setName('');
        setValidationState('neutral');
        setTouched(false);
        setIsFormValid(false);
        setError(null);
      }
    }, 50); // Small delay to ensure React has finished mounting

    return () => clearTimeout(timeout);
  }, []); // Only on mount

  // Real-time validation function
  const validateName = (value: string): { isValid: boolean; message?: string } => {
    const trimmedValue = value.trim();
    
    if (!trimmedValue) {
      return { isValid: false, message: 'Entity name is required' };
    }
    
    // Check trimmed length for consistency
    if (trimmedValue.length > 20) {
      return { isValid: false, message: 'Entity name must be 20 characters or less' };
    }
    
    // Check for consecutive spaces (edge case)
    if (/\s{2,}/.test(value)) {
      return { isValid: false, message: 'Entity name cannot contain consecutive spaces' };
    }
    
    // Check for leading/trailing spaces
    if (value !== trimmedValue) {
      return { isValid: false, message: 'Entity name cannot start or end with spaces' };
    }
    
    if (!/^[a-zA-Z\s]+$/.test(value)) {
      return { isValid: false, message: 'Entity name can only contain letters and spaces' };
    }
    
    return { isValid: true };
  };

  // Update validation state when name changes
  useEffect(() => {
    const validation = validateName(name);
    setIsFormValid(validation.isValid);
    
    
    // Always update validation state for better UX, but only show errors when touched
    if (validation.isValid) {
      setValidationState('valid');
      setError(null);
    } else {
      setValidationState('invalid');
      // Only show error message if touched to avoid showing errors before user interaction
      setError(touched ? (validation.message || null) : null);
    }
  }, [name, touched]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
    if (!touched) setTouched(true);
  };

  const handleNameBlur = () => {
    setTouched(true);
    // Validation state will be updated by useEffect when touched changes
  };

  const handleRetry = () => {
    setRetryAttempts(prev => prev + 1);
    setShowRetry(false);
    handleSubmit(new Event('submit') as any);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Final validation check (should already be validated by real-time validation)
    const validation = validateName(name);
    if (!validation.isValid) {
      setError(validation.message || 'Invalid entity name');
      setValidationState('invalid');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setShowRetry(false);

      const entityData: CreateEntityRequest = { name: name.trim() };
      
      let result: Entity;
      if (isEdit) {
        result = await ApiService.updateEntity(entity.id, entityData);
      } else {
        result = await ApiService.createEntity(entityData);
      }

      onSuccess(result);
    } catch (err: any) {
      // Enhanced error handling with user-friendly messages
      if (err.response?.status === 409) {
        setError('An entity with this name already exists. Please choose a different name.');
      } else if (err.response?.status === 400) {
        const detail = err.response?.data?.detail;
        if (detail && typeof detail === 'string') {
          setError(detail);
        } else {
          setError('The entity name is invalid. Please check the format and try again.');
        }
      } else if (err.response?.status >= 500) {
        setError('Server error occurred. Please try again in a moment.');
      } else if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('Network connection failed. Please check your internet connection and try again.');
        setShowRetry(true);
      } else if (err.response?.data?.detail) {
        setError(err.response.data.detail);
      } else {
        setError(`Unable to ${isEdit ? 'update' : 'create'} entity. Please try again.`);
        setShowRetry(true);
      }
      console.error('Error saving entity:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="entity-form" data-testid="entity-form" onKeyDown={handleKeyDown}>
      <h3 data-testid="entity-form-title">{isEdit ? 'Edit Entity' : 'Create New Entity'}</h3>
      <form onSubmit={handleSubmit} data-testid="entity-form-element">
        <div className="form-group">
          <label htmlFor="entityName">Entity Name:</label>
          <input
            id="entityName"
            type="text"
            value={name}
            onChange={handleNameChange}
            onBlur={handleNameBlur}
            placeholder="Enter entity name (letters and spaces only)"
            maxLength={20}
            disabled={loading}
            autoFocus
            className={`form-input ${touched ? (validationState === 'valid' ? 'valid' : validationState === 'invalid' ? 'invalid' : '') : ''}`}
            data-testid="entity-name-input"
            aria-invalid={validationState === 'invalid'}
            aria-describedby={error ? 'entity-form-error entity-form-help' : 'entity-form-help'}
          />
          <small id="entity-form-help" className="form-help">
            Max 20 characters, letters and spaces only
            {touched && validationState === 'valid' && (
              <span className="validation-success" aria-live="polite"> ✓ Valid</span>
            )}
          </small>
        </div>

        {error && touched && (
          <div id="entity-form-error" className="error-message" data-testid="entity-form-error" role="alert" aria-live="polite">
            {error}
            {showRetry && (
              <button 
                type="button" 
                onClick={handleRetry}
                className="btn-retry"
                disabled={loading}
                style={{ marginTop: '8px', padding: '4px 8px', fontSize: '12px' }}
              >
                Try Again
              </button>
            )}
          </div>
        )}

        <div className="form-actions">
          <button
            type="submit"
            disabled={loading || !isFormValid}
            className="btn-primary"
            data-testid="entity-submit-button"
          >
            {loading ? 'Saving...' : (isEdit ? 'Update' : 'Create')}
          </button>
          <button
            type="button"
            onClick={(e) => {
              console.log('EntityForm: Cancel button clicked');
              e.preventDefault();
              e.stopPropagation();
              console.log('EntityForm: onCancel type:', typeof onCancel);
              console.log('EntityForm: onCancel is:', onCancel);
              if (typeof onCancel === 'function') {
                console.log('EntityForm: Calling onCancel function');
                onCancel();
                console.log('EntityForm: onCancel function called successfully');
              } else {
                console.error('EntityForm: onCancel is not a function!');
                // Manually trigger form close as fallback
                const form = document.querySelector('[data-testid="entity-form"]');
                if (form) {
                  form.remove();
                }
              }
            }}
            disabled={loading}
            className="btn-secondary"
            data-testid="entity-cancel-button"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default EntityForm;