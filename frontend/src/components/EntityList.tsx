import React, { useState, useEffect } from 'react';
import { Entity } from '../types/api';
import ApiService from '../services/cachedApi';
import { SkeletonCard } from './Skeleton';

interface EntityListProps {
  onEntitySelect?: (entity: Entity) => void;
  onEntityEdit?: (entity: Entity) => void;
  onEntityDelete?: (entity: Entity) => void;
  refreshTrigger?: number; // Add explicit refresh trigger
}

const EntityList: React.FC<EntityListProps> = ({
  onEntitySelect,
  onEntityEdit,
  onEntityDelete,
  refreshTrigger
}) => {
  const [entities, setEntities] = useState<Entity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadEntities();
  }, [refreshTrigger]); // Re-load when refreshTrigger changes

  const loadEntities = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Clear cache before fetching to ensure fresh data
      ApiService.cache.delete('entities:list');
      
      const data = await ApiService.getEntities();
      setEntities(data);
    } catch (err) {
      setError('Failed to load entities');
      console.error('Error loading entities:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (entity: Entity) => {
    if (window.confirm(`Are you sure you want to delete "${entity.name}"?`)) {
      try {
        await ApiService.deleteEntity(entity.id);
        setEntities(entities.filter(e => e.id !== entity.id));
        if (onEntityDelete) {
          onEntityDelete(entity);
        }
      } catch (err) {
        setError('Failed to delete entity');
        console.error('Error deleting entity:', err);
      }
    }
  };

  if (loading) {
    return (
      <div className="entity-list">
        <h3>Entities</h3>
        <div className="entity-grid">
          <SkeletonCard count={3} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error">
        <p>{error}</p>
        <button onClick={loadEntities}>Retry</button>
      </div>
    );
  }

  return (
    <div className="entity-list" data-testid="entity-list">
      <h3 data-testid="entity-list-title">Entities ({entities.length})</h3>
      {entities.length === 0 ? (
        <p>No entities found. Create one to get started!</p>
      ) : (
        <div className="entity-grid" data-testid="entity-grid">
          {entities.map(entity => (
            <div key={entity.id} className="entity-card" data-testid={`entity-card-${entity.name}`}>
              <h4 data-testid={`entity-name-${entity.name}`}>{entity.name}</h4>
              <p className="entity-id">ID: {entity.id}</p>
              <div className="entity-actions">
                {onEntitySelect && (
                  <button
                    onClick={() => onEntitySelect(entity)}
                    className="btn-select"
                  >
                    Select
                  </button>
                )}
                {onEntityEdit && (
                  <button
                    onClick={() => onEntityEdit(entity)}
                    className="btn-edit"
                    data-testid={`edit-entity-${entity.name}`}
                  >
                    Edit
                  </button>
                )}
                {onEntityDelete && (
                  <button
                    onClick={() => handleDelete(entity)}
                    className="btn-delete"
                    data-testid={`delete-entity-${entity.name}`}
                  >
                    Delete
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EntityList;