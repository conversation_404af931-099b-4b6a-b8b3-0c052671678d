import React, { useState, useRef, useEffect, useMemo } from 'react';

// Custom hook for debounced values
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
};

interface AutoCompleteOption {
  id: number;
  name: string;
}

interface AutoCompleteProps {
  options: AutoCompleteOption[];
  value: string;
  onChange: (value: string) => void;
  onSelect: (option: AutoCompleteOption) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  debounceMs?: number; // Debounce delay in milliseconds
  'data-testid'?: string;
  loading?: boolean; // Show loading state
  error?: string; // Show error state
}

const AutoComplete: React.FC<AutoCompleteProps> = ({
  options,
  value,
  onChange,
  onSelect,
  placeholder = 'Type to search...',
  disabled = false,
  className = '',
  debounceMs = 100, // Further reduced for better responsiveness in Phase A.3
  'data-testid': dataTestId,
  loading = false,
  error
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [selectedOption, setSelectedOption] = useState<AutoCompleteOption | null>(null);
  const [isSelectionConfirmed, setIsSelectionConfirmed] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Debounce the search value to reduce filtering frequency
  const debouncedSearchValue = useDebounce(value, debounceMs);

  // Memoize filtered options to avoid recalculating on every render
  const filteredOptions = useMemo(() => {
    if (!debouncedSearchValue) {
      return [];
    }
    return options.filter(option =>
      option.name.toLowerCase().includes(debouncedSearchValue.toLowerCase())
    );
  }, [debouncedSearchValue, options]);

  // Improved dropdown visibility logic with faster response
  useEffect(() => {
    const shouldOpen = value.length > 0 && filteredOptions.length > 0 && !isSelectionConfirmed;
    setIsOpen(shouldOpen);
    setHighlightedIndex(-1);
    
    // Clear selection confirmation when user starts typing again
    if (value !== selectedOption?.name) {
      setIsSelectionConfirmed(false);
      setSelectedOption(null);
    }
  }, [filteredOptions, value, isSelectionConfirmed, selectedOption]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const handleInputFocus = () => {
    // Open dropdown if there are potential matches or if the user has typed something
    if (value.length > 0) {
      setIsOpen(true);
    }
  };

  const handleOptionClick = (option: AutoCompleteOption) => {
    onChange(option.name);
    onSelect(option);
    setSelectedOption(option);
    setIsSelectionConfirmed(true);
    setIsOpen(false);
    setHighlightedIndex(-1);
    
    // Brief visual confirmation
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.blur();
      }
    }, 100);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < filteredOptions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => prev > 0 ? prev - 1 : prev);
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
          handleOptionClick(filteredOptions[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  // Extract validation classes to apply to the input element
  const validationClasses = ['valid', 'invalid', 'form-input'];
  const inputValidationClasses = validationClasses.filter(cls => className.includes(cls)).join(' ');
  const wrapperClasses = className.split(' ').filter(cls => !validationClasses.includes(cls)).join(' ');

  return (
    <div className={`autocomplete ${wrapperClasses} ${loading ? 'loading' : ''} ${error ? 'autocomplete-has-error' : ''} ${isSelectionConfirmed ? 'confirmed' : ''}`}>
      <div className="autocomplete-input-wrapper">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled || loading}
          className={`autocomplete-input ${inputValidationClasses}`}
          autoComplete="off"
          data-testid={dataTestId}
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          aria-autocomplete="list"
          role="combobox"
        />
        {loading && <div className="autocomplete-loading" data-testid={`${dataTestId}-loading`}>⏳</div>}
        {isSelectionConfirmed && <div className="autocomplete-confirmed" data-testid={`${dataTestId}-confirmed`}>✓</div>}
      </div>
      {error && <div className="autocomplete-error" role="alert" data-testid={`${dataTestId}-error`}>{error}</div>}
      
      {isOpen && (
        <div 
          ref={dropdownRef} 
          className="autocomplete-dropdown"
          role="listbox"
          data-testid={`${dataTestId}-dropdown`}
        >
          {filteredOptions.map((option, index) => (
            <div
              key={option.id}
              className={`autocomplete-option ${
                index === highlightedIndex ? 'highlighted' : ''
              }`}
              onClick={() => handleOptionClick(option)}
              onMouseEnter={() => setHighlightedIndex(index)}
              role="option"
              aria-selected={index === highlightedIndex}
              data-testid={`${dataTestId}-option-${option.id}`}
              data-option-name={option.name}
              data-option-id={option.id}
            >
              <span className="option-name">{option.name}</span>
              <span className="option-id">ID: {option.id}</span>
            </div>
          ))}
          {filteredOptions.length === 0 && value.length > 0 && (
            <div 
              className="autocomplete-no-results"
              role="option"
              data-testid={`${dataTestId}-no-results`}
            >
              No entities found matching "{value}"
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AutoComplete;
export type { AutoCompleteOption };