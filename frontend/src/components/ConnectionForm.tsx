import React, { useState, useEffect } from 'react';
import { Entity, Unit, CreateConnectionRequest, Connection } from '../types/api';
import ApiService from '../services/cachedApi';
import { SkeletonForm } from './Skeleton';
import AutoComplete, { AutoCompleteOption } from './AutoComplete';

interface ConnectionFormProps {
  onSuccess: (connection: Connection) => void;
  onCancel: () => void;
}

const ConnectionForm: React.FC<ConnectionFormProps> = ({
  onSuccess,
  onCancel
}) => {
  const [entities, setEntities] = useState<Entity[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [fromEntityId, setFromEntityId] = useState<number | ''>('');
  const [toEntityId, setToEntityId] = useState<number | ''>('');
  const [unitId, setUnitId] = useState<number | ''>('');
  const [multiplier, setMultiplier] = useState<string>('');
  const [fromEntityText, setFromEntityText] = useState<string>('');
  const [toEntityText, setToEntityText] = useState<string>('');
  
  // Enhanced setters with instant validation feedback
  const setFromEntityTextWithValidation = (text: string) => {
    setFromEntityText(text);
    
    // Clear entity ID if text doesn't match selected entity (but not during selection)
    const currentEntity = entities.find(e => e.id === fromEntityId);
    if (currentEntity && text !== currentEntity.name && touched.entities) {
      console.log(`Clearing fromEntityId because text "${text}" doesn't match selected entity "${currentEntity.name}"`);
      setFromEntityId('');
    }
    
    if (touched.entities) {
      // Immediate validation when typing if already touched
      setTimeout(() => {
        const validation = validateEntitySelection();
        setValidationStates(prev => ({ ...prev, entities: validation.isValid ? 'valid' : 'invalid' }));
        setValidationErrors(prev => ({ 
          ...prev, 
          entities: validation.message || '' 
        }));
      }, 50); // Increased timeout to avoid race conditions
    }
  };
  
  const setToEntityTextWithValidation = (text: string) => {
    setToEntityText(text);
    
    // Clear entity ID if text doesn't match selected entity (but not during selection)
    const currentEntity = entities.find(e => e.id === toEntityId);
    if (currentEntity && text !== currentEntity.name && touched.entities) {
      console.log(`Clearing toEntityId because text "${text}" doesn't match selected entity "${currentEntity.name}"`);
      setToEntityId('');
    }
    
    if (touched.entities) {
      // Immediate validation when typing if already touched  
      setTimeout(() => {
        const validation = validateEntitySelection();
        setValidationStates(prev => ({ ...prev, entities: validation.isValid ? 'valid' : 'invalid' }));
        setValidationErrors(prev => ({ 
          ...prev, 
          entities: validation.message || '' 
        }));
      }, 50); // Increased timeout to avoid race conditions
    }
  };
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [touched, setTouched] = useState<{[key: string]: boolean}>({});
  const [validationStates, setValidationStates] = useState<{[key: string]: 'neutral' | 'valid' | 'invalid'}>({});
  const [, setRetryAttempts] = useState(0);
  const [showRetry, setShowRetry] = useState(false);

  useEffect(() => {
    loadData();
  }, []);
  
  // Clear entity selections if they no longer exist when entities list changes
  useEffect(() => {
    if (entities.length > 0) {
      if (fromEntityId && !entities.some(e => e.id === Number(fromEntityId))) {
        console.log('Clearing fromEntityId as entity no longer exists');
        setFromEntityId('');
        setFromEntityText('');
      }
      if (toEntityId && !entities.some(e => e.id === Number(toEntityId))) {
        console.log('Clearing toEntityId as entity no longer exists');
        setToEntityId('');
        setToEntityText('');
      }
    }
  }, [entities]);

  const loadData = async () => {
    try {
      setDataLoading(true);
      const [entitiesData, unitsData] = await Promise.all([
        ApiService.getEntities(),
        ApiService.getUnits()
      ]);
      setEntities(entitiesData);
      setUnits(unitsData);
      setError(null);
    } catch (err: any) {
      // Enhanced data loading error handling
      if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('Network connection failed while loading data. Please check your internet connection.');
      } else if (err.response?.status >= 500) {
        setError('Server error occurred while loading data. Please try again in a moment.');
      } else {
        setError('Failed to load entities and units. Please refresh the page and try again.');
      }
      console.error('Error loading data:', err);
    } finally {
      setDataLoading(false);
    }
  };

  // Real-time validation functions
  const validateMultiplier = (value: string): { isValid: boolean; message?: string } => {
    if (!value.trim()) {
      return { isValid: false, message: 'Multiplier is required' };
    }
    
    const num = parseFloat(value);
    if (isNaN(num)) {
      return { isValid: false, message: 'Multiplier must be a valid number' };
    }
    
    if (num <= 0) {
      return { isValid: false, message: 'Multiplier must be a positive number' };
    }
    
    if (Math.round(num * 10) / 10 !== num) {
      return { isValid: false, message: 'Multiplier must have at most 1 decimal place' };
    }
    
    return { isValid: true };
  };

  const validateEntitySelection = (): { isValid: boolean; message?: string } => {
    if (!fromEntityId || !toEntityId) {
      return { isValid: false, message: 'Both entities must be selected' };
    }
    
    if (fromEntityId === toEntityId) {
      return { isValid: false, message: 'From and To entities must be different' };
    }
    
    // Validate that the selected IDs actually exist in the entities list
    const fromEntityExists = entities.some(e => e.id === Number(fromEntityId));
    const toEntityExists = entities.some(e => e.id === Number(toEntityId));
    
    if (!fromEntityExists) {
      return { isValid: false, message: 'Selected "From" entity no longer exists. Please select a different entity.' };
    }
    
    if (!toEntityExists) {
      return { isValid: false, message: 'Selected "To" entity no longer exists. Please select a different entity.' };
    }
    
    return { isValid: true };
  };

  const validateUnit = (): { isValid: boolean; message?: string } => {
    if (!unitId) {
      return { isValid: false, message: 'Unit is required' };
    }
    return { isValid: true };
  };

  // Update validation state for multiplier with improved timing
  useEffect(() => {
    if (!touched.multiplier) return;
    
    const validation = validateMultiplier(multiplier);
    setValidationStates(prev => ({ ...prev, multiplier: validation.isValid ? 'valid' : 'invalid' }));
    setValidationErrors(prev => ({ 
      ...prev, 
      multiplier: validation.message || '' 
    }));
    
    // Debug logging for timing issues
    console.log(`Multiplier validation: value="${multiplier}", isValid=${validation.isValid}, message="${validation.message}"`);
  }, [multiplier, touched.multiplier]);

  // Update validation state for entity selection - only when touched changes, not on every ID change
  useEffect(() => {
    if (!touched.entities) return;
    
    // Use a small delay to ensure all state updates are complete
    const timeoutId = setTimeout(() => {
      const validation = validateEntitySelection();
      setValidationStates(prev => ({ ...prev, entities: validation.isValid ? 'valid' : 'invalid' }));
      setValidationErrors(prev => ({ 
        ...prev, 
        entities: validation.message || '' 
      }));
      
    }, 10);
    
    return () => clearTimeout(timeoutId);
  }, [touched.entities]); // Removed fromEntityId and toEntityId dependencies to avoid race conditions

  // Update validation state for unit
  useEffect(() => {
    if (!touched.unit) return;
    
    const validation = validateUnit();
    setValidationStates(prev => ({ ...prev, unit: validation.isValid ? 'valid' : 'invalid' }));
    setValidationErrors(prev => ({ 
      ...prev, 
      unit: validation.message || '' 
    }));
  }, [unitId, touched.unit]);

  const handleMultiplierChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setMultiplier(newValue);
    setTouched(prev => ({ ...prev, multiplier: true }));
    
    // Immediate validation for better responsiveness - use setTimeout to avoid state race conditions
    setTimeout(() => {
      const validation = validateMultiplier(newValue);
      setValidationStates(prev => ({ ...prev, multiplier: validation.isValid ? 'valid' : 'invalid' }));
      setValidationErrors(prev => ({ 
        ...prev, 
        multiplier: validation.message || '' 
      }));
      console.log(`Multiplier changed: "${newValue}", validation: ${validation.isValid}, message: "${validation.message}"`);
    }, 0);
  };

  const handleMultiplierBlur = () => {
    setTouched(prev => ({ ...prev, multiplier: true }));
    const validation = validateMultiplier(multiplier);
    setValidationStates(prev => ({ ...prev, multiplier: validation.isValid ? 'valid' : 'invalid' }));
    setValidationErrors(prev => ({ 
      ...prev, 
      multiplier: validation.message || '' 
    }));
  };

  const handleUnitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newUnitId = e.target.value ? Number(e.target.value) : '';
    setUnitId(newUnitId);
    setTouched(prev => ({ ...prev, unit: true }));
    
    // Immediate validation for better responsiveness
    setTimeout(() => {
      const validation = validateUnit();
      setValidationStates(prev => ({ ...prev, unit: validation.isValid ? 'valid' : 'invalid' }));
      setValidationErrors(prev => ({ 
        ...prev, 
        unit: validation.message || '' 
      }));
      console.log(`Unit changed: ${newUnitId}, validation: ${validation.isValid}`);
    }, 0);
  };

  const handleUnitBlur = () => {
    setTouched(prev => ({ ...prev, unit: true }));
    const validation = validateUnit();
    setValidationStates(prev => ({ ...prev, unit: validation.isValid ? 'valid' : 'invalid' }));
    setValidationErrors(prev => ({ 
      ...prev, 
      unit: validation.message || '' 
    }));
  };

  const handleRetry = () => {
    setRetryAttempts(prev => prev + 1);
    setShowRetry(false);
    handleSubmit(new Event('submit') as any);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Mark all fields as touched for final validation
    setTouched({ multiplier: true, entities: true, unit: true });

    // Final validation check
    const multiplierValidation = validateMultiplier(multiplier);
    const entityValidation = validateEntitySelection();
    const unitValidation = validateUnit();

    if (!multiplierValidation.isValid || !entityValidation.isValid || !unitValidation.isValid) {
      setError('Please fix all validation errors before submitting');
      setValidationStates({
        multiplier: multiplierValidation.isValid ? 'valid' : 'invalid',
        entities: entityValidation.isValid ? 'valid' : 'invalid',
        unit: unitValidation.isValid ? 'valid' : 'invalid'
      });
      setValidationErrors({
        multiplier: multiplierValidation.message || '',
        entities: entityValidation.message || '',
        unit: unitValidation.message || ''
      });
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccessMessage(null);
      setShowRetry(false);

      const multiplierNum = parseFloat(multiplier);
      const connectionData: CreateConnectionRequest = {
        from_entity_id: Number(fromEntityId),
        to_entity_id: Number(toEntityId),
        unit_id: Number(unitId),
        multiplier: multiplierNum
      };

      console.log('Creating connection with data:', connectionData);
      console.log('Current entities:', entities.map(e => ({ id: e.id, name: e.name })));
      console.log('From entity:', entities.find(e => e.id === connectionData.from_entity_id));
      console.log('To entity:', entities.find(e => e.id === connectionData.to_entity_id));
      
      const result = await ApiService.createConnection(connectionData);
      console.log('Connection created successfully:', result);
      
      // Check if this was an update to an existing connection
      // If created_at and updated_at are different, it means the connection was updated
      const createdTime = new Date(result.created_at).getTime();
      const updatedTime = new Date(result.updated_at).getTime();
      const wasUpdated = updatedTime - createdTime > 1000; // More than 1 second difference
      
      if (wasUpdated) {
        setSuccessMessage('Connection already existed and has been updated with the new multiplier value.');
        // Show the update message briefly before closing
        setTimeout(() => {
          onSuccess(result);
        }, 2000);
      } else {
        onSuccess(result);
      }
    } catch (err: any) {
      // Enhanced error handling with user-friendly messages
      if (err.response?.status === 409) {
        setError('A connection between these entities already exists for this unit. The connection list will be refreshed to show the existing connection.');
        
        // Treat "already exists" as success to trigger refresh and show existing connection
        // Create a minimal mock connection to trigger the existing refresh mechanism
        const mockConnection: Connection = {
          id: -1, // Mock ID to indicate this triggered a refresh
          from_entity_id: Number(fromEntityId),
          to_entity_id: Number(toEntityId), 
          unit_id: Number(unitId),
          multiplier: parseFloat(multiplier),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        // Brief delay to let user see the message, then trigger success callback
        setTimeout(() => {
          onSuccess(mockConnection);
        }, 1500);
      } else if (err.response?.status === 400) {
        const detail = err.response?.data?.detail;
        if (detail && typeof detail === 'string') {
          if (detail.includes('multiplier')) {
            setError('The multiplier value is invalid. Please enter a positive number with at most 1 decimal place.');
          } else if (detail.includes('entity')) {
            setError('One or both entities are invalid. Please select valid entities.');
          } else {
            setError(detail);
          }
        } else {
          setError('The connection data is invalid. Please check all fields and try again.');
        }
      } else if (err.response?.status >= 500) {
        setError('Server error occurred. Please try again in a moment.');
        setShowRetry(true);
      } else if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('Network connection failed. Please check your internet connection and try again.');
        setShowRetry(true);
      } else if (err.response?.data?.detail) {
        setError(err.response.data.detail);
      } else {
        setError('Unable to create connection. Please check your inputs and try again.');
        setShowRetry(true);
      }
      console.error('Error creating connection:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFromEntitySelect = (option: AutoCompleteOption) => {
    // Set both ID and text in a single batch
    setFromEntityId(option.id);
    setFromEntityText(option.name);
    setTouched(prev => ({ ...prev, entities: true }));
    
    // Validation with proper state closure
    setTimeout(() => {
      // Use the option values directly to avoid stale state
      const currentToId = toEntityId; // Capture current value
      const isValid = option.id && currentToId && option.id !== currentToId;
      
      setValidationStates(prev => ({ ...prev, entities: isValid ? 'valid' : 'invalid' }));
      setValidationErrors(prev => ({ 
        ...prev, 
        entities: isValid ? '' : 'Both entities must be selected' 
      }));
    }, 50);
  };

  const handleToEntitySelect = (option: AutoCompleteOption) => {
    // Set both ID and text in a single batch
    setToEntityId(option.id);
    setToEntityText(option.name);
    setTouched(prev => ({ ...prev, entities: true }));
    
    // Validation with proper state closure
    setTimeout(() => {
      // Use the option values directly to avoid stale state
      const currentFromId = fromEntityId; // Capture current value
      const isValid = option.id && currentFromId && option.id !== currentFromId;
      
      setValidationStates(prev => ({ ...prev, entities: isValid ? 'valid' : 'invalid' }));
      setValidationErrors(prev => ({ 
        ...prev, 
        entities: isValid ? '' : 'Both entities must be selected' 
      }));
    }, 50);
  };

  if (dataLoading) {
    return <SkeletonForm fields={4} />;
  }

  if (entities.length === 0) {
    return (
      <div className="no-data">
        <h3>Create Connection</h3>
        <p>You need at least 2 entities to create a connection.</p>
        <button onClick={onCancel} className="btn-secondary">
          Back
        </button>
      </div>
    );
  }

  const fromEntity = entities.find(e => e.id === fromEntityId);
  const toEntity = entities.find(e => e.id === toEntityId);
  const selectedUnit = units.find(u => u.id === unitId);

  return (
    <div className={`connection-form ${loading ? 'loading-overlay' : ''}`} data-testid="connection-form">
      <h3>Create New Connection</h3>
      
      {fromEntity && toEntity && selectedUnit && (
        <div className="connection-preview">
          <p>
            <strong>{fromEntity.name}</strong> is{' '}
            <span className="multiplier">{multiplier || '?'}</span> times{' '}
            <strong>{toEntity.name}</strong> in{' '}
            <strong>{selectedUnit.name}</strong>
          </p>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="fromEntity">From Entity:</label>
            <AutoComplete
              options={entities}
              value={fromEntityText}
              onChange={setFromEntityTextWithValidation}
              onSelect={handleFromEntitySelect}
              placeholder="Type to search entities..."
              disabled={loading}
              loading={dataLoading}
              error={touched.entities && validationStates.entities === 'invalid' ? validationErrors.entities : undefined}
              data-testid="connection-from-entity-input"
              className={`form-input ${touched.entities ? (validationStates.entities === 'valid' ? 'valid' : validationStates.entities === 'invalid' ? 'invalid' : '') : ''}`}
              aria-invalid={touched.entities && validationStates.entities === 'invalid'}
              aria-describedby="entities-error entities-help"
            />
          </div>

          <div className="form-group">
            <label htmlFor="toEntity">To Entity:</label>
            <AutoComplete
              options={entities.filter(entity => entity.id !== fromEntityId)}
              value={toEntityText}
              onChange={setToEntityTextWithValidation}
              onSelect={handleToEntitySelect}
              placeholder="Type to search entities..."
              disabled={loading}
              loading={dataLoading}
              error={touched.entities && validationStates.entities === 'invalid' ? validationErrors.entities : undefined}
              data-testid="connection-to-entity-input"
              className={`form-input ${touched.entities ? (validationStates.entities === 'valid' ? 'valid' : validationStates.entities === 'invalid' ? 'invalid' : '') : ''}`}
              aria-invalid={touched.entities && validationStates.entities === 'invalid'}
              aria-describedby="entities-error entities-help"
            />
          </div>
        </div>

        {touched.entities && validationErrors.entities && (
          <div id="entities-error" className="error-message" data-testid="connection-entities-error" role="alert" aria-live="polite">
            {validationErrors.entities}
          </div>
        )}
        
        <small id="entities-help" className="form-help">
          Select different entities for the connection
          {touched.entities && validationStates.entities === 'valid' && (
            <span className="validation-success" aria-live="polite"> ✓ Valid</span>
          )}
        </small>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="unit">Unit:</label>
            <select
              id="unit"
              value={unitId}
              onChange={handleUnitChange}
              onBlur={handleUnitBlur}
              disabled={loading}
              className={`form-input ${touched.unit ? (validationStates.unit === 'valid' ? 'valid' : validationStates.unit === 'invalid' ? 'invalid' : '') : ''}`}
              aria-invalid={touched.unit && validationStates.unit === 'invalid'}
              aria-describedby="unit-error unit-help"
            >
              <option value="">Select unit...</option>
              {units.map(unit => (
                <option key={unit.id} value={unit.id}>
                  {unit.name} ({unit.symbol})
                </option>
              ))}
            </select>
            {touched.unit && validationErrors.unit && (
              <div id="unit-error" className="error-message" data-testid="connection-unit-error" role="alert" aria-live="polite">
                {validationErrors.unit}
              </div>
            )}
            <small id="unit-help" className="form-help">
              Select a unit for the measurement
              {touched.unit && validationStates.unit === 'valid' && (
                <span className="validation-success" aria-live="polite"> ✓ Valid</span>
              )}
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="multiplier">Multiplier:</label>
            <input
              id="multiplier"
              type="number"
              step="0.1"
              min="0.1"
              value={multiplier}
              onChange={handleMultiplierChange}
              onBlur={handleMultiplierBlur}
              placeholder="e.g., 2.5"
              disabled={loading}
              className={`form-input ${touched.multiplier ? (validationStates.multiplier === 'valid' ? 'valid' : validationStates.multiplier === 'invalid' ? 'invalid' : '') : ''}`}
              data-testid="connection-multiplier-input"
              aria-invalid={touched.multiplier && validationStates.multiplier === 'invalid'}
              aria-describedby="multiplier-error multiplier-help"
            />
            {touched.multiplier && validationErrors.multiplier && (
              <div id="multiplier-error" className="error-message" data-testid="connection-multiplier-error" role="alert" aria-live="polite">
                {validationErrors.multiplier}
              </div>
            )}
            <small id="multiplier-help" className="form-help">
              Positive number with max 1 decimal place
              {touched.multiplier && validationStates.multiplier === 'valid' && (
                <span className="validation-success" aria-live="polite"> ✓ Valid</span>
              )}
            </small>
          </div>
        </div>

        {error && (
          <div className="error-message" data-testid="connection-form-error" role="alert" aria-live="polite">
            {error}
            {showRetry && (
              <button 
                type="button" 
                onClick={handleRetry}
                className="btn-retry"
                disabled={loading}
                style={{ marginTop: '8px', padding: '4px 8px', fontSize: '12px' }}
              >
                Try Again
              </button>
            )}
          </div>
        )}
        
        {successMessage && (
          <div className="success-message" data-testid="connection-form-success" role="status" aria-live="polite">
            {successMessage}
          </div>
        )}

        <div className="form-actions">
          <button
            type="submit"
            disabled={
              loading || 
              !fromEntityId || 
              !toEntityId || 
              !unitId || 
              !multiplier ||
              // Only check validation states if fields have been touched and validate them in real-time
              validateMultiplier(multiplier).isValid === false ||
              validateEntitySelection().isValid === false ||
              validateUnit().isValid === false
            }
            className="btn-primary"
            data-testid="connection-submit-button"
          >
            {loading ? 'Creating...' : 'Create Connection'}
          </button>
          <button
            type="button"
            onClick={onCancel}
            disabled={loading}
            className="btn-secondary"
            data-testid="connection-cancel-button"
          >
            Cancel
          </button>
        </div>

        <div className="form-note">
          <p><strong>Note:</strong> Creating this connection will automatically create the inverse connection.</p>
        </div>
      </form>
      
      {loading && (
        <div className="loading-spinner"></div>
      )}
    </div>
  );
};

export default ConnectionForm;