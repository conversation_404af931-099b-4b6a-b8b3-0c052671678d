import React, { useState, useEffect } from 'react';
import { Connection, Entity, Unit } from '../types/api';
import ApiService from '../services/cachedApi';
import { SkeletonList } from './Skeleton';

interface ConnectionListProps {
  onConnectionDelete?: (connection: Connection) => void;
  onConnectionUpdate?: (connection: Connection) => void;
}

interface ConnectionWithDetails extends Connection {
  from_entity: Entity;
  to_entity: Entity;
  unit: Unit;
}

const ConnectionList: React.FC<ConnectionListProps> = ({
  onConnectionDelete,
  onConnectionUpdate
}) => {
  const [connections, setConnections] = useState<ConnectionWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editingMultiplier, setEditingMultiplier] = useState<string>('');

  useEffect(() => {
    console.log('ConnectionList mounted, loading connections...');
    loadConnections();
  }, []);

  // Add a public method to refresh connections (for debugging)
  const refreshConnections = () => {
    loadConnections();
  };

  // Expose refresh function to window for debugging in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      (window as any).refreshConnections = refreshConnections;
      return () => {
        delete (window as any).refreshConnections;
      };
    }
  }, []);

  const loadConnections = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Aggressively clear all cache to ensure fresh data
      console.log('Clearing all cache before loading connections');
      ApiService.cache.clear();
      
      const [connectionsData, entitiesData, unitsData] = await Promise.all([
        ApiService.getConnections(),
        ApiService.getEntities(),
        ApiService.getUnits()
      ]);

      // Debug logging in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Loading connections - API data:', { 
          connections: connectionsData.length, 
          entities: entitiesData.length, 
          units: unitsData.length 
        });
      }
      
      const connectionsWithDetails = connectionsData.map(connection => {
        const from_entity = entitiesData.find(e => e.id === connection.from_entity_id);
        const to_entity = entitiesData.find(e => e.id === connection.to_entity_id);
        const unit = unitsData.find(u => u.id === connection.unit_id);
        
        if (!from_entity || !to_entity || !unit) {
          console.error('Missing data for connection:', {
            connectionId: connection.id,
            from_entity_id: connection.from_entity_id,
            to_entity_id: connection.to_entity_id,
            unit_id: connection.unit_id,
            found_from: !!from_entity,
            found_to: !!to_entity,
            found_unit: !!unit
          });
        }
        
        return {
          ...connection,
          from_entity: from_entity!,
          to_entity: to_entity!,
          unit: unit!
        };
      }).filter(conn => conn.from_entity && conn.to_entity && conn.unit);

      if (process.env.NODE_ENV === 'development') {
        console.log('Loaded connections:', connectionsWithDetails.length, 'connections');
      }
      setConnections(connectionsWithDetails);
      setError(null);
    } catch (err: any) {
      // Enhanced error handling
      if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('Network connection failed. Please check your internet connection and try again.');
      } else if (err.response?.status >= 500) {
        setError('Server error occurred. Please try again in a moment.');
      } else {
        setError('Failed to load connections. Please refresh and try again.');
      }
      console.error('Error loading connections:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (connection: ConnectionWithDetails) => {
    const confirmMessage = `Are you sure you want to delete the connection: "${connection.from_entity.name} is ${connection.multiplier}x ${connection.to_entity.name} in ${connection.unit.name}"? This will also delete the inverse connection.`;
    
    if (window.confirm(confirmMessage)) {
      try {
        await ApiService.deleteConnection(connection.id);
        setConnections(connections.filter(c => c.id !== connection.id));
        if (onConnectionDelete) {
          onConnectionDelete(connection);
        }
      } catch (err) {
        setError('Failed to delete connection');
        console.error('Error deleting connection:', err);
      }
    }
  };

  const handleEdit = (connection: ConnectionWithDetails) => {
    setEditingId(connection.id);
    setEditingMultiplier(connection.multiplier.toString());
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditingMultiplier('');
  };

  const handleSaveEdit = async (connection: ConnectionWithDetails) => {
    const newMultiplier = parseFloat(editingMultiplier);
    
    if (isNaN(newMultiplier) || newMultiplier <= 0) {
      setError('Multiplier must be a positive number');
      return;
    }

    try {
      const updatedConnection = await ApiService.updateConnection(connection.id, {
        multiplier: newMultiplier
      });
      
      // Update the connection in the list
      setConnections(connections.map(c => 
        c.id === connection.id 
          ? { ...c, multiplier: updatedConnection.multiplier, updated_at: updatedConnection.updated_at }
          : c
      ));
      
      setEditingId(null);
      setEditingMultiplier('');
      
      if (onConnectionUpdate) {
        onConnectionUpdate(updatedConnection);
      }
    } catch (err) {
      setError('Failed to update connection');
      console.error('Error updating connection:', err);
    }
  };

  if (loading) {
    return <SkeletonList items={4} />;
  }

  if (error) {
    return (
      <div className="error">
        <p>{error}</p>
        <button onClick={loadConnections}>Retry</button>
      </div>
    );
  }

  return (
    <div className="connection-list">
      <h3>Connections ({connections.length})</h3>
      {connections.length === 0 ? (
        <p>No connections found. Create one to get started!</p>
      ) : (
        <div className="connections-grid">
          {connections.map(connection => (
            <div key={connection.id} className="connection-card">
              <div className="connection-relationship">
                <span className="entity-name">{connection.from_entity.name}</span>
                <span className="relationship-text">is</span>
                {editingId === connection.id ? (
                  <input
                    type="number"
                    step="0.1"
                    min="0.1"
                    value={editingMultiplier}
                    onChange={(e) => setEditingMultiplier(e.target.value)}
                    className="multiplier-edit"
                    autoFocus
                  />
                ) : (
                  <span className="multiplier">{connection.multiplier}x</span>
                )}
                <span className="entity-name">{connection.to_entity.name}</span>
                <span className="unit-text">in {connection.unit.name}</span>
              </div>
              
              <div className="connection-details">
                <small>ID: {connection.id}</small>
                <small>Unit: {connection.unit.symbol}</small>
              </div>

              <div className="connection-actions">
                {editingId === connection.id ? (
                  <>
                    <button
                      onClick={() => handleSaveEdit(connection)}
                      className="btn-save"
                    >
                      Save
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      className="btn-cancel"
                    >
                      Cancel
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => handleEdit(connection)}
                      className="btn-edit"
                    >
                      Edit
                    </button>
                    {onConnectionDelete && (
                      <button
                        onClick={() => handleDelete(connection)}
                        className="btn-delete"
                      >
                        Delete
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ConnectionList;