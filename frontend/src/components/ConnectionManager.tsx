import React, { useState } from 'react';
import { Connection } from '../types/api';
import ConnectionList from './ConnectionList';
import ConnectionForm from './ConnectionForm';

const ConnectionManager: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleCreateNew = () => {
    setShowForm(true);
  };

  const handleFormSuccess = (connection: Connection) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Connection created successfully:', connection);
    }
    setShowForm(false);
    // Force refresh of connection list
    setRefreshKey(prev => prev + 1);
  };

  const handleFormCancel = () => {
    setShowForm(false);
  };

  const handleConnectionDelete = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleConnectionUpdate = (connection: Connection) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Connection updated successfully:', connection);
    }
    // Force refresh of connection list to show updated data
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="connection-manager" data-testid="connection-manager">
      <div className="manager-header">
        <h2>Connection Management</h2>
        {!showForm && (
          <button onClick={handleCreateNew} className="btn-primary" data-testid="create-new-connection-button">
            Create New Connection
          </button>
        )}
      </div>

      {showForm ? (
        <ConnectionForm
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      ) : (
        <ConnectionList
          key={refreshKey}
          onConnectionDelete={handleConnectionDelete}
          onConnectionUpdate={handleConnectionUpdate}
        />
      )}
    </div>
  );
};

export default ConnectionManager;