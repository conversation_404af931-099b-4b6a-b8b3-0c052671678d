import React from 'react';
import { ErrorFallbackProps } from './ErrorBoundary';

const ApiErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary 
}) => {
  const isNetworkError = error?.message?.includes('Network Error') || 
                        error?.message?.includes('Failed to fetch');
  const isTimeoutError = error?.message?.includes('timeout');
  
  let errorMessage = 'An unexpected error occurred while communicating with the server.';
  let suggestion = 'Please try again in a moment.';
  
  if (isNetworkError) {
    errorMessage = 'Unable to connect to the server.';
    suggestion = 'Please check your internet connection and try again.';
  } else if (isTimeoutError) {
    errorMessage = 'The request took too long to complete.';
    suggestion = 'The server might be busy. Please try again.';
  }

  return (
    <div className="api-error-fallback">
      <div className="error-icon">⚠️</div>
      <h3>Connection Problem</h3>
      <p className="error-message">{errorMessage}</p>
      <p className="error-suggestion">{suggestion}</p>
      
      <div className="error-actions">
        <button onClick={resetErrorBoundary} className="btn-primary">
          Retry
        </button>
        <button 
          onClick={() => window.location.reload()} 
          className="btn-secondary"
        >
          Refresh Page
        </button>
      </div>

      {process.env.NODE_ENV === 'development' && error && (
        <details className="error-details">
          <summary>Development Error Details</summary>
          <pre>{error.message}</pre>
          <pre>{error.stack}</pre>
        </details>
      )}
    </div>
  );
};

export default ApiErrorFallback;