import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navigation: React.FC = () => {
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="navigation">
      <div className="nav-brand">
        <Link to="/" className="brand-link" data-testid="nav-brand-link">
          <h1>SIMILE</h1>
          <p>Entity Comparison System</p>
        </Link>
      </div>
      
      <div className="nav-links">
        <Link 
          to="/" 
          className={`nav-link ${isActive('/') ? 'active' : ''}`}
          data-testid="nav-compare-link"
        >
          Compare
        </Link>
        <Link 
          to="/entities" 
          className={`nav-link ${isActive('/entities') ? 'active' : ''}`}
          data-testid="nav-entities-link"
        >
          Entities
        </Link>
        <Link 
          to="/connections" 
          className={`nav-link ${isActive('/connections') ? 'active' : ''}`}
          data-testid="nav-connections-link"
        >
          Connections
        </Link>
      </div>
    </nav>
  );
};

export default Navigation;