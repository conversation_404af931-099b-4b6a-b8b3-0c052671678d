import React, { useState, useCallback } from 'react';
import { ComparisonResult } from '../types/api';
import ComparisonForm from './ComparisonForm';
import ComparisonResultComponent from './ComparisonResult';

const ComparisonManager: React.FC = () => {
  const [result, setResult] = useState<ComparisonResult | null>(null);

  const handleResult = useCallback((newResult: ComparisonResult | null) => {
    setResult(newResult);
  }, []);

  return (
    <div className="comparison-manager">
      <div className="manager-header">
        <h2>Entity Comparison</h2>
      </div>

      <div className="comparison-content">
        <ComparisonForm onResult={handleResult} />
        
        {result && (
          <div className="result-section">
            <ComparisonResultComponent result={result} />
          </div>
        )}
      </div>
    </div>
  );
};

export default ComparisonManager;