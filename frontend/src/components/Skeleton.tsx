import React from 'react';

interface SkeletonProps {
  width?: string;
  height?: string;
  className?: string;
}

const Skeleton: React.FC<SkeletonProps> = ({ 
  width = '100%', 
  height = '1rem', 
  className = '' 
}) => {
  return (
    <div 
      className={`skeleton ${className}`}
      style={{ width, height }}
    />
  );
};

interface SkeletonCardProps {
  count?: number;
}

const SkeletonCard: React.FC<SkeletonCardProps> = ({ count = 1 }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="entity-card skeleton-card">
          <Skeleton height="1.5rem" className="skeleton-title" />
          <Skeleton height="1rem" width="60%" className="skeleton-subtitle" />
          <div className="entity-actions">
            <Skeleton height="2rem" width="4rem" />
            <Skeleton height="2rem" width="4rem" />
            <Skeleton height="2rem" width="4rem" />
          </div>
        </div>
      ))}
    </>
  );
};

interface SkeletonFormProps {
  fields?: number;
}

const SkeletonForm: React.FC<SkeletonFormProps> = ({ fields = 3 }) => {
  return (
    <div className="skeleton-form">
      <Skeleton height="1.5rem" width="200px" className="skeleton-form-title" />
      <div className="form-row">
        {Array.from({ length: fields }).map((_, index) => (
          <div key={index} className="form-group">
            <Skeleton height="1rem" width="80px" className="skeleton-label" />
            <Skeleton height="2.5rem" className="skeleton-input" />
          </div>
        ))}
      </div>
      <div className="form-actions">
        <Skeleton height="2.5rem" width="100px" />
        <Skeleton height="2.5rem" width="80px" />
      </div>
    </div>
  );
};

interface SkeletonListProps {
  items?: number;
}

const SkeletonList: React.FC<SkeletonListProps> = ({ items = 3 }) => {
  return (
    <div className="skeleton-list">
      <Skeleton height="1.5rem" width="150px" className="skeleton-list-title" />
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="skeleton-list-item">
          <Skeleton height="1rem" width="100%" />
          <Skeleton height="0.8rem" width="70%" />
        </div>
      ))}
    </div>
  );
};

export default Skeleton;
export { SkeletonCard, SkeletonForm, SkeletonList };