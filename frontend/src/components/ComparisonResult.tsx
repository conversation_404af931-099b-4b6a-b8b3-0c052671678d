import React from 'react';
import { ComparisonResult } from '../types/api';

interface ComparisonResultProps {
  result: ComparisonResult;
}

const ComparisonResultComponent: React.FC<ComparisonResultProps> = ({ result }) => {
  const hasMultipleHops = result.path && result.path.length > 1;

  // Determine the relationship text based on unit
  const getRelationshipText = (unitName: string) => {
    switch (unitName.toLowerCase()) {
      case 'length':
        return 'is as tall as';
      case 'mass':
        return 'weighs as much as';
      case 'time':
        return 'takes as long as';
      case 'volume':
        return 'has the same volume as';
      case 'area':
        return 'covers the same area as';
      default:
        return 'is equivalent to';
    }
  };

  const relationshipText = getRelationshipText(result.unit.name);

  return (
    <div className="comparison-result">
      <div className="main-result">
        <div className="result-statement-template">
          <span className="template-prefix">Did you know that</span>
          <span className="template-from-count">{1}</span>
          <span className="template-from-entity">{result.from_entity.name}</span>
          <span className="template-relationship">{relationshipText}</span>
          <span className="template-multiplier">{result.multiplier}</span>
          <span className="template-to-entity">{result.to_entity.name}</span>
          <span className="template-suffix">?</span>
        </div>
      </div>

      {hasMultipleHops && (
        <div className="calculation-path">
          <h4>Calculation Path:</h4>
          <div className="path-steps">
            {result.path.map((step, index) => (
              <div key={index} className="path-step">
                <div className="step-number">{index + 1}</div>
                <div className="step-content">
                  <span className="entity-name">{step.from_entity_name || step.from_entity?.name || 'Unknown'}</span>
                  <span className="relationship-text">is</span>
                  <span className="multiplier">{step.multiplier}x</span>
                  <span className="entity-name">{step.to_entity_name || step.to_entity?.name || 'Unknown'}</span>
                </div>
                {index < result.path.length - 1 && (
                  <div className="step-arrow">↓</div>
                )}
              </div>
            ))}
          </div>
          
          <div className="calculation-formula">
            <h5>Final Calculation:</h5>
            <p>
              {result.path.map(step => step.multiplier).join(' × ')} = {result.multiplier}
            </p>
          </div>
        </div>
      )}

      {!hasMultipleHops && (
        <div className="direct-connection">
          <p className="connection-note">
            This is a direct connection between the entities.
          </p>
        </div>
      )}

      <div className="result-details">
        <div className="detail-item">
          <strong>From:</strong> {result.from_entity.name} (ID: {result.from_entity.id})
        </div>
        <div className="detail-item">
          <strong>To:</strong> {result.to_entity.name} (ID: {result.to_entity.id})
        </div>
        <div className="detail-item">
          <strong>Unit:</strong> {result.unit.name} ({result.unit.symbol})
        </div>
        <div className="detail-item">
          <strong>Path Length:</strong> {result.path ? result.path.length : 1} hop{result.path && result.path.length !== 1 ? 's' : ''}
        </div>
      </div>
    </div>
  );
};

export default ComparisonResultComponent;