import React, { useState } from 'react';
import { Entity } from '../types/api';
import EntityList from './EntityList';
import EntityForm from './EntityForm';
import ErrorBoundary from './ErrorBoundary';
import ApiErrorFallback from './ApiErrorFallback';

const EntityManager: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingEntity, setEditingEntity] = useState<Entity | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [formKey, setFormKey] = useState(0); // Add form key to force remounting

  const handleCreateNew = () => {
    setEditingEntity(null);
    setShowForm(true);
    setFormKey(prev => prev + 1); // Force remount of form for clean state
  };

  const handleEdit = (entity: Entity) => {
    setEditingEntity(entity);
    setShowForm(true);
    setFormKey(prev => prev + 1); // Force remount of form for clean state
  };

  const handleFormSuccess = (entity: Entity) => {
    setShowForm(false);
    setEditingEntity(null);
    setRefreshKey(prev => prev + 1);
    // Note: Don't increment formKey here - it will be incremented when form is next shown
  };

  const handleFormCancel = () => {
    console.log('EntityManager: handleFormCancel called');
    setShowForm(false);
    setEditingEntity(null);
  };

  const handleEntityDelete = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="entity-manager" data-testid="entity-manager">
      <div className="manager-header" data-testid="entity-manager-header">
        <h2 data-testid="entity-manager-title">Entity Management</h2>
        {!showForm && (
          <button onClick={handleCreateNew} className="btn-primary" data-testid="create-new-entity-button">
            Create New Entity
          </button>
        )}
      </div>

      {showForm ? (
        <ErrorBoundary fallback={ApiErrorFallback}>
          <EntityForm
            key={formKey} // Force remount for clean state
            entity={editingEntity || undefined}
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallback={ApiErrorFallback}>
          <EntityList
            key={refreshKey}
            refreshTrigger={refreshKey}
            onEntityEdit={handleEdit}
            onEntityDelete={handleEntityDelete}
          />
        </ErrorBoundary>
      )}
    </div>
  );
};

export default EntityManager;