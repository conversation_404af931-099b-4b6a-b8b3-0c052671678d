#!/usr/bin/env node

/**
 * Test Isolation Verification Script
 * Helps determine if E2E failures are due to test issues or system issues
 */

const { spawn } = require('child_process');

console.log('🔍 E2E Test Isolation Verification\n');
console.log('This will help determine if failures are TEST issues or SYSTEM issues.\n');

// Test 1: Run two tests sequentially (no parallelization)
console.log('📋 Test 1: Running tests SEQUENTIALLY (workers=1)...\n');

const sequentialTest = spawn('npx', [
  'playwright', 'test',
  'e2e/tests/comparisons.spec.ts',
  '--project=chromium',
  '--workers=1',  // Force sequential execution
  '--grep="should calculate direct relationships|should calculate transitive relationships"',
  '--reporter=list'
], {
  cwd: process.cwd(),
  stdio: 'inherit',
  shell: true
});

sequentialTest.on('close', (code) => {
  console.log(`\n📊 Sequential test result: ${code === 0 ? '✅ PASSED' : '❌ FAILED'}\n`);
  
  if (code === 0) {
    console.log('💡 Tests pass when run sequentially. Running parallel test...\n');
    
    // Test 2: Run same tests in parallel
    console.log('📋 Test 2: Running same tests in PARALLEL (workers=2)...\n');
    
    const parallelTest = spawn('npx', [
      'playwright', 'test',
      'e2e/tests/comparisons.spec.ts', 
      '--project=chromium',
      '--workers=2',  // Allow parallel execution
      '--grep="should calculate direct relationships|should calculate transitive relationships"',
      '--reporter=list'
    ], {
      cwd: process.cwd(),
      stdio: 'inherit',
      shell: true
    });
    
    parallelTest.on('close', (parallelCode) => {
      console.log(`\n📊 Parallel test result: ${parallelCode === 0 ? '✅ PASSED' : '❌ FAILED'}\n`);
      
      // Analysis
      console.log('🎯 ANALYSIS:\n');
      
      if (code === 0 && parallelCode !== 0) {
        console.log('❌ TEST ISSUE CONFIRMED: Tests pass sequentially but fail in parallel');
        console.log('   → Problem: Test isolation/cleanup issues');
        console.log('   → Solution: Fix test data cleanup and worker isolation\n');
      } else if (code === 0 && parallelCode === 0) {
        console.log('✅ TESTS STABLE: Both sequential and parallel execution pass');
        console.log('   → The issue may be with other tests in the suite\n');
      }
    });
  } else {
    console.log('❌ SYSTEM ISSUE SUSPECTED: Tests fail even when run sequentially');
    console.log('   → Problem: The application may have actual bugs');
    console.log('   → Action: Manually verify the failing scenarios\n');
  }
});

// Test 3: Database state check
console.log('\n📋 Test 3: Database State Check\n');
console.log('Run this command to check for leftover test data:');
console.log('docker exec -it simile-db psql -U postgres -d simile -c "SELECT name FROM entities WHERE name LIKE \'%Test%\' OR name LIKE \'%TQYO%\' OR name LIKE \'%TSZI%\';"');
console.log('\nIf you see test entities remaining, it indicates cleanup issues.\n');