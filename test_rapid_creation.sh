#!/bin/bash

# Test rapid entity creation functionality
echo "=== Starting Rapid Entity Creation Investigation ==="
echo

# Base URL
BASE_URL="http://localhost:8000"
TIMESTAMP=$(date +%s)

# Function to create an entity and capture response
create_entity() {
    local name="$1"
    local delay="$2"
    
    if [ -n "$delay" ]; then
        sleep "$delay"
    fi
    
    echo "Creating entity: $name"
    response=$(curl -s -w "\nHTTP_STATUS:%{http_code}\nTIME_TOTAL:%{time_total}" \
        -X POST "$BASE_URL/api/v1/entities" \
        -H "Content-Type: application/json" \
        -d "{\"name\":\"$name\"}")
    
    echo "Response: $response"
    echo "---"
}

# Function to clean up entities
cleanup_entities() {
    echo "Cleaning up test entities..."
    # Get all entities and delete those starting with "RapidTest"
    entities=$(curl -s "$BASE_URL/api/v1/entities" | python3 -c "
import sys, json
data = json.load(sys.stdin)
for entity in data:
    if entity['name'].startswith('RapidTest'):
        print(entity['id'])
")
    
    for id in $entities; do
        if [ -n "$id" ]; then
            echo "Deleting entity ID: $id"
            curl -s -X DELETE "$BASE_URL/api/v1/entities/$id"
        fi
    done
}

# Test 1: Sequential creation with no delay (like rapid typing)
echo "=== Test 1: Sequential creation (no delay) ==="
create_entity "RapidTest ${TIMESTAMP} A" 
create_entity "RapidTest ${TIMESTAMP} B"
create_entity "RapidTest ${TIMESTAMP} C"
echo

# Test 2: Sequential creation with small delay (100ms)
echo "=== Test 2: Sequential creation (100ms delay) ==="
create_entity "RapidTest ${TIMESTAMP} D" 
create_entity "RapidTest ${TIMESTAMP} E" 0.1
create_entity "RapidTest ${TIMESTAMP} F" 0.1
echo

# Test 3: Sequential creation with E2E test delay (500ms)
echo "=== Test 3: Sequential creation (500ms delay - E2E style) ==="
create_entity "RapidTest ${TIMESTAMP} G"
create_entity "RapidTest ${TIMESTAMP} H" 0.5
create_entity "RapidTest ${TIMESTAMP} I" 0.5
echo

# Test 4: Parallel creation (background processes)
echo "=== Test 4: Parallel creation (simultaneous) ==="
create_entity "RapidTest ${TIMESTAMP} J" &
create_entity "RapidTest ${TIMESTAMP} K" &
create_entity "RapidTest ${TIMESTAMP} L" &
wait  # Wait for all background processes to complete
echo

# Test 5: Rapid creation with validation edge cases
echo "=== Test 5: Testing validation under rapid creation ==="
create_entity "RapidTest ${TIMESTAMP} M"
create_entity "RapidTest ${TIMESTAMP} M"  # Duplicate name
create_entity "RapidTest${TIMESTAMP}N"     # No space (valid)
create_entity "RapidTest ${TIMESTAMP} O123" # Invalid characters
echo

# Show final state
echo "=== Final Entity List ==="
curl -s "$BASE_URL/api/v1/entities" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(f'Total entities: {len(data)}')
    rapid_test_entities = [e for e in data if 'RapidTest' in e['name']]
    print(f'RapidTest entities: {len(rapid_test_entities)}')
    for entity in rapid_test_entities:
        print(f'  ID {entity[\"id\"]}: {entity[\"name\"]}')
except json.JSONDecodeError as e:
    print(f'Error parsing JSON: {e}')
    print('Raw response:')
    print(sys.stdin.read())
"

# Cleanup
echo
cleanup_entities
echo "Investigation complete."