#!/bin/bash
set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_FILE="test-e2e.log"

# Function to log and display messages
log_message() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Initialize log file
echo "SIMILE E2E Tests - $(date)" > "$LOG_FILE"
echo "=========================" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

log_message "${BLUE}🎭 SIMILE E2E Tests${NC}"
log_message "=================="
log_message ""

# Change to frontend directory
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    log_message "${YELLOW}📦 Installing frontend dependencies...${NC}"
    if ! npm install 2>&1 | tee -a "../$LOG_FILE"; then
        log_message "${RED}❌ Failed to install dependencies${NC}"
        exit 1
    fi
    log_message "${GREEN}✅ Dependencies installed${NC}"
else
    log_message "${GREEN}✅ Dependencies already installed${NC}"
fi

log_message ""

# Check if Playwright browsers are installed
log_message "${YELLOW}🎭 Checking Playwright browsers...${NC}"
if ! npx playwright --version &> /dev/null; then
    log_message "${YELLOW}📦 Installing Playwright browsers...${NC}"
    if ! npm run test:e2e:install 2>&1 | tee -a "../$LOG_FILE"; then
        log_message "${RED}❌ Failed to install Playwright browsers${NC}"
        exit 1
    fi
    log_message "${GREEN}✅ Playwright browsers installed${NC}"
else
    log_message "${GREEN}✅ Playwright browsers already installed${NC}"
fi

log_message ""

# Check if backend services are running (required for E2E tests)
log_message "${BLUE}🔍 Checking backend services...${NC}"
if curl -s -f http://localhost:8000/health >/dev/null 2>&1; then
    log_message "${GREEN}✅ Backend services are running${NC}"
else
    log_message "${RED}❌ Backend services not running - required for E2E tests${NC}"
    log_message "${YELLOW}   Please run: docker-compose -f docker-compose.dev.yml up -d${NC}"
    log_message "${YELLOW}   Or: podman-compose up -d${NC}"
    exit 1
fi

log_message ""

# Check if frontend dev server is running
log_message "${BLUE}🔍 Checking frontend dev server...${NC}"
if curl -s -f http://localhost:3000 >/dev/null 2>&1; then
    log_message "${GREEN}✅ Frontend dev server is running${NC}"
    SERVER_RUNNING=true
else
    log_message "${YELLOW}⚠️  Frontend dev server not running - Playwright will start it${NC}"
    SERVER_RUNNING=false
fi

log_message ""

# Run E2E tests
log_message "${YELLOW}🧪 Running E2E tests...${NC}"
if npm run test:e2e 2>&1 | tee -a "../$LOG_FILE"; then
    TEST_EXIT_CODE=0
    log_message "${GREEN}✅ All E2E tests passed!${NC}"
else
    TEST_EXIT_CODE=1
    log_message "${RED}❌ Some E2E tests failed!${NC}"
fi

log_message ""

# Generate test report
log_message "${YELLOW}📊 Generating test report...${NC}"
if npm run test:e2e:report >/dev/null 2>&1; then
    log_message "${GREEN}✅ Test report generated${NC}"
    log_message "${BLUE}   View report: npm run test:e2e:report${NC}"
else
    log_message "${YELLOW}⚠️  Test report generation failed${NC}"
fi

# Return to project root
cd ..

log_message ""
log_message "E2E test run completed at $(date)"
log_message "Test results saved to: $LOG_FILE"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    log_message "${GREEN}🎉 E2E tests completed successfully!${NC}"
else
    log_message "${RED}💥 E2E tests failed!${NC}"
    log_message "${YELLOW}Check $LOG_FILE for details${NC}"
    log_message "${BLUE}Debug commands:${NC}"
    log_message "${YELLOW}   View failures: cd frontend && npm run test:e2e:report${NC}"
    log_message "${YELLOW}   Debug mode: cd frontend && npm run test:e2e:debug${NC}"
    log_message "${YELLOW}   Interactive: cd frontend && npm run test:e2e:ui${NC}"
fi

exit $TEST_EXIT_CODE