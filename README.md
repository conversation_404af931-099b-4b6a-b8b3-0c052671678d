# SIMILE - Entity Comparison System

SIMILE is a web application that allows users to compare physical entities based on measurable relationships. It calculates transitive relationships automatically (e.g., if A is 2x B and B is 3x C, then A is 6x C).

## Current Status (June 2025)

### ✅ Completed Features
- **Core Backend Services**: Entity, Connection, and Comparison APIs fully implemented
- **Frontend UI**: Complete React application with routing and state management
- **Template Interface**: Seamless comparison interface with native text inputs
- **Database**: PostgreSQL with recursive CTEs for path finding
- **Containerization**: Full Podman/Docker support with compose files
- **Test Infrastructure**: Comprehensive test suites for backend and frontend
- **Error Handling**: Global error boundaries and user-friendly error messages
- **Data Validation**: Input validation on both frontend and backend

### ✅ Recently Completed
- **Test Coverage Goal**: **100% backend test coverage achieved** (119/119 tests passing)
- **Performance Optimization**: Caching and query optimization for large datasets  
- **Documentation**: Comprehensive project documentation maintained

### 📋 Remaining Work
- **Authentication & Authorization**: User accounts and access control
- **Data Import/Export**: CSV/JSON import and export functionality
- **Advanced Features**: Bulk operations, favorites, comparison history
- **Production Deployment**: CI/CD pipeline and production configurations
- **Monitoring**: Logging, metrics, and observability tools

## Project Structure

```
simile-web-app/
├── backend/          # Python FastAPI backend
├── frontend/         # React TypeScript frontend
├── database/         # PostgreSQL database migrations and seeds
├── scripts/          # Utility scripts
├── docs/             # Documentation
├── PLAN.md          # Implementation plan
└── podman-compose.yml
```

## Technology Stack

- **Backend**: Python 3.11 with FastAPI
- **Frontend**: React 18 with TypeScript
- **Database**: PostgreSQL 16
- **Container**: Podman (Docker-compatible)
- **Orchestration**: podman-compose

## Prerequisites

- Podman or Docker installed
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)
- Git flow initialized

## Quick Start

### Using Podman (Recommended)

1. Clone the repository and navigate to the project directory:
   ```bash
   git clone <repository-url>
   cd simile-web-app
   ```

2. Copy the environment variables:
   ```bash
   cp .env.example .env
   ```

3. Build and start all services:
   ```bash
   podman-compose up -d
   ```

4. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/api/v1/docs

### Local Development

#### Backend Setup

1. Create a Python virtual environment:
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the backend:
   ```bash
   uvicorn src.main:app --reload --port 8000
   ```

#### Frontend Setup

1. Install dependencies:
   ```bash
   cd frontend
   npm install
   ```

2. Start the development server:
   ```bash
   npm start
   ```

## Development Commands

### Backend

```bash
# Run tests
cd backend
pytest

# Run linting
flake8 src tests

# Run type checking
mypy src

# Format code
black src tests
isort src tests
```

### Frontend

```bash
# Run tests
cd frontend
npm test

# Run linting
npm run lint

# Run type checking
npm run typecheck

# Build for production
npm run build
```

### Database

```bash
# Connect to database
podman exec -it simile-db psql -U postgres -d simile

# Run migrations manually
podman exec -it simile-db psql -U postgres -d simile -f /docker-entrypoint-initdb.d/001_create_tables.sql
```

## Git Workflow

This project uses git flow for branch management:

```bash
# Start a new feature
git flow feature start feature-name

# Finish a feature
git flow feature finish feature-name

# Start a release
git flow release start 1.0.0

# Finish a release
git flow release finish 1.0.0
```

## API Endpoints

### Entities
- `POST /api/v1/entities` - Create a new entity
- `GET /api/v1/entities` - List all entities
- `GET /api/v1/entities/{id}` - Get entity by ID
- `PUT /api/v1/entities/{id}` - Update entity
- `DELETE /api/v1/entities/{id}` - Delete entity

### Connections
- `POST /api/v1/connections` - Create a connection (auto-creates inverse)
- `GET /api/v1/connections` - List all connections
- `DELETE /api/v1/connections/{id}` - Delete connection

### Units
- `GET /api/v1/units` - List all units
- `POST /api/v1/units` - Create a new unit

### Comparisons
- `GET /api/v1/compare?from={id}&to={id}&unit={id}` - Compare two entities

## Testing

The project includes comprehensive test coverage:

- Unit tests for business logic
- Integration tests for API endpoints
- End-to-end tests for user workflows
- Performance tests with data generators

### Test Status
- **Frontend**: ✅ All tests passing (84% coverage)
- **Backend**: ✅ **100% tests passing** - see [Test Coverage Status](docs/TEST-COVERAGE-FINAL-STATUS.md) for details

Run all tests:
```bash
# Backend tests in container
cd backend && ./run_comprehensive_tests.sh

# Backend tests locally
cd backend && pytest

# Frontend tests
cd frontend && npm test

# Frontend tests with coverage
cd frontend && npm test -- --coverage --watchAll=false
```

## Documentation

- **[Documentation Index](docs/DOCUMENTATION-INDEX.md)** - Complete guide to all project documentation
- **[Development Status](docs/development-status.md)** - Current project status and achievements  
- **[Test Coverage Status](docs/TEST-COVERAGE-FINAL-STATUS.md)** - Single source of truth for test results

## Deployment

See deployment documentation for production deployment instructions.

## Contributing

1. Create a feature branch using git flow
2. Write tests for new functionality
3. Ensure all tests pass and coverage is maintained
4. Submit a pull request to the develop branch

## License

[License information to be added]