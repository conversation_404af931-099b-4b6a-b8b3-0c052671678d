#!/bin/bash
set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_FILE="performance-e2e.log"

# Function to log and display messages
log_message() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

# Initialize log file
echo "SIMILE E2E Performance Tests - $(date)" > "$LOG_FILE"
echo "=======================================" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

log_message "${BLUE}⚡ SIMILE E2E Performance Tests${NC}"
log_message "================================"
log_message "${YELLOW}Running performance and stress tests${NC}"
log_message ""

# Change to frontend directory
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    log_message "${RED}❌ Frontend dependencies not installed!${NC}"
    log_message "${YELLOW}   Please run: npm install${NC}"
    exit 1
fi

# Check if Playwright is installed
if ! npx playwright --version >/dev/null 2>&1; then
    log_message "${RED}❌ Playwright not installed!${NC}"
    log_message "${YELLOW}   Please run: npm run test:e2e:install${NC}"
    exit 1
fi

# Check if backend services are running
log_message "${BLUE}🔍 Checking backend services...${NC}"
if curl -s -f http://localhost:8000/api/v1/health >/dev/null 2>&1; then
    log_message "${GREEN}✅ Backend services are running${NC}"
else
    log_message "${RED}❌ Backend services not accessible${NC}"
    log_message "${YELLOW}   Please run: podman-compose up -d${NC}"
    log_message "${YELLOW}   Or: docker-compose -f docker-compose.dev.yml up -d${NC}"
    exit 1
fi

log_message ""

# Define performance test files
PERFORMANCE_TESTS=(
    "e2e/tests/performance/performance-benchmark.spec.ts"
    "e2e/tests/performance/cleanup-performance-validation.spec.ts"
)

# Run performance tests
log_message "${YELLOW}⚡ Running performance tests...${NC}"
log_message "Tests to run:"
for test in "${PERFORMANCE_TESTS[@]}"; do
    log_message "  - $test"
done
log_message ""

log_message "${YELLOW}⚠️  Performance tests run with single worker for accurate timing${NC}"
log_message ""

# Execute performance tests with single worker for accurate measurements
TEST_EXIT_CODE=0
if npx playwright test \
    "${PERFORMANCE_TESTS[@]}" \
    --reporter=list \
    --workers=1 \
    --timeout=180000 \
    --retries=0 \
    --max-failures=5 \
    2>&1 | tee -a "../$LOG_FILE"; then
    
    log_message "${GREEN}✅ All performance tests passed!${NC}"
else
    TEST_EXIT_CODE=1
    log_message "${RED}❌ Some performance tests failed!${NC}"
fi

# Return to project root
cd ..

log_message ""
log_message "Performance test run completed at $(date)"
log_message "Test results saved to: $LOG_FILE"

if [ $TEST_EXIT_CODE -eq 0 ]; then
    log_message "${GREEN}🎉 Performance tests completed successfully!${NC}"
    log_message "${BLUE}💡 Check test output for performance metrics${NC}"
else
    log_message "${RED}💥 Performance tests failed!${NC}"
    log_message "${YELLOW}Check $LOG_FILE for details${NC}"
    log_message "${YELLOW}Performance issues may indicate system resource constraints${NC}"
fi

exit $TEST_EXIT_CODE
