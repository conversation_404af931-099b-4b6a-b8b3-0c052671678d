# Combined .gitignore file for SIMILE project
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Logs and debugging
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Node and build artifacts
node_modules/
dist/
build/
frontend/build/
frontend/package-lock.json

# Environment variables
.env

# Compiled source
*.class
*.py[cod]

# Package files
*.jar

# Maven/Build directories
target/

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
TEST*.xml
coverage/
*.lcov

# OS specific files
.DS_Store
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Added by Task Master AI
# Logs
logs
# Dependency directories
.idea
.vscode
# OS specific
# Task files
tasks.json
tasks/ 
# Playwright
frontend/playwright-report/
frontend/test-results/

# Debug artifacts and temporary files
frontend/debug-*.png
**/debug-*.png
