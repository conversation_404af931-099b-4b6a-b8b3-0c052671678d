#!/bin/bash

# Test rapid entity creation functionality with correct endpoints
echo "=== Rapid Entity Creation Investigation (Fixed) ==="
echo

BASE_URL="http://localhost:8000"
TIMESTAMP=$(date +%s)

# Function to create an entity
create_entity() {
    local name="$1"
    local test_type="$2"
    
    echo "[$test_type] Creating: $name"
    start_time=$(date +%s.%N)
    
    response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -X POST "$BASE_URL/api/v1/entities/" \
        -H "Content-Type: application/json" \
        -d "{\"name\":\"$name\"}")
    
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc -l)
    
    status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    body=$(echo "$response" | grep -v "HTTP_STATUS:")
    
    if [ "$status" = "201" ]; then
        entity_id=$(echo "$body" | jq -r '.id')
        echo "  ✅ SUCCESS: ID $entity_id (${duration}s)"
    else
        echo "  ❌ FAILED: Status $status"
        echo "  Response: $body"
    fi
    
    echo "  Duration: ${duration}s"
    echo "---"
}

# Test 1: Rapid sequential creation (no delays)
echo "=== Test 1: Rapid Sequential Creation (No Delays) ==="
create_entity "Rapid Sequential $TIMESTAMP A" "Sequential"
create_entity "Rapid Sequential $TIMESTAMP B" "Sequential" 
create_entity "Rapid Sequential $TIMESTAMP C" "Sequential"
echo

# Test 2: Parallel creation using background processes
echo "=== Test 2: Parallel Creation (Simultaneous) ==="
(create_entity "Rapid Parallel $TIMESTAMP X" "Parallel") &
(create_entity "Rapid Parallel $TIMESTAMP Y" "Parallel") &
(create_entity "Rapid Parallel $TIMESTAMP Z" "Parallel") &
wait
echo

# Test 3: Test duplicate names in rapid succession
echo "=== Test 3: Duplicate Name Testing ==="
dup_name="Rapid Duplicate $TIMESTAMP"
create_entity "$dup_name" "Duplicate-1"
create_entity "$dup_name" "Duplicate-2"
echo

# Test 4: Test validation edge cases rapidly
echo "=== Test 4: Validation Edge Cases ==="
create_entity "Rapid Valid $TIMESTAMP" "Valid"
create_entity "Rapid Invalid123 $TIMESTAMP" "Invalid-Chars"
create_entity "Rapid Too Long Name That Exceeds Twenty Characters $TIMESTAMP" "Too-Long"
create_entity "" "Empty-Name"
echo

# Test 5: E2E test simulation (500ms delays)
echo "=== Test 5: E2E Test Simulation (500ms delays) ==="
create_entity "E2E Sim $TIMESTAMP 1" "E2E-Sim"
sleep 0.5
create_entity "E2E Sim $TIMESTAMP 2" "E2E-Sim"
sleep 0.5  
create_entity "E2E Sim $TIMESTAMP 3" "E2E-Sim"
echo

# Test 6: Higher volume rapid creation
echo "=== Test 6: Higher Volume (10 entities rapidly) ==="
for i in {1..10}; do
    create_entity "Volume Test $TIMESTAMP $i" "Volume" &
done
wait
echo

# Show all our test entities
echo "=== Test Results Summary ==="
echo "Fetching our test entities..."
curl -s "$BASE_URL/api/v1/entities/" | jq -r '.[] | select(.name | contains("'$TIMESTAMP'")) | "\(.id): \(.name)"' | sort

# Cleanup function
cleanup_test_entities() {
    echo
    echo "=== Cleanup ==="
    echo "Deleting test entities..."
    
    entity_ids=$(curl -s "$BASE_URL/api/v1/entities/" | jq -r '.[] | select(.name | contains("'$TIMESTAMP'")) | .id')
    
    count=0
    for id in $entity_ids; do
        if [ -n "$id" ]; then
            curl -s -X DELETE "$BASE_URL/api/v1/entities/$id/"
            echo "Deleted entity ID: $id"
            ((count++))
        fi
    done
    
    echo "Deleted $count test entities."
}

cleanup_test_entities
echo "Investigation complete."